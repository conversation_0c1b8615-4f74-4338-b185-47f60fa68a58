// /Users/<USER>/gitlab1/bangliaotian/admin-frontend/pm2.config.cjs

const path = require('path');
const fs = require('fs');

// 智能路径解析函数：优先使用生产路径，失败时回退到当前目录
function resolveWorkingPath() {
  const preferredPath = '/home/<USER>/gitlab/production/admin-frontend';
  const fallbackPath = process.cwd();

  try {
    // 检查首选路径是否存在且可访问
    if (fs.existsSync(preferredPath)) {
      // 尝试在首选路径中创建测试文件来验证写权限
      const testFile = path.join(preferredPath, '.pm2-test');
      try {
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        console.log(`[PM2 Config] 使用生产路径: ${preferredPath}`);
        return preferredPath;
      } catch (writeError) {
        console.warn(`[PM2 Config] 生产路径无写权限，回退到当前目录: ${fallbackPath}`);
        return fallbackPath;
      }
    } else {
      console.log(`[PM2 Config] 生产路径不存在，使用当前目录: ${fallbackPath}`);
      return fallbackPath;
    }
  } catch (error) {
    console.warn(`[PM2 Config] 路径检查失败，使用当前目录: ${fallbackPath}`, error.message);
    return fallbackPath;
  }
}

// 确保日志目录存在
function ensureLogsDirectory(basePath) {
  const logsDir = path.join(basePath, 'logs');
  try {
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
      console.log(`[PM2 Config] 创建日志目录: ${logsDir}`);
    }
    return logsDir;
  } catch (error) {
    console.warn(`[PM2 Config] 无法创建日志目录 ${logsDir}，使用临时目录`);
    // 回退到系统临时目录
    const tempLogsDir = path.join(require('os').tmpdir(), 'admin-frontend-logs');
    try {
      if (!fs.existsSync(tempLogsDir)) {
        fs.mkdirSync(tempLogsDir, { recursive: true });
      }
      return tempLogsDir;
    } catch (tempError) {
      console.error(`[PM2 Config] 无法创建临时日志目录，使用当前目录`);
      return basePath;
    }
  }
}

// 解析工作路径和日志目录
const workingPath = resolveWorkingPath();
const logsDirectory = ensureLogsDirectory(workingPath);

module.exports = {
  apps: [
    {
      // 应用名称
      name: 'admin-frontend',
      // 执行脚本 - 使用 npx 来确保正确执行 serve
      script: 'npx',
      // serve的参数：使用数组格式避免参数解析问题
      args: ['serve', '-s', 'dist', '-p', '54001'],
      // 应用的工作目录 - 使用智能解析的路径
      cwd: workingPath,
      // 禁用监听模式
      watch: false,
      // 自动重启
      autorestart: true,
      // 环境变量
      env: {
        NODE_ENV: 'production',
        PM2_WORKING_PATH: workingPath,
        PM2_LOGS_PATH: logsDirectory,
      },
      // 日志文件路径 - 使用确保存在的日志目录
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: path.join(logsDirectory, 'error.log'),
      out_file: path.join(logsDirectory, 'out.log'),
      merge_logs: true,
      // 进程实例数量
      instances: 1,
      // 执行模式
      exec_mode: 'fork',
      // 重启前的最长内存占用
      max_memory_restart: '500M',
    },
  ],
};