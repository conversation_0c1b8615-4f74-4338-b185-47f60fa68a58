#!/bin/bash

# ChatAdvisor Admin Frontend 独立部署脚本
# 专用于前端服务的快速部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
PROJECT_NAME="Admin Frontend"
SERVICE_NAME="admin-frontend-release"
DEBUG_SERVICE_NAME="admin-frontend-debug"
FRONTEND_PORT="54001"
DEBUG_PORT="34001"

# 显示帮助信息
show_help() {
    echo "ChatAdvisor Admin Frontend 部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --env production    部署到生产环境 (默认)"
    echo "  --env development   部署到开发环境"
    echo "  --build-only        仅构建，不部署"
    echo "  --deploy-only       仅部署，不构建"
    echo "  --help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                  # 构建并部署到生产环境"
    echo "  $0 --env development # 部署到开发环境"
    echo "  $0 --build-only     # 仅构建项目"
}

# 解析命令行参数
parse_args() {
    ENVIRONMENT="production"
    BUILD_ONLY=false
    DEPLOY_ONLY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            --build-only)
                BUILD_ONLY=true
                shift
                ;;
            --deploy-only)
                DEPLOY_ONLY=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置环境相关变量
    if [[ "$ENVIRONMENT" == "development" ]]; then
        SERVICE_NAME="$DEBUG_SERVICE_NAME"
        FRONTEND_PORT="$DEBUG_PORT"
    fi
}

# 检查项目环境
check_environment() {
    log_info "检查前端项目环境..."
    
    # 检查是否在正确的目录
    if [[ ! -f "package.json" ]]; then
        log_error "请在admin-frontend目录下运行此脚本"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 未安装"
        exit 1
    fi
    
    local node_version=$(node --version)
    local npm_version=$(npm --version)
    
    log_info "Node.js版本: $node_version"
    log_info "npm版本: $npm_version"
    log_info "环境: $ENVIRONMENT"
    log_info "服务名: $SERVICE_NAME"
    log_info "端口: $FRONTEND_PORT"
    
    log_success "环境检查完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装前端依赖..."
    
    if [[ -f "package-lock.json" ]]; then
        npm ci --silent
    else
        npm install --silent
    fi
    
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    if [[ "$DEPLOY_ONLY" == true ]]; then
        log_info "跳过构建阶段"
        return 0
    fi
    
    log_info "构建前端项目..."
    
    # 清理旧的构建产物
    if [[ -d "dist" ]]; then
        rm -rf dist
        log_info "已清理旧的构建产物"
    fi
    
    # 代码质量检查
    if npm run | grep -q "lint"; then
        log_info "执行代码质量检查..."
        npm run lint || log_warning "代码质量检查发现问题"
    fi
    
    # 构建项目
    if [[ "$ENVIRONMENT" == "production" ]]; then
        npm run build:prod
    else
        npm run build
    fi
    
    # 验证构建结果
    if [[ ! -d "dist" ]]; then
        log_error "构建失败，未生成dist目录"
        exit 1
    fi
    
    local build_size=$(du -sh dist | cut -f1)
    log_info "构建产物大小: $build_size"
    
    log_success "项目构建完成"
}

# 备份当前版本
backup_current() {
    log_info "备份当前版本..."
    
    if [[ -d "dist" ]]; then
        local backup_name="dist.backup.$(date +%Y%m%d_%H%M%S)"
        cp -r dist "$backup_name"
        log_info "备份已创建: $backup_name"
        
        # 只保留最近5个备份
        ls -dt dist.backup.* 2>/dev/null | tail -n +6 | xargs rm -rf 2>/dev/null || true
        log_info "已清理旧备份"
    else
        log_info "无需备份，dist目录不存在"
    fi
}

# 部署服务
deploy_service() {
    if [[ "$BUILD_ONLY" == true ]]; then
        log_info "跳过部署阶段"
        return 0
    fi
    
    log_info "部署前端服务..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 停止现有服务
    log_info "停止现有服务: $SERVICE_NAME"
    pm2 stop "$SERVICE_NAME" 2>/dev/null || log_info "服务未运行: $SERVICE_NAME"
    
    # 启动服务
    log_info "启动服务: $SERVICE_NAME"
    if [[ "$ENVIRONMENT" == "production" ]]; then
        pm2 start pm2.config.js --only "$SERVICE_NAME"
    else
        pm2 start pm2.config.js --only "$SERVICE_NAME"
    fi
    
    # 保存PM2配置
    pm2 save
    
    log_success "服务部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 5
    
    local max_attempts=10
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "健康检查尝试 $attempt/$max_attempts..."
        
        if curl -f -s "http://localhost:$FRONTEND_PORT" > /dev/null; then
            log_success "前端服务运行正常"
            
            # 检查响应时间
            local response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://localhost:$FRONTEND_PORT")
            log_info "响应时间: ${response_time}秒"
            
            return 0
        else
            log_warning "服务未就绪，等待3秒后重试..."
            sleep 3
            ((attempt++))
        fi
    done
    
    log_error "健康检查失败，服务可能未正常启动"
    return 1
}

# 显示部署信息
show_deployment_info() {
    echo ""
    log_success "🎉 前端部署完成！"
    echo ""
    echo "部署信息:"
    echo "  - 项目: $PROJECT_NAME"
    echo "  - 环境: $ENVIRONMENT"
    echo "  - 服务: $SERVICE_NAME"
    echo "  - 端口: $FRONTEND_PORT"
    echo "  - 访问地址: http://localhost:$FRONTEND_PORT"
    echo ""
    echo "PM2管理命令:"
    echo "  - 查看状态: pm2 status"
    echo "  - 查看日志: pm2 logs $SERVICE_NAME"
    echo "  - 重启服务: pm2 restart $SERVICE_NAME"
    echo "  - 停止服务: pm2 stop $SERVICE_NAME"
    echo ""
    echo "日志文件:"
    echo "  - 综合日志: logs/admin-${ENVIRONMENT}-combined.log"
    echo "  - 错误日志: logs/admin-${ENVIRONMENT}-error.log"
    echo "  - 输出日志: logs/admin-${ENVIRONMENT}-out.log"
    echo ""
}

# 错误处理
handle_error() {
    log_error "部署过程中发生错误"
    
    # 尝试恢复服务
    log_info "尝试恢复服务..."
    pm2 restart "$SERVICE_NAME" 2>/dev/null || log_warning "无法恢复服务"
    
    exit 1
}

# 主函数
main() {
    echo "🚀 ChatAdvisor Admin Frontend 部署开始"
    echo "========================================"
    
    # 设置错误处理
    trap handle_error ERR
    
    # 解析参数
    parse_args "$@"
    
    # 执行部署步骤
    check_environment
    
    if [[ "$DEPLOY_ONLY" != true ]]; then
        install_dependencies
        backup_current
        build_project
    fi
    
    if [[ "$BUILD_ONLY" != true ]]; then
        deploy_service
        health_check
    fi
    
    show_deployment_info
    
    log_success "部署流程完成"
}

# 运行主函数
main "$@"
