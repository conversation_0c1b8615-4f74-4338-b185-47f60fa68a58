#!/bin/bash

# 登录页面环境切换功能测试脚本
# 验证隐藏环境切换功能是否正确实现

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查登录页面文件
check_login_page() {
    log_info "检查登录页面环境切换功能..."
    
    local login_file="src/pages/Login/index.tsx"
    
    if [[ ! -f "$login_file" ]]; then
        log_error "登录页面文件不存在: $login_file"
        return 1
    fi
    
    log_success "✓ 登录页面文件存在"
    
    # 检查关键功能
    local checks=(
        "EnvironmentIndicator"
        "clickCount"
        "showEnvironmentSwitch"
        "handleHiddenClick"
        "fixed bottom-4 right-4"
        "allowSwitch={true}"
    )
    
    for check in "${checks[@]}"; do
        if grep -q "$check" "$login_file"; then
            log_success "✓ 找到 $check"
        else
            log_error "✗ 未找到 $check"
        fi
    done
}

# 检查环境指示器组件
check_environment_indicator() {
    log_info "检查环境指示器组件..."
    
    local indicator_file="src/components/common/EnvironmentIndicator.tsx"
    
    if [[ ! -f "$indicator_file" ]]; then
        log_error "环境指示器组件不存在: $indicator_file"
        return 1
    fi
    
    log_success "✓ 环境指示器组件存在"
    
    # 检查关键功能
    local checks=(
        "allowSwitch"
        "showDetails"
        "switchEnvironment"
        "environmentManager"
    )
    
    for check in "${checks[@]}"; do
        if grep -q "$check" "$indicator_file"; then
            log_success "✓ 找到 $check"
        else
            log_error "✗ 未找到 $check"
        fi
    done
}

# 检查TypeScript类型
check_typescript() {
    log_info "检查TypeScript编译..."
    
    if command -v npx &> /dev/null; then
        # 只检查登录页面相关的文件
        if npx tsc --noEmit --skipLibCheck src/pages/Login/index.tsx 2>/dev/null; then
            log_success "✓ 登录页面TypeScript编译通过"
        else
            log_warning "⚠ 登录页面TypeScript编译有警告"
        fi
    else
        log_warning "⚠ 未找到npx命令，跳过TypeScript检查"
    fi
}

# 生成使用说明
show_usage() {
    log_info "登录页面环境切换功能使用说明:"
    echo ""
    echo "🚀 启动开发服务器:"
    echo "   npm run dev"
    echo ""
    echo "🌐 访问登录页面:"
    echo "   http://localhost:3000/login"
    echo ""
    echo "🔧 激活环境切换:"
    echo "   1. 在登录页面右下角快速点击5次"
    echo "   2. 环境切换面板会自动显示"
    echo "   3. 可以在开发和生产环境间切换"
    echo "   4. 点击X按钮关闭面板"
    echo ""
    echo "⚠️ 注意事项:"
    echo "   - 隐藏点击区域位于右下角，透明但可点击"
    echo "   - 需要在3秒内完成5次点击"
    echo "   - 环境切换功能仅在开发环境下有效"
    echo "   - 切换后建议刷新页面以完全应用新配置"
    echo ""
    echo "🎯 功能特点:"
    echo "   - 隐藏设计，不影响正常登录流程"
    echo "   - 开发者友好，便于测试不同环境"
    echo "   - 安全性高，生产环境下无法切换"
    echo ""
}

# 检查相关依赖
check_dependencies() {
    log_info "检查相关依赖..."
    
    local package_file="package.json"
    
    if [[ ! -f "$package_file" ]]; then
        log_error "package.json 文件不存在"
        return 1
    fi
    
    # 检查必要的依赖
    local deps=(
        "react"
        "react-router-dom"
        "react-hook-form"
    )
    
    for dep in "${deps[@]}"; do
        if grep -q "\"$dep\"" "$package_file"; then
            log_success "✓ 依赖 $dep 已安装"
        else
            log_warning "⚠ 依赖 $dep 可能未安装"
        fi
    done
}

# 验证功能完整性
verify_functionality() {
    log_info "验证功能完整性..."
    
    # 检查是否有语法错误
    local login_file="src/pages/Login/index.tsx"
    
    # 简单的语法检查
    if node -c <(echo "const React = require('react'); $(cat $login_file | sed 's/import.*from.*;//g' | sed 's/export default.*;//g')") 2>/dev/null; then
        log_success "✓ 登录页面语法检查通过"
    else
        log_warning "⚠ 登录页面可能存在语法问题"
    fi
    
    # 检查环境管理器
    if [[ -f "src/utils/environmentManager.ts" ]]; then
        log_success "✓ 环境管理器文件存在"
    else
        log_error "✗ 环境管理器文件不存在"
    fi
}

# 主函数
main() {
    echo "🔧 登录页面环境切换功能测试"
    echo "================================"
    echo ""
    
    # 检查是否在正确的目录
    if [[ ! -f "package.json" ]]; then
        log_error "请在admin-frontend目录下运行此脚本"
        exit 1
    fi
    
    # 执行各项检查
    local all_passed=true
    
    if ! check_login_page; then
        all_passed=false
    fi
    
    check_environment_indicator
    check_dependencies
    check_typescript
    verify_functionality
    
    echo ""
    if $all_passed; then
        log_success "✅ 所有检查通过，登录页面环境切换功能已就绪"
    else
        log_error "❌ 部分检查失败，请修复后重试"
    fi
    
    echo ""
    show_usage
}

# 运行主函数
main "$@"
