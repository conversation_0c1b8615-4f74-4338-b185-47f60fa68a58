#!/bin/bash

# 环境切换脚本
# 用于快速切换开发和生产环境配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "环境切换脚本"
    echo ""
    echo "用法: $0 [环境]"
    echo ""
    echo "环境选项:"
    echo "  dev         切换到开发环境"
    echo "  prod        切换到生产环境"
    echo "  status      显示当前环境状态"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev      # 切换到开发环境"
    echo "  $0 prod     # 切换到生产环境"
    echo "  $0 status   # 查看当前环境"
}

# 检查当前环境状态
check_status() {
    log_info "检查当前环境状态..."
    
    # 检查前端配置
    if [[ -f "src/config/api.ts" ]]; then
        local api_config=$(grep -n "baseURL.*localhost" src/config/api.ts || echo "")
        if [[ -n "$api_config" ]]; then
            log_info "前端API配置: 开发环境 (localhost)"
        else
            log_info "前端API配置: 生产环境 (admin.sanva.top)"
        fi
    fi
    
    # 检查环境变量文件
    if [[ -f ".env.development" ]]; then
        local dev_api=$(grep "VITE_API_BASE_URL" .env.development | cut -d'=' -f2)
        log_info "开发环境API: $dev_api"
    fi
    
    if [[ -f ".env.production" ]]; then
        local prod_api=$(grep "VITE_API_BASE_URL" .env.production | cut -d'=' -f2)
        log_info "生产环境API: $prod_api"
    fi
    
    # 检查后端配置
    if [[ -f "../ChatAdvisorServer/.env" ]]; then
        local backend_db=$(grep "MONGODB_URI=" ../ChatAdvisorServer/.env | head -1 | cut -d'=' -f2)
        log_info "后端当前数据库: $backend_db"
    fi

    log_info "数据库选择逻辑:"
    echo "  - NODE_ENV=development: ChatAdvisor_test"
    echo "  - NODE_ENV=production: ChatAdvisor?replicaSet=rs0"
}

# 切换到开发环境
switch_to_dev() {
    log_info "切换到开发环境..."
    
    # 确保开发环境配置文件存在
    if [[ ! -f ".env.development" ]]; then
        log_error "开发环境配置文件不存在: .env.development"
        exit 1
    fi
    
    log_success "开发环境配置已就绪"
    log_info "开发环境特点:"
    echo "  - API端点: http://localhost:33001"
    echo "  - 数据库: ChatAdvisor_test"
    echo "  - 环境标识: development"
    echo ""
    log_info "启动开发服务器:"
    echo "  npm run dev"
}

# 切换到生产环境
switch_to_prod() {
    log_info "切换到生产环境..."
    
    # 确保生产环境配置文件存在
    if [[ ! -f ".env.production" ]]; then
        log_error "生产环境配置文件不存在: .env.production"
        exit 1
    fi
    
    log_success "生产环境配置已就绪"
    log_info "生产环境特点:"
    echo "  - API端点: https://admin.sanva.top/api"
    echo "  - 数据库: ChatAdvisor?replicaSet=rs0"
    echo "  - 环境标识: production"
    echo ""
    log_info "构建生产版本:"
    echo "  npm run build:prod"
}

# 主函数
main() {
    # 检查是否在正确的目录
    if [[ ! -f "package.json" ]]; then
        log_error "请在admin-frontend目录下运行此脚本"
        exit 1
    fi
    
    case "${1:-help}" in
        "dev")
            switch_to_dev
            ;;
        "prod")
            switch_to_prod
            ;;
        "status")
            check_status
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
