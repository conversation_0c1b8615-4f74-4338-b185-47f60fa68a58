#!/bin/bash

# PM2 环境设置脚本
# 用于安装和配置PM2及相关依赖

echo "🚀 开始设置PM2环境..."

# 检查Node.js版本
echo "📋 检查Node.js版本..."
node_version=$(node --version)
echo "   Node.js版本: $node_version"

# 检查npm/yarn
if command -v yarn &> /dev/null; then
    echo "✅ 发现yarn包管理器"
    PKG_MANAGER="yarn"
elif command -v npm &> /dev/null; then
    echo "✅ 发现npm包管理器"
    PKG_MANAGER="npm"
else
    echo "❌ 未找到包管理器，请先安装Node.js和npm"
    exit 1
fi

# 全局安装PM2
echo "📦 安装PM2..."
if command -v pm2 &> /dev/null; then
    echo "✅ PM2已安装"
    pm2 --version
else
    echo "   正在全局安装PM2..."
    if [ "$PKG_MANAGER" = "yarn" ]; then
        yarn global add pm2
    else
        npm install -g pm2
    fi
    
    if command -v pm2 &> /dev/null; then
        echo "✅ PM2安装成功"
        pm2 --version
    else
        echo "❌ PM2安装失败"
        exit 1
    fi
fi

# 全局安装serve
echo "📦 安装serve..."
if command -v serve &> /dev/null; then
    echo "✅ serve已安装"
    serve --version
else
    echo "   正在全局安装serve..."
    if [ "$PKG_MANAGER" = "yarn" ]; then
        yarn global add serve
    else
        npm install -g serve
    fi
    
    if command -v serve &> /dev/null; then
        echo "✅ serve安装成功"
        serve --version
    else
        echo "❌ serve安装失败"
        exit 1
    fi
fi

# 测试PM2配置
echo "🔍 测试PM2配置..."
if [ -f "scripts/test-pm2-config.cjs" ]; then
    node scripts/test-pm2-config.cjs
else
    echo "⚠️  测试脚本不存在，跳过配置测试"
fi

echo ""
echo "🎉 PM2环境设置完成！"
echo ""
echo "💡 接下来可以使用以下命令："
echo "   yarn pm-test     - 测试PM2配置"
echo "   yarn pm-release  - 构建并启动应用"
echo "   yarn pm-stop     - 停止应用"
echo "   yarn pm-restart  - 重启应用"
echo "   yarn pm-logs     - 查看应用日志"
echo ""
echo "📚 PM2常用命令："
echo "   pm2 list         - 查看所有应用状态"
echo "   pm2 monit        - 监控面板"
echo "   pm2 stop all     - 停止所有应用"
echo "   pm2 delete all   - 删除所有应用"
