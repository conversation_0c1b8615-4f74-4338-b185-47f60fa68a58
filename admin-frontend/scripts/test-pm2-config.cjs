#!/usr/bin/env node

/**
 * PM2配置测试脚本
 * 用于验证PM2配置的路径解析和日志目录创建功能
 */

const path = require('path');
const fs = require('fs');

console.log('🔍 测试PM2配置...\n');

// 加载PM2配置
let pm2Config;
try {
  pm2Config = require('../pm2.config.cjs');
  console.log('✅ PM2配置文件加载成功');
} catch (error) {
  console.error('❌ PM2配置文件加载失败:', error.message);
  process.exit(1);
}

// 获取应用配置
const appConfig = pm2Config.apps[0];
console.log('\n📋 应用配置信息:');
console.log(`   应用名称: ${appConfig.name}`);
console.log(`   工作目录: ${appConfig.cwd}`);
console.log(`   错误日志: ${appConfig.error_file}`);
console.log(`   输出日志: ${appConfig.out_file}`);

// 测试工作目录
console.log('\n🔍 测试工作目录访问权限...');
try {
  const stats = fs.statSync(appConfig.cwd);
  if (stats.isDirectory()) {
    console.log('✅ 工作目录存在且可访问');
    
    // 测试写权限
    const testFile = path.join(appConfig.cwd, '.pm2-write-test');
    try {
      fs.writeFileSync(testFile, 'write test');
      fs.unlinkSync(testFile);
      console.log('✅ 工作目录具有写权限');
    } catch (writeError) {
      console.warn('⚠️  工作目录缺少写权限:', writeError.message);
    }
  } else {
    console.error('❌ 工作目录路径不是目录');
  }
} catch (error) {
  console.error('❌ 工作目录不可访问:', error.message);
}

// 测试日志目录
console.log('\n🔍 测试日志目录...');
const logDir = path.dirname(appConfig.error_file);
try {
  const stats = fs.statSync(logDir);
  if (stats.isDirectory()) {
    console.log('✅ 日志目录存在且可访问');
    
    // 测试日志文件写权限
    const testLogFile = path.join(logDir, '.test-log');
    try {
      fs.writeFileSync(testLogFile, 'log test');
      fs.unlinkSync(testLogFile);
      console.log('✅ 日志目录具有写权限');
    } catch (writeError) {
      console.warn('⚠️  日志目录缺少写权限:', writeError.message);
    }
  } else {
    console.error('❌ 日志目录路径不是目录');
  }
} catch (error) {
  console.error('❌ 日志目录不可访问:', error.message);
}

// 测试dist目录（serve需要）
console.log('\n🔍 测试dist目录...');
const distPath = path.join(appConfig.cwd, 'dist');
try {
  const stats = fs.statSync(distPath);
  if (stats.isDirectory()) {
    console.log('✅ dist目录存在');
    
    // 检查index.html
    const indexPath = path.join(distPath, 'index.html');
    if (fs.existsSync(indexPath)) {
      console.log('✅ index.html文件存在');
    } else {
      console.warn('⚠️  index.html文件不存在，需要先运行 yarn build');
    }
  } else {
    console.warn('⚠️  dist目录不存在，需要先运行 yarn build');
  }
} catch (error) {
  console.warn('⚠️  dist目录不存在，需要先运行 yarn build');
}

// 环境变量检查
console.log('\n🔍 环境变量配置:');
Object.entries(appConfig.env).forEach(([key, value]) => {
  console.log(`   ${key}: ${value}`);
});

console.log('\n🎉 PM2配置测试完成！');
console.log('\n💡 使用建议:');
console.log('   1. 如果dist目录不存在，请先运行: yarn build');
console.log('   2. 启动PM2应用: yarn pm-release');
console.log('   3. 查看PM2状态: pm2 status');
console.log('   4. 查看日志: pm2 logs admin-frontend');
