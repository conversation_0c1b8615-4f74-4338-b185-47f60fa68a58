#!/bin/bash

# 环境切换功能测试脚本
# 验证环境切换功能是否正常工作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要文件
check_files() {
    log_info "检查环境切换相关文件..."
    
    local files=(
        "src/utils/environmentManager.ts"
        "src/components/common/EnvironmentIndicator.tsx"
        "src/pages/EnvironmentDemo.tsx"
        "src/config/api.ts"
        "src/services/api.ts"
        "src/vite-env.d.ts"
    )
    
    local missing_files=()
    
    for file in "${files[@]}"; do
        if [[ -f "$file" ]]; then
            log_success "✓ $file 存在"
        else
            log_error "✗ $file 不存在"
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "缺少必要文件，请检查："
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        return 1
    fi
    
    return 0
}

# 检查TypeScript类型
check_typescript() {
    log_info "检查TypeScript类型定义..."
    
    if command -v npx &> /dev/null; then
        if npx tsc --noEmit --skipLibCheck 2>/dev/null; then
            log_success "✓ TypeScript类型检查通过"
        else
            log_warning "⚠ TypeScript类型检查有警告，但不影响功能"
        fi
    else
        log_warning "⚠ 未找到npx命令，跳过TypeScript检查"
    fi
}

# 检查环境管理器代码
check_environment_manager() {
    log_info "检查环境管理器代码..."
    
    local env_manager="src/utils/environmentManager.ts"
    
    # 检查关键功能
    local checks=(
        "class EnvironmentManager"
        "getCurrentEnvironment"
        "switchEnvironment"
        "hasEnvironmentOverride"
        "resetToDefault"
        "ENVIRONMENTS"
    )
    
    for check in "${checks[@]}"; do
        if grep -q "$check" "$env_manager"; then
            log_success "✓ 找到 $check"
        else
            log_error "✗ 未找到 $check"
        fi
    done
}

# 检查环境指示器组件
check_environment_indicator() {
    log_info "检查环境指示器组件..."
    
    local indicator="src/components/common/EnvironmentIndicator.tsx"
    
    # 检查关键功能
    local checks=(
        "allowSwitch"
        "switchEnvironment"
        "resetToDefault"
        "environmentManager"
        "recreateApiInstance"
    )
    
    for check in "${checks[@]}"; do
        if grep -q "$check" "$indicator"; then
            log_success "✓ 找到 $check"
        else
            log_error "✗ 未找到 $check"
        fi
    done
}

# 检查API配置
check_api_config() {
    log_info "检查API配置..."
    
    local api_config="src/config/api.ts"
    
    # 检查关键功能
    local checks=(
        "getApiConfig"
        "dev_environment_override"
        "recreateApiInstance"
    )
    
    for check in "${checks[@]}"; do
        if grep -q "$check" "$api_config" || grep -q "$check" "src/services/api.ts"; then
            log_success "✓ 找到 $check"
        else
            log_error "✗ 未找到 $check"
        fi
    done
}

# 检查路由配置
check_routes() {
    log_info "检查路由配置..."
    
    local app_file="src/App.tsx"
    
    if grep -q "EnvironmentDemo" "$app_file" && grep -q "/env-demo" "$app_file"; then
        log_success "✓ 环境演示页面路由已配置"
    else
        log_error "✗ 环境演示页面路由未配置"
    fi
}

# 生成使用说明
show_usage() {
    log_info "环境切换功能使用说明:"
    echo ""
    echo "🚀 启动开发服务器:"
    echo "   npm run dev"
    echo ""
    echo "🌐 访问演示页面:"
    echo "   http://localhost:3000/env-demo"
    echo ""
    echo "🔧 环境切换方法:"
    echo "   1. 点击左侧边栏底部的环境指示器"
    echo "   2. 选择要切换的环境"
    echo "   3. 确认刷新页面以应用新配置"
    echo ""
    echo "💻 控制台命令:"
    echo "   - environmentManager.getCurrentEnvironment()"
    echo "   - environmentManager.switchEnvironment('production')"
    echo "   - environmentManager.resetToDefault()"
    echo "   - environmentManager.getDebugInfo()"
    echo ""
    echo "📋 可用环境:"
    echo "   - development: http://localhost:33001"
    echo "   - production: https://admin.sanva.top/api"
    echo ""
    echo "⚠️ 注意事项:"
    echo "   - 环境切换功能仅在开发环境下可用"
    echo "   - 切换后建议刷新页面以完全应用新配置"
    echo "   - 环境设置保存在localStorage中"
    echo ""
}

# 主函数
main() {
    echo "🔧 环境切换功能测试"
    echo "====================="
    echo ""
    
    # 检查是否在正确的目录
    if [[ ! -f "package.json" ]]; then
        log_error "请在admin-frontend目录下运行此脚本"
        exit 1
    fi
    
    # 执行各项检查
    local all_passed=true
    
    if ! check_files; then
        all_passed=false
    fi
    
    check_typescript
    check_environment_manager
    check_environment_indicator
    check_api_config
    check_routes
    
    echo ""
    if $all_passed; then
        log_success "✅ 所有检查通过，环境切换功能已就绪"
    else
        log_error "❌ 部分检查失败，请修复后重试"
    fi
    
    echo ""
    show_usage
}

# 运行主函数
main "$@"
