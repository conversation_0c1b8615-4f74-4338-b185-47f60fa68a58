#!/usr/bin/env node

/**
 * Vite配置测试脚本
 * 验证Vite在不同模式下的配置是否正确
 */

const path = require('path');
const fs = require('fs');

// 颜色定义
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function testViteConfig(mode) {
    log('blue', `\n=== 测试 Vite Mode: ${mode} ===`);
    
    try {
        // 模拟Vite配置加载
        const viteConfigPath = path.join(__dirname, '..', 'vite.config.ts');
        
        if (!fs.existsSync(viteConfigPath)) {
            log('red', '❌ vite.config.ts 文件不存在');
            return;
        }
        
        log('green', '✓ vite.config.ts 文件存在');
        
        // 读取配置文件内容
        const configContent = fs.readFileSync(viteConfigPath, 'utf8');
        
        // 检查关键配置
        const checks = [
            {
                name: '模式检测',
                pattern: /mode === 'production'/,
                description: '检查是否正确检测生产模式'
            },
            {
                name: 'API端点配置',
                pattern: /apiTarget.*admin\.sanva\.top/,
                description: '检查生产环境API端点配置'
            },
            {
                name: '代理配置',
                pattern: /proxy.*\/api/,
                description: '检查API代理配置'
            },
            {
                name: '构建输出',
                pattern: /outDir.*dist/,
                description: '检查构建输出目录'
            },
            {
                name: '路径别名',
                pattern: /@.*path\.resolve/,
                description: '检查路径别名配置'
            }
        ];
        
        checks.forEach(check => {
            if (check.pattern.test(configContent)) {
                log('green', `✓ ${check.name}: ${check.description}`);
            } else {
                log('red', `✗ ${check.name}: ${check.description}`);
            }
        });
        
        // 模拟不同模式下的配置
        const isProduction = mode === 'production';
        const expectedApiTarget = isProduction 
            ? 'https://admin.sanva.top/api' 
            : 'http://localhost:33001';
            
        log('yellow', `模式: ${mode}`);
        log('yellow', `生产环境: ${isProduction}`);
        log('yellow', `预期API端点: ${expectedApiTarget}`);
        
    } catch (error) {
        log('red', `配置测试失败: ${error.message}`);
    }
}

function checkPackageScripts() {
    log('blue', '\n=== 检查 package.json 脚本 ===');
    
    const packagePath = path.join(__dirname, '..', 'package.json');
    
    if (!fs.existsSync(packagePath)) {
        log('red', '❌ package.json 文件不存在');
        return;
    }
    
    try {
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        const scripts = packageJson.scripts || {};
        
        const requiredScripts = [
            {
                name: 'build:prod',
                expected: 'vite build --mode production',
                description: '生产环境构建脚本'
            },
            {
                name: 'dev',
                expected: 'vite',
                description: '开发环境启动脚本'
            },
            {
                name: 'preview',
                expected: 'vite preview',
                description: '预览脚本'
            }
        ];
        
        requiredScripts.forEach(script => {
            if (scripts[script.name]) {
                if (scripts[script.name].includes(script.expected)) {
                    log('green', `✓ ${script.name}: ${script.description}`);
                    log('blue', `  命令: ${scripts[script.name]}`);
                } else {
                    log('yellow', `⚠ ${script.name}: 命令可能不标准`);
                    log('blue', `  实际: ${scripts[script.name]}`);
                    log('blue', `  期望: ${script.expected}`);
                }
            } else {
                log('red', `✗ ${script.name}: 缺少${script.description}`);
            }
        });
        
    } catch (error) {
        log('red', `package.json 解析失败: ${error.message}`);
    }
}

function checkEnvironmentFiles() {
    log('blue', '\n=== 检查环境配置文件 ===');
    
    const envFiles = [
        {
            name: '.env.production',
            description: '生产环境配置',
            required: ['VITE_API_BASE_URL', 'VITE_IS_PRODUCTION']
        },
        {
            name: '.env.development',
            description: '开发环境配置',
            required: ['VITE_API_BASE_URL', 'VITE_IS_PRODUCTION']
        }
    ];
    
    envFiles.forEach(envFile => {
        const filePath = path.join(__dirname, '..', envFile.name);
        
        if (fs.existsSync(filePath)) {
            log('green', `✓ ${envFile.name}: ${envFile.description}`);
            
            const content = fs.readFileSync(filePath, 'utf8');
            
            envFile.required.forEach(variable => {
                if (content.includes(variable)) {
                    const line = content.split('\n').find(line => line.startsWith(variable));
                    log('blue', `  ${line}`);
                } else {
                    log('yellow', `  ⚠ 缺少变量: ${variable}`);
                }
            });
        } else {
            log('red', `✗ ${envFile.name}: 文件不存在`);
        }
    });
}

function checkBuildOutput() {
    log('blue', '\n=== 检查构建输出 ===');
    
    const distPath = path.join(__dirname, '..', 'dist');
    
    if (fs.existsSync(distPath)) {
        log('green', '✓ dist 目录存在');
        
        const indexPath = path.join(distPath, 'index.html');
        if (fs.existsSync(indexPath)) {
            log('green', '✓ index.html 存在');
        } else {
            log('yellow', '⚠ index.html 不存在（可能需要先构建）');
        }
        
        const assetsPath = path.join(distPath, 'assets');
        if (fs.existsSync(assetsPath)) {
            log('green', '✓ assets 目录存在');
            
            const files = fs.readdirSync(assetsPath);
            log('blue', `  资源文件数量: ${files.length}`);
        } else {
            log('yellow', '⚠ assets 目录不存在（可能需要先构建）');
        }
    } else {
        log('yellow', '⚠ dist 目录不存在（需要先运行构建）');
        log('blue', '  运行构建命令: yarn build:prod');
    }
}

function main() {
    log('blue', '🔧 Vite 配置测试');
    log('blue', '==================');
    
    // 检查Vite配置
    testViteConfig('development');
    testViteConfig('production');
    
    // 检查package.json脚本
    checkPackageScripts();
    
    // 检查环境配置文件
    checkEnvironmentFiles();
    
    // 检查构建输出
    checkBuildOutput();
    
    log('green', '\n✅ Vite 配置测试完成');
    
    log('blue', '\n📋 使用说明:');
    log('blue', '开发环境: yarn dev');
    log('blue', '生产构建: yarn build:prod');
    log('blue', '预览构建: yarn preview');
    log('blue', 'PM2部署: yarn pm-release');
}

// 运行测试
main();
