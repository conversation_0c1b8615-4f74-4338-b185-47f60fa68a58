# /Users/<USER>/gitlab1/bangliaotian/admin-frontend/.gitlab-ci.yml

# 工作流触发规则 - 只有包含 release: 的提交消息才会触发流水线
workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^release:/
    - if: $CI_PIPELINE_SOURCE == "web"

variables:
  # 目标部署路径
  DEPLOY_PATH: "/home/<USER>/gitlab/production/admin-frontend"
  # PM2中定义的应用名称
  SERVICE_NAME: "admin-frontend"
  # 服务端口
  PORT: "54001"
  # 使用的Node.js版本
  NODE_VERSION: "21"
  # 远程服务器连接信息
  SSH_USER: "ubuntu"
  SSH_HOST: "**************"

stages:
  - deploy
  - test

# --- SSH SETUP TEMPLATE ---
.ssh_setup:
  before_script:
    - |
      echo "🔧 Setting up SSH connection..."
      eval $(ssh-agent -s)
      echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
      mkdir -p ~/.ssh
      chmod 700 ~/.ssh
      ssh-keyscan -H "$SSH_HOST" >> ~/.ssh/known_hosts
      chmod 644 ~/.ssh/known_hosts
      echo "✅ SSH setup complete."

# --- DEPLOY STAGE ---
deploy_frontend:
  stage: deploy
  tags:
    - Eva
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^release:/

  extends: .ssh_setup
  script:
    - |
      echo "🚀 Starting remote deployment for $SERVICE_NAME..."
      set -e

      # 1. 在Runner上本地构建
      echo "🔧 Setting up Node.js v$NODE_VERSION..."
      export NVM_DIR="$HOME/.nvm"
      [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
      nvm use $NODE_VERSION

      echo "📦 Installing dependencies..."
      yarn install --frozen-lockfile

      echo "🏗️ Building React app for production..."
      # 设置生产环境变量
      export NODE_ENV=production
      rm -rf dist/
      yarn build:prod
      echo "✅ Build complete."

      # 显示构建统计
      echo "📊 Build artifacts summary:"
      du -sh dist/ 2>/dev/null || true
      echo "📁 Checking build output structure:"
      ls -la dist/ 2>/dev/null || echo "⚠️  dist/ directory not found"
      echo "🔍 Looking for entry files:"
      [ -f "dist/index.html" ] && echo "✅ Found dist/index.html" || echo "❌ Missing dist/index.html"

      # 2. 通过Rsync over SSH部署文件
      echo "🚚 Deploying files to $SSH_USER@$SSH_HOST:$DEPLOY_PATH..."
      # 注意：'dist'后面没有斜杠，确保整个目录被复制
      rsync -avz --delete \
        -e "ssh -o StrictHostKeyChecking=no" \
        dist package.json yarn.lock pm2.config.cjs \
        "$SSH_USER@$SSH_HOST:$DEPLOY_PATH/"

      # 3. 通过SSH在远程服务器上执行命令 - 使用yarn pm-release
      echo "🔄 Installing production dependencies and starting service with yarn pm-release..."
      ssh -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "
        set -e
        cd $DEPLOY_PATH

        # 设置生产环境变量
        echo '🔧 Setting production environment variables...'
        export NODE_ENV=production

        echo '📦 Installing production dependencies (for serve) on remote...'
        export NVM_DIR=\"$HOME/.nvm\"
        [ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"
        nvm use $NODE_VERSION
        yarn install --production
        echo '🚀 Starting service with PM2...'
        yarn pm-start-only
        pm2 save
        echo '✅ Service started successfully with PM2.'
      "
      echo "✅ Deployment of $SERVICE_NAME finished successfully."

# --- TEST STAGE ---
health_check:
  stage: test
  tags:
    - Eva
  timeout: 1m
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^release:/

  extends: .ssh_setup
  needs:
    - deploy_frontend
  script:
    - |
      echo "🏥 Performing remote health check for $SERVICE_NAME..."
      set -e
      
      echo "⏳ Waiting 5 seconds for service to become available..."
      sleep 5

      ssh -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "
        set -e
        echo '🔍 Checking PM2 status on remote...'
        if ! pm2 describe $SERVICE_NAME | grep -q 'online'; then
          echo '❌ PM2 service \"\$SERVICE_NAME\" is not online.'
          pm2 logs \"\$SERVICE_NAME\" --lines 20
          exit 1
        fi
        echo '✅ PM2 service is online.'

        echo '🌐 Curling service endpoint on remote...'
        curl -f http://127.0.0.1:$PORT
        
        echo '✅ Health check passed!'
      "
