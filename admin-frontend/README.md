# ChatAdvisor 管理后台

基于 React + TypeScript + Tailwind CSS 构建的现代化管理后台系统。

## 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS 3.x
- **路由管理**: React Router v6
- **状态管理**: React Context + useReducer
- **HTTP客户端**: Axios
- **表单处理**: React Hook Form
- **UI增强**: Headless UI + Heroicons
- **图表库**: Recharts
- **工具库**: date-fns, clsx

## 功能模块

### 已完成 ✅
- **项目基础架构** - React 18 + TypeScript + Vite + Tailwind CSS
- **认证系统** - JWT Token 认证，登录/登出，路由保护
- **基础布局** - 响应式侧边栏导航，头部导航，面包屑
- **UI组件库** - 完整的可复用组件（Button、Input、Table、Modal等）
- **用户管理模块** - 用户列表、详情、编辑、余额管理、状态控制
- **聊天管理模块** - 消息记录、会话管理、内容审核、统计分析
- **财务管理模块** - 交易记录、产品管理、定价配置、收入分析
- **系统管理模块** - 配置管理、系统状态监控、问题库管理
- **日志管理模块** - 请求/响应/错误日志查看、统计、清理
- **仪表板模块** - 系统概览、实时监控、趋势分析、告警展示
- **性能优化** - 错误边界、Toast通知、性能监控、响应式设计

### 待完成 🚧
- **图表集成** - Recharts 或 Chart.js 数据可视化
- **国际化** - i18n 多语言支持
- **主题系统** - 暗色主题切换
- **单元测试** - Jest + React Testing Library
- **E2E测试** - Playwright 或 Cypress
- **PWA支持** - 离线功能、推送通知

## 开发指南

### 环境要求
- Node.js >= 18.0.0
- npm 或 yarn

### 安装依赖
```bash
cd admin-frontend
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 项目结构

```
admin-frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 组件
│   │   ├── common/        # 通用组件
│   │   ├── layout/        # 布局组件
│   │   └── charts/        # 图表组件
│   ├── pages/             # 页面组件
│   │   ├── Login/         # 登录页面
│   │   ├── Dashboard/     # 仪表板
│   │   ├── Users/         # 用户管理
│   │   ├── Chat/          # 聊天管理
│   │   ├── Financial/     # 财务管理
│   │   ├── System/        # 系统管理
│   │   ├── Logs/          # 日志管理
│   │   └── Reports/       # 统计报表
│   ├── hooks/             # 自定义Hooks
│   ├── services/          # API服务
│   ├── context/           # 全局状态
│   ├── types/             # TypeScript类型
│   ├── utils/             # 工具函数
│   └── styles/            # 样式文件
├── package.json
├── vite.config.ts
├── tailwind.config.js
└── tsconfig.json
```

## API接口

管理后台通过代理访问后端API：
- 开发环境：`http://localhost:33001/api`
- 生产环境：根据部署配置

### 主要接口
- 认证：`/api/admin/auth/*`
- 用户管理：`/api/admin/users/*`
- 聊天管理：`/api/admin/chat/*`
- 财务管理：`/api/admin/financial/*`
- 系统管理：`/api/admin/system/*`
- 日志管理：`/api/admin/logs/*`
- 统计报表：`/api/admin/reports/*`

## 开发规范

### 组件开发
- 使用函数组件 + Hooks
- TypeScript 严格模式
- 组件文件命名：PascalCase
- 样式使用 Tailwind CSS 类名

### 状态管理
- 全局状态：React Context
- 本地状态：useState/useReducer
- 表单状态：React Hook Form

### 代码风格
- ESLint + Prettier
- 2空格缩进
- 单引号字符串
- 尾随逗号

## 部署说明

### 构建
```bash
npm run build
```

### 部署
将 `dist` 目录部署到静态文件服务器，确保：
1. 配置正确的API代理
2. 支持SPA路由（History API）
3. 设置合适的缓存策略

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
