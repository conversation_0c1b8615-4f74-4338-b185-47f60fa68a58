# GitLab CI/CD 流水线配置优化总结

## 1. 最终目标

实现通过在 `git commit` 信息中包含 `release:` 关键字，来自动触发 GitLab CI/CD 流水线，从而完成 `admin-frontend` 项目的自动化构建、部署和验证。

## 2. 核心实现

我们对 `.gitlab-ci.yml` 和 `pm2.config.cjs` 文件进行了多次迭代和优化，最终的实现流程如下：

### 触发机制

- **workflow:rules**: 严格规定只有当提交信息匹配 `/release:/` 正则表达式或手动通过 Web 界面触发时，流水线才会运行。

### 流水线阶段 (Stages)

1.  **`build-and-deploy` (构建与部署)**:
    - 将原先分离的构建和部署作业合并为一个原子阶段，消除了在不同作业间传递构建产物（artifacts）可能出现的各种问题，显著提升了流程的可靠性。
    - **构建**: 在临时的 `gitlab-runner` 工作区中，脚本会依次执行：安装依赖 -> 代码质量检查 (Lint) -> 生产环境构建。
    - **部署**:
        - 定义一个持久化的生产目录 `/home/<USER>/gitlab/production/admin-frontend`。
        - 使用 `rsync` 命令高效地将���建好的 `dist.current` 目录和 `package.json`、`pm2.config.cjs` 等运行时文件同步到生产目录。
        - `cd` 进入生产目录，然后以 `ubuntu` 用户的身份启动或**重载** PM2 服务。

2.  **`test` (健康检查)**:
    - 在部署完成后，此阶段会运行健康检查脚本。
    - 脚本首先确认 PM2 服务是否以 `ubuntu` 用户身份成功启动并处于 `online` 状态。
    - 接着，通过 `curl` 命令检查 `http://127.0.0.1:54001` 是否能正常返回内容，确保服务在服务器本地是可访问的。

3.  **`notify` (通知)**:
    - 根据流水线的最终状态（成功或失败），输出对应的通知信息。

## 3. 关键问题与解决方案

在调试过程中，我们遇到了几个核心问题，并通过逐层排查最终定位并解决了根源：

- **问题一：健康检查超时**
  - **现象**: `curl` 无法连接到 `localhost:54001`。
  - **根源**: PM2 的 `serve` 默认监听在 IPv6 地址，而 `curl` 默认连接 IPv4。
  - **解决**: 在 `pm2.config.cjs` 中明确指定监听地址为 `tcp://0.0.0.0:54001`，统一了通信协议。

- **问题二：PM2 进程在流水线结束后消失**
  - **现象**: 流水线成功，但 `ubuntu` 用户看不到 PM2 服务。
  - **根源**: PM2 服务由临时的 `gitlab-runner` 用户启动，生命周期与 CI 作业绑定。
  - **解决**: 改用 `sudo -u ubuntu pm2 ...`，让 `gitlab-runner` 以 `ubuntu` 用户的身份去执行 PM2 命令，使服务持久化。

- **问题三：`sudo` 权限不足**
  - **现象**: 流水线报错 `gitlab-runner 不在 sudoers 文件中` 或 `a terminal is required to read the password`。
  - **根源**: `gitlab-runner` 用户没有执行 `sudo` 的基础权限，且后续添加的规则过于严格，没有覆盖所有需要的命令（如 `mkdir`, `chown`）。
  - **解决**: 在服务器上创建 `/etc/sudoers.d/gitlab-runner-pm2` 文件，内容为 `gitlab-runner ALL=(ALL) NOPASSWD: /usr/bin/pm2, /bin/mkdir, /usr/bin/rsync, /bin/chown`，精确地授予了免密执行所有必要命令的权限。

- **问题四：PM2 日志写入权限错误**
  - **现象**: 流水线报错 `EACCES: permission denied, open '/home/<USER>/...'`。
  - **根源**: 即便 `pm2` 以 `ubuntu` 用户身份运行，但它的**工作目录**仍在 `gitlab-runner` 的主目录中，导致 `ubuntu` 用户没有权限在此创建日志文件。
  - **最终解决**:
    1.  **分离环境**: 彻底分离了临时的构建环境和持久的生产环境。
    2.  **指定部署目录**: 在 CI 脚本中，将所有构建产物和运行时文件同步到一个专门的生产目录 `/home/<USER>/gitlab/production/admin-frontend`。
    3.  **更新PM2配置**: 在 `pm2.config.cjs` 中，将工作目录和日志路径全部更新为指向该生产目录的**绝对路径**。

## 4. 最终效果

通过上述配置，我们实现了一个健壮、安全且可靠的自动化部署流程。现在，任何包含 `release:` 的提交都将自动触发一个可预测的部署，将应用发布到指定的生产目录，并由 `ubuntu` 用户持久化管理，彻底解决了所有权限和路径问题。
