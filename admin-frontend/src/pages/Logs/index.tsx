import React from 'react';
import { Routes, Route } from 'react-router-dom';
import LogsDashboard from './LogsDashboard';
import RequestLogs from './RequestLogs';
import ResponseLogs from './ResponseLogs';
import ErrorLogs from './ErrorLogs';

const Logs: React.FC = () => {
  return (
    <Routes>
      <Route index element={<LogsDashboard />} />
      <Route path="requests" element={<RequestLogs />} />
      <Route path="responses" element={<ResponseLogs />} />
      <Route path="errors" element={<ErrorLogs />} />
    </Routes>
  );
};

export default Logs;
