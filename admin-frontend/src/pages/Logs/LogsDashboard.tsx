import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useApi } from '@/hooks/useApi';
import { systemService } from '@/services/system';
import Loading from '@/components/common/Loading';

interface LogsOverview {
  requestLogs: {
    total: number;
    todayCount: number;
    averageResponseTime: number;
  };
  responseLogs: {
    total: number;
    successRate: number;
    errorRate: number;
  };
  errorLogs: {
    total: number;
    todayCount: number;
    criticalCount: number;
  };
}

const LogsDashboard: React.FC = () => {
  const [overview, setOverview] = useState<LogsOverview | null>(null);

  // API调用
  const {
    loading,
    execute: fetchOverview,
  } = useApi(systemService.getLogsOverview);

  // 加载概览数据
  const loadOverview = async () => {
    try {
      const result = await fetchOverview();
      setOverview(result);
    } catch (error) {
      console.error('加载日志概览失败:', error);
      // 设置默认值以避免页面崩溃
      setOverview({
        requestLogs: {
          total: 0,
          todayCount: 0,
          averageResponseTime: 0,
        },
        responseLogs: {
          total: 0,
          successRate: 0,
          errorRate: 0,
        },
        errorLogs: {
          total: 0,
          todayCount: 0,
          criticalCount: 0,
        },
      });
    }
  };

  // 初始加载
  useEffect(() => {
    loadOverview();
  }, []);

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">日志管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看和管理系统运行日志
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadOverview}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            刷新
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总请求数</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.requestLogs?.total || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">成功率</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.responseLogs?.successRate?.toFixed(1) || '0.0'}%
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">错误数</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.errorLogs?.total || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">平均响应时间</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.requestLogs?.averageResponseTime || 0}ms
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* 快速导航 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link to="/logs/requests" className="block">
          <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">请求日志</h3>
                <p className="text-sm text-gray-500">查看HTTP请求记录</p>
                <div className="mt-2 text-sm text-gray-600">
                  总数: {overview?.requestLogs?.total || 0} | 
                  今日: {overview?.requestLogs?.todayCount || 0}
                </div>
              </div>
            </div>
          </div>
        </Link>

        <Link to="/logs/responses" className="block">
          <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">响应日志</h3>
                <p className="text-sm text-gray-500">查看HTTP响应记录</p>
                <div className="mt-2 text-sm text-gray-600">
                  成功率: {overview?.responseLogs?.successRate?.toFixed(1) || '0.0'}% | 
                  错误率: {overview?.responseLogs?.errorRate?.toFixed(1) || '0.0'}%
                </div>
              </div>
            </div>
          </div>
        </Link>

        <Link to="/logs/errors" className="block">
          <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">错误日志</h3>
                <p className="text-sm text-gray-500">查看系统错误记录</p>
                <div className="mt-2 text-sm text-gray-600">
                  总数: {overview?.errorLogs?.total || 0} | 
                  今日: {overview?.errorLogs?.todayCount || 0}
                </div>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* 最近活动 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">系统状态概览</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">今日活动统计</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">请求数</span>
                  <span className="font-medium">{overview?.requestLogs?.todayCount || 0}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">错误数</span>
                  <span className="font-medium text-red-600">{overview?.errorLogs?.todayCount || 0}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">严重错误</span>
                  <span className="font-medium text-red-600">{overview?.errorLogs?.criticalCount || 0}</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">性能指标</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">平均响应时间</span>
                  <span className="font-medium">{overview?.requestLogs?.averageResponseTime || 0}ms</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">成功率</span>
                  <span className="font-medium text-green-600">{overview?.responseLogs?.successRate?.toFixed(1) || '0.0'}%</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">错误率</span>
                  <span className="font-medium text-red-600">{overview?.responseLogs?.errorRate?.toFixed(1) || '0.0'}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogsDashboard;
