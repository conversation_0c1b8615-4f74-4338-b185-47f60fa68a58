import React, { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { useApi } from '@/hooks/useApi';
import { useTable } from '@/hooks/useTable';
import { systemService } from '@/services/system';
import { ErrorLog } from '@/types/system';
import { TableColumn } from '@/types/common';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';

const ErrorLogs: React.FC = () => {
  const [logs, setLogs] = useState<ErrorLog[]>([]);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({
    level: '',
    dateFrom: '',
    dateTo: '',
  });

  // 表格状态管理
  const {
    tableState,
    pagination,
    sorting,
    searching,
    getQueryParams,
  } = useTable({
    initialPageSize: 20,
    initialSortBy: 'timestamp',
    initialSortOrder: 'desc',
  });

  // API调用
  const {
    loading,
    execute: fetchLogs,
  } = useApi(systemService.getErrorLogs);

  // 加载日志列表
  const loadLogs = useCallback(async () => {
    try {
      const params = {
        ...getQueryParams(),
        ...filters,
      };
      // 清理空值
      Object.keys(params).forEach(key => {
        if (params[key as keyof typeof params] === '') {
          delete params[key as keyof typeof params];
        }
      });
      
      const result = await fetchLogs(params);
      setLogs(result.items);
      setTotal(result.total);
    } catch (error) {
      console.error('加载错误日志失败:', error);
    }
  }, [fetchLogs, getQueryParams, filters]);

  // 初始加载和参数变化时重新加载
  useEffect(() => {
    loadLogs();
  }, [loadLogs]);

  // 筛选器变化处理
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 导出日志
  const handleExport = async (format: 'json' | 'csv') => {
    try {
      const blob = await systemService.exportLogs({
        type: 'error',
        format,
        ...filters,
        limit: 10000,
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `error-logs-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('导出日志失败:', error);
    }
  };

  // 表格列定义
  const columns: TableColumn<ErrorLog>[] = [
    {
      key: 'timestamp',
      title: '错误时间',
      dataIndex: 'timestamp',
      width: 150,
      sortable: true,
      render: (time: string) => (
        <div>
          <p className="text-sm text-gray-900">
            {format(new Date(time), 'yyyy-MM-dd')}
          </p>
          <p className="text-xs text-gray-500">
            {format(new Date(time), 'HH:mm:ss')}
          </p>
        </div>
      ),
    },
    {
      key: 'level',
      title: '级别',
      dataIndex: 'level',
      width: 80,
      render: (level: string = 'error') => (
        <Badge variant={level === 'error' ? 'danger' : level === 'warn' ? 'warning' : 'info'}>
          {level.toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'errorCode',
      title: '错误码',
      dataIndex: 'errorCode',
      width: 100,
      render: (code: number) => (
        <Badge variant="danger">{code}</Badge>
      ),
    },
    {
      key: 'error',
      title: '错误信息',
      dataIndex: 'error',
      render: (error: string) => (
        <div>
          <span className="text-sm text-gray-900" title={error}>
            {error.length > 60 ? `${error.substring(0, 60)}...` : error}
          </span>
        </div>
      ),
    },
    {
      key: 'requestId',
      title: '请求ID',
      dataIndex: 'requestId',
      width: 120,
      render: (id: string) => (
        <span className="text-sm text-gray-600 font-mono">
          {id ? `${id.substring(0, 8)}...` : '-'}
        </span>
      ),
    },
    {
      key: 'stack',
      title: '堆栈信息',
      dataIndex: 'stack',
      render: (stack: string) => (
        <div>
          {stack && (
            <button
              className="text-sm text-blue-600 hover:text-blue-800"
              onClick={() => {
                // 显示完整堆栈信息的模态框
                alert(stack);
              }}
            >
              查看详情
            </button>
          )}
        </div>
      ),
    },
  ];

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">错误日志</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看系统错误记录
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={() => handleExport('csv')}
          >
            导出CSV
          </Button>
          <Button
            variant="secondary"
            onClick={() => handleExport('json')}
          >
            导出JSON
          </Button>
          <Button onClick={loadLogs}>
            刷新
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              搜索
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="搜索错误信息..."
                value={searching.searchTerm}
                onChange={(e) => searching.setSearchTerm(e.target.value)}
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              错误级别
            </label>
            <select
              value={filters.level}
              onChange={(e) => handleFilterChange('level', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有级别</option>
              <option value="error">ERROR</option>
              <option value="warn">WARN</option>
              <option value="info">INFO</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              开始日期
            </label>
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              结束日期
            </label>
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">错误总数</p>
              <p className="text-2xl font-semibold text-gray-900">{total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">今日错误</p>
              <p className="text-2xl font-semibold text-gray-900">
                {logs.filter(log => {
                  const logDate = new Date(log.timestamp);
                  const today = new Date();
                  return logDate.toDateString() === today.toDateString();
                }).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">严重错误</p>
              <p className="text-2xl font-semibold text-gray-900">
                {logs.filter(log => log.level === 'error').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">警告</p>
              <p className="text-2xl font-semibold text-gray-900">
                {logs.filter(log => log.level === 'warn').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 错误日志表格 */}
      <Table
        columns={columns}
        data={logs}
        loading={loading}
        emptyText="暂无错误日志"
        rowKey="_id"
      />

      {/* 分页 */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-700">
          共 {total} 条记录，第 {pagination.currentPage} / {Math.ceil(total / pagination.pageSize)} 页
        </div>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            disabled={pagination.currentPage <= 1}
            onClick={() => pagination.setCurrentPage(pagination.currentPage - 1)}
          >
            上一页
          </Button>
          <Button
            variant="secondary"
            size="sm"
            disabled={pagination.currentPage >= Math.ceil(total / pagination.pageSize)}
            onClick={() => pagination.setCurrentPage(pagination.currentPage + 1)}
          >
            下一页
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ErrorLogs;
