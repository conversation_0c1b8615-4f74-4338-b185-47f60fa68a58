import React, { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { useApi } from '@/hooks/useApi';
import { useTable } from '@/hooks/useTable';
import { systemService } from '@/services/system';
import { RequestLog } from '@/types/system';
import { TableColumn } from '@/types/common';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';

const RequestLogs: React.FC = () => {
  const [logs, setLogs] = useState<RequestLog[]>([]);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({
    url: '',
    method: '',
    dateFrom: '',
    dateTo: '',
  });

  // 表格状态管理
  const {
    tableState,
    pagination,
    sorting,
    searching,
    getQueryParams,
  } = useTable({
    initialPageSize: 20,
    initialSortBy: 'requestTime',
    initialSortOrder: 'desc',
  });

  // API调用
  const {
    loading,
    execute: fetchLogs,
  } = useApi(systemService.getRequestLogs);

  // 加载日志列表
  const loadLogs = useCallback(async () => {
    try {
      const params = {
        ...getQueryParams(),
        ...filters,
      };
      // 清理空值
      Object.keys(params).forEach(key => {
        if (params[key as keyof typeof params] === '') {
          delete params[key as keyof typeof params];
        }
      });
      
      const result = await fetchLogs(params);
      setLogs(result.items);
      setTotal(result.total);
    } catch (error) {
      console.error('加载请求日志失败:', error);
    }
  }, [fetchLogs, getQueryParams, filters]);

  // 初始加载和参数变化时重新加载
  useEffect(() => {
    loadLogs();
  }, [loadLogs]);

  // 筛选器变化处理
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 导出日志
  const handleExport = async (format: 'json' | 'csv') => {
    try {
      const blob = await systemService.exportLogs({
        type: 'request',
        format,
        ...filters,
        limit: 10000,
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `request-logs-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('导出日志失败:', error);
    }
  };

  // 表格列定义
  const columns: TableColumn<RequestLog>[] = [
    {
      key: 'requestTime',
      title: '请求时间',
      dataIndex: 'requestTime',
      width: 150,
      sortable: true,
      render: (time: string) => (
        <div>
          <p className="text-sm text-gray-900">
            {format(new Date(time), 'yyyy-MM-dd')}
          </p>
          <p className="text-xs text-gray-500">
            {format(new Date(time), 'HH:mm:ss')}
          </p>
        </div>
      ),
    },
    {
      key: 'method',
      title: '方法',
      dataIndex: 'method',
      width: 80,
      render: (method: string) => (
        <Badge variant={method === 'GET' ? 'info' : method === 'POST' ? 'success' : 'warning'}>
          {method}
        </Badge>
      ),
    },
    {
      key: 'url',
      title: 'URL',
      dataIndex: 'url',
      render: (url: string) => (
        <span className="text-sm text-gray-900 font-mono" title={url}>
          {url.length > 50 ? `${url.substring(0, 50)}...` : url}
        </span>
      ),
    },
    {
      key: 'ip',
      title: 'IP地址',
      dataIndex: 'ip',
      width: 120,
      render: (ip: string) => (
        <span className="text-sm text-gray-600 font-mono">{ip || '-'}</span>
      ),
    },
    {
      key: 'userId',
      title: '用户ID',
      dataIndex: 'userId',
      width: 120,
      render: (userId: string) => (
        <span className="text-sm text-gray-600 font-mono">
          {userId ? `${userId.substring(0, 8)}...` : '-'}
        </span>
      ),
    },
    {
      key: 'userAgent',
      title: 'User Agent',
      dataIndex: 'userAgent',
      render: (userAgent: string) => (
        <span className="text-sm text-gray-600" title={userAgent}>
          {userAgent ? (userAgent.length > 30 ? `${userAgent.substring(0, 30)}...` : userAgent) : '-'}
        </span>
      ),
    },
  ];

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">请求日志</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看HTTP请求记录
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={() => handleExport('csv')}
          >
            导出CSV
          </Button>
          <Button
            variant="secondary"
            onClick={() => handleExport('json')}
          >
            导出JSON
          </Button>
          <Button onClick={loadLogs}>
            刷新
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              搜索
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="搜索URL或用户..."
                value={searching.searchTerm}
                onChange={(e) => searching.setSearchTerm(e.target.value)}
                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              URL路径
            </label>
            <input
              type="text"
              placeholder="URL路径"
              value={filters.url}
              onChange={(e) => handleFilterChange('url', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              请求方法
            </label>
            <select
              value={filters.method}
              onChange={(e) => handleFilterChange('method', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有方法</option>
              <option value="GET">GET</option>
              <option value="POST">POST</option>
              <option value="PUT">PUT</option>
              <option value="DELETE">DELETE</option>
              <option value="PATCH">PATCH</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              开始日期
            </label>
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              结束日期
            </label>
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* 请求日志表格 */}
      <Table
        columns={columns}
        data={logs}
        loading={loading}
        emptyText="暂无请求日志"
        rowKey="_id"
      />

      {/* 分页 */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-700">
          共 {total} 条记录，第 {pagination.currentPage} / {Math.ceil(total / pagination.pageSize)} 页
        </div>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            disabled={pagination.currentPage <= 1}
            onClick={() => pagination.setCurrentPage(pagination.currentPage - 1)}
          >
            上一页
          </Button>
          <Button
            variant="secondary"
            size="sm"
            disabled={pagination.currentPage >= Math.ceil(total / pagination.pageSize)}
            onClick={() => pagination.setCurrentPage(pagination.currentPage + 1)}
          >
            下一页
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RequestLogs;
