import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  UserIcon, 
  ShieldCheckIcon, 
  CogIcon,
  KeyIcon
} from '@heroicons/react/24/outline';

interface SettingsLayoutProps {
  children: React.ReactNode;
}

const SettingsLayout: React.FC<SettingsLayoutProps> = ({ children }) => {
  const location = useLocation();

  const navigationItems = [
    {
      name: '个人资料',
      href: '/settings/profile',
      icon: UserIcon,
      current: location.pathname === '/settings' || location.pathname === '/settings/profile'
    },
    {
      name: '安全设置',
      href: '/settings/security',
      icon: ShieldCheckIcon,
      current: location.pathname === '/settings/security'
    },
    {
      name: '偏好设置',
      href: '/settings/preferences',
      icon: CogIcon,
      current: location.pathname === '/settings/preferences'
    }
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">设置</h1>
        <p className="mt-1 text-sm text-gray-600">
          管理您的账户设置和偏好
        </p>
      </div>

      <div className="flex flex-col lg:flex-row lg:space-x-8">
        {/* 侧边导航 */}
        <div className="lg:w-64 mb-6 lg:mb-0">
          <nav className="space-y-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`${
                    item.current
                      ? 'bg-primary-50 border-primary-500 text-primary-700'
                      : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  } group border-l-4 px-3 py-2 flex items-center text-sm font-medium transition-colors duration-200`}
                >
                  <Icon
                    className={`${
                      item.current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                    } flex-shrink-0 -ml-1 mr-3 h-6 w-6`}
                  />
                  <span className="truncate">{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* 两步验证快捷入口 */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center">
              <KeyIcon className="h-6 w-6 text-blue-600" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-900">
                  两步验证
                </h3>
                <p className="mt-1 text-xs text-blue-700">
                  为您的账户添加额外安全保护
                </p>
              </div>
            </div>
            <div className="mt-3">
              <Link
                to="/system/2fa"
                className="text-sm font-medium text-blue-600 hover:text-blue-500"
              >
                管理两步验证 →
              </Link>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 max-w-4xl">
          {children}
        </div>
      </div>
    </div>
  );
};

export default SettingsLayout;
