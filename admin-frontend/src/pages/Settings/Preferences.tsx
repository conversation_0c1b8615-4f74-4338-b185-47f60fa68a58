import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import Button from '@/components/common/Button';
import Card from '@/components/common/Card';
import { useToast } from '@/hooks/useToast';
import { 
  GlobeAltIcon, 
  ClockIcon, 
  BellIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface PreferencesFormData {
  language: string;
  timezone: string;
  theme: string;
  notifications: {
    email: boolean;
    browser: boolean;
    security: boolean;
  };
  dashboard: {
    autoRefresh: boolean;
    refreshInterval: number;
    defaultView: string;
  };
}

const Preferences: React.FC = () => {
  const { showToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<PreferencesFormData>({
    defaultValues: {
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      theme: 'light',
      notifications: {
        email: true,
        browser: true,
        security: true
      },
      dashboard: {
        autoRefresh: true,
        refreshInterval: 30,
        defaultView: 'overview'
      }
    }
  });

  // 监听自动刷新设置
  const autoRefresh = watch('dashboard.autoRefresh');

  // 加载用户偏好设置
  useEffect(() => {
    const loadPreferences = () => {
      try {
        const saved = localStorage.getItem('admin_preferences');
        if (saved) {
          const preferences = JSON.parse(saved);
          Object.keys(preferences).forEach(key => {
            setValue(key as any, preferences[key]);
          });
        }
      } catch (error) {
        console.error('Failed to load preferences:', error);
      }
    };

    loadPreferences();
  }, [setValue]);

  // 保存偏好设置
  const onSubmit = async (data: PreferencesFormData) => {
    try {
      setIsLoading(true);
      
      // 保存到本地存储
      localStorage.setItem('admin_preferences', JSON.stringify(data));
      
      // 这里可以添加保存到服务器的逻辑
      // await savePreferencesToServer(data);
      
      showToast('偏好设置已保存', 'success');
    } catch (error) {
      showToast('保存失败', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h2 className="text-lg font-medium text-gray-900">偏好设置</h2>
        <p className="mt-1 text-sm text-gray-600">
          自定义您的管理后台体验
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* 语言和地区设置 */}
        <Card>
          <div className="p-6">
            <div className="flex items-center mb-4">
              <GlobeAltIcon className="h-6 w-6 text-gray-400 mr-3" />
              <h3 className="text-lg font-medium text-gray-900">语言和地区</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  显示语言
                </label>
                <select
                  {...register('language')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="zh-CN">简体中文</option>
                  <option value="zh-TW">繁體中文</option>
                  <option value="en-US">English</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  时区
                </label>
                <select
                  {...register('timezone')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="Asia/Shanghai">北京时间 (UTC+8)</option>
                  <option value="Asia/Hong_Kong">香港时间 (UTC+8)</option>
                  <option value="Asia/Taipei">台北时间 (UTC+8)</option>
                  <option value="UTC">协调世界时 (UTC)</option>
                  <option value="America/New_York">纽约时间 (UTC-5)</option>
                  <option value="Europe/London">伦敦时间 (UTC+0)</option>
                </select>
              </div>
            </div>
          </div>
        </Card>

        {/* 外观设置 */}
        <Card>
          <div className="p-6">
            <div className="flex items-center mb-4">
              <EyeIcon className="h-6 w-6 text-gray-400 mr-3" />
              <h3 className="text-lg font-medium text-gray-900">外观</h3>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                主题
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="light"
                    {...register('theme')}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">浅色主题</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="dark"
                    {...register('theme')}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">深色主题</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="auto"
                    {...register('theme')}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">跟随系统</span>
                </label>
              </div>
            </div>
          </div>
        </Card>

        {/* 通知设置 */}
        <Card>
          <div className="p-6">
            <div className="flex items-center mb-4">
              <BellIcon className="h-6 w-6 text-gray-400 mr-3" />
              <h3 className="text-lg font-medium text-gray-900">通知设置</h3>
            </div>

            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <span className="text-sm font-medium text-gray-700">邮件通知</span>
                  <p className="text-sm text-gray-500">接收重要系统通知邮件</p>
                </div>
                <input
                  type="checkbox"
                  {...register('notifications.email')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <span className="text-sm font-medium text-gray-700">浏览器通知</span>
                  <p className="text-sm text-gray-500">在浏览器中显示通知</p>
                </div>
                <input
                  type="checkbox"
                  {...register('notifications.browser')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
              </label>

              <label className="flex items-center justify-between">
                <div>
                  <span className="text-sm font-medium text-gray-700">安全通知</span>
                  <p className="text-sm text-gray-500">账户安全相关通知</p>
                </div>
                <input
                  type="checkbox"
                  {...register('notifications.security')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
              </label>
            </div>
          </div>
        </Card>

        {/* 仪表板设置 */}
        <Card>
          <div className="p-6">
            <div className="flex items-center mb-4">
              <ClockIcon className="h-6 w-6 text-gray-400 mr-3" />
              <h3 className="text-lg font-medium text-gray-900">仪表板设置</h3>
            </div>

            <div className="space-y-4">
              <label className="flex items-center justify-between">
                <div>
                  <span className="text-sm font-medium text-gray-700">自动刷新</span>
                  <p className="text-sm text-gray-500">自动刷新仪表板数据</p>
                </div>
                <input
                  type="checkbox"
                  {...register('dashboard.autoRefresh')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
              </label>

              {autoRefresh && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    刷新间隔（秒）
                  </label>
                  <select
                    {...register('dashboard.refreshInterval', { valueAsNumber: true })}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  >
                    <option value={15}>15秒</option>
                    <option value={30}>30秒</option>
                    <option value={60}>1分钟</option>
                    <option value={300}>5分钟</option>
                  </select>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  默认视图
                </label>
                <select
                  {...register('dashboard.defaultView')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="overview">概览</option>
                  <option value="users">用户统计</option>
                  <option value="chat">聊天统计</option>
                  <option value="financial">财务统计</option>
                </select>
              </div>
            </div>
          </div>
        </Card>

        {/* 保存按钮 */}
        <div className="flex justify-end">
          <Button
            type="submit"
            variant="primary"
            loading={isLoading}
          >
            保存设置
          </Button>
        </div>
      </form>
    </div>
  );
};

export default Preferences;
