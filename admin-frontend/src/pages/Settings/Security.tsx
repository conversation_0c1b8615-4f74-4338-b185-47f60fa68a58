import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Card from '@/components/common/Card';
import { useToast } from '@/hooks/useToast';
import { useApi } from '@/hooks/useApi';
import { 
  KeyIcon, 
  ShieldCheckIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface PasswordChangeForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const Security: React.FC = () => {
  const { user } = useAuth();
  const { showToast } = useToast();
  const [showPasswordForm, setShowPasswordForm] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<PasswordChangeForm>();

  // 监听新密码以验证确认密码
  const newPassword = watch('newPassword');

  // API 调用
  const {
    loading: passwordLoading,
    execute: executePasswordChange
  } = useApi(async (data: PasswordChangeForm) => {
    const response = await fetch('/api/admin/auth/change-password', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword
      })
    });
    if (!response.ok) throw new Error('Password change failed');
    return response.json();
  });

  // 提交密码修改
  const onSubmitPassword = async (data: PasswordChangeForm) => {
    try {
      const result = await executePasswordChange(data);
      if (result.success) {
        reset();
        setShowPasswordForm(false);
        showToast('密码修改成功', 'success');
      } else {
        showToast(result.message || '密码修改失败', 'error');
      }
    } catch (error) {
      showToast('密码修改失败', 'error');
    }
  };

  // 取消密码修改
  const handleCancelPassword = () => {
    reset();
    setShowPasswordForm(false);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h2 className="text-lg font-medium text-gray-900">安全设置</h2>
        <p className="mt-1 text-sm text-gray-600">
          管理您的账户安全和认证设置
        </p>
      </div>

      {/* 密码设置 */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <KeyIcon className="h-6 w-6 text-gray-400 mr-3" />
              <div>
                <h3 className="text-lg font-medium text-gray-900">登录密码</h3>
                <p className="text-sm text-gray-600">定期更改密码以保护账户安全</p>
              </div>
            </div>
            {!showPasswordForm && (
              <Button
                variant="secondary"
                onClick={() => setShowPasswordForm(true)}
              >
                修改密码
              </Button>
            )}
          </div>

          {showPasswordForm && (
            <form onSubmit={handleSubmit(onSubmitPassword)} className="mt-6 space-y-4">
              <Input
                label="当前密码"
                type="password"
                {...register('currentPassword', {
                  required: '请输入当前密码'
                })}
                error={errors.currentPassword?.message}
              />

              <Input
                label="新密码"
                type="password"
                {...register('newPassword', {
                  required: '请输入新密码',
                  minLength: {
                    value: 6,
                    message: '密码至少6位字符'
                  }
                })}
                error={errors.newPassword?.message}
              />

              <Input
                label="确认新密码"
                type="password"
                {...register('confirmPassword', {
                  required: '请确认新密码',
                  validate: value => value === newPassword || '两次输入的密码不一致'
                })}
                error={errors.confirmPassword?.message}
              />

              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleCancelPassword}
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  loading={passwordLoading}
                >
                  修改密码
                </Button>
              </div>
            </form>
          )}
        </div>
      </Card>

      {/* 两步验证 */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ShieldCheckIcon className="h-6 w-6 text-gray-400 mr-3" />
              <div>
                <h3 className="text-lg font-medium text-gray-900">两步验证</h3>
                <p className="text-sm text-gray-600">
                  为您的账户添加额外的安全保护层
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {user?.twoFactorEnabled ? (
                <div className="flex items-center text-green-600">
                  <CheckCircleIcon className="h-5 w-5 mr-1" />
                  <span className="text-sm font-medium">已启用</span>
                </div>
              ) : (
                <div className="flex items-center text-yellow-600">
                  <ExclamationTriangleIcon className="h-5 w-5 mr-1" />
                  <span className="text-sm font-medium">未启用</span>
                </div>
              )}
              <Link to="/system/2fa">
                <Button variant="secondary">
                  管理设置
                </Button>
              </Link>
            </div>
          </div>

          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <ShieldCheckIcon className="h-5 w-5 text-blue-400" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  推荐启用两步验证
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    两步验证可以显著提高您账户的安全性。即使密码被泄露，
                    攻击者也无法在没有您的手机或认证器应用的情况下访问您的账户。
                  </p>
                </div>
                <div className="mt-3">
                  <Link
                    to="/system/2fa"
                    className="text-sm font-medium text-blue-600 hover:text-blue-500"
                  >
                    立即设置两步验证 →
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* 登录历史 */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">登录历史</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-3 border-b border-gray-200">
              <div>
                <p className="text-sm font-medium text-gray-900">当前会话</p>
                <p className="text-sm text-gray-600">
                  {user?.lastLoginAt 
                    ? new Date(user.lastLoginAt).toLocaleString('zh-CN')
                    : '未知时间'
                  }
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-900">
                  IP: {user?.lastLoginIP || '未知'}
                </p>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  当前会话
                </span>
              </div>
            </div>
          </div>
          <div className="mt-4">
            <p className="text-sm text-gray-600">
              如需查看完整的登录历史，请联系系统管理员。
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Security;
