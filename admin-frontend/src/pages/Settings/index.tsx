import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Profile from './Profile';
import Security from './Security';
import Preferences from './Preferences';
import SettingsLayout from './SettingsLayout';

const Settings: React.FC = () => {
  return (
    <SettingsLayout>
      <Routes>
        <Route index element={<Profile />} />
        <Route path="profile" element={<Profile />} />
        <Route path="security" element={<Security />} />
        <Route path="preferences" element={<Preferences />} />
      </Routes>
    </SettingsLayout>
  );
};

export default Settings;
