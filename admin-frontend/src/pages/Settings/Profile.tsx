import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useAuth } from '@/context/AuthContext';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Card from '@/components/common/Card';
import { useToast } from '@/hooks/useToast';
import { useApi } from '@/hooks/useApi';

interface ProfileFormData {
  fullName: string;
  email: string;
  phone?: string;
  company?: string;
  occupation?: string;
}

const Profile: React.FC = () => {
  const { user, updateUser } = useAuth();
  const { showToast } = useToast();
  const [isEditing, setIsEditing] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue
  } = useForm<ProfileFormData>();

  // API 调用
  const {
    loading: updateLoading,
    execute: executeUpdate
  } = useApi(async (data: ProfileFormData) => {
    const response = await fetch('/api/admin/auth/profile', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    if (!response.ok) throw new Error('Profile update failed');
    return response.json();
  });

  // 初始化表单数据
  useEffect(() => {
    if (user) {
      setValue('fullName', user.fullName || '');
      setValue('email', user.email || '');
      setValue('phone', user.phone || '');
      setValue('company', user.company || '');
      setValue('occupation', user.occupation || '');
    }
  }, [user, setValue]);

  // 提交表单
  const onSubmit = async (data: ProfileFormData) => {
    try {
      const result = await executeUpdate(data);
      if (result.success) {
        // 更新本地用户信息
        updateUser(result.data);
        setIsEditing(false);
        showToast('个人资料更新成功', 'success');
      } else {
        showToast(result.message || '更新失败', 'error');
      }
    } catch (error) {
      showToast('更新失败', 'error');
    }
  };

  // 取消编辑
  const handleCancel = () => {
    reset();
    setIsEditing(false);
    // 重新设置表单值
    if (user) {
      setValue('fullName', user.fullName || '');
      setValue('email', user.email || '');
      setValue('phone', user.phone || '');
      setValue('company', user.company || '');
      setValue('occupation', user.occupation || '');
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h2 className="text-lg font-medium text-gray-900">个人资料</h2>
        <p className="mt-1 text-sm text-gray-600">
          管理您的个人信息和联系方式
        </p>
      </div>

      {/* 个人资料卡片 */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">基本信息</h3>
            {!isEditing && (
              <Button
                variant="secondary"
                onClick={() => setIsEditing(true)}
              >
                编辑资料
              </Button>
            )}
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 姓名 */}
              <Input
                label="姓名"
                {...register('fullName', {
                  required: '请输入姓名'
                })}
                error={errors.fullName?.message}
                disabled={!isEditing}
              />

              {/* 邮箱 */}
              <Input
                label="邮箱地址"
                type="email"
                {...register('email', {
                  required: '请输入邮箱地址',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: '请输入有效的邮箱地址'
                  }
                })}
                error={errors.email?.message}
                disabled={true} // 邮箱通常不允许修改
              />

              {/* 电话 */}
              <Input
                label="电话号码"
                {...register('phone')}
                error={errors.phone?.message}
                disabled={!isEditing}
                placeholder="请输入电话号码"
              />

              {/* 公司 */}
              <Input
                label="公司"
                {...register('company')}
                error={errors.company?.message}
                disabled={!isEditing}
                placeholder="请输入公司名称"
              />

              {/* 职位 */}
              <div className="md:col-span-2">
                <Input
                  label="职位"
                  {...register('occupation')}
                  error={errors.occupation?.message}
                  disabled={!isEditing}
                  placeholder="请输入职位"
                />
              </div>
            </div>

            {/* 操作按钮 */}
            {isEditing && (
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleCancel}
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  loading={updateLoading}
                >
                  保存更改
                </Button>
              </div>
            )}
          </form>
        </div>
      </Card>

      {/* 账户信息 */}
      <Card>
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">账户信息</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                用户ID
              </label>
              <p className="mt-1 text-sm text-gray-900 font-mono">
                {user?.userId || 'N/A'}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                角色
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {user?.role === 'super_admin' ? '超级管理员' : 
                 user?.role === 'admin' ? '管理员' : '用户'}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                账户状态
              </label>
              <span className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                user?.status === 'active' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {user?.status === 'active' ? '正常' : '异常'}
              </span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                最后登录
              </label>
              <p className="mt-1 text-sm text-gray-900">
                {user?.lastLoginAt 
                  ? new Date(user.lastLoginAt).toLocaleString('zh-CN')
                  : '从未登录'
                }
              </p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Profile;
