import React, { useState, useEffect } from 'react';
import { format, subDays } from 'date-fns';
import Button from '@/components/common/Button';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';
import { useApi } from '@/hooks/useApi';
import { dashboardService } from '@/services/dashboard';
import { DashboardOverview, TrendData, RealTimeData, AlertData } from '@/types/dashboard';

const Dashboard: React.FC = () => {
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [trends, setTrends] = useState<TrendData[]>([]);
  const [realTimeData, setRealTimeData] = useState<RealTimeData | null>(null);
  const [alerts, setAlerts] = useState<AlertData[]>([]);
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d'>('7d');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API调用
  const {
    loading: overviewLoading,
    execute: fetchOverview,
  } = useApi(dashboardService.getOverview);

  const {
    loading: trendsLoading,
    execute: fetchTrends,
  } = useApi(dashboardService.getTrendData);

  const {
    loading: realTimeLoading,
    execute: fetchRealTime,
  } = useApi(dashboardService.getRealTimeData);

  const {
    loading: alertsLoading,
    execute: fetchAlerts,
  } = useApi(dashboardService.getAlerts);

  // 加载所有数据
  const loadAllData = async () => {
    try {
      setError(null); // 清除之前的错误
      const query = { timeRange };

      console.log('开始加载仪表板数据...', { timeRange });

      const [overviewData, trendsData, realTimeDataResult, alertsData] = await Promise.all([
        fetchOverview(query),
        fetchTrends(query),
        fetchRealTime(),
        fetchAlerts({ resolved: false, limit: 5 }),
      ]);

      console.log('仪表板数据加载成功', {
        overview: overviewData,
        trends: trendsData,
        realTime: realTimeDataResult,
        alerts: alertsData
      });

      setOverview(overviewData);
      setTrends(trendsData);
      setRealTimeData(realTimeDataResult);
      setAlerts(alertsData);
    } catch (error) {
      console.error('加载仪表板数据失败:', error);

      let errorMessage = '加载数据失败';
      let errorDetails = '';

      if (error instanceof Error) {
        errorMessage = error.message;
        errorDetails = error.stack || '';
      }

      // 根据错误类型提供更具体的错误信息
      if (errorMessage.includes('认证失败') || errorMessage.includes('401')) {
        errorMessage = '认证失败，请重新登录';
      } else if (errorMessage.includes('403')) {
        errorMessage = '权限不足，请联系管理员';
      } else if (errorMessage.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络设置';
      } else if (errorMessage.includes('超时')) {
        errorMessage = '请求超时，请稍后重试';
      }

      console.error('错误详情:', { errorMessage, errorDetails, timeRange });
      setError(errorMessage);

      // 如果是认证错误，不需要额外处理，API拦截器会处理
      if (errorMessage.includes('认证失败')) {
        return;
      }
    }
  };

  // 初始加载
  useEffect(() => {
    loadAllData();
  }, [timeRange]);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadAllData();
    }, 30000); // 30秒刷新一次

    return () => clearInterval(interval);
  }, [autoRefresh, timeRange]);

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // 格式化增长率
  const formatGrowth = (growth: number) => {
    const isPositive = growth >= 0;
    return (
      <span className={`text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? '+' : ''}{growth.toFixed(1)}%
      </span>
    );
  };

  // 获取告警级别颜色
  const getAlertColor = (type: string) => {
    switch (type) {
      case 'critical':
        return 'danger';
      case 'error':
        return 'danger';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'default';
    }
  };

  const loading = overviewLoading || trendsLoading || realTimeLoading || alertsLoading;

  if (loading && !overview) {
    return <Loading size="lg" text="加载仪表板数据中..." />;
  }

  return (
    <div className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-red-800">
                数据加载失败
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
                {process.env.NODE_ENV === 'development' && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-xs text-red-600 hover:text-red-800">
                      调试信息 (开发环境)
                    </summary>
                    <div className="mt-1 text-xs text-red-600 font-mono bg-red-100 p-2 rounded">
                      <p>时间范围: {timeRange}</p>
                      <p>认证Token: {localStorage.getItem('admin_token') ? '已设置' : '未设置'}</p>
                      <p>API基础URL: /api</p>
                      <p>请检查浏览器开发者工具的Network和Console标签页获取更多信息</p>
                    </div>
                  </details>
                )}
              </div>
              <div className="mt-4">
                <div className="-mx-2 -my-1.5 flex">
                  <button
                    type="button"
                    onClick={() => {
                      setError(null);
                      loadAllData();
                    }}
                    className="bg-red-50 px-2 py-1.5 rounded-md text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600"
                  >
                    重试
                  </button>
                  <button
                    type="button"
                    onClick={() => setError(null)}
                    className="ml-3 bg-red-50 px-2 py-1.5 rounded-md text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">仪表板</h1>
          <p className="mt-1 text-sm text-gray-600">
            系统概览和数据统计
          </p>
        </div>
        <div className="flex space-x-3">
          {/* 时间范围选择 */}
          <div className="flex rounded-md shadow-sm">
            {(['24h', '7d', '30d'] as const).map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`px-4 py-2 text-sm font-medium ${
                  timeRange === range
                    ? 'bg-primary-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                } ${
                  range === '24h' ? 'rounded-l-md' : range === '30d' ? 'rounded-r-md' : ''
                } border border-gray-300 ${range !== '24h' ? 'border-l-0' : ''}`}
              >
                {range === '24h' ? '24小时' : range === '7d' ? '7天' : '30天'}
              </button>
            ))}
          </div>

          <Button
            variant={autoRefresh ? 'primary' : 'secondary'}
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
          </Button>

          <Button
            variant="secondary"
            onClick={loadAllData}
            loading={loading}
          >
            刷新
          </Button>
        </div>
      </div>

      {/* 概览卡片 */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* 用户统计 */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      总用户数
                    </dt>
                    <dd className="flex items-center space-x-2">
                      <span className="text-lg font-medium text-gray-900">
                        {formatNumber(overview.users.total)}
                      </span>
                      {formatGrowth(overview.users.growth)}
                    </dd>
                    <dd className="text-xs text-gray-500">
                      活跃: {formatNumber(overview.users.active)} | 今日新增: {overview.users.newToday}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* 聊天统计 */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      总消息数
                    </dt>
                    <dd className="flex items-center space-x-2">
                      <span className="text-lg font-medium text-gray-900">
                        {formatNumber(overview.chat.totalMessages)}
                      </span>
                      {formatGrowth(overview.chat.growth)}
                    </dd>
                    <dd className="text-xs text-gray-500">
                      会话: {formatNumber(overview.chat.totalSessions)} | 今日: {overview.chat.todayMessages}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* 财务统计 */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      总收入
                    </dt>
                    <dd className="flex items-center space-x-2">
                      <span className="text-lg font-medium text-gray-900">
                        ¥{formatNumber(overview.financial.totalRevenue)}
                      </span>
                      {formatGrowth(overview.financial.growth)}
                    </dd>
                    <dd className="text-xs text-gray-500">
                      交易: {formatNumber(overview.financial.totalTransactions)} | 今日: ¥{overview.financial.todayRevenue.toFixed(0)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* 系统状态 */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                    overview.system.errorRate < 1 ? 'bg-green-500' :
                    overview.system.errorRate < 5 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}>
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      系统状态
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {overview.system.errorRate < 1 ? '健康' :
                       overview.system.errorRate < 5 ? '警告' : '异常'}
                    </dd>
                    <dd className="text-xs text-gray-500">
                      错误率: {overview.system.errorRate.toFixed(2)}% | 响应: {overview.system.avgResponseTime.toFixed(0)}ms
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 实时数据和告警 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 实时监控 */}
        {realTimeData && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">实时监控</h3>
              <p className="text-sm text-gray-500">
                更新时间: {format(new Date(realTimeData.timestamp), 'HH:mm:ss')}
              </p>
            </div>
            <div className="p-6 space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">在线用户</span>
                <span className="text-sm font-medium text-gray-900">{realTimeData.onlineUsers}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">活跃聊天</span>
                <span className="text-sm font-medium text-gray-900">{realTimeData.activeChats}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">请求/秒</span>
                <span className="text-sm font-medium text-gray-900">{realTimeData.requestsPerSecond.toFixed(1)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">错误/秒</span>
                <span className="text-sm font-medium text-red-600">{realTimeData.errorsPerSecond.toFixed(1)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">平均响应时间</span>
                <span className="text-sm font-medium text-gray-900">{realTimeData.avgResponseTime.toFixed(0)}ms</span>
              </div>
            </div>
          </div>
        )}

        {/* 系统告警 */}
        <div className="lg:col-span-2 bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">系统告警</h3>
              <Badge variant={alerts.length > 0 ? 'danger' : 'success'}>
                {alerts.length} 条未解决
              </Badge>
            </div>
          </div>
          <div className="p-6">
            {alerts.length === 0 ? (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="mt-2 text-sm text-gray-500">系统运行正常，暂无告警</p>
              </div>
            ) : (
              <div className="space-y-3">
                {alerts.map((alert) => (
                  <div key={alert.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-md">
                    <Badge variant={getAlertColor(alert.type)} size="sm">
                      {alert.type.toUpperCase()}
                    </Badge>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{alert.title}</p>
                      <p className="text-sm text-gray-500">{alert.message}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        {format(new Date(alert.timestamp), 'MM-dd HH:mm')} | {alert.source}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 趋势图表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">用户增长趋势</h3>
          </div>
          <div className="p-6">
            <div className="h-64 flex items-center justify-center text-gray-500">
              {trends.length > 0 ? (
                <div className="w-full">
                  <p className="text-center text-sm text-gray-600 mb-4">
                    过去{timeRange === '24h' ? '24小时' : timeRange === '7d' ? '7天' : '30天'}的用户增长
                  </p>
                  {/* 这里可以集成图表库，如 Recharts */}
                  <div className="text-center text-gray-400">
                    图表组件待集成（数据已准备：{trends.length}个数据点）
                  </div>
                </div>
              ) : (
                '暂无趋势数据'
              )}
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">消息统计</h3>
          </div>
          <div className="p-6">
            <div className="h-64 flex items-center justify-center text-gray-500">
              {trends.length > 0 ? (
                <div className="w-full">
                  <p className="text-center text-sm text-gray-600 mb-4">
                    过去{timeRange === '24h' ? '24小时' : timeRange === '7d' ? '7天' : '30天'}的消息统计
                  </p>
                  {/* 这里可以集成图表库，如 Recharts */}
                  <div className="text-center text-gray-400">
                    图表组件待集成（数据已准备：{trends.length}个数据点）
                  </div>
                </div>
              ) : (
                '暂无统计数据'
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
