import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Loading from '@/components/common/Loading';
import { useApi } from '@/hooks/useApi';
import { userService } from '@/services/users';
import { UserFormData } from '@/types/user';

const UserForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEdit = !!id;
  const [loading, setLoading] = useState(false);
  const [currentBalance, setCurrentBalance] = useState<number>(0);
  const [showBalanceAdjustment, setShowBalanceAdjustment] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<UserFormData>();

  // API调用
  const {
    loading: fetching,
    execute: fetchUser,
  } = useApi(userService.getUserById);

  const {
    loading: saving,
    execute: saveUser,
  } = useApi(isEdit ? userService.updateUser : userService.createUser);

  // 加载用户数据（编辑模式）
  useEffect(() => {
    if (isEdit && id) {
      loadUser();
    }
  }, [isEdit, id]);

  const loadUser = async () => {
    if (!id) return;
    try {
      const user = await fetchUser(id);
      // 填充表单数据
      setValue('email', user.email);
      setValue('fullName', user.fullName || '');
      setValue('username', user.username || '');
      setValue('phone', user.phone || '');
      setValue('role', user.role);
      setValue('status', user.status);
      setValue('emailVerified', user.emailVerified);
      setValue('phoneVerified', user.phoneVerified || false);
      setValue('gender', user.gender);
      setValue('birthDate', user.birthDate ? user.birthDate.split('T')[0] : '');
      setValue('occupation', user.occupation || '');
      setValue('company', user.company || '');
      // 设置当前余额
      setCurrentBalance(user.balance || 0);
    } catch (error) {
      console.error('加载用户数据失败:', error);
    }
  };

  // 提交表单
  const onSubmit = async (data: UserFormData) => {
    try {
      // 先保存用户基本信息
      const { balanceAdjustment, ...userData } = data;

      if (isEdit && id) {
        await saveUser(id, userData);

        // 如果有余额调整且金额大于0，则调用余额调整API
        if (balanceAdjustment && balanceAdjustment.amount > 0 && balanceAdjustment.reason.trim()) {
          await userService.adjustUserBalance(id, balanceAdjustment);
        }
      } else {
        await saveUser(userData);
      }
      navigate('/users');
    } catch (error) {
      console.error('保存用户失败:', error);
    }
  };

  if (fetching) {
    return <Loading size="lg" text="加载用户数据中..." />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            {isEdit ? '编辑用户' : '新增用户'}
          </h1>
          <p className="mt-1 text-sm text-gray-600">
            {isEdit ? '修改用户信息' : '创建新的用户账户'}
          </p>
        </div>
        <Link to="/users">
          <Button variant="secondary">返回列表</Button>
        </Link>
      </div>

      {/* 表单 */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">基本信息</h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="邮箱地址 *"
                type="email"
                {...register('email', {
                  required: '请输入邮箱地址',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: '请输入有效的邮箱地址',
                  },
                })}
                error={errors.email?.message}
                fullWidth
              />
              
              <Input
                label="姓名"
                {...register('fullName')}
                error={errors.fullName?.message}
                fullWidth
              />
              
              <Input
                label="用户名"
                {...register('username')}
                error={errors.username?.message}
                fullWidth
              />
              
              <Input
                label="手机号"
                {...register('phone')}
                error={errors.phone?.message}
                fullWidth
              />

              {!isEdit && (
                <Input
                  label="密码 *"
                  type="password"
                  {...register('password', {
                    required: !isEdit ? '请输入密码' : false,
                    minLength: {
                      value: 6,
                      message: '密码至少6位字符',
                    },
                  })}
                  error={errors.password?.message}
                  fullWidth
                />
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  性别
                </label>
                <select
                  {...register('gender')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                >
                  <option value="">请选择</option>
                  <option value="Male">男</option>
                  <option value="Female">女</option>
                  <option value="Other">其他</option>
                </select>
              </div>

              <Input
                label="出生日期"
                type="date"
                {...register('birthDate')}
                error={errors.birthDate?.message}
                fullWidth
              />

              <Input
                label="职业"
                {...register('occupation')}
                error={errors.occupation?.message}
                fullWidth
              />

              <Input
                label="公司"
                {...register('company')}
                error={errors.company?.message}
                fullWidth
              />
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">账户设置</h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  角色 *
                </label>
                <select
                  {...register('role', { required: '请选择角色' })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                >
                  <option value="">请选择角色</option>
                  <option value="user">用户</option>
                  <option value="admin">管理员</option>
                  <option value="super_admin">超级管理员</option>
                </select>
                {errors.role && (
                  <p className="mt-1 text-sm text-red-600">{errors.role.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  状态 *
                </label>
                <select
                  {...register('status', { required: '请选择状态' })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                >
                  <option value="">请选择状态</option>
                  <option value="active">正常</option>
                  <option value="inactive">未激活</option>
                  <option value="suspended">已停用</option>
                </select>
                {errors.status && (
                  <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
                )}
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  {...register('emailVerified')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  邮箱已验证
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  {...register('phoneVerified')}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  手机已验证
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* 提交按钮 */}

        {/* 余额管理 - 仅编辑模式显示 */}
        {isEdit && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">余额管理</h3>
            </div>
            <div className="px-6 py-4">
              <div className="mb-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <span className="text-sm font-medium text-gray-700">当前余额</span>
                    <div className="text-2xl font-bold text-gray-900">
                      ¥{currentBalance.toFixed(2)}
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => setShowBalanceAdjustment(!showBalanceAdjustment)}
                    className="px-4 py-2 text-sm font-medium text-primary-600 bg-primary-50 border border-primary-200 rounded-md hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    {showBalanceAdjustment ? '取消调整' : '调整余额'}
                  </button>
                </div>
              </div>

              {showBalanceAdjustment && (
                <div className="border border-gray-200 rounded-lg p-4 bg-yellow-50">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">余额调整</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        调整类型 *
                      </label>
                      <select
                        {...register('balanceAdjustment.type')}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      >
                        <option value="add">增加余额</option>
                        <option value="deduct">扣减余额</option>
                        <option value="set">设置余额</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        金额 *
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        {...register('balanceAdjustment.amount', {
                          valueAsNumber: true,
                          min: { value: 0, message: '金额不能为负数' }
                        })}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        placeholder="0.00"
                      />
                      {errors.balanceAdjustment?.amount && (
                        <p className="mt-1 text-sm text-red-600">{errors.balanceAdjustment.amount.message}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        调整原因 *
                      </label>
                      <input
                        type="text"
                        {...register('balanceAdjustment.reason', {
                          required: showBalanceAdjustment ? '请输入调整原因' : false
                        })}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                        placeholder="请输入调整原因"
                      />
                      {errors.balanceAdjustment?.reason && (
                        <p className="mt-1 text-sm text-red-600">{errors.balanceAdjustment.reason.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-blue-700">
                          <strong>说明：</strong>
                          增加余额：在当前余额基础上增加指定金额；
                          扣减余额：从当前余额中扣除指定金额；
                          设置余额：将余额直接设置为指定金额。
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        <div className="flex justify-end space-x-3">
          <Link to="/users">
            <Button variant="secondary">取消</Button>
          </Link>
          <Button
            type="submit"
            variant="primary"
            loading={saving}
          >
            {isEdit ? '保存修改' : '创建用户'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default UserForm;
