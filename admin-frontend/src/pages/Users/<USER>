import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Badge from '@/components/common/Badge';
import Pagination from '@/components/common/Pagination';
import Modal, { ModalBody, ModalFooter } from '@/components/common/Modal';
import { useTable } from '@/hooks/useTable';
import { useApi } from '@/hooks/useApi';
import { userService } from '@/services/users';
import { User, UserListParams } from '@/types/user';
import { TableColumn } from '@/types/common';
import clsx from 'clsx';

const UserList: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [total, setTotal] = useState(0);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // 表格状态管理
  const {
    tableState,
    pagination,
    sorting,
    searching,
    selection,
    getQueryParams,
  } = useTable({
    initialPageSize: 20,
    initialSortBy: 'createdAt',
    initialSortOrder: 'desc',
  });

  // API调用
  const {
    loading,
    execute: fetchUsers,
  } = useApi(userService.getUsers);

  const {
    loading: deleting,
    execute: deleteUser,
  } = useApi(userService.deleteUser);

  // 加载用户列表
  const loadUsers = useCallback(async () => {
    try {
      const params: UserListParams = getQueryParams();
      const result = await fetchUsers(params);
      setUsers(result.items);
      setTotal(result.total);
    } catch (error) {
      console.error('加载用户列表失败:', error);
    }
  }, [fetchUsers, getQueryParams]);

  // 初始加载和参数变化时重新加载
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  // 删除用户
  const handleDelete = async () => {
    if (!selectedUser) return;
    
    try {
      await deleteUser(selectedUser._id);
      setDeleteModalOpen(false);
      setSelectedUser(null);
      loadUsers(); // 重新加载列表
    } catch (error) {
      console.error('删除用户失败:', error);
    }
  };

  // 表格列定义
  const columns: TableColumn<User>[] = [
    {
      key: 'avatar',
      title: '头像',
      dataIndex: 'avatar',
      width: 80,
      render: (avatar: string, record: User) => (
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center text-primary-600 font-medium">
            {avatar ? (
              <img src={avatar} alt="" className="h-10 w-10 rounded-full" />
            ) : (
              record.fullName?.[0] || record.email[0] || 'U'
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'user',
      title: '用户信息',
      dataIndex: 'email',
      sortable: true,
      render: (email: string, record: User) => (
        <div>
          <div className="text-sm font-medium text-gray-900">
            {record.fullName || record.username || '未设置'}
          </div>
          <div className="text-sm text-gray-500">{email}</div>
          {record.phone && (
            <div className="text-xs text-gray-400">{record.phone}</div>
          )}
        </div>
      ),
    },
    {
      key: 'role',
      title: '角色',
      dataIndex: 'role',
      width: 100,
      render: (role: string) => {
        const roleMap = {
          user: { text: '用户', variant: 'default' as const },
          admin: { text: '管理员', variant: 'primary' as const },
          super_admin: { text: '超级管理员', variant: 'danger' as const },
        };
        const roleInfo = roleMap[role as keyof typeof roleMap] || roleMap.user;
        return <Badge variant={roleInfo.variant}>{roleInfo.text}</Badge>;
      },
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (status: string, record: User) => {
        const statusMap = {
          active: { text: '正常', variant: 'success' as const },
          inactive: { text: '未激活', variant: 'warning' as const },
          suspended: { text: '已停用', variant: 'danger' as const },
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.active;
        return (
          <div className="flex flex-col space-y-1">
            <Badge variant={statusInfo.variant}>{statusInfo.text}</Badge>
            {record.emailVerified && (
              <Badge variant="info" size="sm">邮箱已验证</Badge>
            )}
            {record.isVip && (
              <Badge variant="warning" size="sm">VIP</Badge>
            )}
          </div>
        );
      },
    },
    {
      key: 'balance',
      title: '余额',
      dataIndex: 'balance',
      width: 120,
      sortable: true,
      render: (balance: number) => (
        <span className="text-sm font-medium">
          ¥{balance.toFixed(2)}
        </span>
      ),
    },
    {
      key: 'lastLoginAt',
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      width: 150,
      sortable: true,
      render: (lastLoginAt: string) => (
        <span className="text-sm text-gray-500">
          {lastLoginAt ? format(new Date(lastLoginAt), 'yyyy-MM-dd HH:mm') : '从未登录'}
        </span>
      ),
    },
    {
      key: 'createdAt',
      title: '注册时间',
      dataIndex: 'createdAt',
      width: 150,
      sortable: true,
      render: (createdAt: string) => (
        <span className="text-sm text-gray-500">
          {createdAt ? format(new Date(createdAt), 'yyyy-MM-dd HH:mm') : '未知'}
        </span>
      ),
    },
    {
      key: 'actions',
      title: '操作',
      dataIndex: '_id',
      width: 200,
      render: (id: string, record: User) => (
        <div className="flex space-x-2">
          <Link to={`/users/${id}`}>
            <Button size="sm" variant="secondary">
              查看
            </Button>
          </Link>
          <Link to={`/users/${id}/edit`}>
            <Button size="sm" variant="primary">
              编辑
            </Button>
          </Link>
          <Button
            size="sm"
            variant="danger"
            onClick={() => {
              setSelectedUser(record);
              setDeleteModalOpen(true);
            }}
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">用户管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            管理系统中的所有用户账户
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary">
            导出数据
          </Button>
          <Link to="/users/create">
            <Button variant="primary">
              新增用户
            </Button>
          </Link>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Input
            placeholder="搜索用户..."
            value={searching.search}
            onChange={(e) => searching.onSearch(e.target.value)}
            leftIcon={
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            }
          />
          {/* 这里可以添加更多筛选条件 */}
        </div>
      </div>

      {/* 用户表格 */}
      <Table
        columns={columns}
        data={users}
        loading={loading}
        emptyText="暂无用户数据"
        rowKey="_id"
      />

      {/* 分页 */}
      <Pagination
        current={pagination.current}
        total={total}
        pageSize={pagination.pageSize}
        onChange={pagination.onChange}
        onPageSizeChange={pagination.onPageSizeChange}
        showSizeChanger
        showQuickJumper
        showTotal
      />

      {/* 删除确认弹窗 */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="确认删除"
        size="sm"
      >
        <ModalBody>
          <p className="text-sm text-gray-500">
            确定要删除用户 <span className="font-medium">{selectedUser?.fullName || selectedUser?.email}</span> 吗？
            此操作将软删除用户，可以通过恢复功能找回。
          </p>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setDeleteModalOpen(false)}
          >
            取消
          </Button>
          <Button
            variant="danger"
            loading={deleting}
            onClick={handleDelete}
          >
            确认删除
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default UserList;
