import React from 'react';
import { Routes, Route } from 'react-router-dom';
import UserList from './UserList';
import UserDetail from './UserDetail';
import UserForm from './UserForm';
import UserStats from './UserStats';

const Users: React.FC = () => {
  return (
    <Routes>
      <Route index element={<UserList />} />
      <Route path="create" element={<UserForm />} />
      <Route path="stats" element={<UserStats />} />
      <Route path=":id" element={<UserDetail />} />
      <Route path=":id/edit" element={<UserForm />} />
    </Routes>
  );
};

export default Users;
