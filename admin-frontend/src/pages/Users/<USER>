import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import Button from '@/components/common/Button';
import Badge from '@/components/common/Badge';
import Modal, { ModalBody, ModalFooter } from '@/components/common/Modal';
import Input from '@/components/common/Input';
import Loading from '@/components/common/Loading';
import { useApi } from '@/hooks/useApi';
import { userService } from '@/services/users';
import { UserDetail as UserDetailType, BalanceAdjustment } from '@/types/user';

const UserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [user, setUser] = useState<UserDetailType | null>(null);
  const [balanceModalOpen, setBalanceModalOpen] = useState(false);
  const [balanceForm, setBalanceForm] = useState({
    amount: '',
    reason: '',
    type: 'increase' as 'increase' | 'decrease',
  });

  // API调用
  const {
    loading,
    execute: fetchUser,
  } = useApi(userService.getUserById);

  const {
    loading: adjustingBalance,
    execute: adjustBalance,
  } = useApi(userService.adjustBalance);

  const {
    loading: deleting,
    execute: deleteUser,
  } = useApi(userService.deleteUser);

  // 加载用户详情
  useEffect(() => {
    if (id) {
      loadUser();
    }
  }, [id]);

  const loadUser = async () => {
    if (!id) return;
    try {
      const userData = await fetchUser(id);
      setUser(userData);
    } catch (error) {
      console.error('加载用户详情失败:', error);
    }
  };

  // 调整余额
  const handleBalanceAdjustment = async () => {
    if (!user || !balanceForm.amount || !balanceForm.reason) return;

    try {
      const adjustment: BalanceAdjustment = {
        userId: user._id,
        amount: parseFloat(balanceForm.amount),
        reason: balanceForm.reason,
        type: balanceForm.type,
      };
      
      await adjustBalance(adjustment);
      setBalanceModalOpen(false);
      setBalanceForm({ amount: '', reason: '', type: 'increase' });
      loadUser(); // 重新加载用户信息
    } catch (error) {
      console.error('调整余额失败:', error);
    }
  };

  // 删除用户
  const handleDelete = async () => {
    if (!user) return;
    
    if (window.confirm(`确定要删除用户 ${user.fullName || user.email} 吗？`)) {
      try {
        await deleteUser(user._id);
        navigate('/users');
      } catch (error) {
        console.error('删除用户失败:', error);
      }
    }
  };

  if (loading) {
    return <Loading size="lg" text="加载用户详情中..." />;
  }

  if (!user) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">用户不存在</p>
        <Link to="/users">
          <Button className="mt-4">返回用户列表</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">用户详情</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看和管理用户信息
          </p>
        </div>
        <div className="flex space-x-3">
          <Link to="/users">
            <Button variant="secondary">返回列表</Button>
          </Link>
          <Link to={`/users/${user._id}/edit`}>
            <Button variant="primary">编辑用户</Button>
          </Link>
          <Button
            variant="danger"
            loading={deleting}
            onClick={handleDelete}
          >
            删除用户
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 基本信息 */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">基本信息</h3>
            </div>
            <div className="px-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">用户ID</label>
                  <p className="mt-1 text-sm text-gray-900">{user.userId}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">邮箱</label>
                  <div className="flex items-center space-x-2">
                    <p className="mt-1 text-sm text-gray-900">{user.email}</p>
                    {user.emailVerified && (
                      <Badge variant="success" size="sm">已验证</Badge>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">姓名</label>
                  <p className="mt-1 text-sm text-gray-900">{user.fullName || '未设置'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">用户名</label>
                  <p className="mt-1 text-sm text-gray-900">{user.username || '未设置'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">手机号</label>
                  <div className="flex items-center space-x-2">
                    <p className="mt-1 text-sm text-gray-900">{user.phone || '未设置'}</p>
                    {user.phoneVerified && (
                      <Badge variant="success" size="sm">已验证</Badge>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">性别</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {user.gender === 'Male' ? '男' : user.gender === 'Female' ? '女' : '未设置'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">出生日期</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {user.birthDate ? format(new Date(user.birthDate), 'yyyy-MM-dd') : '未设置'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">职业</label>
                  <p className="mt-1 text-sm text-gray-900">{user.occupation || '未设置'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* 账户信息 */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">账户信息</h3>
            </div>
            <div className="px-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">角色</label>
                  <div className="mt-1">
                    <Badge variant={user.role === 'admin' ? 'primary' : user.role === 'super_admin' ? 'danger' : 'default'}>
                      {user.role === 'admin' ? '管理员' : user.role === 'super_admin' ? '超级管理员' : '用户'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">状态</label>
                  <div className="mt-1">
                    <Badge variant={user.status === 'active' ? 'success' : user.status === 'suspended' ? 'danger' : 'warning'}>
                      {user.status === 'active' ? '正常' : user.status === 'suspended' ? '已停用' : '未激活'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">注册时间</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {user.createdAt ? format(new Date(user.createdAt), 'yyyy-MM-dd HH:mm:ss') : '未知'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">最后登录</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {user.lastLoginAt ? format(new Date(user.lastLoginAt), 'yyyy-MM-dd HH:mm:ss') : '从未登录'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">最后登录IP</label>
                  <p className="mt-1 text-sm text-gray-900">{user.lastLoginIP || '未知'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">VIP状态</label>
                  <div className="mt-1">
                    {user.isVip ? (
                      <div className="flex items-center space-x-2">
                        <Badge variant="warning">VIP用户</Badge>
                        {user.vipExpiredAt && (
                          <span className="text-xs text-gray-500">
                            到期: {format(new Date(user.vipExpiredAt), 'yyyy-MM-dd')}
                          </span>
                        )}
                      </div>
                    ) : (
                      <Badge variant="default">普通用户</Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 余额信息 */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">余额信息</h3>
                <Button
                  size="sm"
                  variant="primary"
                  onClick={() => setBalanceModalOpen(true)}
                >
                  调整余额
                </Button>
              </div>
            </div>
            <div className="px-6 py-4">
              <div className="text-center">
                <p className="text-3xl font-bold text-gray-900">¥{user.balance.toFixed(2)}</p>
                <p className="text-sm text-gray-500 mt-1">当前余额</p>
              </div>
              {user.totalSpent !== undefined && (
                <div className="mt-4 text-center">
                  <p className="text-lg font-medium text-gray-700">¥{user.totalSpent.toFixed(2)}</p>
                  <p className="text-sm text-gray-500">累计消费</p>
                </div>
              )}
            </div>
          </div>

          {/* 聊天统计 */}
          {user.chatStats && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">聊天统计</h3>
              </div>
              <div className="px-6 py-4 space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">总聊天数</span>
                  <span className="text-sm font-medium">{user.chatStats.totalChats}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">总消息数</span>
                  <span className="text-sm font-medium">{user.chatStats.totalMessages}</span>
                </div>
                {user.chatStats.lastChatAt && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">最后聊天</span>
                    <span className="text-sm font-medium">
                      {format(new Date(user.chatStats.lastChatAt), 'MM-dd HH:mm')}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 余额调整弹窗 */}
      <Modal
        isOpen={balanceModalOpen}
        onClose={() => setBalanceModalOpen(false)}
        title="调整用户余额"
        size="md"
      >
        <ModalBody>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                调整类型
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="increase"
                    checked={balanceForm.type === 'increase'}
                    onChange={(e) => setBalanceForm(prev => ({ ...prev, type: e.target.value as 'increase' }))}
                    className="mr-2"
                  />
                  增加余额
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="decrease"
                    checked={balanceForm.type === 'decrease'}
                    onChange={(e) => setBalanceForm(prev => ({ ...prev, type: e.target.value as 'decrease' }))}
                    className="mr-2"
                  />
                  减少余额
                </label>
              </div>
            </div>
            <Input
              label="调整金额"
              type="number"
              step="0.01"
              min="0"
              value={balanceForm.amount}
              onChange={(e) => setBalanceForm(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="请输入调整金额"
            />
            <Input
              label="调整原因"
              value={balanceForm.reason}
              onChange={(e) => setBalanceForm(prev => ({ ...prev, reason: e.target.value }))}
              placeholder="请输入调整原因"
            />
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setBalanceModalOpen(false)}
          >
            取消
          </Button>
          <Button
            variant="primary"
            loading={adjustingBalance}
            onClick={handleBalanceAdjustment}
            disabled={!balanceForm.amount || !balanceForm.reason}
          >
            确认调整
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default UserDetail;
