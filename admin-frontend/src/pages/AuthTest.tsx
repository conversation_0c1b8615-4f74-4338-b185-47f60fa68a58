import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import Button from '@/components/common/Button';
import { authTestUtils } from '@/utils/authTest';

const AuthTest: React.FC = () => {
  const { user, isAuthenticated, loading, error, login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [testResult, setTestResult] = useState<string>('');
  const [routerStatus, setRouterStatus] = useState<string>('检查中...');
  const [loginTestResult, setLoginTestResult] = useState<string>('');

  // 检查React Router状态
  useEffect(() => {
    try {
      // 测试useNavigate和useLocation是否正常工作
      if (navigate && location) {
        setRouterStatus('✅ React Router正常工作');
      } else {
        setRouterStatus('❌ React Router异常');
      }
    } catch (error) {
      setRouterStatus(`❌ React Router错误: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [navigate, location]);

  const runTest = async (testName: string, testFn: () => Promise<void> | void) => {
    try {
      setTestResult(`正在执行测试: ${testName}...`);
      await testFn();
      setTestResult(`测试 "${testName}" 执行完成，请查看控制台输出`);
    } catch (error) {
      setTestResult(`测试 "${testName}" 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // 测试登录流程
  const testLogin = async () => {
    try {
      setLoginTestResult('正在测试登录流程...');

      // 使用测试账号登录
      await login({
        email: '<EMAIL>',
        password: 'password123'
      });

      setLoginTestResult('✅ 登录测试成功');
    } catch (error) {
      setLoginTestResult(`❌ 登录测试失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">认证系统测试页面</h1>
      
      {/* React Router状态检查 */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">React Router状态</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Router状态</label>
            <p className="mt-1 text-sm text-gray-900">{routerStatus}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">当前路径</label>
            <p className="mt-1 text-sm text-gray-900">{location.pathname}</p>
          </div>
        </div>
      </div>

      {/* 当前认证状态 */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">当前认证状态</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">认证状态</label>
            <p className={`mt-1 text-sm ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
              {loading ? '加载中...' : isAuthenticated ? '已认证' : '未认证'}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">用户信息</label>
            <p className="mt-1 text-sm text-gray-900">
              {user ? `${user.fullName || user.email} (${user.role})` : '无'}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">错误信息</label>
            <p className="mt-1 text-sm text-red-600">
              {error || '无'}
            </p>
          </div>
        </div>
      </div>

      {/* 测试按钮 */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">认证测试工具</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Button
            onClick={() => runTest('检查认证状态', authTestUtils.checkAuthStatus)}
            variant="outline"
          >
            检查认证状态
          </Button>
          
          <Button
            onClick={() => runTest('设置有效Token', authTestUtils.setValidTestToken)}
            variant="outline"
          >
            设置有效Token
          </Button>
          
          <Button
            onClick={() => runTest('模拟Token过期', authTestUtils.simulateTokenExpiry)}
            variant="outline"
          >
            模拟Token过期
          </Button>
          
          <Button
            onClick={() => runTest('清除认证信息', authTestUtils.clearAuth)}
            variant="outline"
          >
            清除认证信息
          </Button>
          
          <Button
            onClick={() => runTest('测试API请求', authTestUtils.testApiRequest)}
            variant="outline"
          >
            测试API请求
          </Button>
          
          <Button
            onClick={() => window.location.reload()}
            variant="primary"
          >
            刷新页面
          </Button>

          <Button
            onClick={() => {
              try {
                navigate('/');
                setTestResult('✅ React Router导航测试成功 - 已跳转到首页');
              } catch (error) {
                setTestResult(`❌ React Router导航测试失败: ${error instanceof Error ? error.message : String(error)}`);
              }
            }}
            variant="secondary"
          >
            测试Router导航
          </Button>

          <Button
            onClick={testLogin}
            variant="primary"
          >
            测试登录流程
          </Button>
        </div>
      </div>

      {/* 测试结果 */}
      {(testResult || loginTestResult) && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">测试结果</h2>
          {testResult && (
            <div className="bg-gray-50 rounded-md p-4 mb-4">
              <p className="text-sm text-gray-700">{testResult}</p>
            </div>
          )}
          {loginTestResult && (
            <div className="bg-blue-50 rounded-md p-4">
              <p className="text-sm text-blue-700">{loginTestResult}</p>
            </div>
          )}
        </div>
      )}

      {/* 测试说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
        <h2 className="text-lg font-medium text-blue-900 mb-4">测试说明</h2>
        <div className="text-sm text-blue-800 space-y-2">
          <p><strong>修复验证步骤：</strong></p>
          <ol className="list-decimal list-inside space-y-1 ml-4">
            <li>点击"模拟Token过期"按钮设置无效token</li>
            <li>点击左侧导航菜单切换页面，观察是否还会跳转到登录页</li>
            <li>刷新页面，观察是否会跳转到登录页</li>
            <li>检查控制台是否有认证错误处理的日志</li>
            <li>验证跳转是否通过React Router进行（URL变化平滑）</li>
          </ol>
          <p className="mt-4"><strong>预期结果：</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>不再出现意外的强制页面跳转</li>
            <li>认证失败时会优雅地跳转到登录页</li>
            <li>控制台显示认证错误处理日志而非强制跳转</li>
            <li>用户体验更加流畅</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AuthTest;
