import React, { useState, useEffect } from 'react';
import { useApi } from '@/hooks/useApi';
import { reportsService } from '@/services/reports';
import Loading from '@/components/common/Loading';

const UserReports: React.FC = () => {
  const [reports, setReports] = useState<any>(null);
  const [timeRange, setTimeRange] = useState('30d');

  // API调用
  const {
    loading,
    execute: fetchReports,
  } = useApi(reportsService.getUserReports);

  // 加载报表数据
  const loadReports = async () => {
    try {
      const result = await fetchReports({ timeRange });
      setReports(result);
    } catch (error) {
      console.error('加载用户报表失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadReports();
  }, [timeRange]);

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">用户统计报表</h1>
          <p className="mt-1 text-sm text-gray-600">
            用户注册、活跃度、行为分析
          </p>
        </div>
        <div className="flex space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="90d">最近90天</option>
            <option value="1y">最近1年</option>
          </select>
          <button
            onClick={loadReports}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            刷新
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.totalUsers || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">活跃用户</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.activeUsers || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">新增用户</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.newUsers || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">增长率</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.growthRate ? `${reports.growthRate.toFixed(1)}%` : '0.0%'}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* 详细报表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 用户注册趋势 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">用户注册趋势</h3>
          <div className="text-sm text-gray-500 mb-4">
            图表组件待集成（数据已准备）
          </div>
          {reports?.registrationTrend && (
            <div className="space-y-2">
              {reports.registrationTrend.slice(0, 5).map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">{item.date}</span>
                  <span className="font-medium">{item.count} 人</span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 用户活跃度分析 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">用户活跃度分析</h3>
          <div className="space-y-3">
            {reports?.activityAnalysis && Object.entries(reports.activityAnalysis).map(([key, value]: [string, any]) => (
              <div key={key} className="flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  {key === 'daily' ? '日活跃' : key === 'weekly' ? '周活跃' : '月活跃'}
                </span>
                <span className="text-sm font-medium text-gray-900">{value}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 用户行为分析 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">用户行为分析</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">平均会话时长</h4>
              <div className="text-2xl font-bold text-blue-600">
                {reports?.averageSessionDuration || '0'} 分钟
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">平均页面浏览量</h4>
              <div className="text-2xl font-bold text-green-600">
                {reports?.averagePageViews || '0'} 页面
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">用户留存率</h4>
              <div className="text-2xl font-bold text-purple-600">
                {reports?.retentionRate ? `${reports.retentionRate.toFixed(1)}%` : '0.0%'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserReports;
