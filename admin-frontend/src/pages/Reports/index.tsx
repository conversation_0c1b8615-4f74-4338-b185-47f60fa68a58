import React from 'react';
import { Routes, Route } from 'react-router-dom';
import ReportsDashboard from './ReportsDashboard';
import UserReports from './UserReports';
import FinancialReports from './FinancialReports';
import SystemReports from './SystemReports';

const Reports: React.FC = () => {
  return (
    <Routes>
      <Route index element={<ReportsDashboard />} />
      <Route path="users" element={<UserReports />} />
      <Route path="financial" element={<FinancialReports />} />
      <Route path="system" element={<SystemReports />} />
    </Routes>
  );
};

export default Reports;
