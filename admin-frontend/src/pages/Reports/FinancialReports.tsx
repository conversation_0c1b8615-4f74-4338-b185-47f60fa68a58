import React, { useState, useEffect } from 'react';
import { useApi } from '@/hooks/useApi';
import { reportsService } from '@/services/reports';
import Loading from '@/components/common/Loading';

const FinancialReports: React.FC = () => {
  const [reports, setReports] = useState<any>(null);
  const [timeRange, setTimeRange] = useState('30d');

  // API调用
  const {
    loading,
    execute: fetchReports,
  } = useApi(reportsService.getFinancialReports);

  // 加载报表数据
  const loadReports = async () => {
    try {
      const result = await fetchReports({ timeRange });
      setReports(result);
    } catch (error) {
      console.error('加载财务报表失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadReports();
  }, [timeRange]);

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">财务统计报表</h1>
          <p className="mt-1 text-sm text-gray-600">
            收入、支出、交易分析
          </p>
        </div>
        <div className="flex space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="90d">最近90天</option>
            <option value="1y">最近1年</option>
          </select>
          <button
            onClick={loadReports}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            刷新
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总收入</dt>
                <dd className="text-lg font-medium text-gray-900">
                  ¥{reports?.totalRevenue?.toFixed(2) || '0.00'}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总交易数</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.totalTransactions || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">收入增长</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.revenueGrowth ? `${reports.revenueGrowth.toFixed(1)}%` : '0.0%'}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">平均交易额</dt>
                <dd className="text-lg font-medium text-gray-900">
                  ¥{reports?.averageTransactionAmount?.toFixed(2) || '0.00'}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* 详细报表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 收入趋势 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">收入趋势</h3>
          <div className="text-sm text-gray-500 mb-4">
            图表组件待集成（数据已准备）
          </div>
          {reports?.revenueTrend && (
            <div className="space-y-2">
              {reports.revenueTrend.slice(0, 5).map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">{item.date}</span>
                  <span className="font-medium">¥{item.amount?.toFixed(2) || '0.00'}</span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 交易类型分布 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">交易类型分布</h3>
          <div className="space-y-3">
            {reports?.transactionTypes && Object.entries(reports.transactionTypes).map(([type, data]: [string, any]) => (
              <div key={type} className="flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  {type === 'recharge' ? '充值' : type === 'consume' ? '消费' : type === 'refund' ? '退款' : '调整'}
                </span>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">{data.count} 笔</div>
                  <div className="text-xs text-gray-500">¥{data.amount?.toFixed(2) || '0.00'}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 财务分析 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">财务分析</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">月度经常性收入 (MRR)</h4>
              <div className="text-2xl font-bold text-green-600">
                ¥{reports?.monthlyRecurringRevenue?.toFixed(2) || '0.00'}
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">客户生命周期价值 (LTV)</h4>
              <div className="text-2xl font-bold text-blue-600">
                ¥{reports?.customerLifetimeValue?.toFixed(2) || '0.00'}
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">平均收入增长率</h4>
              <div className="text-2xl font-bold text-purple-600">
                {reports?.averageRevenueGrowthRate ? `${reports.averageRevenueGrowthRate.toFixed(1)}%` : '0.0%'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialReports;
