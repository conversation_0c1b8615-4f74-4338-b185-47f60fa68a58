import React, { useState, useEffect } from 'react';
import { useApi } from '@/hooks/useApi';
import { reportsService } from '@/services/reports';
import Loading from '@/components/common/Loading';

const SystemReports: React.FC = () => {
  const [reports, setReports] = useState<any>(null);
  const [timeRange, setTimeRange] = useState('24h');

  // API调用
  const {
    loading,
    execute: fetchReports,
  } = useApi(reportsService.getSystemReports);

  // 加载报表数据
  const loadReports = async () => {
    try {
      const result = await fetchReports({ timeRange });
      setReports(result);
    } catch (error) {
      console.error('加载系统报表失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadReports();
  }, [timeRange]);

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">系统统计报表</h1>
          <p className="mt-1 text-sm text-gray-600">
            性能、错误、监控数据
          </p>
        </div>
        <div className="flex space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="1h">最近1小时</option>
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
          </select>
          <button
            onClick={loadReports}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            刷新
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总请求数</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.totalRequests || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">平均响应时间</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.averageResponseTime || 0}ms
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">错误率</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.errorRate ? `${reports.errorRate.toFixed(2)}%` : '0.00%'}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">系统正常运行时间</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {reports?.uptime ? `${reports.uptime.toFixed(1)}%` : '0.0%'}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* 详细报表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 请求趋势 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">请求趋势</h3>
          <div className="text-sm text-gray-500 mb-4">
            图表组件待集成（数据已准备）
          </div>
          {reports?.requestTrend && (
            <div className="space-y-2">
              {reports.requestTrend.slice(0, 5).map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">{item.time}</span>
                  <span className="font-medium">{item.count} 次</span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 错误类型分布 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">错误类型分布</h3>
          <div className="space-y-3">
            {reports?.errorTypes && Object.entries(reports.errorTypes).map(([type, count]: [string, any]) => (
              <div key={type} className="flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  {type === '4xx' ? '客户端错误' : type === '5xx' ? '服务器错误' : '其他错误'}
                </span>
                <span className="text-sm font-medium text-gray-900">{count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 系统资源使用情况 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">系统资源使用情况</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">CPU使用率</h4>
              <div className="text-2xl font-bold text-blue-600">
                {reports?.cpuUsage ? `${reports.cpuUsage.toFixed(1)}%` : '0.0%'}
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">内存使用率</h4>
              <div className="text-2xl font-bold text-green-600">
                {reports?.memoryUsage ? `${reports.memoryUsage.toFixed(1)}%` : '0.0%'}
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">磁盘使用率</h4>
              <div className="text-2xl font-bold text-yellow-600">
                {reports?.diskUsage ? `${reports.diskUsage.toFixed(1)}%` : '0.0%'}
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">网络带宽</h4>
              <div className="text-2xl font-bold text-purple-600">
                {reports?.networkBandwidth ? `${reports.networkBandwidth.toFixed(1)} MB/s` : '0.0 MB/s'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* API端点性能 */}
      {reports?.apiEndpoints && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">API端点性能</h3>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-3">
              {reports.apiEndpoints.slice(0, 10).map((endpoint: any, index: number) => (
                <div key={index} className="flex justify-between items-center">
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">{endpoint.path}</div>
                    <div className="text-xs text-gray-500">{endpoint.method}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">{endpoint.avgResponseTime}ms</div>
                    <div className="text-xs text-gray-500">{endpoint.requestCount} 次请求</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemReports;
