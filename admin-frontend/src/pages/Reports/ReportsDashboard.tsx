import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useApi } from '@/hooks/useApi';
import { reportsService } from '@/services/reports';
import Loading from '@/components/common/Loading';

interface ReportsOverview {
  userReports: {
    totalUsers: number;
    activeUsers: number;
    newUsersToday: number;
    userGrowthRate: number;
  };
  financialReports: {
    totalRevenue: number;
    totalTransactions: number;
    revenueToday: number;
    revenueGrowthRate: number;
  };
  systemReports: {
    totalRequests: number;
    averageResponseTime: number;
    errorRate: number;
    uptime: number;
  };
}

const ReportsDashboard: React.FC = () => {
  const [overview, setOverview] = useState<ReportsOverview | null>(null);

  // API调用
  const {
    loading,
    execute: fetchOverview,
  } = useApi(reportsService.getOverview);

  // 加载概览数据
  const loadOverview = async () => {
    try {
      const result = await fetchOverview();
      setOverview(result);
    } catch (error) {
      console.error('加载报表概览失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadOverview();
  }, []);

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">统计报表</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看系统各项数据统计和分析报告
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadOverview}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            刷新
          </button>
        </div>
      </div>

      {/* 快速统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.userReports?.totalUsers || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总收入</dt>
                <dd className="text-lg font-medium text-gray-900">
                  ¥{overview?.financialReports?.totalRevenue?.toFixed(2) || '0.00'}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总请求数</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.systemReports?.totalRequests || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">系统正常运行时间</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.systemReports?.uptime?.toFixed(1) || '0.0'}%
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* 报表模块导航 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link to="/reports/users" className="block">
          <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">用户统计</h3>
                <p className="text-sm text-gray-500">用户注册、活跃度、行为分析</p>
                <div className="mt-2 text-sm text-gray-600">
                  活跃用户: {overview?.userReports?.activeUsers || 0} | 
                  今日新增: {overview?.userReports?.newUsersToday || 0}
                </div>
              </div>
            </div>
          </div>
        </Link>

        <Link to="/reports/financial" className="block">
          <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">财务报表</h3>
                <p className="text-sm text-gray-500">收入、支出、交易分析</p>
                <div className="mt-2 text-sm text-gray-600">
                  总交易: {overview?.financialReports?.totalTransactions || 0} | 
                  今日收入: ¥{overview?.financialReports?.revenueToday?.toFixed(2) || '0.00'}
                </div>
              </div>
            </div>
          </div>
        </Link>

        <Link to="/reports/system" className="block">
          <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">系统报表</h3>
                <p className="text-sm text-gray-500">性能、错误、监控数据</p>
                <div className="mt-2 text-sm text-gray-600">
                  响应时间: {overview?.systemReports?.averageResponseTime || 0}ms | 
                  错误率: {overview?.systemReports?.errorRate?.toFixed(2) || '0.00'}%
                </div>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* 快速洞察 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">快速洞察</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">用户增长趋势</h4>
              <div className="text-2xl font-bold text-green-600">
                +{overview?.userReports?.userGrowthRate?.toFixed(1) || '0.0'}%
              </div>
              <p className="text-sm text-gray-500">相比上月</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">收入增长趋势</h4>
              <div className="text-2xl font-bold text-green-600">
                +{overview?.financialReports?.revenueGrowthRate?.toFixed(1) || '0.0'}%
              </div>
              <p className="text-sm text-gray-500">相比上月</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportsDashboard;
