import React, { useState } from 'react';
import { format } from 'date-fns';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Badge from '@/components/common/Badge';
import Pagination from '@/components/common/Pagination';
import { ChatMessage, ChatRole } from '@/types/chat';
import { TableColumn } from '@/types/common';

const ChatDemo: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      _id: '1',
      id: 'msg_001',
      chatId: 'chat_001',
      role: 'user',
      content: '你好，我想了解一下如何提高工作效率？',
      createdTime: '2024-01-15T10:30:00Z',
      isComplete: true,
      messageType: 'text'
    },
    {
      _id: '2',
      id: 'msg_002',
      chatId: 'chat_001',
      role: 'assistant',
      content: '你好！提高工作效率是一个很好的话题。以下是一些实用的建议：\n\n1. **时间管理**：使用番茄工作法，将工作分解为25分钟的专注时段\n2. **优先级排序**：使用艾森豪威尔矩阵来区分重要和紧急的任务\n3. **减少干扰**：关闭不必要的通知，创造专注的工作环境\n4. **合理休息**：定期休息可以保持大脑的活力和创造力\n\n你想了解其中哪个方面的更多细节吗？',
      createdTime: '2024-01-15T10:30:15Z',
      isComplete: true,
      messageType: 'text'
    },
    {
      _id: '3',
      id: 'msg_003',
      chatId: 'chat_001',
      role: 'user',
      content: '番茄工作法具体是怎么操作的？',
      createdTime: '2024-01-15T10:31:00Z',
      isComplete: true,
      messageType: 'text'
    },
    {
      _id: '4',
      id: 'msg_004',
      chatId: 'chat_001',
      role: 'assistant',
      content: '番茄工作法（Pomodoro Technique）的具体操作步骤如下：\n\n**基本步骤：**\n1. 选择一个要完成的任务\n2. 设定25分钟的计时器\n3. 专注工作，直到计时器响起\n4. 休息5分钟\n5. 重复上述步骤\n\n**进阶技巧：**\n- 每完成4个番茄时段，休息15-30分钟\n- 在番茄时段内不要做其他事情\n- 如果被打断，重新开始计时\n- 记录完成的番茄数量来跟踪进度\n\n这种方法可以帮助你保持专注，同时避免疲劳。你可以先试试看效果如何！',
      createdTime: '2024-01-15T10:31:30Z',
      isComplete: true,
      messageType: 'text'
    },
    {
      _id: '5',
      id: 'msg_005',
      chatId: 'chat_002',
      role: 'user',
      content: '请帮我分析一下这张图片中的内容',
      createdTime: '2024-01-15T11:00:00Z',
      isComplete: true,
      messageType: 'image'
    },
    {
      _id: '6',
      id: 'msg_006',
      chatId: 'chat_002',
      role: 'assistant',
      content: '我看到您想要分析图片内容，但是我没有收到任何图片。请确保您已经正确上传了图片文件。\n\n如果您遇到上传问题，可以尝试：\n1. 检查图片格式（支持 JPG、PNG、GIF 等）\n2. 确认图片大小不超过限制\n3. 刷新页面后重新上传\n\n上传成功后，我将很乐意为您分析图片内容！',
      createdTime: '2024-01-15T11:00:15Z',
      isComplete: true,
      messageType: 'text'
    },
    {
      _id: '7',
      id: 'msg_007',
      chatId: 'chat_003',
      role: 'user',
      content: '系统出现了一些问题，请帮忙检查',
      createdTime: '2024-01-15T12:00:00Z',
      isComplete: true,
      messageType: 'system'
    },
    {
      _id: '8',
      id: 'msg_008',
      chatId: 'chat_003',
      role: 'assistant',
      content: '我注意到您提到系统出现了问题。为了更好地帮助您，请提供以下信息：\n\n**问题详情：**\n- 具体出现了什么错误或异常？\n- 错误发生的时间和频率\n- 您当时正在进行什么操作？\n\n**系统环境：**\n- 使用的设备和操作系统\n- 浏览器类型和版本\n- 网络连接状况\n\n**错误信息：**\n- 是否有错误代码或提示信息？\n- 可以截图显示错误界面吗？\n\n提供这些信息后，我可以更准确地诊断问题并提供解决方案。',
      createdTime: '2024-01-15T12:00:30Z',
      isComplete: true,
      messageType: 'text'
    }
  ]);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [roleFilter, setRoleFilter] = useState('');

  // 获取角色信息
  const getRoleInfo = (role: ChatRole) => {
    switch (role) {
      case 'user':
        return { text: '用户', variant: 'info' as const };
      case 'assistant':
        return { text: 'AI助手', variant: 'success' as const };
      case 'other':
        return { text: '其他', variant: 'secondary' as const };
      default:
        return { text: '未知', variant: 'secondary' as const };
    }
  };

  // 过滤消息
  const filteredMessages = messages.filter(msg => {
    const matchesSearch = !searchKeyword || msg.content.toLowerCase().includes(searchKeyword.toLowerCase());
    const matchesRole = !roleFilter || msg.role === roleFilter;
    return matchesSearch && matchesRole;
  });

  // 分页
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedMessages = filteredMessages.slice(startIndex, endIndex);

  // 表格列定义
  const columns: TableColumn<ChatMessage>[] = [
    {
      key: 'id',
      title: '消息ID',
      dataIndex: 'id',
      width: 120,
      render: (id: string) => (
        <span className="text-xs font-mono text-gray-600">
          {id.substring(0, 8)}...
        </span>
      ),
    },
    {
      key: 'chatId',
      title: '会话ID',
      dataIndex: 'chatId',
      width: 120,
      render: (chatId: string) => (
        <span className="text-xs font-mono text-gray-600">
          {chatId.substring(0, 8)}...
        </span>
      ),
    },
    {
      key: 'role',
      title: '角色',
      dataIndex: 'role',
      width: 100,
      render: (role: ChatRole) => {
        const roleInfo = getRoleInfo(role);
        return <Badge variant={roleInfo.variant}>{roleInfo.text}</Badge>;
      },
    },
    {
      key: 'content',
      title: '消息内容',
      dataIndex: 'content',
      render: (content: string) => (
        <div className="max-w-md">
          <p className="text-sm text-gray-900 truncate" title={content}>
            {content}
          </p>
          {content.length > 100 && (
            <span className="text-xs text-gray-500">
              {content.length} 字符
            </span>
          )}
        </div>
      ),
    },
    {
      key: 'isComplete',
      title: '状态',
      dataIndex: 'isComplete',
      width: 100,
      render: (isComplete: boolean) => (
        <Badge variant={isComplete ? 'success' : 'warning'}>
          {isComplete ? '已完成' : '进行中'}
        </Badge>
      ),
    },
    {
      key: 'messageType',
      title: '类型',
      dataIndex: 'messageType',
      width: 80,
      render: (messageType: string = 'text') => {
        const typeMap = {
          text: '文本',
          image: '图片',
          audio: '音频',
          video: '视频',
          file: '文件',
          location: '位置',
          contact: '联系人',
          system: '系统',
        };
        return (
          <span className="text-xs text-gray-600">
            {typeMap[messageType as keyof typeof typeMap] || messageType}
          </span>
        );
      },
    },
    {
      key: 'createdTime',
      title: '创建时间',
      dataIndex: 'createdTime',
      width: 150,
      render: (createdTime: string) => (
        <span className="text-sm text-gray-500">
          {format(new Date(createdTime), 'yyyy-MM-dd HH:mm:ss')}
        </span>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="space-y-6">
          {/* 页面标题 */}
          <div>
            <h1 className="text-3xl font-bold text-gray-900">聊天消息管理演示</h1>
            <p className="mt-2 text-lg text-gray-600">
              展示用户消息和AI助手回复的完整对话记录
            </p>
          </div>

          {/* 功能说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-blue-900 mb-2">功能特点</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>✅ <strong>用户消息保存</strong>：现在可以正确保存和显示用户的消息</li>
              <li>✅ <strong>AI回复记录</strong>：AI助手的回复也会被完整保存</li>
              <li>✅ <strong>角色区分</strong>：通过不同颜色的标签区分用户和AI助手</li>
              <li>✅ <strong>消息类型</strong>：支持文本、图片、音频等多种消息类型</li>
              <li>✅ <strong>搜索过滤</strong>：可以按内容、角色等条件筛选消息</li>
              <li>✅ <strong>时间排序</strong>：按时间顺序展示对话流程</li>
            </ul>
          </div>

          {/* 搜索和筛选 */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                placeholder="搜索消息内容..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                fullWidth
              />
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">所有角色</option>
                <option value="user">用户</option>
                <option value="assistant">AI助手</option>
                <option value="other">其他</option>
              </select>
              <Button
                variant="secondary"
                onClick={() => {
                  setSearchKeyword('');
                  setRoleFilter('');
                  setCurrentPage(1);
                }}
                fullWidth
              >
                重置筛选
              </Button>
            </div>
          </div>

          {/* 消息列表 */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                聊天消息列表 ({filteredMessages.length} 条)
              </h3>
            </div>

            <Table
              columns={columns}
              data={paginatedMessages}
              emptyText="暂无消息数据"
              rowKey="_id"
            />

            {filteredMessages.length > 0 && (
              <Pagination
                current={currentPage}
                total={filteredMessages.length}
                pageSize={pageSize}
                onChange={setCurrentPage}
                onPageSizeChange={setPageSize}
                showSizeChanger
                showQuickJumper
                showTotal
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatDemo;
