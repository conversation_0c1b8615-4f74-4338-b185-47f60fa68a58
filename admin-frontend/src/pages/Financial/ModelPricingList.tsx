import React, { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { useApi } from '@/hooks/useApi';
import { useTable } from '@/hooks/useTable';
import { modelPricingService, ModelPricing } from '@/services/modelPricingService';
import * as aiConfigService from '@/services/aiConfigService';
import { TableColumn } from '@/types/common';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';
import Select from '@/components/common/Select';

const ModelPricingList: React.FC = () => {
  const [modelPricings, setModelPricings] = useState<ModelPricing[]>([]);
  const [total, setTotal] = useState(0);
  const [aiConfigs, setAiConfigs] = useState<any[]>([]);
  const [selectedConfigId, setSelectedConfigId] = useState<string>('');

  // 表格状态管理
  const {
    tableState,
    pagination,
    sorting,
    searching,
    getQueryParams,
  } = useTable({
    initialPageSize: 20,
    initialSortBy: 'createdAt',
    initialSortOrder: 'desc',
  });

  // API调用
  const {
    loading,
    execute: fetchModelPricings,
  } = useApi(modelPricingService.getModelPricings);

  const {
    loading: fixingZeroPricing,
    execute: fixZeroModelPricing,
  } = useApi(modelPricingService.fixZeroModelPricing);

  const {
    loading: syncingModels,
    execute: syncModelsWithPricing,
  } = useApi(modelPricingService.syncModelsWithPricing);

  const {
    loading: loadingConfigs,
    execute: getAiConfigs,
  } = useApi(aiConfigService.getConfigs);

  // 加载模型价格列表
  const loadModelPricings = useCallback(async () => {
    try {
      const params = {
        ...getQueryParams(),
        configId: selectedConfigId || undefined
      };
      const result = await fetchModelPricings(params);
      setModelPricings(result.items);
      setTotal(result.total);
    } catch (error) {
      console.error('加载模型价格列表失败:', error);
    }
  }, [fetchModelPricings, getQueryParams, selectedConfigId]);

  // 加载AI配置列表
  const loadAiConfigs = useCallback(async () => {
    try {
      const response = await getAiConfigs({ page: 1, limit: 100 });
      setAiConfigs(response.data || []);
    } catch (error) {
      console.error('加载AI配置失败:', error);
    }
  }, [getAiConfigs]);

  // 初始加载
  useEffect(() => {
    loadModelPricings();
    loadAiConfigs();
  }, [loadModelPricings, loadAiConfigs]);

  // 修复零价格
  const handleFixZeroPricing = async () => {
    try {
      const result = await fixZeroModelPricing();
      if (result.fixed > 0) {
        alert(`成功修复了 ${result.fixed} 个模型的零价格问题。\n默认价格设置为：${result.defaultPrice.toFixed(6)}/字符\n修复的模型：${result.models.join(', ')}`);
        loadModelPricings();
      } else {
        alert('没有发现需要修复的零价格模型');
      }
    } catch (error) {
      console.error('修复零价格失败:', error);
      alert('修复零价格失败，请稍后重试');
    }
  };

  // 同步模型
  const handleSyncModels = async () => {
    if (!selectedConfigId) {
      alert('请先选择一个AI配置');
      return;
    }

    if (!confirm('同步操作将删除现有模型并重新创建，是否继续？')) {
      return;
    }

    try {
      const result = await syncModelsWithPricing(selectedConfigId);
      const { configName, totalModels, defaultPrice, results } = result;
      
      alert(`同步完成！\n配置：${configName}\n总模型数：${totalModels}\n创建：${results.created} 个\n错误：${results.errors} 个\n默认价格：${defaultPrice.toFixed(6)}/字符`);
      
      loadModelPricings();
    } catch (error) {
      console.error('同步模型失败:', error);
      alert('同步模型失败，请稍后重试');
    }
  };

  // 表格列定义
  const columns: TableColumn<ModelPricing>[] = [
    {
      key: 'modelName',
      title: '模型名称',
      dataIndex: 'modelName',
      width: 150,
      render: (modelName: string, record: ModelPricing) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {modelName}
            {record.isCurrentModel && (
              <Badge variant="success" className="ml-2">当前</Badge>
            )}
          </div>
          <div className="text-gray-500">{record.displayName}</div>
        </div>
      ),
    },
    {
      key: 'pricing',
      title: '价格信息',
      dataIndex: 'pricing',
      width: 200,
      render: (pricing: ModelPricing['pricing']) => (
        <div className="text-sm">
          <div>输入: ¥{pricing.inputPrice.toFixed(6)}/字符</div>
          <div>输出: ¥{pricing.outputPrice.toFixed(6)}/字符</div>
          <div className="text-gray-500">货币: {pricing.currency}</div>
        </div>
      ),
    },
    {
      key: 'configInfo',
      title: '配置信息',
      dataIndex: 'configInfo',
      width: 150,
      render: (_, record: ModelPricing) => (
        <div className="text-sm">
          <div className="font-medium">{record.configName}</div>
          <div className="text-gray-500">{record.provider}</div>
        </div>
      ),
    },
    {
      key: 'maxTokens',
      title: '最大Token',
      dataIndex: 'maxTokens',
      width: 100,
      render: (maxTokens: number) => (
        <span className="text-sm">{maxTokens.toLocaleString()}</span>
      ),
    },
    {
      key: 'supportedFeatures',
      title: '支持功能',
      dataIndex: 'supportedFeatures',
      width: 120,
      render: (features: string[]) => (
        <div className="text-xs">
          {features.map(feature => (
            <Badge key={feature} variant="info" className="mr-1 mb-1">
              {feature}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'isActive',
      width: 80,
      render: (isActive: boolean) => (
        <Badge variant={isActive ? 'success' : 'default'}>
          {isActive ? '启用' : '禁用'}
        </Badge>
      ),
    },
    {
      key: 'updatedAt',
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 150,
      sortable: true,
      render: (updatedAt: string) => (
        <span className="text-sm text-gray-500">
          {format(new Date(updatedAt), 'yyyy-MM-dd HH:mm')}
        </span>
      ),
    },
    {
      key: 'actions',
      title: '操作',
      dataIndex: '_id',
      width: 120,
      render: (id: string, record: ModelPricing) => (
        <div className="flex space-x-2">
          <Button size="sm" variant="secondary">
            编辑
          </Button>
        </div>
      ),
    },
  ];

  if (loading && modelPricings.length === 0) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">模型价格管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            基于AI配置系统的统一模型价格管理
          </p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="warning" 
            onClick={handleFixZeroPricing}
            loading={fixingZeroPricing}
          >
            修复零价格
          </Button>
        </div>
      </div>

      {/* 筛选和同步区域 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择AI配置
            </label>
            <Select
              value={selectedConfigId}
              onChange={setSelectedConfigId}
              placeholder="选择AI配置"
              className="w-64"
            >
              <option value="">所有配置</option>
              {aiConfigs.map(config => (
                <option key={config._id} value={config._id}>
                  {config.name} ({config.provider})
                </option>
              ))}
            </Select>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="primary" 
              onClick={handleSyncModels}
              loading={syncingModels}
              disabled={!selectedConfigId}
            >
              同步模型
            </Button>
            <Button 
              variant="secondary" 
              onClick={loadModelPricings}
              loading={loading}
            >
              刷新
            </Button>
          </div>
        </div>
      </div>

      {/* 数据表格 */}
      <div className="bg-white rounded-lg shadow">
        <Table
          columns={columns}
          data={modelPricings}
          loading={loading}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total,
            onChange: pagination.handlePageChange,
            onShowSizeChange: pagination.handlePageSizeChange,
          }}
          sorting={{
            sortBy: sorting.sortBy,
            sortOrder: sorting.sortOrder,
            onSort: sorting.handleSort,
          }}
          searching={{
            searchValue: searching.searchValue,
            onSearch: searching.handleSearch,
            placeholder: '搜索模型名称...',
          }}
        />
      </div>
    </div>
  );
};

export default ModelPricingList;
