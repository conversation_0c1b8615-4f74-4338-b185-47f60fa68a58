import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useApi } from '@/hooks/useApi';
import { financialService } from '@/services/financial';
import Loading from '@/components/common/Loading';

interface FinancialOverview {
  totalRevenue: number;
  totalTransactions: number;
  totalProducts: number;
  recentTransactions: any[];
  revenueGrowth: number;
  transactionGrowth: number;
}

const FinancialDashboard: React.FC = () => {
  const [overview, setOverview] = useState<FinancialOverview | null>(null);

  // API调用
  const {
    loading: transactionsLoading,
    execute: fetchTransactions,
  } = useApi(financialService.getTransactions);

  const {
    loading: productsLoading,
    execute: fetchProducts,
  } = useApi(financialService.getProducts);

  const loading = transactionsLoading || productsLoading;

  // 加载概览数据
  const loadOverview = async () => {
    try {
      // 并行获取交易和产品数据来构建概览
      const [transactionsResult, productsResult] = await Promise.all([
        fetchTransactions({ page: 1, limit: 10 }),
        fetchProducts({ page: 1, limit: 10 })
      ]);

      // 构建概览数据
      const overviewData = {
        totalRevenue: transactionsResult.items.reduce((sum: number, t: any) =>
          sum + (t.type === 'recharge' ? t.amount : 0), 0),
        totalTransactions: transactionsResult.total,
        totalProducts: productsResult.total,
        recentTransactions: transactionsResult.items.slice(0, 5),
        revenueGrowth: 0, // 暂时设为0，需要历史数据计算
        transactionGrowth: 0, // 暂时设为0，需要历史数据计算
      };

      setOverview(overviewData);
    } catch (error) {
      console.error('加载财务概览失败:', error);
      // 设置默认值以避免页面崩溃
      setOverview({
        totalRevenue: 0,
        totalTransactions: 0,
        totalProducts: 0,
        recentTransactions: [],
        revenueGrowth: 0,
        transactionGrowth: 0,
      });
    }
  };

  // 初始加载
  useEffect(() => {
    loadOverview();
  }, []);

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">财务管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            管理交易记录、产品和定价策略
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={loadOverview}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            刷新
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总收入</dt>
                <dd className="text-lg font-medium text-gray-900">
                  ¥{overview?.totalRevenue?.toFixed(2) || '0.00'}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">总交易数</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.totalTransactions || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">产品数量</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.totalProducts || 0}
                </dd>
              </dl>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div className="ml-5 w-0 flex-1">
              <dl>
                <dt className="text-sm font-medium text-gray-500 truncate">收入增长</dt>
                <dd className="text-lg font-medium text-gray-900">
                  {overview?.revenueGrowth ? `${overview.revenueGrowth.toFixed(1)}%` : '0.0%'}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      {/* 快速导航 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link to="/financial/transactions" className="block">
          <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">交易记录</h3>
                <p className="text-sm text-gray-500">查看和管理所有交易记录</p>
              </div>
            </div>
          </div>
        </Link>

        <Link to="/financial/products" className="block">
          <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">产品管理</h3>
                <p className="text-sm text-gray-500">管理产品信息和库存</p>
              </div>
            </div>
          </div>
        </Link>

        <Link to="/financial/pricing" className="block">
          <div className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">定价管理</h3>
                <p className="text-sm text-gray-500">设置和调整产品定价</p>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* 最近交易 */}
      {overview?.recentTransactions && overview.recentTransactions.length > 0 && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">最近交易</h3>
          </div>
          <div className="px-6 py-4">
            <div className="text-sm text-gray-500 mb-4">
              显示最近的交易记录
            </div>
            <div className="space-y-2">
              {overview.recentTransactions.slice(0, 5).map((transaction, index) => (
                <div key={index} className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">交易 #{index + 1}</span>
                  <span className="font-medium">¥{transaction.amount || '0.00'}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FinancialDashboard;
