import React from 'react';
import { Routes, Route } from 'react-router-dom';
import FinancialDashboard from './FinancialDashboard';
import TransactionList from './TransactionList';
import HierarchicalTransactionView from './HierarchicalTransactionView';
import ProductList from './ProductList';
import PricingList from './PricingList';

const Financial: React.FC = () => {
  return (
    <Routes>
      <Route index element={<FinancialDashboard />} />
      <Route path="transactions" element={<TransactionList />} />
      <Route path="transactions/hierarchical" element={<HierarchicalTransactionView />} />
      <Route path="products" element={<ProductList />} />
      <Route path="pricing" element={<PricingList />} />
    </Routes>
  );
};

export default Financial;
