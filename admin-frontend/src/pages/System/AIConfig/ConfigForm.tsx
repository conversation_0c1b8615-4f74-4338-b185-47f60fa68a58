import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { AIServiceConfig, CreateConfigRequest, UpdateConfigRequest } from '../../../types/aiConfig';
import { aiConfigService } from '../../../services/aiConfigService';
import { useToast } from '../../../hooks/useToast';

interface ConfigFormProps {
  config?: AIServiceConfig | null;
  onSuccess: () => void;
  onCancel: () => void;
}

const ConfigForm: React.FC<ConfigFormProps> = ({ config, onSuccess, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    baseURL: '',
    apiKey: '',
    provider: 'openai' as const,
    maxRetries: 3,
    timeout: 60000,
    proxyEnabled: false,
    proxyUrl: '',
    requestsPerMinute: 60,
    tokensPerMinute: 100000,
    isActive: true
  });
  const [loading, setLoading] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const { showToast } = useToast();

  useEffect(() => {
    if (config) {
      setFormData({
        name: config.name,
        description: config.description || '',
        baseURL: config.baseURL,
        apiKey: '', // 不显示现有的API密钥
        provider: config.provider,
        maxRetries: config.maxRetries,
        timeout: config.timeout,
        proxyEnabled: config.proxyConfig?.enabled || false,
        proxyUrl: config.proxyConfig?.url || '',
        requestsPerMinute: config.rateLimits?.requestsPerMinute || 60,
        tokensPerMinute: config.rateLimits?.tokensPerMinute || 100000,
        isActive: config.isActive
      });
    }
  }, [config]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const requestData: CreateConfigRequest | UpdateConfigRequest = {
        name: formData.name,
        description: formData.description || undefined,
        baseURL: formData.baseURL,
        provider: formData.provider,
        maxRetries: formData.maxRetries,
        timeout: formData.timeout,
        proxyConfig: {
          enabled: formData.proxyEnabled,
          url: formData.proxyEnabled ? formData.proxyUrl : undefined
        },
        rateLimits: {
          requestsPerMinute: formData.requestsPerMinute,
          tokensPerMinute: formData.tokensPerMinute
        }
      };

      // 只有在输入了API密钥时才包含它
      if (formData.apiKey) {
        requestData.apiKey = formData.apiKey;
      }

      // 如果是编辑模式，添加isActive字段
      if (config) {
        (requestData as UpdateConfigRequest).isActive = formData.isActive;
      }

      if (config) {
        await aiConfigService.updateConfig(config._id, requestData as UpdateConfigRequest);
        showToast('配置更新成功', 'success');
      } else {
        if (!formData.apiKey) {
          showToast('API密钥不能为空', 'error');
          return;
        }
        await aiConfigService.createConfig(requestData as CreateConfigRequest);
        showToast('配置创建成功', 'success');
      }

      onSuccess();
    } catch (error: any) {
      const message = error.response?.data?.message || (config ? '更新配置失败' : '创建配置失败');
      showToast(message, 'error');
      console.error('配置操作失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* 标题 */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900">
            {config ? '编辑AI配置' : '新增AI配置'}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <div className="grid grid-cols-1 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                配置名称 *
              </label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="input mt-1"
                placeholder="输入配置名称"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                描述
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className="input mt-1"
                rows={3}
                placeholder="输入配置描述"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  服务提供商 *
                </label>
                <select
                  value={formData.provider}
                  onChange={(e) => handleInputChange('provider', e.target.value)}
                  className="input mt-1"
                >
                  <option value="openai">OpenAI</option>
                  <option value="anthropic">Anthropic</option>
                  <option value="google">Google</option>
                  <option value="azure">Azure</option>
                  <option value="custom">自定义</option>
                </select>
              </div>

              {config && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    状态
                  </label>
                  <select
                    value={formData.isActive ? 'active' : 'inactive'}
                    onChange={(e) => handleInputChange('isActive', e.target.value === 'active')}
                    className="input mt-1"
                  >
                    <option value="active">启用</option>
                    <option value="inactive">禁用</option>
                  </select>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                API端点 *
              </label>
              <input
                type="url"
                required
                value={formData.baseURL}
                onChange={(e) => handleInputChange('baseURL', e.target.value)}
                className="input mt-1"
                placeholder="https://api.openai.com/v1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">
                API密钥 {config ? '' : '*'}
              </label>
              <div className="mt-1 relative">
                <input
                  type={showApiKey ? 'text' : 'password'}
                  required={!config}
                  value={formData.apiKey}
                  onChange={(e) => handleInputChange('apiKey', e.target.value)}
                  className="input pr-10"
                  placeholder={config ? '留空表示不修改' : '输入API密钥'}
                />
                <button
                  type="button"
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                >
                  {showApiKey ? '隐藏' : '显示'}
                </button>
              </div>
            </div>
          </div>

          {/* 高级设置 */}
          <div className="border-t pt-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">高级设置</h4>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  最大重试次数
                </label>
                <input
                  type="number"
                  min="0"
                  max="10"
                  value={formData.maxRetries}
                  onChange={(e) => handleInputChange('maxRetries', parseInt(e.target.value))}
                  className="input mt-1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  超时时间 (毫秒)
                </label>
                <input
                  type="number"
                  min="5000"
                  max="300000"
                  value={formData.timeout}
                  onChange={(e) => handleInputChange('timeout', parseInt(e.target.value))}
                  className="input mt-1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  每分钟请求数
                </label>
                <input
                  type="number"
                  min="1"
                  max="1000"
                  value={formData.requestsPerMinute}
                  onChange={(e) => handleInputChange('requestsPerMinute', parseInt(e.target.value))}
                  className="input mt-1"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">
                  每分钟Token数
                </label>
                <input
                  type="number"
                  min="1000"
                  max="1000000"
                  value={formData.tokensPerMinute}
                  onChange={(e) => handleInputChange('tokensPerMinute', parseInt(e.target.value))}
                  className="input mt-1"
                />
              </div>
            </div>

            {/* 代理设置 */}
            <div className="mt-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.proxyEnabled}
                  onChange={(e) => handleInputChange('proxyEnabled', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  启用代理
                </label>
              </div>
              
              {formData.proxyEnabled && (
                <div className="mt-2">
                  <input
                    type="url"
                    value={formData.proxyUrl}
                    onChange={(e) => handleInputChange('proxyUrl', e.target.value)}
                    className="input"
                    placeholder="http://proxy.example.com:8080"
                  />
                </div>
              )}
            </div>
          </div>

          {/* 按钮 */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="btn btn-primary"
            >
              {loading ? '保存中...' : (config ? '更新' : '创建')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ConfigForm;
