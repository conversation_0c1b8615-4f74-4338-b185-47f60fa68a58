import React, { useState, useEffect } from 'react';
import { 
  ChartBarIcon, 
  ClockIcon, 
  CurrencyDollarIcon,
  CheckCircleIcon,
  XCircleIcon 
} from '@heroicons/react/24/outline';
import { AIServiceConfig, AIConfigUsageStats } from '../../../types/aiConfig';
import { aiConfigService } from '../../../services/aiConfigService';
import { useToast } from '../../../hooks/useToast';

interface UsageStatsProps {
  configs: AIServiceConfig[];
}

const UsageStats: React.FC<UsageStatsProps> = ({ configs }) => {
  const [selectedConfigId, setSelectedConfigId] = useState<string>('');
  const [stats, setStats] = useState<AIConfigUsageStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7天前
    endDate: new Date().toISOString().split('T')[0] // 今天
  });
  const { showToast } = useToast();

  // 加载使用统计
  const loadStats = async (configId: string) => {
    if (!configId) {
      setStats(null);
      return;
    }

    try {
      setLoading(true);
      const response = await aiConfigService.getUsageStats(configId, {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      setStats(response.data);
    } catch (error) {
      showToast('加载使用统计失败', 'error');
      console.error('加载统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (configs.length > 0 && !selectedConfigId) {
      const defaultConfig = configs.find(c => c.isDefault) || configs[0];
      setSelectedConfigId(defaultConfig._id);
    }
  }, [configs]);

  useEffect(() => {
    loadStats(selectedConfigId);
  }, [selectedConfigId, dateRange]);

  const selectedConfig = configs.find(c => c._id === selectedConfigId);

  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(4)}`;
  };

  const formatTime = (ms: number) => {
    return ms < 1000 ? `${ms.toFixed(0)}ms` : `${(ms / 1000).toFixed(2)}s`;
  };

  const getSuccessRate = () => {
    if (!stats || stats.totalRequests === 0) return 0;
    return ((stats.successfulRequests / stats.totalRequests) * 100).toFixed(1);
  };

  return (
    <div className="space-y-6">
      {/* 配置和日期选择 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">
            选择配置
          </label>
          <select
            value={selectedConfigId}
            onChange={(e) => setSelectedConfigId(e.target.value)}
            className="input mt-1"
          >
            <option value="">请选择配置</option>
            {configs.map(config => (
              <option key={config._id} value={config._id}>
                {config.name} ({config.provider})
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            开始日期
          </label>
          <input
            type="date"
            value={dateRange.startDate}
            onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
            className="input mt-1"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">
            结束日期
          </label>
          <input
            type="date"
            value={dateRange.endDate}
            onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
            className="input mt-1"
          />
        </div>
      </div>

      {/* 统计卡片 */}
      {selectedConfigId && (
        <div>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="spinner h-8 w-8"></div>
            </div>
          ) : stats ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* 总请求数 */}
              <div className="card card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">总请求数</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {formatNumber(stats.totalRequests)}
                    </p>
                  </div>
                </div>
              </div>

              {/* 成功率 */}
              <div className="card card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircleIcon className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">成功率</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {getSuccessRate()}%
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatNumber(stats.successfulRequests)} / {formatNumber(stats.totalRequests)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Token使用量 */}
              <div className="card card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <span className="text-purple-600 font-bold text-sm">T</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Token使用量</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {formatNumber(stats.totalTokens)}
                    </p>
                  </div>
                </div>
              </div>

              {/* 总成本 */}
              <div className="card card-body">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyDollarIcon className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">总成本</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {formatCurrency(stats.totalCost)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500">暂无统计数据</div>
            </div>
          )}

          {/* 详细统计 */}
          {stats && (
            <div className="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 请求统计 */}
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">请求统计</h3>
                </div>
                <div className="card-body">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">成功请求</span>
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                        <span className="font-medium">{formatNumber(stats.successfulRequests)}</span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">失败请求</span>
                      <div className="flex items-center">
                        <XCircleIcon className="h-4 w-4 text-red-500 mr-1" />
                        <span className="font-medium">{formatNumber(stats.failedRequests)}</span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">平均响应时间</span>
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-blue-500 mr-1" />
                        <span className="font-medium">{formatTime(stats.averageResponseTime)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 成本分析 */}
              <div className="card">
                <div className="card-header">
                  <h3 className="text-lg font-medium text-gray-900">成本分析</h3>
                </div>
                <div className="card-body">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">总成本</span>
                      <span className="font-medium">{formatCurrency(stats.totalCost)}</span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">平均每请求成本</span>
                      <span className="font-medium">
                        {stats.totalRequests > 0 
                          ? formatCurrency(stats.totalCost / stats.totalRequests)
                          : '$0.0000'
                        }
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">平均每Token成本</span>
                      <span className="font-medium">
                        {stats.totalTokens > 0 
                          ? `$${(stats.totalCost / stats.totalTokens * 1000).toFixed(6)}/1K`
                          : '$0.000000/1K'
                        }
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default UsageStats;
