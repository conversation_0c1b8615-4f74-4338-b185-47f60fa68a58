import React, { useState, useEffect } from 'react';
import {
  PencilIcon,
  TrashIcon,
  PlayIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  BoltIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon, BoltIcon as BoltSolidIcon } from '@heroicons/react/24/solid';
import { AIServiceConfig, ActiveConfigStats } from '../../../types/aiConfig';
import { aiConfigService } from '../../../services/aiConfigService';
import { useToast } from '../../../hooks/useToast';
import ModelSelectDialog from './ModelSelectDialog';

interface ConfigListProps {
  configs: AIServiceConfig[];
  loading: boolean;
  onEdit: (config: AIServiceConfig) => void;
  onDelete: (config: AIServiceConfig) => void;
  onTest: (config: AIServiceConfig) => void;
  onSetDefault: (config: AIServiceConfig) => void;
  onActivate: (config: AIServiceConfig) => void;
  onRefresh: () => void;
}

const ConfigList: React.FC<ConfigListProps> = ({
  configs,
  loading,
  onEdit,
  onDelete,
  onTest,
  onSetDefault,
  onActivate,
  onRefresh
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterProvider, setFilterProvider] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [activeConfigStats, setActiveConfigStats] = useState<ActiveConfigStats | null>(null);
  const [showModelDialog, setShowModelDialog] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<AIServiceConfig | null>(null);
  const { showToast } = useToast();

  // 加载启用配置统计
  useEffect(() => {
    const loadActiveConfigStats = async () => {
      try {
        const response = await aiConfigService.getActiveConfigStats();
        setActiveConfigStats(response.data);
      } catch (error) {
        console.error('加载启用配置统计失败:', error);
      }
    };

    loadActiveConfigStats();
  }, [configs]); // 当配置列表变化时重新加载

  // 处理启用配置
  const handleActivateConfig = (config: AIServiceConfig) => {
    setSelectedConfig(config);
    setShowModelDialog(true);
  };

  // 模型选择成功
  const handleModelSelectSuccess = () => {
    setShowModelDialog(false);
    setSelectedConfig(null);
    onRefresh(); // 刷新配置列表
  };

  // 模型选择取消
  const handleModelSelectCancel = () => {
    setShowModelDialog(false);
    setSelectedConfig(null);
  };

  // 检查配置是否为当前启用配置
  const isConfigCurrentlyActive = (configId: string): boolean => {
    return !!(activeConfigStats?.hasActiveConfig && activeConfigStats.configId === configId);
  };

  // 过滤配置
  const filteredConfigs = (configs || []).filter(config => {
    const matchesSearch = config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         config.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesProvider = !filterProvider || config.provider === filterProvider;
    const matchesStatus = !filterStatus ||
                         (filterStatus === 'active' && config.isActive) ||
                         (filterStatus === 'inactive' && !config.isActive);

    return matchesSearch && matchesProvider && matchesStatus;
  });

  const getProviderBadge = (provider: string) => {
    const colors = {
      openai: 'bg-green-100 text-green-800',
      anthropic: 'bg-blue-100 text-blue-800',
      google: 'bg-yellow-100 text-yellow-800',
      azure: 'bg-purple-100 text-purple-800',
      custom: 'bg-gray-100 text-gray-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[provider] || colors.custom}`}>
        {provider.toUpperCase()}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="spinner h-8 w-8"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 搜索和过滤 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="搜索配置名称或描述..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input w-full"
          />
        </div>
        <div className="flex gap-2">
          <select
            value={filterProvider}
            onChange={(e) => setFilterProvider(e.target.value)}
            className="input"
          >
            <option value="">所有提供商</option>
            <option value="openai">OpenAI</option>
            <option value="anthropic">Anthropic</option>
            <option value="google">Google</option>
            <option value="azure">Azure</option>
            <option value="custom">自定义</option>
          </select>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="input"
          >
            <option value="">所有状态</option>
            <option value="active">启用</option>
            <option value="inactive">禁用</option>
          </select>
          <button
            onClick={onRefresh}
            className="btn btn-secondary"
          >
            <ArrowPathIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* 配置列表 */}
      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th>配置名称</th>
                <th>提供商</th>
                <th>API端点</th>
                <th>状态</th>
                <th>最后使用</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {filteredConfigs.map((config) => (
                <tr key={config._id} className="table-row">
                  <td>
                    <div className="flex items-center">
                      {config.isDefault && (
                        <StarSolidIcon className="h-4 w-4 text-yellow-400 mr-2" title="默认配置" />
                      )}
                      {isConfigCurrentlyActive(config._id) && (
                        <BoltSolidIcon className="h-4 w-4 text-green-500 mr-2" title="当前启用" />
                      )}
                      <div>
                        <div className="font-medium text-gray-900">{config.name}</div>
                        {config.description && (
                          <div className="text-sm text-gray-500">{config.description}</div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td>
                    {getProviderBadge(config.provider)}
                  </td>
                  <td>
                    <div className="text-sm text-gray-900 font-mono">
                      {config.baseURL}
                    </div>
                  </td>
                  <td>
                    {config.isActive ? (
                      <span className="badge badge-success">
                        <CheckCircleIcon className="h-4 w-4 mr-1" />
                        启用
                      </span>
                    ) : (
                      <span className="badge badge-danger">
                        <XCircleIcon className="h-4 w-4 mr-1" />
                        禁用
                      </span>
                    )}
                  </td>
                  <td>
                    <div className="text-sm text-gray-500">
                      {config.lastUsedAt ? formatDate(config.lastUsedAt) : '从未使用'}
                    </div>
                  </td>
                  <td>
                    <div className="text-sm text-gray-500">
                      {formatDate(config.createdAt)}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onTest(config)}
                        className="text-blue-600 hover:text-blue-900"
                        title="测试连接"
                      >
                        <PlayIcon className="h-4 w-4" />
                      </button>
                      {!isConfigCurrentlyActive(config._id) && config.isActive && (
                        <button
                          onClick={() => handleActivateConfig(config)}
                          className="text-green-600 hover:text-green-900"
                          title="启用配置"
                        >
                          <BoltIcon className="h-4 w-4" />
                        </button>
                      )}
                      {!config.isDefault && (
                        <button
                          onClick={() => onSetDefault(config)}
                          className="text-yellow-600 hover:text-yellow-900"
                          title="设为默认"
                        >
                          <StarIcon className="h-4 w-4" />
                        </button>
                      )}
                      <button
                        onClick={() => onEdit(config)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="编辑"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      {!config.isDefault && (
                        <button
                          onClick={() => onDelete(config)}
                          className="text-red-600 hover:text-red-900"
                          title="删除"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredConfigs.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">
              {configs.length === 0 ? '暂无配置' : '没有找到匹配的配置'}
            </div>
          </div>
        )}
      </div>

      {/* 模型选择对话框 */}
      {showModelDialog && selectedConfig && (
        <ModelSelectDialog
          config={selectedConfig}
          onSuccess={handleModelSelectSuccess}
          onCancel={handleModelSelectCancel}
        />
      )}
    </div>
  );
};

export default ConfigList;
