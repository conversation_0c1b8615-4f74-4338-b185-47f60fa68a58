import React, { useState } from 'react';
import { XMarkIcon, CheckCircleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/outline';
import { AIServiceConfig } from '../../../types/aiConfig';
import { aiConfigService } from '../../../services/aiConfigService';

interface TestConnectionProps {
  config: AIServiceConfig;
  onClose: () => void;
}

const TestConnection: React.FC<TestConnectionProps> = ({ config, onClose }) => {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    models?: string[];
    error?: string;
    responseTime?: number;
  } | null>(null);

  const handleTest = async () => {
    setTesting(true);
    setResult(null);

    try {
      const response = await aiConfigService.testConfig(config._id);
      setResult(response.data);
    } catch (error: any) {
      setResult({
        success: false,
        error: error.response?.data?.message || '测试失败'
      });
    } finally {
      setTesting(false);
    }
  };

  const formatResponseTime = (time?: number) => {
    if (!time) return '';
    return time < 1000 ? `${time}ms` : `${(time / 1000).toFixed(2)}s`;
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* 标题 */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900">
            测试连接 - {config.name}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 配置信息 */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-3">配置信息</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">提供商:</span>
              <span className="ml-2 font-medium">{config.provider.toUpperCase()}</span>
            </div>
            <div>
              <span className="text-gray-500">API端点:</span>
              <span className="ml-2 font-mono text-xs">{config.baseURL}</span>
            </div>
            <div>
              <span className="text-gray-500">超时时间:</span>
              <span className="ml-2">{config.timeout}ms</span>
            </div>
            <div>
              <span className="text-gray-500">最大重试:</span>
              <span className="ml-2">{config.maxRetries}次</span>
            </div>
          </div>
        </div>

        {/* 测试按钮 */}
        <div className="mb-6">
          <button
            onClick={handleTest}
            disabled={testing}
            className="btn btn-primary w-full"
          >
            {testing ? (
              <>
                <div className="spinner h-4 w-4 mr-2"></div>
                测试中...
              </>
            ) : (
              '开始测试'
            )}
          </button>
        </div>

        {/* 测试结果 */}
        {result && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-900 mb-3">测试结果</h4>
            
            {result.success ? (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center mb-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                  <span className="text-green-800 font-medium">连接成功</span>
                  {result.responseTime && (
                    <span className="ml-auto text-sm text-green-600">
                      响应时间: {formatResponseTime(result.responseTime)}
                    </span>
                  )}
                </div>
                
                {result.models && result.models.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium text-green-800 mb-2">
                      可用模型 ({result.models.length}个)
                    </h5>
                    <div className="max-h-40 overflow-y-auto">
                      <div className="grid grid-cols-1 gap-1">
                        {result.models.map((model, index) => (
                          <div
                            key={index}
                            className="px-3 py-1 bg-white border border-green-200 rounded text-sm font-mono"
                          >
                            {model}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center mb-3">
                  <XCircleIcon className="h-5 w-5 text-red-500 mr-2" />
                  <span className="text-red-800 font-medium">连接失败</span>
                  {result.responseTime && (
                    <span className="ml-auto text-sm text-red-600">
                      响应时间: {formatResponseTime(result.responseTime)}
                    </span>
                  )}
                </div>
                
                {result.error && (
                  <div>
                    <h5 className="text-sm font-medium text-red-800 mb-2">错误信息</h5>
                    <div className="p-3 bg-white border border-red-200 rounded text-sm text-red-700 font-mono">
                      {result.error}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* 测试说明 */}
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">测试说明</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• 测试会验证API密钥和端点的有效性</li>
            <li>• 成功时会获取可用的模型列表</li>
            <li>• 响应时间包含网络延迟和API处理时间</li>
            <li>• 失败时请检查API密钥、网络连接和端点配置</li>
          </ul>
        </div>

        {/* 按钮 */}
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="btn btn-secondary"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestConnection;
