import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Modal, { ModalBody, ModalFooter } from '@/components/common/Modal';
import Loading from '@/components/common/Loading';
import { useApi } from '@/hooks/useApi';
import { systemService } from '@/services/system';
import { VersionControlConfig, VersionControlUpdateData, VersionCheckResponse } from '@/types/system';

const VersionControl: React.FC = () => {
  const [config, setConfig] = useState<VersionControlConfig | null>(null);
  const [versionCheckResult, setVersionCheckResult] = useState<VersionCheckResponse | null>(null);
  const [testModalOpen, setTestModalOpen] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<'ios' | 'android'>('ios');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<VersionControlUpdateData>();

  // 监听表单字段变化
  const watchedFields = watch();

  // API调用
  const {
    loading,
    execute: fetchConfig,
  } = useApi(systemService.getVersionConfig);

  const {
    loading: saving,
    execute: updateConfig,
  } = useApi(systemService.updateVersionConfig);

  const {
    loading: testing,
    execute: testVersionCheck,
  } = useApi(systemService.checkVersion);

  // 加载配置
  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const configData = await fetchConfig();
      setConfig(configData);
      // 填充表单
      setValue('latestVersion', configData.latestVersion);
      setValue('minimumVersion', configData.minimumVersion);
      setValue('forceUpdate', configData.forceUpdate);
      setValue('updateMessage', configData.updateMessage);
      setValue('appStoreUrls', configData.appStoreUrls);
      setValue('updateType', configData.updateType);
      setValue('versionCheckEnabled', configData.versionCheckEnabled);
    } catch (error) {
      console.error('加载版本控制配置失败:', error);
    }
  };

  // 保存配置
  const onSubmit = async (data: VersionControlUpdateData) => {
    try {
      const updatedConfig = await updateConfig(data);
      setConfig(updatedConfig);
      alert('版本控制配置保存成功！');
    } catch (error) {
      console.error('保存版本控制配置失败:', error);
    }
  };

  // 测试版本检查
  const handleTestVersionCheck = async (platform: 'ios' | 'android') => {
    try {
      const result = await testVersionCheck(platform);
      setVersionCheckResult(result);
      setSelectedPlatform(platform);
      setTestModalOpen(true);
    } catch (error) {
      console.error('版本检查测试失败:', error);
    }
  };

  if (loading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900">版本控制管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            管理应用版本控制策略，包括版本检测、强制更新和应用商店链接配置
          </p>
        </div>
      </div>

      {/* 版本控制配置表单 */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* 基础版本配置 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">版本配置</h3>
            <p className="mt-1 text-sm text-gray-600">配置应用的版本号和更新策略</p>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="最新版本号"
                {...register('latestVersion', {
                  required: '请输入最新版本号',
                  pattern: {
                    value: /^\d+\.\d+\.\d+$/,
                    message: '版本号格式应为 x.y.z',
                  },
                })}
                error={errors.latestVersion?.message}
                placeholder="例如：1.2.0"
                fullWidth
              />
              
              <Input
                label="最低支持版本号"
                {...register('minimumVersion', {
                  required: '请输入最低支持版本号',
                  pattern: {
                    value: /^\d+\.\d+\.\d+$/,
                    message: '版本号格式应为 x.y.z',
                  },
                })}
                error={errors.minimumVersion?.message}
                placeholder="例如：1.0.0"
                fullWidth
              />
            </div>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  更新类型
                </label>
                <select
                  {...register('updateType', { required: '请选择更新类型' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="optional">可选更新</option>
                  <option value="force">强制更新</option>
                </select>
                {errors.updateType && (
                  <p className="mt-1 text-sm text-red-600">{errors.updateType.message}</p>
                )}
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  {...register('versionCheckEnabled')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  启用版本检测
                </label>
              </div>
            </div>

            <div className="mt-6">
              <input
                type="checkbox"
                {...register('forceUpdate')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                强制更新（当有新版本时强制用户更新）
              </label>
            </div>
          </div>
        </div>

        {/* 应用商店链接配置 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">应用商店链接</h3>
            <p className="mt-1 text-sm text-gray-600">配置不同平台的应用商店下载链接</p>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 gap-6">
              <Input
                label="iOS App Store 链接"
                {...register('appStoreUrls.ios', {
                  required: '请输入iOS应用商店链接',
                })}
                error={errors.appStoreUrls?.ios?.message}
                placeholder="https://apps.apple.com/app/your-app-id"
                fullWidth
              />
              
              <Input
                label="Android Google Play 链接"
                {...register('appStoreUrls.android', {
                  required: '请输入Android应用商店链接',
                })}
                error={errors.appStoreUrls?.android?.message}
                placeholder="https://play.google.com/store/apps/details?id=your.package.name"
                fullWidth
              />
            </div>
          </div>
        </div>

        {/* 更新消息配置 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">更新提示消息</h3>
            <p className="mt-1 text-sm text-gray-600">配置不同语言的更新提示消息</p>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  中文消息
                </label>
                <textarea
                  {...register('updateMessage.zh_CN', {
                    required: '请输入中文更新消息',
                  })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="发现新版本，建议立即更新以获得更好的体验。"
                />
                {errors.updateMessage?.zh_CN && (
                  <p className="mt-1 text-sm text-red-600">{errors.updateMessage.zh_CN.message}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  英文消息
                </label>
                <textarea
                  {...register('updateMessage.en', {
                    required: '请输入英文更新消息',
                  })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="New version available, please update for better experience."
                />
                {errors.updateMessage?.en && (
                  <p className="mt-1 text-sm text-red-600">{errors.updateMessage.en.message}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4">
            <div className="flex justify-between items-center">
              <div className="flex space-x-4">
                <Button
                  type="submit"
                  loading={saving}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  保存配置
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => reset()}
                >
                  重置
                </Button>
              </div>

              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  loading={testing}
                  onClick={() => handleTestVersionCheck('ios')}
                >
                  测试 iOS 版本检查
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  loading={testing}
                  onClick={() => handleTestVersionCheck('android')}
                >
                  测试 Android 版本检查
                </Button>
              </div>
            </div>
          </div>
        </div>
      </form>

      {/* 版本检查测试结果模态框 */}
      <Modal
        isOpen={testModalOpen}
        onClose={() => setTestModalOpen(false)}
        title={`${selectedPlatform === 'ios' ? 'iOS' : 'Android'} 版本检查测试结果`}
      >
        <ModalBody>
          {versionCheckResult && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">是否需要更新</label>
                  <p className={`text-sm font-semibold ${versionCheckResult.needUpdate ? 'text-orange-600' : 'text-green-600'}`}>
                    {versionCheckResult.needUpdate ? '是' : '否'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">更新类型</label>
                  <p className="text-sm">{versionCheckResult.updateType}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">当前版本</label>
                  <p className="text-sm">{versionCheckResult.currentVersion}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">最新版本</label>
                  <p className="text-sm">{versionCheckResult.latestVersion}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">最低版本</label>
                  <p className="text-sm">{versionCheckResult.minimumVersion}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">平台</label>
                  <p className="text-sm">{versionCheckResult.platform}</p>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">更新消息</label>
                <p className="text-sm bg-gray-50 p-3 rounded">{versionCheckResult.updateMessage}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">下载链接</label>
                <a 
                  href={versionCheckResult.downloadUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:text-blue-800 break-all"
                >
                  {versionCheckResult.downloadUrl}
                </a>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">版本状态</label>
                <div className="text-sm space-y-1">
                  <p>是否最新版本: {versionCheckResult.versionInfo.isLatest ? '是' : '否'}</p>
                  <p>是否低于最低版本: {versionCheckResult.versionInfo.isBelowMinimum ? '是' : '否'}</p>
                  <p>是否有新版本: {versionCheckResult.versionInfo.hasNewVersion ? '是' : '否'}</p>
                </div>
              </div>
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button
            variant="outline"
            onClick={() => setTestModalOpen(false)}
          >
            关闭
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default VersionControl;
