import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Modal, { ModalBody, ModalFooter } from '@/components/common/Modal';
import Loading from '@/components/common/Loading';
import { useApi } from '@/hooks/useApi';
import { systemService } from '@/services/system';
import { SystemConfig, ConfigUpdateData } from '@/types/system';

const ConfigManagement: React.FC = () => {
  const [config, setConfig] = useState<SystemConfig | null>(null);
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [resetModalOpen, setResetModalOpen] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<ConfigUpdateData>();

  // API调用
  const {
    loading,
    execute: fetchConfig,
  } = useApi(systemService.getConfig);

  const {
    loading: saving,
    execute: updateConfig,
  } = useApi(systemService.updateConfig);

  const {
    loading: resetting,
    execute: resetConfig,
  } = useApi(systemService.resetConfig);

  // 加载配置
  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      const configData = await fetchConfig();
      setConfig(configData);
      // 填充表单
      setValue('privacyPolicy', configData.privacyPolicy);
      setValue('termsOfService', configData.termsOfService);
      setValue('appVersion', configData.appVersion);
      setValue('supportEmail', configData.supportEmail);
      setValue('compressRate', configData.compressRate);
      // 版本控制相关字段
      setValue('latestVersion', configData.latestVersion);
      setValue('minimumVersion', configData.minimumVersion);
      setValue('forceUpdate', configData.forceUpdate);
      setValue('updateType', configData.updateType);
      setValue('versionCheckEnabled', configData.versionCheckEnabled);
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  };

  // 保存配置
  const onSubmit = async (data: ConfigUpdateData) => {
    try {
      const updatedConfig = await updateConfig(data);
      setConfig(updatedConfig);
      alert('配置保存成功！');
    } catch (error) {
      console.error('保存配置失败:', error);
    }
  };

  // 重置配置
  const handleReset = async () => {
    try {
      const resetConfigData = await resetConfig();
      setConfig(resetConfigData);
      reset();
      setResetModalOpen(false);
      alert('配置重置成功！');
      loadConfig(); // 重新加载配置
    } catch (error) {
      console.error('重置配置失败:', error);
    }
  };

  // 导出配置
  const handleExport = async (format: 'json' | 'file', includeSecrets: boolean) => {
    try {
      const blob = await systemService.exportConfig({ format, includeSecrets });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `config-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      setExportModalOpen(false);
    } catch (error) {
      console.error('导出配置失败:', error);
    }
  };

  if (loading && !config) {
    return <Loading size="lg" text="加载配置中..." />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">系统配置管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            管理系统的全局配置参数
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={() => setExportModalOpen(true)}
          >
            导出配置
          </Button>
          <Button
            variant="secondary"
            onClick={() => setImportModalOpen(true)}
          >
            导入配置
          </Button>
          <Button
            variant="danger"
            onClick={() => setResetModalOpen(true)}
          >
            重置配置
          </Button>
        </div>
      </div>

      {/* 配置表单 */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* 基础配置 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">基础配置</h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="应用版本"
                {...register('appVersion', {
                  required: '请输入应用版本',
                  pattern: {
                    value: /^\d+\.\d+\.\d+$/,
                    message: '版本格式应为 x.y.z',
                  },
                })}
                error={errors.appVersion?.message}
                fullWidth
              />
              
              <Input
                label="支持邮箱"
                type="email"
                {...register('supportEmail', {
                  required: '请输入支持邮箱',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: '请输入有效的邮箱地址',
                  },
                })}
                error={errors.supportEmail?.message}
                fullWidth
              />

              <Input
                label="压缩率"
                type="number"
                step="0.01"
                min="0"
                max="1"
                {...register('compressRate', {
                  required: '请输入压缩率',
                  min: {
                    value: 0,
                    message: '压缩率不能小于0',
                  },
                  max: {
                    value: 1,
                    message: '压缩率不能大于1',
                  },
                })}
                error={errors.compressRate?.message}
                fullWidth
              />
            </div>
          </div>
        </div>

        {/* 版本控制配置 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">版本控制配置</h3>
            <p className="mt-1 text-sm text-gray-600">
              配置应用版本控制和更新策略
              <a
                href="/admin/system/version-control"
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                详细配置 →
              </a>
            </p>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="最新版本号"
                {...register('latestVersion', {
                  pattern: {
                    value: /^\d+\.\d+\.\d+$/,
                    message: '版本号格式应为 x.y.z',
                  },
                })}
                error={errors.latestVersion?.message}
                placeholder="例如：1.2.0"
                fullWidth
              />

              <Input
                label="最低支持版本号"
                {...register('minimumVersion', {
                  pattern: {
                    value: /^\d+\.\d+\.\d+$/,
                    message: '版本号格式应为 x.y.z',
                  },
                })}
                error={errors.minimumVersion?.message}
                placeholder="例如：1.0.0"
                fullWidth
              />
            </div>

            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  更新类型
                </label>
                <select
                  {...register('updateType')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="optional">可选更新</option>
                  <option value="force">强制更新</option>
                </select>
              </div>

              <div className="flex items-center space-x-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('versionCheckEnabled')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    启用版本检测
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    {...register('forceUpdate')}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    强制更新
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 法律文档 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">法律文档</h3>
          </div>
          <div className="px-6 py-4 space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                隐私政策
              </label>
              <textarea
                {...register('privacyPolicy')}
                rows={8}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="请输入隐私政策内容..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                服务条款
              </label>
              <textarea
                {...register('termsOfService')}
                rows={8}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="请输入服务条款内容..."
              />
            </div>
          </div>
        </div>

        {/* 功能开关 */}
        {config?.featureFlags && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">功能开关</h3>
            </div>
            <div className="px-6 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(config.featureFlags).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-700">
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </label>
                    <input
                      type="checkbox"
                      defaultChecked={value}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 提交按钮 */}
        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            variant="secondary"
            onClick={() => reset()}
          >
            重置表单
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={saving}
          >
            保存配置
          </Button>
        </div>
      </form>

      {/* 导出配置弹窗 */}
      <Modal
        isOpen={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
        title="导出配置"
        size="sm"
      >
        <ModalBody>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              选择导出格式和选项：
            </p>
            <div className="space-y-3">
              <Button
                variant="primary"
                fullWidth
                onClick={() => handleExport('json', false)}
              >
                导出为 JSON（不含敏感信息）
              </Button>
              <Button
                variant="secondary"
                fullWidth
                onClick={() => handleExport('json', true)}
              >
                导出为 JSON（含敏感信息）
              </Button>
              <Button
                variant="secondary"
                fullWidth
                onClick={() => handleExport('file', false)}
              >
                导出为配置文件
              </Button>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setExportModalOpen(false)}
          >
            取消
          </Button>
        </ModalFooter>
      </Modal>

      {/* 重置配置确认弹窗 */}
      <Modal
        isOpen={resetModalOpen}
        onClose={() => setResetModalOpen(false)}
        title="确认重置"
        size="sm"
      >
        <ModalBody>
          <p className="text-sm text-gray-500">
            确定要重置所有配置到默认值吗？此操作不可撤销。
          </p>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setResetModalOpen(false)}
          >
            取消
          </Button>
          <Button
            variant="danger"
            loading={resetting}
            onClick={handleReset}
          >
            确认重置
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default ConfigManagement;
