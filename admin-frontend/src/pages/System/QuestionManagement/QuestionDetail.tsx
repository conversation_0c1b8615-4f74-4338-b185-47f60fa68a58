import React, { useState } from 'react';
import Button from '@/components/common/Button';
import Badge from '@/components/common/Badge';
import { ModalBody, ModalFooter } from '@/components/common/Modal';
import { Question } from '@/types/system';
import { PencilIcon, DocumentDuplicateIcon } from '@heroicons/react/24/outline';

interface QuestionDetailProps {
  question: Question;
  onEdit: () => void;
  onClose: () => void;
}

const QuestionDetail: React.FC<QuestionDetailProps> = ({
  question,
  onEdit,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<'zh_CN' | 'en'>('zh_CN');

  // 复制到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert('已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('已复制到剪贴板');
    }
  };

  // 获取当前语言的内容
  const getCurrentContent = () => {
    return {
      sketch: question.sketch[activeTab] || '',
      content: question.content[activeTab] || '',
      question: question.question[activeTab] || '',
    };
  };

  const currentContent = getCurrentContent();

  // 检查是否有内容
  const hasContent = (lang: 'zh_CN' | 'en') => {
    return question.sketch[lang] || question.content[lang] || question.question[lang];
  };

  return (
    <>
      <ModalBody>
        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">基本信息</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">问题ID：</span>
                <span className="text-gray-900 font-mono">{question._id}</span>
              </div>
              <div>
                <span className="text-gray-500">支持语言：</span>
                <div className="flex space-x-1 mt-1">
                  {hasContent('zh_CN') && (
                    <Badge variant="success" size="sm">中文</Badge>
                  )}
                  {hasContent('en') && (
                    <Badge variant="info" size="sm">英文</Badge>
                  )}
                </div>
              </div>
              {question.createdAt && (
                <div>
                  <span className="text-gray-500">创建时间：</span>
                  <span className="text-gray-900">
                    {new Date(question.createdAt).toLocaleString('zh-CN')}
                  </span>
                </div>
              )}
              {question.updatedAt && (
                <div>
                  <span className="text-gray-500">更新时间：</span>
                  <span className="text-gray-900">
                    {new Date(question.updatedAt).toLocaleString('zh-CN')}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* 语言切换标签 */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {hasContent('zh_CN') && (
                <button
                  type="button"
                  onClick={() => setActiveTab('zh_CN')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'zh_CN'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  中文内容
                </button>
              )}
              {hasContent('en') && (
                <button
                  type="button"
                  onClick={() => setActiveTab('en')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'en'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  英文内容
                </button>
              )}
            </nav>
          </div>

          {/* 内容展示 */}
          <div className="space-y-6">
            {/* 问题概要 */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-gray-900">问题概要</h4>
                {currentContent.sketch && (
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => copyToClipboard(currentContent.sketch)}
                    icon={<DocumentDuplicateIcon className="h-3 w-3" />}
                  >
                    复制
                  </Button>
                )}
              </div>
              <div className="bg-white border border-gray-200 rounded-md p-3">
                {currentContent.sketch ? (
                  <p className="text-sm text-gray-900">{currentContent.sketch}</p>
                ) : (
                  <p className="text-sm text-gray-500 italic">暂无{activeTab === 'zh_CN' ? '中文' : '英文'}概要</p>
                )}
              </div>
            </div>

            {/* 问题内容 */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-gray-900">问题内容</h4>
                {currentContent.content && (
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => copyToClipboard(currentContent.content)}
                    icon={<DocumentDuplicateIcon className="h-3 w-3" />}
                  >
                    复制
                  </Button>
                )}
              </div>
              <div className="bg-white border border-gray-200 rounded-md p-3">
                {currentContent.content ? (
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">{currentContent.content}</p>
                ) : (
                  <p className="text-sm text-gray-500 italic">暂无{activeTab === 'zh_CN' ? '中文' : '英文'}内容</p>
                )}
              </div>
            </div>

            {/* 问题模板 */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-gray-900">问题模板</h4>
                {currentContent.question && (
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => copyToClipboard(currentContent.question)}
                    icon={<DocumentDuplicateIcon className="h-3 w-3" />}
                  >
                    复制
                  </Button>
                )}
              </div>
              <div className="bg-white border border-gray-200 rounded-md p-3">
                {currentContent.question ? (
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">{currentContent.question}</p>
                ) : (
                  <p className="text-sm text-gray-500 italic">暂无{activeTab === 'zh_CN' ? '中文' : '英文'}模板</p>
                )}
              </div>
            </div>
          </div>

          {/* 完整内容复制 */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="text-sm font-medium text-blue-900">完整内容</h4>
                <p className="text-sm text-blue-700 mt-1">
                  复制当前语言的所有内容到剪贴板
                </p>
              </div>
              <Button
                size="sm"
                variant="primary"
                onClick={() => {
                  const fullContent = [
                    `概要：${currentContent.sketch}`,
                    `内容：${currentContent.content}`,
                    `模板：${currentContent.question}`,
                  ].join('\n\n');
                  copyToClipboard(fullContent);
                }}
                icon={<DocumentDuplicateIcon className="h-3 w-3" />}
              >
                复制全部
              </Button>
            </div>
          </div>

          {/* 使用说明 */}
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-2">使用说明</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p>• <strong>问题概要</strong>：用于列表展示和快速识别</p>
              <p>• <strong>问题内容</strong>：详细的问题描述和背景信息</p>
              <p>• <strong>问题模板</strong>：实际使用时的具体问题格式</p>
              <p>• 点击"复制"按钮可以将内容复制到剪贴板</p>
            </div>
          </div>
        </div>
      </ModalBody>

      <ModalFooter>
        <Button
          variant="secondary"
          onClick={onClose}
        >
          关闭
        </Button>
        <Button
          variant="primary"
          onClick={onEdit}
          icon={<PencilIcon className="h-4 w-4" />}
        >
          编辑问题
        </Button>
      </ModalFooter>
    </>
  );
};

export default QuestionDetail;
