import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import { ModalBody, ModalFooter } from '@/components/common/Modal';
import { useApi } from '@/hooks/useApi';
import { systemService } from '@/services/system';
import { Question, QuestionFormData } from '@/types/system';

interface QuestionFormProps {
  question?: Question | null;
  onSuccess: () => void;
  onCancel: () => void;
}

interface FormData {
  sketch_zh_CN: string;
  sketch_en: string;
  content_zh_CN: string;
  content_en: string;
  question_zh_CN: string;
  question_en: string;
}

const QuestionForm: React.FC<QuestionFormProps> = ({
  question,
  onSuccess,
  onCancel,
}) => {
  const [activeTab, setActiveTab] = useState<'zh_CN' | 'en'>('zh_CN');

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<FormData>();

  // API调用
  const {
    loading: creating,
    execute: createQuestion,
  } = useApi(systemService.createQuestion);

  const {
    loading: updating,
    execute: updateQuestion,
  } = useApi(systemService.updateQuestion);

  const loading = creating || updating;

  // 初始化表单数据
  useEffect(() => {
    if (question) {
      setValue('sketch_zh_CN', question.sketch.zh_CN || '');
      setValue('sketch_en', question.sketch.en || '');
      setValue('content_zh_CN', question.content.zh_CN || '');
      setValue('content_en', question.content.en || '');
      setValue('question_zh_CN', question.question.zh_CN || '');
      setValue('question_en', question.question.en || '');
    } else {
      reset();
    }
  }, [question, setValue, reset]);

  // 表单提交
  const onSubmit = async (data: FormData) => {
    try {
      const formData: QuestionFormData = {
        sketch: {
          zh_CN: data.sketch_zh_CN,
          en: data.sketch_en,
        },
        content: {
          zh_CN: data.content_zh_CN,
          en: data.content_en,
        },
        question: {
          zh_CN: data.question_zh_CN,
          en: data.question_en,
        },
      };

      if (question) {
        await updateQuestion(question._id, formData);
        alert('更新成功！');
      } else {
        await createQuestion(formData);
        alert('创建成功！');
      }

      onSuccess();
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  // 监听表单变化，用于实时预览
  const watchedValues = watch();

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <ModalBody>
        <div className="space-y-6">
          {/* 语言切换标签 */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                type="button"
                onClick={() => setActiveTab('zh_CN')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'zh_CN'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                中文内容
              </button>
              <button
                type="button"
                onClick={() => setActiveTab('en')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'en'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                英文内容
              </button>
            </nav>
          </div>

          {/* 中文内容表单 */}
          {activeTab === 'zh_CN' && (
            <div className="space-y-4">
              <Input
                label="问题概要（中文）"
                {...register('sketch_zh_CN', {
                  required: '请输入中文问题概要',
                  maxLength: {
                    value: 200,
                    message: '概要不能超过200个字符',
                  },
                })}
                error={errors.sketch_zh_CN?.message}
                placeholder="请输入问题的简短概要..."
                fullWidth
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  问题内容（中文） *
                </label>
                <textarea
                  {...register('content_zh_CN', {
                    required: '请输入中文问题内容',
                    maxLength: {
                      value: 2000,
                      message: '内容不能超过2000个字符',
                    },
                  })}
                  rows={6}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="请输入详细的问题内容..."
                />
                {errors.content_zh_CN && (
                  <p className="mt-1 text-sm text-red-600">{errors.content_zh_CN.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  问题模板（中文） *
                </label>
                <textarea
                  {...register('question_zh_CN', {
                    required: '请输入中文问题模板',
                    maxLength: {
                      value: 1000,
                      message: '问题模板不能超过1000个字符',
                    },
                  })}
                  rows={4}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="请输入问题的具体模板..."
                />
                {errors.question_zh_CN && (
                  <p className="mt-1 text-sm text-red-600">{errors.question_zh_CN.message}</p>
                )}
              </div>
            </div>
          )}

          {/* 英文内容表单 */}
          {activeTab === 'en' && (
            <div className="space-y-4">
              <Input
                label="问题概要（英文）"
                {...register('sketch_en', {
                  maxLength: {
                    value: 200,
                    message: '概要不能超过200个字符',
                  },
                })}
                error={errors.sketch_en?.message}
                placeholder="Please enter a brief summary of the question..."
                fullWidth
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  问题内容（英文）
                </label>
                <textarea
                  {...register('content_en', {
                    maxLength: {
                      value: 2000,
                      message: '内容不能超过2000个字符',
                    },
                  })}
                  rows={6}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Please enter detailed question content..."
                />
                {errors.content_en && (
                  <p className="mt-1 text-sm text-red-600">{errors.content_en.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  问题模板（英文）
                </label>
                <textarea
                  {...register('question_en', {
                    maxLength: {
                      value: 1000,
                      message: '问题模板不能超过1000个字符',
                    },
                  })}
                  rows={4}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Please enter the specific question template..."
                />
                {errors.question_en && (
                  <p className="mt-1 text-sm text-red-600">{errors.question_en.message}</p>
                )}
              </div>
            </div>
          )}

          {/* 实时预览 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">预览</h4>
            <div className="space-y-3">
              <div>
                <span className="text-xs font-medium text-gray-500">概要：</span>
                <p className="text-sm text-gray-900 mt-1">
                  {activeTab === 'zh_CN' 
                    ? watchedValues.sketch_zh_CN || '暂无内容'
                    : watchedValues.sketch_en || '暂无内容'
                  }
                </p>
              </div>
              <div>
                <span className="text-xs font-medium text-gray-500">内容：</span>
                <p className="text-sm text-gray-900 mt-1 whitespace-pre-wrap">
                  {activeTab === 'zh_CN' 
                    ? watchedValues.content_zh_CN || '暂无内容'
                    : watchedValues.content_en || '暂无内容'
                  }
                </p>
              </div>
              <div>
                <span className="text-xs font-medium text-gray-500">问题模板：</span>
                <p className="text-sm text-gray-900 mt-1 whitespace-pre-wrap">
                  {activeTab === 'zh_CN' 
                    ? watchedValues.question_zh_CN || '暂无内容'
                    : watchedValues.question_en || '暂无内容'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* 提示信息 */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  填写提示
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc pl-5 space-y-1">
                    <li>中文内容为必填项，英文内容为可选项</li>
                    <li>问题概要用于列表展示，建议简洁明了</li>
                    <li>问题内容是详细描述，支持换行</li>
                    <li>问题模板是具体的问题格式，用于实际应用</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ModalBody>

      <ModalFooter>
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
        >
          取消
        </Button>
        <Button
          type="submit"
          variant="primary"
          loading={loading}
        >
          {question ? '更新' : '创建'}
        </Button>
      </ModalFooter>
    </form>
  );
};

export default QuestionForm;
