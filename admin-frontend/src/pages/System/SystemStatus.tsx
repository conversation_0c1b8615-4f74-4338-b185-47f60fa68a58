import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import Button from '@/components/common/Button';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';
import { useApi } from '@/hooks/useApi';
import { systemService } from '@/services/system';
import { SystemStatus, DatabaseStatus, SystemMonitorData } from '@/types/system';

const SystemStatusPage: React.FC = () => {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [dbStatus, setDbStatus] = useState<DatabaseStatus | null>(null);
  const [monitorData, setMonitorData] = useState<SystemMonitorData | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API调用
  const {
    loading: statusLoading,
    execute: fetchStatus,
  } = useApi(systemService.getSystemStatus);

  const {
    loading: dbLoading,
    execute: fetchDbStatus,
  } = useApi(systemService.getDatabaseStatus);

  const {
    loading: monitorLoading,
    execute: fetchMonitorData,
  } = useApi(systemService.getSystemMonitorData);

  // 加载所有状态数据
  const loadAllData = async () => {
    try {
      setError(null); // 清除之前的错误
      const [statusData, dbData, monitorDataResult] = await Promise.all([
        fetchStatus(),
        fetchDbStatus(),
        fetchMonitorData(),
      ]);
      setStatus(statusData);
      setDbStatus(dbData);
      setMonitorData(monitorDataResult);
    } catch (error) {
      console.error('加载状态数据失败:', error);
      const errorMessage = error instanceof Error ? error.message : '加载系统状态失败';
      setError(errorMessage);

      // 如果是认证错误，不需要额外处理，API拦截器会处理
      if (errorMessage.includes('认证失败')) {
        return;
      }
    }
  };

  // 初始加载
  useEffect(() => {
    loadAllData();
  }, []);

  // 自动刷新
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      loadAllData();
    }, 30000); // 30秒刷新一次

    return () => clearInterval(interval);
  }, [autoRefresh]);

  // 格式化运行时间
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  // 格式化文件大小
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
        return 'success';
      case 'warning':
      case 'unhealthy':
        return 'warning';
      case 'error':
      case 'disconnected':
        return 'danger';
      default:
        return 'default';
    }
  };

  // 获取使用率颜色
  const getUsageColor = (percentage: number) => {
    if (percentage < 60) return 'text-green-600';
    if (percentage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const loading = statusLoading || dbLoading || monitorLoading;

  if (loading && !status) {
    return <Loading size="lg" text="加载系统状态中..." />;
  }

  return (
    <div className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                系统状态加载失败
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <div className="-mx-2 -my-1.5 flex">
                  <button
                    type="button"
                    onClick={() => {
                      setError(null);
                      loadAllData();
                    }}
                    className="bg-red-50 px-2 py-1.5 rounded-md text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600"
                  >
                    重试
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">系统状态监控</h1>
          <p className="mt-1 text-sm text-gray-600">
            实时监控系统运行状态和性能指标
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant={autoRefresh ? 'primary' : 'secondary'}
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
          </Button>
          <Button
            variant="secondary"
            onClick={loadAllData}
            loading={loading}
          >
            手动刷新
          </Button>
        </div>
      </div>

      {/* 系统概览 */}
      {status && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">系统概览</h3>
              <div className="flex items-center space-x-2">
                <Badge variant={getStatusColor(status.status)}>
                  {status.status === 'healthy' ? '健康' : status.status === 'warning' ? '警告' : '错误'}
                </Badge>
                <span className="text-sm text-gray-500">
                  更新时间: {format(new Date(status.timestamp), 'HH:mm:ss')}
                </span>
              </div>
            </div>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <p className="text-sm font-medium text-gray-500">系统版本</p>
                <p className="text-lg font-semibold text-gray-900">{status.version}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">运行环境</p>
                <p className="text-lg font-semibold text-gray-900">{status.environment}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">运行时间</p>
                <p className="text-lg font-semibold text-gray-900">{formatUptime(status.uptime)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">最后更新</p>
                <p className="text-lg font-semibold text-gray-900">
                  {format(new Date(status.timestamp), 'MM-dd HH:mm')}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 服务状态 */}
      {status && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">服务状态</h3>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-4">
              {/* 数据库状态 */}
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium text-gray-900">数据库</p>
                  <p className="text-xs text-gray-500">
                    响应时间: {status.services.database.responseTime || 0}ms
                  </p>
                </div>
                <Badge variant={getStatusColor(status.services.database.status)}>
                  {status.services.database.status === 'connected' ? '已连接' : '未连接'}
                </Badge>
              </div>

              {/* Redis状态 */}
              {status.services.redis && (
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Redis</p>
                    <p className="text-xs text-gray-500">
                      响应时间: {status.services.redis.responseTime || 0}ms
                    </p>
                  </div>
                  <Badge variant={getStatusColor(status.services.redis.status)}>
                    {status.services.redis.status === 'connected' ? '已连接' : '未连接'}
                  </Badge>
                </div>
              )}

              {/* 外部服务状态 */}
              {status.services.external && status.services.external.map((service, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{service.name}</p>
                    <p className="text-xs text-gray-500">
                      响应时间: {service.responseTime || 0}ms
                    </p>
                  </div>
                  <Badge variant={getStatusColor(service.status)}>
                    {service.status === 'healthy' ? '健康' : service.status === 'unhealthy' ? '不健康' : '未知'}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 资源使用情况 */}
      {status && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">资源使用情况</h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* 内存使用 */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm font-medium text-gray-900">内存使用</p>
                  <p className={`text-sm font-medium ${getUsageColor(status.resources.memory.percentage)}`}>
                    {status.resources.memory.percentage.toFixed(1)}%
                  </p>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      status.resources.memory.percentage < 60
                        ? 'bg-green-500'
                        : status.resources.memory.percentage < 80
                        ? 'bg-yellow-500'
                        : 'bg-red-500'
                    }`}
                    style={{ width: `${status.resources.memory.percentage}%` }}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {formatBytes(status.resources.memory.used)} / {formatBytes(status.resources.memory.total)}
                </p>
              </div>

              {/* CPU使用 */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm font-medium text-gray-900">CPU使用</p>
                  <p className={`text-sm font-medium ${getUsageColor(status.resources.cpu.usage)}`}>
                    {status.resources.cpu.usage.toFixed(1)}%
                  </p>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      status.resources.cpu.usage < 60
                        ? 'bg-green-500'
                        : status.resources.cpu.usage < 80
                        ? 'bg-yellow-500'
                        : 'bg-red-500'
                    }`}
                    style={{ width: `${status.resources.cpu.usage}%` }}
                  />
                </div>
              </div>

              {/* 磁盘使用 */}
              {status.resources.disk && (
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <p className="text-sm font-medium text-gray-900">磁盘使用</p>
                    <p className={`text-sm font-medium ${getUsageColor(status.resources.disk.percentage)}`}>
                      {status.resources.disk.percentage.toFixed(1)}%
                    </p>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        status.resources.disk.percentage < 60
                          ? 'bg-green-500'
                          : status.resources.disk.percentage < 80
                          ? 'bg-yellow-500'
                          : 'bg-red-500'
                      }`}
                      style={{ width: `${status.resources.disk.percentage}%` }}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {formatBytes(status.resources.disk.used)} / {formatBytes(status.resources.disk.total)}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 数据库详细状态 */}
      {dbStatus && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">数据库详细状态</h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">连接信息</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">连接状态</span>
                    <Badge variant={getStatusColor(dbStatus.status)}>
                      {dbStatus.status === 'connected' ? '已连接' : '未连接'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">连接数</span>
                    <span className="text-sm font-medium text-gray-900">{dbStatus.connectionCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">活跃连接</span>
                    <span className="text-sm font-medium text-gray-900">{dbStatus.performance.activeConnections}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">性能指标</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">平均响应时间</span>
                    <span className="text-sm font-medium text-gray-900">
                      {dbStatus.performance.avgResponseTime.toFixed(2)}ms
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">慢查询数</span>
                    <span className="text-sm font-medium text-gray-900">{dbStatus.performance.slowQueries}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 集合信息 */}
            {dbStatus.collections && dbStatus.collections.length > 0 && (
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">集合信息</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          集合名称
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          文档数量
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          大小
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          索引数
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {dbStatus.collections.slice(0, 5).map((collection, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {collection.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {collection.documentCount.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatBytes(collection.size)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {collection.indexes}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 实时监控数据 */}
      {monitorData && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">实时监控</h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">{monitorData.metrics.requestsPerMinute}</p>
                <p className="text-sm text-gray-500">请求/分钟</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">{monitorData.metrics.errorsPerMinute}</p>
                <p className="text-sm text-gray-500">错误/分钟</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{monitorData.metrics.avgResponseTime.toFixed(0)}ms</p>
                <p className="text-sm text-gray-500">平均响应时间</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">{monitorData.metrics.activeUsers}</p>
                <p className="text-sm text-gray-500">活跃用户</p>
              </div>
            </div>

            {/* 告警信息 */}
            {monitorData.alerts && monitorData.alerts.length > 0 && (
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">系统告警</h4>
                <div className="space-y-2">
                  {monitorData.alerts.slice(0, 5).map((alert, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                      <div className="flex items-center space-x-3">
                        <Badge variant={alert.level === 'critical' ? 'danger' : alert.level === 'error' ? 'danger' : alert.level === 'warning' ? 'warning' : 'info'}>
                          {alert.level.toUpperCase()}
                        </Badge>
                        <span className="text-sm text-gray-900">{alert.message}</span>
                      </div>
                      <span className="text-xs text-gray-500">
                        {format(new Date(alert.timestamp), 'HH:mm:ss')}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemStatusPage;
