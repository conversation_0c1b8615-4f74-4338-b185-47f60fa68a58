import React from 'react';
import { Routes, Route } from 'react-router-dom';
import ConfigManagement from './ConfigManagement';
import LogManagement from './LogManagement';
import SystemStatus from './SystemStatus';
import VersionControl from './VersionControl';
import AIConfig from './AIConfig';
import QuestionManagement from './QuestionManagement';
import TwoFactorAuth from './TwoFactorAuth';

const System: React.FC = () => {
  return (
    <Routes>
      <Route index element={<SystemStatus />} />
      <Route path="status" element={<SystemStatus />} />
      <Route path="config" element={<ConfigManagement />} />
      <Route path="ai-config" element={<AIConfig />} />
      <Route path="version-control" element={<VersionControl />} />
      <Route path="logs" element={<LogManagement />} />
      <Route path="questions" element={<QuestionManagement />} />
      <Route path="2fa" element={<TwoFactorAuth />} />
    </Routes>
  );
};

export default System;
