import React, { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Badge from '@/components/common/Badge';
import Pagination from '@/components/common/Pagination';
import Modal, { ModalBody, ModalFooter } from '@/components/common/Modal';
import { useTable } from '@/hooks/useTable';
import { useApi } from '@/hooks/useApi';
import { systemService } from '@/services/system';
import { RequestLog, ResponseLog, ErrorLog, LogListParams } from '@/types/system';
import { TableColumn } from '@/types/common';

type LogType = 'request' | 'response' | 'error';

const LogManagement: React.FC = () => {
  const [logs, setLogs] = useState<(RequestLog | ResponseLog | ErrorLog)[]>([]);
  const [total, setTotal] = useState(0);
  const [logType, setLogType] = useState<LogType>('request');
  const [cleanupModalOpen, setCleanupModalOpen] = useState(false);
  const [filters, setFilters] = useState({
    url: '',
    method: '',
    status: '',
    dateFrom: '',
    dateTo: '',
  });

  // 表格状态管理
  const {
    tableState,
    pagination,
    sorting,
    searching,
    getQueryParams,
  } = useTable({
    initialPageSize: 20,
    initialSortBy: logType === 'request' ? 'requestTime' : logType === 'response' ? 'responseTime' : 'timestamp',
    initialSortOrder: 'desc',
  });

  // API调用
  const {
    loading,
    execute: fetchLogs,
  } = useApi(
    logType === 'request' 
      ? systemService.getRequestLogs 
      : logType === 'response' 
        ? systemService.getResponseLogs 
        : systemService.getErrorLogs
  );

  const {
    loading: cleaning,
    execute: cleanupLogs,
  } = useApi(systemService.cleanupLogs);

  // 加载日志列表
  const loadLogs = useCallback(async () => {
    try {
      const params: LogListParams = {
        ...getQueryParams(),
        ...filters,
      };
      // 清理空值
      Object.keys(params).forEach(key => {
        if (params[key as keyof LogListParams] === '') {
          delete params[key as keyof LogListParams];
        }
      });
      
      const result = await fetchLogs(params);
      setLogs(result.items);
      setTotal(result.total);
    } catch (error) {
      console.error('加载日志失败:', error);
    }
  }, [fetchLogs, getQueryParams, filters, logType]);

  // 初始加载和参数变化时重新加载
  useEffect(() => {
    loadLogs();
  }, [loadLogs]);

  // 筛选器变化处理
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 日志类型变化
  const handleLogTypeChange = (type: LogType) => {
    setLogType(type);
    setLogs([]);
    setTotal(0);
  };

  // 清理日志
  const handleCleanup = async (daysToKeep: number) => {
    try {
      await cleanupLogs({
        type: logType,
        daysToKeep,
        batchSize: 1000,
      });
      setCleanupModalOpen(false);
      loadLogs(); // 重新加载列表
      alert('日志清理成功！');
    } catch (error) {
      console.error('清理日志失败:', error);
    }
  };

  // 导出日志
  const handleExport = async (format: 'json' | 'csv') => {
    try {
      const blob = await systemService.exportLogs({
        type: logType,
        format,
        ...filters,
        limit: 10000,
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${logType}-logs-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('导出日志失败:', error);
    }
  };

  // 获取状态码颜色
  const getStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return 'success';
    if (status >= 300 && status < 400) return 'info';
    if (status >= 400 && status < 500) return 'warning';
    if (status >= 500) return 'danger';
    return 'default';
  };

  // 请求日志列定义
  const requestColumns: TableColumn<RequestLog>[] = [
    {
      key: 'requestTime',
      title: '请求时间',
      dataIndex: 'requestTime',
      width: 150,
      sortable: true,
      render: (time: string) => (
        <div>
          <p className="text-sm text-gray-900">
            {format(new Date(time), 'yyyy-MM-dd')}
          </p>
          <p className="text-xs text-gray-500">
            {format(new Date(time), 'HH:mm:ss')}
          </p>
        </div>
      ),
    },
    {
      key: 'method',
      title: '方法',
      dataIndex: 'method',
      width: 80,
      render: (method: string) => (
        <Badge variant={method === 'GET' ? 'info' : method === 'POST' ? 'success' : 'warning'}>
          {method}
        </Badge>
      ),
    },
    {
      key: 'url',
      title: 'URL',
      dataIndex: 'url',
      render: (url: string) => (
        <span className="text-sm text-gray-900 font-mono" title={url}>
          {url.length > 50 ? `${url.substring(0, 50)}...` : url}
        </span>
      ),
    },
    {
      key: 'ip',
      title: 'IP地址',
      dataIndex: 'ip',
      width: 120,
      render: (ip: string) => (
        <span className="text-sm text-gray-600 font-mono">{ip || '-'}</span>
      ),
    },
    {
      key: 'userId',
      title: '用户ID',
      dataIndex: 'userId',
      width: 120,
      render: (userId: string) => (
        <span className="text-sm text-gray-600 font-mono">
          {userId ? `${userId.substring(0, 8)}...` : '-'}
        </span>
      ),
    },
  ];

  // 响应日志列定义
  const responseColumns: TableColumn<ResponseLog>[] = [
    {
      key: 'responseTime',
      title: '响应时间',
      dataIndex: 'responseTime',
      width: 150,
      sortable: true,
      render: (time: string) => (
        <div>
          <p className="text-sm text-gray-900">
            {format(new Date(time), 'yyyy-MM-dd')}
          </p>
          <p className="text-xs text-gray-500">
            {format(new Date(time), 'HH:mm:ss')}
          </p>
        </div>
      ),
    },
    {
      key: 'responseStatus',
      title: '状态码',
      dataIndex: 'responseStatus',
      width: 100,
      render: (status: number) => (
        <Badge variant={getStatusColor(status)}>
          {status}
        </Badge>
      ),
    },
    {
      key: 'duration',
      title: '响应时间',
      dataIndex: 'duration',
      width: 100,
      render: (duration: number) => (
        <span className="text-sm text-gray-900">
          {duration ? `${duration}ms` : '-'}
        </span>
      ),
    },
    {
      key: 'requestLogId',
      title: '请求ID',
      dataIndex: 'requestLogId',
      width: 120,
      render: (id: string) => (
        <span className="text-sm text-gray-600 font-mono">
          {id.substring(0, 8)}...
        </span>
      ),
    },
  ];

  // 错误日志列定义
  const errorColumns: TableColumn<ErrorLog>[] = [
    {
      key: 'timestamp',
      title: '错误时间',
      dataIndex: 'timestamp',
      width: 150,
      sortable: true,
      render: (time: string) => (
        <div>
          <p className="text-sm text-gray-900">
            {format(new Date(time), 'yyyy-MM-dd')}
          </p>
          <p className="text-xs text-gray-500">
            {format(new Date(time), 'HH:mm:ss')}
          </p>
        </div>
      ),
    },
    {
      key: 'level',
      title: '级别',
      dataIndex: 'level',
      width: 80,
      render: (level: string = 'error') => (
        <Badge variant={level === 'error' ? 'danger' : level === 'warn' ? 'warning' : 'info'}>
          {level.toUpperCase()}
        </Badge>
      ),
    },
    {
      key: 'errorCode',
      title: '错误码',
      dataIndex: 'errorCode',
      width: 100,
      render: (code: number) => (
        <Badge variant="danger">{code}</Badge>
      ),
    },
    {
      key: 'error',
      title: '错误信息',
      dataIndex: 'error',
      render: (error: string) => (
        <span className="text-sm text-gray-900" title={error}>
          {error.length > 60 ? `${error.substring(0, 60)}...` : error}
        </span>
      ),
    },
    {
      key: 'requestId',
      title: '请求ID',
      dataIndex: 'requestId',
      width: 120,
      render: (id: string) => (
        <span className="text-sm text-gray-600 font-mono">
          {id.substring(0, 8)}...
        </span>
      ),
    },
  ];

  const getColumns = () => {
    switch (logType) {
      case 'request':
        return requestColumns;
      case 'response':
        return responseColumns;
      case 'error':
        return errorColumns;
      default:
        return requestColumns;
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">日志管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看和管理系统运行日志
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={() => handleExport('csv')}
          >
            导出CSV
          </Button>
          <Button
            variant="secondary"
            onClick={() => handleExport('json')}
          >
            导出JSON
          </Button>
          <Button
            variant="danger"
            onClick={() => setCleanupModalOpen(true)}
          >
            清理日志
          </Button>
        </div>
      </div>

      {/* 日志类型选择 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex space-x-4 mb-4">
          <button
            onClick={() => handleLogTypeChange('request')}
            className={`px-4 py-2 text-sm font-medium rounded-md ${
              logType === 'request'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            请求日志
          </button>
          <button
            onClick={() => handleLogTypeChange('response')}
            className={`px-4 py-2 text-sm font-medium rounded-md ${
              logType === 'response'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            响应日志
          </button>
          <button
            onClick={() => handleLogTypeChange('error')}
            className={`px-4 py-2 text-sm font-medium rounded-md ${
              logType === 'error'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            错误日志
          </button>
        </div>

        {/* 筛选条件 */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Input
            placeholder="搜索URL或错误信息..."
            value={searching.search}
            onChange={(e) => searching.onSearch(e.target.value)}
            leftIcon={
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            }
          />

          {logType === 'request' && (
            <>
              <Input
                placeholder="URL路径"
                value={filters.url}
                onChange={(e) => handleFilterChange('url', e.target.value)}
              />
              <select
                value={filters.method}
                onChange={(e) => handleFilterChange('method', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">所有方法</option>
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
                <option value="PATCH">PATCH</option>
              </select>
            </>
          )}

          {logType === 'response' && (
            <Input
              placeholder="状态码"
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            />
          )}

          <Input
            type="date"
            placeholder="开始日期"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
          />

          <Input
            type="date"
            placeholder="结束日期"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
          />
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总日志数</p>
              <p className="text-2xl font-semibold text-gray-900">{total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">今日日志</p>
              <p className="text-2xl font-semibold text-gray-900">
                {logs.filter(log => {
                  const logDate = new Date(
                    logType === 'request' 
                      ? (log as RequestLog).requestTime 
                      : logType === 'response' 
                        ? (log as ResponseLog).responseTime 
                        : (log as ErrorLog).timestamp
                  );
                  const today = new Date();
                  return logDate.toDateString() === today.toDateString();
                }).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">错误日志</p>
              <p className="text-2xl font-semibold text-gray-900">
                {logType === 'error' ? logs.length : '-'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">平均响应时间</p>
              <p className="text-2xl font-semibold text-gray-900">
                {logType === 'response' 
                  ? `${Math.round((logs as ResponseLog[]).reduce((sum, log) => sum + (log.duration || 0), 0) / logs.length) || 0}ms`
                  : '-'
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 日志表格 */}
      <Table
        columns={getColumns()}
        data={logs}
        loading={loading}
        emptyText={`暂无${logType === 'request' ? '请求' : logType === 'response' ? '响应' : '错误'}日志`}
        rowKey="_id"
      />

      {/* 分页 */}
      <Pagination
        current={pagination.current}
        total={total}
        pageSize={pagination.pageSize}
        onChange={pagination.onChange}
        onPageSizeChange={pagination.onPageSizeChange}
        showSizeChanger
        showQuickJumper
        showTotal
      />

      {/* 清理日志弹窗 */}
      <Modal
        isOpen={cleanupModalOpen}
        onClose={() => setCleanupModalOpen(false)}
        title="清理日志"
        size="sm"
      >
        <ModalBody>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              选择要保留的日志天数，超过此时间的日志将被删除：
            </p>
            <div className="space-y-2">
              <Button
                variant="secondary"
                fullWidth
                onClick={() => handleCleanup(7)}
                loading={cleaning}
              >
                保留最近 7 天
              </Button>
              <Button
                variant="secondary"
                fullWidth
                onClick={() => handleCleanup(30)}
                loading={cleaning}
              >
                保留最近 30 天
              </Button>
              <Button
                variant="secondary"
                fullWidth
                onClick={() => handleCleanup(90)}
                loading={cleaning}
              >
                保留最近 90 天
              </Button>
              <Button
                variant="danger"
                fullWidth
                onClick={() => handleCleanup(0)}
                loading={cleaning}
              >
                清空所有日志
              </Button>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setCleanupModalOpen(false)}
          >
            取消
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default LogManagement;
