import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { format, formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Badge from '@/components/common/Badge';
import Pagination from '@/components/common/Pagination';
import { useTable } from '@/hooks/useTable';
import { useApi } from '@/hooks/useApi';
import { chatService } from '@/services/chat';
import { ChatSession, ChatSessionListParams } from '@/types/chat';
import { TableColumn } from '@/types/common';

const SessionList: React.FC = () => {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({
    isArchived: '',
    modelName: '',
    dateFrom: '',
    dateTo: '',
  });

  // 表格状态管理
  const {
    tableState,
    pagination,
    sorting,
    getQueryParams,
  } = useTable({
    initialPageSize: 20,
    initialSortBy: 'lastMessageTime',
    initialSortOrder: 'desc',
  });

  // API调用
  const {
    loading,
    execute: fetchSessions,
  } = useApi(chatService.getSessions);

  // 加载会话列表
  const loadSessions = useCallback(async () => {
    try {
      const params: ChatSessionListParams = {
        ...getQueryParams(),
        ...filters,
      };
      // 清理空值
      Object.keys(params).forEach(key => {
        if (params[key as keyof ChatSessionListParams] === '') {
          delete params[key as keyof ChatSessionListParams];
        }
      });
      
      const result = await fetchSessions(params);
      setSessions(result.items);
      setTotal(result.total);
    } catch (error) {
      console.error('加载会话列表失败:', error);
    }
  }, [fetchSessions, getQueryParams, filters]);

  // 初始加载和参数变化时重新加载
  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  // 筛选器变化处理
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 计算会话持续时间
  const getSessionDuration = (firstTime: string, lastTime: string) => {
    const first = new Date(firstTime);
    const last = new Date(lastTime);
    const diffMinutes = Math.floor((last.getTime() - first.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return '< 1分钟';
    if (diffMinutes < 60) return `${diffMinutes}分钟`;
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}小时${diffMinutes % 60}分钟`;
    return `${Math.floor(diffMinutes / 1440)}天`;
  };

  // 表格列定义
  const columns: TableColumn<ChatSession>[] = [
    {
      key: 'chatId',
      title: '会话ID',
      dataIndex: 'chatId',
      width: 150,
      render: (chatId: string) => (
        <Link 
          to={`/chat/sessions/${chatId}`}
          className="text-primary-600 hover:text-primary-800 font-mono text-sm"
        >
          {chatId.substring(0, 12)}...
        </Link>
      ),
    },
    {
      key: 'title',
      title: '会话标题',
      dataIndex: 'title',
      render: (title: string, record: ChatSession) => (
        <div>
          <p className="text-sm font-medium text-gray-900">
            {title || '未命名会话'}
          </p>
          <p className="text-xs text-gray-500">
            {record.messageCount} 条消息
          </p>
        </div>
      ),
    },
    {
      key: 'modelName',
      title: '使用模型',
      dataIndex: 'modelName',
      width: 120,
      render: (modelName: string = 'unknown') => (
        <Badge variant="info" size="sm">
          {modelName}
        </Badge>
      ),
    },
    {
      key: 'messageCount',
      title: '消息数量',
      dataIndex: 'messageCount',
      width: 100,
      sortable: true,
      render: (count: number) => (
        <span className="text-sm font-medium text-gray-900">
          {count}
        </span>
      ),
    },
    {
      key: 'duration',
      title: '会话时长',
      dataIndex: 'firstMessageTime',
      width: 120,
      render: (firstTime: string, record: ChatSession) => (
        <span className="text-sm text-gray-600">
          {getSessionDuration(firstTime, record.lastMessageTime)}
        </span>
      ),
    },
    {
      key: 'firstMessageTime',
      title: '开始时间',
      dataIndex: 'firstMessageTime',
      width: 150,
      sortable: true,
      render: (time: string) => (
        <div>
          <p className="text-sm text-gray-900">
            {format(new Date(time), 'yyyy-MM-dd')}
          </p>
          <p className="text-xs text-gray-500">
            {format(new Date(time), 'HH:mm:ss')}
          </p>
        </div>
      ),
    },
    {
      key: 'lastMessageTime',
      title: '最后活动',
      dataIndex: 'lastMessageTime',
      width: 150,
      sortable: true,
      render: (time: string) => (
        <div>
          <p className="text-sm text-gray-900">
            {format(new Date(time), 'yyyy-MM-dd HH:mm')}
          </p>
          <p className="text-xs text-gray-500">
            {formatDistanceToNow(new Date(time), { 
              addSuffix: true, 
              locale: zhCN 
            })}
          </p>
        </div>
      ),
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'isArchived',
      width: 100,
      render: (isArchived: boolean = false) => (
        <Badge variant={isArchived ? 'default' : 'success'}>
          {isArchived ? '已归档' : '活跃'}
        </Badge>
      ),
    },
    {
      key: 'actions',
      title: '操作',
      dataIndex: 'chatId',
      width: 150,
      render: (chatId: string, record: ChatSession) => (
        <div className="flex space-x-2">
          <Link to={`/chat/sessions/${chatId}`}>
            <Button size="sm" variant="secondary">
              查看详情
            </Button>
          </Link>
          <Link to={`/chat/messages?chatId=${chatId}`}>
            <Button size="sm" variant="primary">
              查看消息
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">聊天会话管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看和管理所有聊天会话
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary">
            导出数据
          </Button>
          <Link to="/chat/stats">
            <Button variant="primary">
              查看统计
            </Button>
          </Link>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Input
            placeholder="搜索会话ID或标题..."
            // 这里可以添加搜索功能
          />

          <select
            value={filters.modelName}
            onChange={(e) => handleFilterChange('modelName', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="">所有模型</option>
            <option value="gpt-4">GPT-4</option>
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
            <option value="claude-3">Claude-3</option>
          </select>

          <select
            value={filters.isArchived}
            onChange={(e) => handleFilterChange('isArchived', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="">所有状态</option>
            <option value="false">活跃</option>
            <option value="true">已归档</option>
          </select>

          <Input
            type="date"
            placeholder="开始日期"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
          />

          <Input
            type="date"
            placeholder="结束日期"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
          />
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">总会话数</p>
              <p className="text-2xl font-semibold text-gray-900">{total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">活跃会话</p>
              <p className="text-2xl font-semibold text-gray-900">
                {sessions.filter(s => !s.isArchived).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">平均消息数</p>
              <p className="text-2xl font-semibold text-gray-900">
                {sessions.length > 0 
                  ? Math.round(sessions.reduce((sum, s) => sum + s.messageCount, 0) / sessions.length)
                  : 0
                }
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">已归档</p>
              <p className="text-2xl font-semibold text-gray-900">
                {sessions.filter(s => s.isArchived).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 会话表格 */}
      <Table
        columns={columns}
        data={sessions}
        loading={loading}
        emptyText="暂无聊天会话"
        rowKey="chatId"
      />

      {/* 分页 */}
      <Pagination
        current={pagination.current}
        total={total}
        pageSize={pagination.pageSize}
        onChange={pagination.onChange}
        onPageSizeChange={pagination.onPageSizeChange}
        showSizeChanger
        showQuickJumper
        showTotal
      />
    </div>
  );
};

export default SessionList;
