import React from 'react';
import { 
  ChevronRightIcon, 
  ChevronDownIcon, 
  ChatBubbleLeftIcon,
  ClockIcon,
  CpuChipIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';
import { SessionGroup } from '@/types/chat';
import { format } from 'date-fns';
import MessageItemComponent from './MessageItem';

interface SessionGroupProps {
  sessionGroup: SessionGroup;
  onToggleSession: () => void;
  searchKeyword?: string;
}

const SessionGroupComponent: React.FC<SessionGroupProps> = ({
  sessionGroup,
  onToggleSession,
  searchKeyword = ''
}) => {
  const { session, messages, isExpanded, isLoading } = sessionGroup;

  // 高亮搜索关键词
  const highlightText = (text: string, keyword: string) => {
    if (!keyword) return text;
    
    const regex = new RegExp(`(${keyword})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  // 获取会话状态
  const getSessionStatus = () => {
    if (session.isArchived) {
      return { text: '已归档', variant: 'secondary' as const };
    }
    return { text: '活跃', variant: 'success' as const };
  };

  // 计算会话持续时间
  const getSessionDuration = () => {
    const start = new Date(session.firstMessageTime);
    const end = new Date(session.lastMessageTime);
    const diffMs = end.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 60) {
      return `${diffMins} 分钟`;
    } else if (diffMins < 1440) {
      return `${Math.floor(diffMins / 60)} 小时`;
    } else {
      return `${Math.floor(diffMins / 1440)} 天`;
    }
  };

  const sessionStatus = getSessionStatus();

  return (
    <div className="border-l-4 border-l-green-400 ml-6">
      {/* 会话级别 */}
      <div 
        className="p-4 hover:bg-white cursor-pointer transition-colors duration-200"
        onClick={onToggleSession}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* 展开/折叠图标 */}
            <div className="flex-shrink-0">
              {isLoading ? (
                <div className="w-4 h-4 flex items-center justify-center">
                  <div className="w-2 h-2 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : isExpanded ? (
                <ChevronDownIcon className="h-4 w-4 text-gray-400" />
              ) : (
                <ChevronRightIcon className="h-4 w-4 text-gray-400" />
              )}
            </div>

            {/* 会话图标 */}
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-lg bg-green-100 flex items-center justify-center">
                <ChatBubbleLeftIcon className="h-5 w-5 text-green-600" />
              </div>
            </div>

            {/* 会话信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3">
                <h4 className="text-base font-medium text-gray-900 truncate">
                  {session.title ? highlightText(session.title, searchKeyword) : `会话 ${session.chatId.substring(0, 8)}`}
                </h4>
                <Badge variant={sessionStatus.variant} size="sm">
                  {sessionStatus.text}
                </Badge>
                {session.modelName && (
                  <Badge variant="info" size="sm">
                    {session.modelName}
                  </Badge>
                )}
              </div>
              
              <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <ClockIcon className="h-3 w-3" />
                  <span>开始: {format(new Date(session.firstMessageTime), 'MM-dd HH:mm')}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <ClockIcon className="h-3 w-3" />
                  <span>最后: {format(new Date(session.lastMessageTime), 'MM-dd HH:mm')}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CpuChipIcon className="h-3 w-3" />
                  <span>持续: {getSessionDuration()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 消息统计 */}
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <DocumentTextIcon className="h-4 w-4" />
              <span>{session.messageCount} 条消息</span>
            </div>
            <div className="text-xs text-gray-400">
              ID: {session.chatId.substring(0, 8)}...
            </div>
          </div>
        </div>
      </div>

      {/* 消息列表 */}
      {isExpanded && (
        <div className="bg-white border-t border-gray-100">
          {isLoading ? (
            <div className="p-6">
              <Loading />
            </div>
          ) : messages.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              该会话暂无消息记录
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {messages.map((message) => (
                <MessageItemComponent
                  key={message._id}
                  message={message}
                  searchKeyword={searchKeyword}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SessionGroupComponent;
