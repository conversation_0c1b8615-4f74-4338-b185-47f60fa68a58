import React, { useState } from 'react';
import { 
  UserIcon,
  CpuChipIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  PhotoIcon,
  SpeakerWaveIcon,
  VideoCameraIcon,
  DocumentIcon,
  MapPinIcon,
  UserCircleIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import Badge from '@/components/common/Badge';
import Button from '@/components/common/Button';
import { ChatMessage, ChatRole, MessageType } from '@/types/chat';
import { format } from 'date-fns';

interface MessageItemProps {
  message: ChatMessage;
  searchKeyword?: string;
}

const MessageItemComponent: React.FC<MessageItemProps> = ({
  message,
  searchKeyword = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showFullContent, setShowFullContent] = useState(false);

  // 获取角色信息
  const getRoleInfo = (role: ChatRole) => {
    switch (role) {
      case 'user':
        return {
          icon: UserIcon,
          text: '用户',
          variant: 'primary' as const,
          bgColor: 'bg-blue-50',
          borderColor: 'border-l-blue-400'
        };
      case 'assistant':
        return {
          icon: CpuChipIcon,
          text: 'AI助手',
          variant: 'success' as const,
          bgColor: 'bg-green-50',
          borderColor: 'border-l-green-400'
        };
      default:
        return {
          icon: CogIcon,
          text: '其他',
          variant: 'secondary' as const,
          bgColor: 'bg-gray-50',
          borderColor: 'border-l-gray-400'
        };
    }
  };

  // 获取消息类型图标
  const getMessageTypeIcon = (type?: MessageType) => {
    switch (type) {
      case 'image':
        return PhotoIcon;
      case 'audio':
        return SpeakerWaveIcon;
      case 'video':
        return VideoCameraIcon;
      case 'file':
        return DocumentIcon;
      case 'location':
        return MapPinIcon;
      case 'contact':
        return UserCircleIcon;
      case 'system':
        return CogIcon;
      default:
        return DocumentIcon;
    }
  };

  // 高亮搜索关键词
  const highlightText = (text: string, keyword: string) => {
    if (!keyword) return text;
    
    const regex = new RegExp(`(${keyword})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  // 复制消息内容
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert('已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  // 截断长文本
  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const roleInfo = getRoleInfo(message.role);
  const RoleIcon = roleInfo.icon;
  const MessageTypeIcon = getMessageTypeIcon(message.messageType);
  const shouldTruncate = message.content.length > 200;

  return (
    <div className={`${roleInfo.borderColor} border-l-4 ml-12`}>
      <div className={`p-4 ${roleInfo.bgColor} hover:bg-opacity-80 transition-colors duration-200`}>
        <div className="flex items-start space-x-4">
          {/* 角色图标 */}
          <div className="flex-shrink-0">
            <div className={`h-8 w-8 rounded-full ${roleInfo.bgColor} border-2 ${roleInfo.borderColor.replace('border-l-', 'border-')} flex items-center justify-center`}>
              <RoleIcon className="h-4 w-4 text-gray-600" />
            </div>
          </div>

          {/* 消息内容 */}
          <div className="flex-1 min-w-0">
            {/* 消息头部 */}
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-3">
                <Badge variant={roleInfo.variant} size="sm">
                  {roleInfo.text}
                </Badge>
                {message.messageType && message.messageType !== 'text' && (
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <MessageTypeIcon className="h-3 w-3" />
                    <span>{message.messageType}</span>
                  </div>
                )}
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <ClockIcon className="h-3 w-3" />
                  <span>{format(new Date(message.createdTime), 'MM-dd HH:mm:ss')}</span>
                </div>
                <div className="flex items-center space-x-1">
                  {message.isComplete ? (
                    <CheckCircleIcon className="h-3 w-3 text-green-500" />
                  ) : (
                    <ExclamationCircleIcon className="h-3 w-3 text-yellow-500" />
                  )}
                  <span className="text-xs text-gray-500">
                    {message.isComplete ? '已完成' : '进行中'}
                  </span>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center space-x-2">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => copyToClipboard(message.content)}
                  icon={<DocumentDuplicateIcon className="h-3 w-3" />}
                >
                  复制
                </Button>
                {shouldTruncate && (
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => setShowFullContent(!showFullContent)}
                    icon={<EyeIcon className="h-3 w-3" />}
                  >
                    {showFullContent ? '收起' : '展开'}
                  </Button>
                )}
              </div>
            </div>

            {/* 消息内容 */}
            <div className="prose prose-sm max-w-none">
              <div className="text-gray-900 whitespace-pre-wrap break-words">
                {highlightText(
                  showFullContent ? message.content : truncateText(message.content),
                  searchKeyword
                )}
              </div>
            </div>

            {/* 消息元数据 */}
            <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center space-x-4">
                <span>ID: {message.id}</span>
                <span>长度: {message.content.length} 字符</span>
                {message.finishReason && (
                  <span>结束原因: {message.finishReason}</span>
                )}
              </div>
              
              {/* 消息状态指示器 */}
              <div className="flex items-center space-x-2">
                {message.role === 'assistant' && (
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>AI回复</span>
                  </div>
                )}
                {message.role === 'user' && (
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>用户消息</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageItemComponent;
