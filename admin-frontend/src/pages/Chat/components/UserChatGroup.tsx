import React from 'react';
import { 
  ChevronRightIcon, 
  ChevronDownIcon, 
  UserIcon,
  ChatBubbleLeftRightIcon,
  EnvelopeIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';
import { UserChatGroup } from '@/types/chat';
import { format } from 'date-fns';
import SessionGroupComponent from './SessionGroup';

interface UserChatGroupProps {
  userGroup: UserChatGroup;
  onToggleUser: () => void;
  onToggleSession: (chatId: string) => void;
  searchKeyword?: string;
}

const UserChatGroupComponent: React.FC<UserChatGroupProps> = ({
  userGroup,
  onToggleUser,
  onToggleSession,
  searchKeyword = ''
}) => {
  const { user, sessions, totalSessions, totalMessages, isExpanded, isLoading } = userGroup;

  // 获取用户角色样式
  const getRoleStyle = (role?: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'premium':
        return 'bg-purple-100 text-purple-800';
      case 'user':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 高亮搜索关键词
  const highlightText = (text: string, keyword: string) => {
    if (!keyword) return text;
    
    const regex = new RegExp(`(${keyword})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    );
  };

  return (
    <div className="border-l-4 border-l-blue-500">
      {/* 用户级别 */}
      <div 
        className="p-4 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
        onClick={onToggleUser}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* 展开/折叠图标 */}
            <div className="flex-shrink-0">
              {isLoading ? (
                <div className="w-5 h-5 flex items-center justify-center">
                  <div className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : isExpanded ? (
                <ChevronDownIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronRightIcon className="h-5 w-5 text-gray-400" />
              )}
            </div>

            {/* 用户头像 */}
            <div className="flex-shrink-0">
              {user.avatar ? (
                <img 
                  src={user.avatar} 
                  alt={user.fullName || user.email} 
                  className="h-10 w-10 rounded-full object-cover"
                />
              ) : (
                <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                  <UserIcon className="h-6 w-6 text-gray-500" />
                </div>
              )}
            </div>

            {/* 用户信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3">
                <h3 className="text-lg font-medium text-gray-900 truncate">
                  {highlightText(user.fullName || '未知用户', searchKeyword)}
                </h3>
                {user.role && (
                  <Badge 
                    variant="secondary" 
                    className={getRoleStyle(user.role)}
                    size="sm"
                  >
                    {user.role}
                  </Badge>
                )}
              </div>
              
              <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                {user.email && (
                  <div className="flex items-center space-x-1">
                    <EnvelopeIcon className="h-4 w-4" />
                    <span>{highlightText(user.email, searchKeyword)}</span>
                  </div>
                )}
                {user.lastActiveAt && (
                  <div className="flex items-center space-x-1">
                    <CalendarIcon className="h-4 w-4" />
                    <span>最后活跃: {format(new Date(user.lastActiveAt), 'yyyy-MM-dd HH:mm')}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <ChatBubbleLeftRightIcon className="h-4 w-4" />
              <span>{totalSessions} 个会话</span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="inline-block w-2 h-2 bg-green-400 rounded-full"></span>
              <span>{totalMessages} 条消息</span>
            </div>
          </div>
        </div>
      </div>

      {/* 会话列表 */}
      {isExpanded && (
        <div className="bg-gray-50 border-t border-gray-200">
          {isLoading ? (
            <div className="p-8">
              <Loading />
            </div>
          ) : sessions.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              该用户暂无会话记录
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {sessions.map((sessionGroup) => (
                <SessionGroupComponent
                  key={sessionGroup.session.chatId}
                  sessionGroup={sessionGroup}
                  onToggleSession={() => onToggleSession(sessionGroup.session.chatId)}
                  searchKeyword={searchKeyword}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default UserChatGroupComponent;
