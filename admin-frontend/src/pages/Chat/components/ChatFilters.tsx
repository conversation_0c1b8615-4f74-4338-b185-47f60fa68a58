import React from 'react';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import { ChatRole, MessageType } from '@/types/chat';

interface FilterState {
  role?: ChatRole;
  dateFrom?: string;
  dateTo?: string;
  messageType?: string;
  isComplete?: string;
}

interface ChatFiltersProps {
  filters: FilterState;
  onChange: (filters: FilterState) => void;
  onReset: () => void;
}

const ChatFilters: React.FC<ChatFiltersProps> = ({
  filters,
  onChange,
  onReset
}) => {
  const handleFilterChange = (key: keyof FilterState, value: string) => {
    onChange({
      ...filters,
      [key]: value === '' ? undefined : value
    });
  };

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {/* 角色筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            角色
          </label>
          <select
            value={filters.role || ''}
            onChange={(e) => handleFilterChange('role', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">全部角色</option>
            <option value="user">用户</option>
            <option value="assistant">AI助手</option>
            <option value="other">其他</option>
          </select>
        </div>

        {/* 消息类型筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            消息类型
          </label>
          <select
            value={filters.messageType || ''}
            onChange={(e) => handleFilterChange('messageType', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">全部类型</option>
            <option value="text">文本</option>
            <option value="image">图片</option>
            <option value="audio">音频</option>
            <option value="video">视频</option>
            <option value="file">文件</option>
            <option value="location">位置</option>
            <option value="contact">联系人</option>
            <option value="system">系统</option>
          </select>
        </div>

        {/* 完成状态筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            完成状态
          </label>
          <select
            value={filters.isComplete || ''}
            onChange={(e) => handleFilterChange('isComplete', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">全部状态</option>
            <option value="true">已完成</option>
            <option value="false">进行中</option>
          </select>
        </div>

        {/* 开始日期 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            开始日期
          </label>
          <Input
            type="date"
            value={filters.dateFrom || ''}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            fullWidth
          />
        </div>

        {/* 结束日期 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            结束日期
          </label>
          <Input
            type="date"
            value={filters.dateTo || ''}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            fullWidth
          />
        </div>
      </div>

      {/* 筛选操作 */}
      <div className="mt-4 flex justify-between items-center">
        <div className="text-sm text-gray-600">
          {Object.values(filters).filter(Boolean).length > 0 && (
            <span>已应用 {Object.values(filters).filter(Boolean).length} 个筛选条件</span>
          )}
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={onReset}
            size="sm"
          >
            重置筛选
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatFilters;
