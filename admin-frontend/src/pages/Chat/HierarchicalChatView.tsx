import React, { useState, useEffect, useCallback } from 'react';
import { 
  ChevronRightIcon, 
  ChevronDownIcon, 
  UserIcon, 
  ChatBubbleLeftRightIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';
import { useApi } from '@/hooks/useApi';
import { chatService } from '@/services/chat';
import { 
  HierarchicalChatData, 
  UserChatGroup, 
  SessionGroup, 
  ChatMessage, 
  ChatRole,
  ChatMessageListParams 
} from '@/types/chat';
import UserChatGroupComponent from './components/UserChatGroup';
import ChatFilters from './components/ChatFilters';

interface HierarchicalChatViewProps {
  className?: string;
}

interface FilterState {
  role?: ChatRole;
  dateFrom?: string;
  dateTo?: string;
  messageType?: string;
  isComplete?: string;
}

const HierarchicalChatView: React.FC<HierarchicalChatViewProps> = ({ className = '' }) => {
  // 状态管理
  const [hierarchicalData, setHierarchicalData] = useState<HierarchicalChatData>({
    users: [],
    totalUsers: 0,
    totalSessions: 0,
    totalMessages: 0
  });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filters, setFilters] = useState<FilterState>({});
  const [showFilters, setShowFilters] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  // API调用
  const {
    loading: loadingUsers,
    execute: fetchUsers,
  } = useApi(chatService.getUsers);

  const {
    loading: loadingSessions,
    execute: fetchUserSessions,
  } = useApi(chatService.getUserSessions);

  const {
    loading: loadingMessages,
    execute: fetchSessionMessages,
  } = useApi(chatService.getMessages);

  // 加载用户列表
  const loadUsers = useCallback(async () => {
    try {
      const users = await fetchUsers();
      
      // 转换为层级数据结构
      const userGroups: UserChatGroup[] = users.map(user => ({
        user: {
          userId: user._id,
          email: user.email,
          fullName: user.fullName,
          avatar: user.avatar,
          role: user.role,
          createdAt: user.createdAt,
          lastActiveAt: user.lastActiveAt
        },
        sessions: [],
        totalSessions: user.sessionCount || 0,
        totalMessages: user.messageCount || 0,
        isExpanded: false,
        isLoading: false
      }));

      setHierarchicalData({
        users: userGroups,
        totalUsers: users.length,
        totalSessions: users.reduce((sum, user) => sum + (user.sessionCount || 0), 0),
        totalMessages: users.reduce((sum, user) => sum + (user.messageCount || 0), 0)
      });
    } catch (error) {
      console.error('加载用户列表失败:', error);
    }
  }, [fetchUsers]);

  // 加载用户的会话列表
  const loadUserSessions = useCallback(async (userId: string) => {
    try {
      setHierarchicalData(prev => ({
        ...prev,
        users: prev.users.map(userGroup => 
          userGroup.user.userId === userId 
            ? { ...userGroup, isLoading: true }
            : userGroup
        )
      }));

      const sessions = await fetchUserSessions(userId);
      
      const sessionGroups: SessionGroup[] = sessions.map(session => ({
        session,
        messages: [],
        isExpanded: false,
        isLoading: false
      }));

      setHierarchicalData(prev => ({
        ...prev,
        users: prev.users.map(userGroup => 
          userGroup.user.userId === userId 
            ? { 
                ...userGroup, 
                sessions: sessionGroups,
                isLoading: false,
                isExpanded: true
              }
            : userGroup
        )
      }));
    } catch (error) {
      console.error('加载用户会话失败:', error);
      setHierarchicalData(prev => ({
        ...prev,
        users: prev.users.map(userGroup => 
          userGroup.user.userId === userId 
            ? { ...userGroup, isLoading: false }
            : userGroup
        )
      }));
    }
  }, [fetchUserSessions]);

  // 加载会话的消息列表
  const loadSessionMessages = useCallback(async (userId: string, chatId: string) => {
    try {
      setHierarchicalData(prev => ({
        ...prev,
        users: prev.users.map(userGroup => 
          userGroup.user.userId === userId 
            ? {
                ...userGroup,
                sessions: userGroup.sessions.map(sessionGroup =>
                  sessionGroup.session.chatId === chatId
                    ? { ...sessionGroup, isLoading: true }
                    : sessionGroup
                )
              }
            : userGroup
        )
      }));

      const params: ChatMessageListParams = {
        chatId,
        page: 1,
        limit: 100, // 加载更多消息
        sortBy: 'createdTime',
        sortOrder: 'asc'
      };

      const result = await fetchSessionMessages(params);

      setHierarchicalData(prev => ({
        ...prev,
        users: prev.users.map(userGroup => 
          userGroup.user.userId === userId 
            ? {
                ...userGroup,
                sessions: userGroup.sessions.map(sessionGroup =>
                  sessionGroup.session.chatId === chatId
                    ? { 
                        ...sessionGroup, 
                        messages: result.items,
                        isLoading: false,
                        isExpanded: true
                      }
                    : sessionGroup
                )
              }
            : userGroup
        )
      }));
    } catch (error) {
      console.error('加载会话消息失败:', error);
      setHierarchicalData(prev => ({
        ...prev,
        users: prev.users.map(userGroup => 
          userGroup.user.userId === userId 
            ? {
                ...userGroup,
                sessions: userGroup.sessions.map(sessionGroup =>
                  sessionGroup.session.chatId === chatId
                    ? { ...sessionGroup, isLoading: false }
                    : sessionGroup
                )
              }
            : userGroup
        )
      }));
    }
  }, [fetchSessionMessages]);

  // 切换用户展开状态
  const toggleUserExpansion = useCallback((userId: string) => {
    const userGroup = hierarchicalData.users.find(u => u.user.userId === userId);
    if (!userGroup) return;

    if (!userGroup.isExpanded && userGroup.sessions.length === 0) {
      // 首次展开，需要加载会话数据
      loadUserSessions(userId);
    } else {
      // 切换展开状态
      setHierarchicalData(prev => ({
        ...prev,
        users: prev.users.map(userGroup => 
          userGroup.user.userId === userId 
            ? { ...userGroup, isExpanded: !userGroup.isExpanded }
            : userGroup
        )
      }));
    }
  }, [hierarchicalData.users, loadUserSessions]);

  // 切换会话展开状态
  const toggleSessionExpansion = useCallback((userId: string, chatId: string) => {
    const userGroup = hierarchicalData.users.find(u => u.user.userId === userId);
    const sessionGroup = userGroup?.sessions.find(s => s.session.chatId === chatId);
    if (!sessionGroup) return;

    if (!sessionGroup.isExpanded && sessionGroup.messages.length === 0) {
      // 首次展开，需要加载消息数据
      loadSessionMessages(userId, chatId);
    } else {
      // 切换展开状态
      setHierarchicalData(prev => ({
        ...prev,
        users: prev.users.map(userGroup => 
          userGroup.user.userId === userId 
            ? {
                ...userGroup,
                sessions: userGroup.sessions.map(sessionGroup =>
                  sessionGroup.session.chatId === chatId
                    ? { ...sessionGroup, isExpanded: !sessionGroup.isExpanded }
                    : sessionGroup
                )
              }
            : userGroup
        )
      }));
    }
  }, [hierarchicalData.users, loadSessionMessages]);

  // 搜索处理
  const handleSearch = useCallback(() => {
    // 实现搜索逻辑
    loadUsers();
  }, [loadUsers]);

  // 筛选处理
  const handleFilterChange = useCallback((newFilters: FilterState) => {
    setFilters(newFilters);
    loadUsers();
  }, [loadUsers]);

  // 刷新数据
  const handleRefresh = useCallback(() => {
    loadUsers();
  }, [loadUsers]);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        loadUsers();
      }, 30000); // 30秒刷新一次
      setRefreshInterval(interval);
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [autoRefresh, loadUsers]);

  // 初始加载
  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 页面标题和统计 */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">聊天管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            分层级管理用户聊天记录和会话
          </p>
          <div className="mt-3 flex space-x-6 text-sm text-gray-500">
            <span>用户: {hierarchicalData.totalUsers}</span>
            <span>会话: {hierarchicalData.totalSessions}</span>
            <span>消息: {hierarchicalData.totalMessages}</span>
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant={autoRefresh ? 'primary' : 'secondary'}
            onClick={() => setAutoRefresh(!autoRefresh)}
            size="sm"
          >
            {autoRefresh ? '停止自动刷新' : '开启自动刷新'}
          </Button>
          <Button
            variant="secondary"
            onClick={handleRefresh}
            loading={loadingUsers}
            icon={<ArrowPathIcon className="h-4 w-4" />}
            size="sm"
          >
            刷新
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col space-y-4">
          <div className="flex space-x-4">
            <div className="flex-1">
              <Input
                placeholder="搜索用户、会话或消息内容..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                icon={<MagnifyingGlassIcon className="h-4 w-4" />}
                fullWidth
              />
            </div>
            <Button
              variant="secondary"
              onClick={() => setShowFilters(!showFilters)}
              icon={<FunnelIcon className="h-4 w-4" />}
            >
              筛选
            </Button>
            <Button
              variant="primary"
              onClick={handleSearch}
            >
              搜索
            </Button>
          </div>
          
          {showFilters && (
            <ChatFilters
              filters={filters}
              onChange={handleFilterChange}
              onReset={() => {
                setFilters({});
                setSearchKeyword('');
                loadUsers();
              }}
            />
          )}
        </div>
      </div>

      {/* 层级数据展示 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            聊天记录层级视图
          </h3>
        </div>

        <div className="divide-y divide-gray-200">
          {loadingUsers ? (
            <div className="p-8">
              <Loading />
            </div>
          ) : hierarchicalData.users.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              暂无聊天数据
            </div>
          ) : (
            hierarchicalData.users.map((userGroup) => (
              <UserChatGroupComponent
                key={userGroup.user.userId}
                userGroup={userGroup}
                onToggleUser={() => toggleUserExpansion(userGroup.user.userId)}
                onToggleSession={(chatId) => toggleSessionExpansion(userGroup.user.userId, chatId)}
                searchKeyword={searchKeyword}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default HierarchicalChatView;
