import React, { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import {
  ListBulletIcon,
  Squares2X2Icon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import Button from '@/components/common/Button';
import MessageList from './MessageList';
import SessionList from './SessionList';
import ChatStats from './ChatStats';
import HierarchicalChatView from './HierarchicalChatView';

const Chat: React.FC = () => {
  const [viewMode, setViewMode] = useState<'hierarchical' | 'messages' | 'sessions' | 'stats'>('hierarchical');

  const renderContent = () => {
    switch (viewMode) {
      case 'hierarchical':
        return <HierarchicalChatView />;
      case 'messages':
        return <MessageList />;
      case 'sessions':
        return <SessionList />;
      case 'stats':
        return <ChatStats />;
      default:
        return <HierarchicalChatView />;
    }
  };

  return (
    <div className="space-y-6">
      {/* 视图切换器 */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium text-gray-900">聊天管理</h2>
            <p className="text-sm text-gray-600">管理用户聊天记录、会话和消息</p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant={viewMode === 'hierarchical' ? 'primary' : 'secondary'}
              onClick={() => setViewMode('hierarchical')}
              icon={<Squares2X2Icon className="h-4 w-4" />}
              size="sm"
            >
              层级视图
            </Button>
            <Button
              variant={viewMode === 'messages' ? 'primary' : 'secondary'}
              onClick={() => setViewMode('messages')}
              icon={<ListBulletIcon className="h-4 w-4" />}
              size="sm"
            >
              消息列表
            </Button>
            <Button
              variant={viewMode === 'sessions' ? 'primary' : 'secondary'}
              onClick={() => setViewMode('sessions')}
              icon={<ChatBubbleLeftRightIcon className="h-4 w-4" />}
              size="sm"
            >
              会话列表
            </Button>
            <Button
              variant={viewMode === 'stats' ? 'primary' : 'secondary'}
              onClick={() => setViewMode('stats')}
              icon={<ChartBarIcon className="h-4 w-4" />}
              size="sm"
            >
              统计分析
            </Button>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      {renderContent()}
    </div>
  );
};

export default Chat;
