import React, { useState, useEffect } from 'react';
import EnvironmentIndicator from '@/components/common/EnvironmentIndicator';
import { environmentManager } from '@/utils/environmentManager';
import { getApiConfig } from '@/config/api';
import { recreateApiInstance } from '@/services/api';

/**
 * 环境切换演示页面
 * 用于测试和演示环境切换功能
 */
const EnvironmentDemo: React.FC = () => {
  const [currentConfig, setCurrentConfig] = useState(getApiConfig());
  const [debugInfo, setDebugInfo] = useState(environmentManager.getDebugInfo());

  // 刷新配置信息
  const refreshConfig = () => {
    setCurrentConfig(getApiConfig());
    setDebugInfo(environmentManager.getDebugInfo());
  };

  // 监听环境变化
  useEffect(() => {
    const handleEnvironmentChange = () => {
      refreshConfig();
    };

    environmentManager.addListener(handleEnvironmentChange);
    
    return () => {
      environmentManager.removeListener(handleEnvironmentChange);
    };
  }, []);

  // 测试API请求
  const testApiRequest = async () => {
    try {
      // 重新创建API实例以确保使用最新配置
      const api = recreateApiInstance();
      
      // 尝试发送一个简单的请求
      const response = await fetch(`${currentConfig.baseURL}/api/admin/system/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('API测试请求结果:', {
        url: `${currentConfig.baseURL}/api/admin/system/status`,
        status: response.status,
        statusText: response.statusText,
      });
      
      alert(`API请求测试完成\n状态: ${response.status} ${response.statusText}\nURL: ${currentConfig.baseURL}`);
    } catch (error) {
      console.error('API测试请求失败:', error);
      alert(`API请求失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">环境切换演示</h1>
            <p className="mt-1 text-sm text-gray-600">
              测试和演示环境切换功能，仅在开发环境下可用
            </p>
          </div>

          <div className="p-6 space-y-6">
            {/* 环境指示器演示 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-medium text-gray-900 mb-3">环境指示器</h2>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    基础指示器（只显示）
                  </label>
                  <EnvironmentIndicator showDetails={true} />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    可切换指示器（开发环境下可点击切换）
                  </label>
                  <EnvironmentIndicator allowSwitch={true} showDetails={true} />
                </div>
              </div>
            </div>

            {/* 当前配置信息 */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h2 className="text-lg font-medium text-gray-900 mb-3">当前API配置</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">基础URL</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono bg-white px-2 py-1 rounded">
                    {currentConfig.baseURL}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">超时时间</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono bg-white px-2 py-1 rounded">
                    {currentConfig.timeout}ms
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">生产环境</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono bg-white px-2 py-1 rounded">
                    {currentConfig.isProduction ? 'true' : 'false'}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">环境名称</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono bg-white px-2 py-1 rounded">
                    {(currentConfig as any).environment || 'unknown'}
                  </p>
                </div>
              </div>
            </div>

            {/* 调试信息 */}
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h2 className="text-lg font-medium text-gray-900 mb-3">调试信息</h2>
              <pre className="text-xs text-gray-800 bg-white p-3 rounded border overflow-auto">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-wrap gap-3">
              <button
                onClick={refreshConfig}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                刷新配置
              </button>
              
              <button
                onClick={testApiRequest}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                测试API请求
              </button>
              
              <button
                onClick={() => {
                  environmentManager.resetToDefault();
                  refreshConfig();
                }}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                重置到默认环境
              </button>
              
              <button
                onClick={() => {
                  console.log('环境管理器调试信息:', environmentManager.getDebugInfo());
                  console.log('当前API配置:', getApiConfig());
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                输出到控制台
              </button>
            </div>

            {/* 使用说明 */}
            <div className="bg-green-50 p-4 rounded-lg">
              <h2 className="text-lg font-medium text-gray-900 mb-3">使用说明</h2>
              <ul className="text-sm text-gray-700 space-y-2">
                <li>• 环境切换功能仅在开发环境下可用</li>
                <li>• 点击环境指示器可以切换不同的API端点</li>
                <li>• 切换后会自动重新创建API实例</li>
                <li>• 建议切换后刷新页面以完全应用新配置</li>
                <li>• 环境设置会保存在localStorage中</li>
                <li>• 可以通过"重置到默认环境"按钮清除覆盖设置</li>
              </ul>
            </div>

            {/* 控制台命令 */}
            <div className="bg-purple-50 p-4 rounded-lg">
              <h2 className="text-lg font-medium text-gray-900 mb-3">控制台命令</h2>
              <div className="text-sm text-gray-700 space-y-1 font-mono">
                <p>• environmentManager.getCurrentEnvironment() - 获取当前环境</p>
                <p>• environmentManager.switchEnvironment('production') - 切换到生产环境</p>
                <p>• environmentManager.resetToDefault() - 重置到默认环境</p>
                <p>• environmentManager.getDebugInfo() - 获取调试信息</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnvironmentDemo;
