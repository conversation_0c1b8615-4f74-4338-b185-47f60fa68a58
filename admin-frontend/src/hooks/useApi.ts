import React, { useState, useCallback, useEffect } from 'react';
import { ApiResponse } from '@/types/common';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiReturn<T> extends UseApiState<T> {
  execute: (...args: any[]) => Promise<T>;
  reset: () => void;
}

export const useApi = <T = any>(
  apiFunction: (...args: any[]) => Promise<ApiResponse<T>>
): UseApiReturn<T> => {
  const [state, setState] = useState<UseApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(
    async (...args: any[]): Promise<T> => {
      setState(prev => ({ ...prev, loading: true, error: null }));

      try {
        const result = await apiFunction(...args);

        // 检查是否是ApiResponse格式
        if (result && typeof result === 'object' && 'success' in result) {
          // 这是ApiResponse格式
          if (result.success && result.data !== undefined) {
            setState({
              data: result.data,
              loading: false,
              error: null,
            });
            return result.data;
          } else {
            const errorMessage = result.message || '请求失败';
            console.error('API请求失败:', {
              response: result,
              args,
              apiFunction: apiFunction.name
            });
            setState({
              data: null,
              loading: false,
              error: errorMessage,
            });
            throw new Error(errorMessage);
          }
        } else {
          // 这是直接返回的数据，说明service层已经处理了response
          setState({
            data: result,
            loading: false,
            error: null,
          });
          return result;
        }
      } catch (error) {
        let errorMessage = '请求失败';

        if (error instanceof Error) {
          errorMessage = error.message;
        }

        // 增强错误日志记录
        console.error('API执行错误:', {
          error,
          errorMessage,
          args,
          apiFunction: apiFunction.name,
          stack: error instanceof Error ? error.stack : undefined
        });

        setState({
          data: null,
          loading: false,
          error: errorMessage,
        });
        throw error;
      }
    },
    [apiFunction]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
};

// 用于列表数据的特殊Hook
interface UseApiListOptions {
  immediate?: boolean;
}

export const useApiList = <T = any>(
  apiFunction: (...args: any[]) => Promise<ApiResponse<T[]>>,
  options: UseApiListOptions = {}
): UseApiReturn<T[]> & { refresh: () => void } => {
  const { immediate = false } = options;
  const [lastArgs, setLastArgs] = useState<any[]>([]);
  
  const api = useApi(apiFunction);

  const execute = useCallback(
    async (...args: any[]): Promise<T[]> => {
      setLastArgs(args);
      return api.execute(...args);
    },
    [api.execute]
  );

  const refresh = useCallback(() => {
    if (lastArgs.length > 0) {
      execute(...lastArgs);
    }
  }, [execute, lastArgs]);

  // 立即执行
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [immediate, execute]);

  return {
    ...api,
    execute,
    refresh,
  };
};
