import { useCallback } from 'react';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface Toast {
  id: string;
  message: string;
  type: ToastType;
  duration?: number;
}

// 简单的Toast实现，可以根据需要替换为更完整的Toast库
export const useToast = () => {
  const showToast = useCallback((message: string, type: ToastType = 'info', duration: number = 3000) => {
    // 创建Toast元素
    const toastId = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = getToastClasses(type);
    toast.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0">
          ${getToastIcon(type)}
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-white">${message}</p>
        </div>
        <div class="ml-4 flex-shrink-0 flex">
          <button class="bg-transparent rounded-md inline-flex text-white hover:text-gray-200 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.remove()">
            <span class="sr-only">关闭</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    `;

    // 添加到页面
    let container = document.getElementById('toast-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'toast-container';
      container.className = 'fixed top-4 right-4 z-50 space-y-2';
      document.body.appendChild(container);
    }

    container.appendChild(toast);

    // 添加进入动画
    setTimeout(() => {
      toast.classList.add('translate-x-0', 'opacity-100');
      toast.classList.remove('translate-x-full', 'opacity-0');
    }, 10);

    // 自动移除
    if (duration > 0) {
      setTimeout(() => {
        removeToast(toastId);
      }, duration);
    }
  }, []);

  const removeToast = useCallback((toastId: string) => {
    const toast = document.getElementById(toastId);
    if (toast) {
      toast.classList.add('translate-x-full', 'opacity-0');
      toast.classList.remove('translate-x-0', 'opacity-100');
      setTimeout(() => {
        toast.remove();
      }, 300);
    }
  }, []);

  return { showToast, removeToast };
};

const getToastClasses = (type: ToastType): string => {
  const baseClasses = 'transform transition-all duration-300 ease-in-out translate-x-full opacity-0 max-w-sm w-full shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden';
  
  const typeClasses = {
    success: 'bg-green-600',
    error: 'bg-red-600',
    warning: 'bg-yellow-600',
    info: 'bg-blue-600'
  };

  return `${baseClasses} ${typeClasses[type]}`;
};

const getToastIcon = (type: ToastType): string => {
  const icons = {
    success: `
      <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
      </svg>
    `,
    error: `
      <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    `,
    warning: `
      <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    `,
    info: `
      <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    `
  };

  return icons[type];
};
