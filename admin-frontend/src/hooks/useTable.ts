import { useState, useCallback, useMemo } from 'react';
import { PaginationParams, SortParams, SearchParams } from '@/types/common';
import { usePagination } from './usePagination';

interface UseTableOptions {
  initialPageSize?: number;
  initialSortBy?: string;
  initialSortOrder?: 'asc' | 'desc';
}

interface UseTableState extends PaginationParams, SortParams, SearchParams {
  selectedRowKeys: (string | number)[];
}

interface UseTableReturn {
  tableState: UseTableState;
  pagination: {
    current: number;
    pageSize: number;
    onChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
  };
  sorting: {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    onSort: (field: string) => void;
  };
  searching: {
    search?: string;
    onSearch: (value: string) => void;
    clearSearch: () => void;
  };
  selection: {
    selectedRowKeys: (string | number)[];
    onSelectChange: (selectedKeys: (string | number)[]) => void;
    onSelectAll: (allKeys: (string | number)[]) => void;
    onSelectNone: () => void;
    hasSelected: boolean;
  };
  resetTable: () => void;
  getQueryParams: () => Record<string, any>;
}

export const useTable = (options: UseTableOptions = {}): UseTableReturn => {
  const {
    initialPageSize = 20,
    initialSortBy,
    initialSortOrder = 'desc',
  } = options;

  // 分页状态
  const {
    pagination,
    handlePageChange,
    handlePageSizeChange,
    resetPagination,
  } = usePagination({
    initialPageSize,
  });

  // 排序状态
  const [sortState, setSortState] = useState<SortParams>({
    sortBy: initialSortBy,
    sortOrder: initialSortOrder,
  });

  // 搜索状态
  const [searchState, setSearchState] = useState<SearchParams>({
    search: '',
  });

  // 选择状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);

  // 排序处理
  const handleSort = useCallback((field: string) => {
    setSortState(prev => {
      if (prev.sortBy === field) {
        // 同一字段，切换排序方向
        const newOrder = prev.sortOrder === 'asc' ? 'desc' : 'asc';
        return { sortBy: field, sortOrder: newOrder };
      } else {
        // 不同字段，默认降序
        return { sortBy: field, sortOrder: 'desc' };
      }
    });
    // 排序时重置到第一页
    handlePageChange(1);
  }, [handlePageChange]);

  // 搜索处理
  const handleSearch = useCallback((value: string) => {
    setSearchState({ search: value });
    // 搜索时重置到第一页
    handlePageChange(1);
  }, [handlePageChange]);

  const clearSearch = useCallback(() => {
    setSearchState({ search: '' });
    handlePageChange(1);
  }, [handlePageChange]);

  // 选择处理
  const handleSelectChange = useCallback((selectedKeys: (string | number)[]) => {
    setSelectedRowKeys(selectedKeys);
  }, []);

  const handleSelectAll = useCallback((allKeys: (string | number)[]) => {
    setSelectedRowKeys(allKeys);
  }, []);

  const handleSelectNone = useCallback(() => {
    setSelectedRowKeys([]);
  }, []);

  // 重置表格状态
  const resetTable = useCallback(() => {
    resetPagination();
    setSortState({
      sortBy: initialSortBy,
      sortOrder: initialSortOrder,
    });
    setSearchState({ search: '' });
    setSelectedRowKeys([]);
  }, [resetPagination, initialSortBy, initialSortOrder]);

  // 获取查询参数
  const getQueryParams = useCallback(() => {
    const params: Record<string, any> = {
      page: pagination.page,
      limit: pagination.limit,
    };

    if (sortState.sortBy) {
      params.sortBy = sortState.sortBy;
      params.sortOrder = sortState.sortOrder;
    }

    if (searchState.search) {
      params.search = searchState.search;
    }

    return params;
  }, [pagination, sortState, searchState]);

  // 组合状态
  const tableState = useMemo(() => ({
    ...pagination,
    ...sortState,
    ...searchState,
    selectedRowKeys,
  }), [pagination, sortState, searchState, selectedRowKeys]);

  return {
    tableState,
    pagination: {
      current: pagination.page,
      pageSize: pagination.limit,
      onChange: handlePageChange,
      onPageSizeChange: handlePageSizeChange,
    },
    sorting: {
      sortBy: sortState.sortBy,
      sortOrder: sortState.sortOrder,
      onSort: handleSort,
    },
    searching: {
      search: searchState.search,
      onSearch: handleSearch,
      clearSearch,
    },
    selection: {
      selectedRowKeys,
      onSelectChange: handleSelectChange,
      onSelectAll: handleSelectAll,
      onSelectNone: handleSelectNone,
      hasSelected: selectedRowKeys.length > 0,
    },
    resetTable,
    getQueryParams,
  };
};
