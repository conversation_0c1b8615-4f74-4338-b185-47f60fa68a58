import { useState, useCallback } from 'react';
import { PaginationParams } from '@/types/common';

interface UsePaginationOptions {
  initialPage?: number;
  initialPageSize?: number;
  onPageChange?: (page: number, pageSize: number) => void;
}

interface UsePaginationReturn {
  pagination: PaginationParams;
  setPagination: React.Dispatch<React.SetStateAction<PaginationParams>>;
  handlePageChange: (page: number) => void;
  handlePageSizeChange: (pageSize: number) => void;
  resetPagination: () => void;
}

export const usePagination = (options: UsePaginationOptions = {}): UsePaginationReturn => {
  const {
    initialPage = 1,
    initialPageSize = 20,
    onPageChange,
  } = options;

  const [pagination, setPagination] = useState<PaginationParams>({
    page: initialPage,
    limit: initialPageSize,
  });

  const handlePageChange = useCallback((page: number) => {
    setPagination(prev => {
      const newPagination = { ...prev, page };
      onPageChange?.(newPagination.page, newPagination.limit);
      return newPagination;
    });
  }, [onPageChange]);

  const handlePageSizeChange = useCallback((pageSize: number) => {
    setPagination(prev => {
      const newPagination = { page: 1, limit: pageSize };
      onPageChange?.(newPagination.page, newPagination.limit);
      return newPagination;
    });
  }, [onPageChange]);

  const resetPagination = useCallback(() => {
    setPagination({
      page: initialPage,
      limit: initialPageSize,
    });
  }, [initialPage, initialPageSize]);

  return {
    pagination,
    setPagination,
    handlePageChange,
    handlePageSizeChange,
    resetPagination,
  };
};
