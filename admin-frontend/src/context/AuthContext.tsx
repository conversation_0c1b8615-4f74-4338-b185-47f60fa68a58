import React, { createContext, useContext, useReducer, useEffect, useCallback, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AuthUser, LoginCredentials } from '@/types/common';
import { authService } from '@/services/auth';
import { setGlobalAuthErrorHandler } from '@/services/api';

// 认证状态类型
interface AuthState {
  user: AuthUser | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  loginTimestamp: number | null; // 登录时间戳，用于判断是否刚刚登录
}

// 认证动作类型
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: AuthUser; loginTimestamp?: number }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'CLEAR_ERROR' };

// 认证上下文类型
interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
  handleAuthError: (error: Error) => void;
}

// 初始状态
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  loading: true,
  error: null,
  loginTimestamp: null,
};

// 认证reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        loading: true,
        error: null,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        loading: false,
        error: null,
        loginTimestamp: action.loginTimestamp || null,
      };
    case 'AUTH_FAILURE':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        loading: false,
        error: action.payload,
      };
    case 'AUTH_LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        loading: false,
        error: null,
        loginTimestamp: null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 认证提供者组件
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const navigate = useNavigate();
  const location = useLocation();

  // 登录方法
  const login = useCallback(async (credentials: LoginCredentials): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });
      const authData = await authService.login(credentials);
      const loginTimestamp = Date.now();

      // 保存登录时间戳到localStorage
      localStorage.setItem('admin_login_timestamp', loginTimestamp.toString());

      dispatch({ type: 'AUTH_SUCCESS', payload: authData.user, loginTimestamp });
    } catch (error) {
      const message = error instanceof Error ? error.message : '登录失败';
      dispatch({ type: 'AUTH_FAILURE', payload: message });
      throw error;
    }
  }, []);

  // 登出方法
  const logout = useCallback(async (): Promise<void> => {
    try {
      await authService.logout();
    } catch (error) {
      console.warn('登出请求失败:', error);
    } finally {
      // 清除登录时间戳
      localStorage.removeItem('admin_login_timestamp');
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  }, []);

  // 刷新用户信息
  const refreshUser = useCallback(async (): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });
      const user = await authService.getCurrentUser();
      dispatch({ type: 'AUTH_SUCCESS', payload: user });
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取用户信息失败';
      dispatch({ type: 'AUTH_FAILURE', payload: message });
      throw error;
    }
  }, []);

  // 清除错误
  const clearError = useCallback((): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // 处理认证错误
  const handleAuthError = useCallback(async (error: Error): Promise<void> => {
    console.warn('认证错误:', error.message);

    // 如果当前在登录页面，只清除本地状态，不进行跳转
    if (location.pathname === '/login') {
      try {
        await authService.logout(true); // 跳过服务器请求
      } catch (logoutError) {
        console.warn('清除本地认证状态失败:', logoutError);
      }
      dispatch({ type: 'AUTH_LOGOUT' });
      return;
    }

    // 如果刚刚登录成功（10秒内），不要立即处理认证错误，可能是网络延迟
    const now = Date.now();
    const isRecentLogin = state.loginTimestamp && (now - state.loginTimestamp < 10000); // 10秒内

    if (isRecentLogin) {
      console.log('刚登录成功，忽略认证错误，可能是网络延迟');
      return;
    }

    // 清除认证状态，跳过服务器请求避免循环调用
    try {
      await authService.logout(true); // 跳过服务器请求
    } catch (logoutError) {
      console.warn('清除本地认证状态失败:', logoutError);
    }

    dispatch({ type: 'AUTH_LOGOUT' });

    // 跳转到登录页
    navigate('/login', {
      state: { from: location },
      replace: true
    });
  }, [navigate, location, state.loginTimestamp]);

  // 设置全局认证错误处理器
  useEffect(() => {
    setGlobalAuthErrorHandler(handleAuthError);

    // 清理函数
    return () => {
      setGlobalAuthErrorHandler(() => {});
    };
  }, [handleAuthError]);

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        // 如果当前在登录页面，跳过token验证，只设置基本状态
        if (location.pathname === '/login') {
          // 清除可能存在的过期认证信息
          if (authService.isAuthenticated()) {
            const user = authService.getStoredUser();
            if (!user) {
              // 如果没有用户信息但有token，清除token
              await authService.logout(true);
            }
          }
          dispatch({ type: 'AUTH_LOGOUT' });
          return;
        }

        // 非登录页面的正常认证逻辑
        if (authService.isAuthenticated()) {
          const user = authService.getStoredUser();
          if (user) {
            // 先设置用户信息，避免闪烁
            dispatch({ type: 'AUTH_SUCCESS', payload: user });

            // 延迟验证token，但不依赖状态，使用localStorage检查登录时间
            setTimeout(() => {
              // 检查localStorage中是否有最近的登录时间戳
              const loginTimestamp = localStorage.getItem('admin_login_timestamp');
              const now = Date.now();
              const isRecentLogin = loginTimestamp && (now - parseInt(loginTimestamp) < 10000); // 10秒内

              if (!isRecentLogin) {
                authService.getCurrentUser().catch((error) => {
                  console.warn('Token验证失败:', error.message);
                  // 静默处理token失效，通过API拦截器处理后续的认证错误
                  handleAuthError(error);
                });
              }
            }, 2000); // 延迟2秒验证
          } else {
            dispatch({ type: 'AUTH_LOGOUT' });
          }
        } else {
          dispatch({ type: 'AUTH_LOGOUT' });
        }
      } catch (error) {
        console.error('认证初始化失败:', error);
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    };

    initAuth();
  }, []); // 移除handleAuthError依赖，避免重复初始化

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    refreshUser,
    clearError,
    handleAuthError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// 使用认证上下文的Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
