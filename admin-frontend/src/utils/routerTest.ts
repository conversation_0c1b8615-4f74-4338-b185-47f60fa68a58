// React Router修复验证工具
// 用于验证useNavigate错误修复是否成功

export const routerTestUtils = {
  // 检查React Router是否正常工作
  checkRouterStatus: () => {
    try {
      // 检查当前是否在React Router上下文中
      const currentPath = window.location.pathname;
      console.log('当前路径:', currentPath);
      
      // 检查是否有React Router相关的错误
      const hasRouterError = window.console.error.toString().includes('useNavigate');
      
      return {
        success: true,
        currentPath,
        hasRouterError: false,
        message: 'React Router状态正常'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'React Router状态异常'
      };
    }
  },

  // 测试路由导航功能
  testNavigation: (targetPath: string = '/') => {
    try {
      const originalPath = window.location.pathname;
      
      // 尝试使用history API进行导航
      window.history.pushState({}, '', targetPath);
      
      const newPath = window.location.pathname;
      
      console.log(`导航测试: ${originalPath} -> ${newPath}`);
      
      return {
        success: newPath === targetPath,
        originalPath,
        newPath,
        message: newPath === targetPath ? '导航测试成功' : '导航测试失败'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: '导航测试异常'
      };
    }
  },

  // 检查认证上下文是否正常
  checkAuthContext: () => {
    try {
      // 检查localStorage中的认证信息
      const token = localStorage.getItem('admin_token');
      const user = localStorage.getItem('admin_user');
      
      return {
        success: true,
        hasToken: !!token,
        hasUser: !!user,
        message: '认证上下文检查完成'
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: '认证上下文检查异常'
      };
    }
  },

  // 综合测试
  runFullTest: () => {
    console.log('=== React Router修复验证测试 ===');
    
    const routerStatus = routerTestUtils.checkRouterStatus();
    console.log('1. Router状态检查:', routerStatus);
    
    const navigationTest = routerTestUtils.testNavigation('/auth-test');
    console.log('2. 导航功能测试:', navigationTest);
    
    const authContextTest = routerTestUtils.checkAuthContext();
    console.log('3. 认证上下文检查:', authContextTest);
    
    const allSuccess = routerStatus.success && navigationTest.success && authContextTest.success;
    
    console.log('=== 测试结果 ===');
    console.log(`总体状态: ${allSuccess ? '✅ 所有测试通过' : '❌ 部分测试失败'}`);
    
    return {
      success: allSuccess,
      results: {
        router: routerStatus,
        navigation: navigationTest,
        authContext: authContextTest
      }
    };
  },

  // 检查控制台错误
  checkConsoleErrors: () => {
    // 监听控制台错误
    const originalError = console.error;
    const errors: string[] = [];
    
    console.error = (...args) => {
      const errorMessage = args.join(' ');
      if (errorMessage.includes('useNavigate') || errorMessage.includes('Router')) {
        errors.push(errorMessage);
      }
      originalError.apply(console, args);
    };
    
    // 5秒后恢复原始console.error并返回结果
    setTimeout(() => {
      console.error = originalError;
      console.log('控制台错误检查结果:', {
        hasRouterErrors: errors.length > 0,
        errors: errors,
        message: errors.length > 0 ? '发现Router相关错误' : '未发现Router相关错误'
      });
    }, 5000);
    
    return {
      message: '已开始监听控制台错误，5秒后输出结果'
    };
  }
};

// 在开发环境下将测试工具挂载到window对象
if (import.meta.env.DEV) {
  (window as any).routerTest = routerTestUtils;
  console.log('Router测试工具已挂载到 window.routerTest');
  console.log('可用方法:');
  console.log('- routerTest.checkRouterStatus() - 检查Router状态');
  console.log('- routerTest.testNavigation(path) - 测试导航功能');
  console.log('- routerTest.checkAuthContext() - 检查认证上下文');
  console.log('- routerTest.runFullTest() - 运行完整测试');
  console.log('- routerTest.checkConsoleErrors() - 检查控制台错误');
}
