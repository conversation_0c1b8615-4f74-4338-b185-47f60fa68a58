/**
 * 环境管理器
 * 处理开发环境下的环境切换功能
 */

// 环境配置接口
export interface EnvironmentConfig {
  name: string;
  label: string;
  apiBaseURL: string;
  color: string;
  dotColor: string;
  description: string;
  isProduction: boolean;
}

// 可用的环境配置
export const ENVIRONMENTS: Record<string, EnvironmentConfig> = {
  development: {
    name: 'development',
    label: '开发环境',
    apiBaseURL: 'http://localhost:33001',
    color: 'bg-yellow-100 text-yellow-800',
    dotColor: 'bg-yellow-400',
    description: '本地开发环境，连接测试数据库',
    isProduction: false
  },
  production: {
    name: 'production',
    label: '生产环境',
    apiBaseURL: 'https://admin.sanva.top/api',
    color: 'bg-green-100 text-green-800',
    dotColor: 'bg-green-400',
    description: '线上生产环境，谨慎操作',
    isProduction: true
  }
};

// 本地存储键名
const STORAGE_KEY = 'dev_environment_override';

/**
 * 环境管理器类
 */
export class EnvironmentManager {
  private static instance: EnvironmentManager;
  private listeners: Array<(config: EnvironmentConfig) => void> = [];

  private constructor() {}

  static getInstance(): EnvironmentManager {
    if (!EnvironmentManager.instance) {
      EnvironmentManager.instance = new EnvironmentManager();
    }
    return EnvironmentManager.instance;
  }

  /**
   * 获取当前环境配置
   */
  getCurrentEnvironment(): EnvironmentConfig {
    // 检查是否有用户覆盖的环境
    const overrideEnv = localStorage.getItem(STORAGE_KEY);
    if (overrideEnv && ENVIRONMENTS[overrideEnv]) {
      return ENVIRONMENTS[overrideEnv];
    }

    // 使用默认环境检测
    const isProduction = process.env.NODE_ENV === 'production' || import.meta.env.PROD;
    return isProduction ? ENVIRONMENTS.production : ENVIRONMENTS.development;
  }

  /**
   * 获取当前API基础URL
   */
  getCurrentApiBaseURL(): string {
    return this.getCurrentEnvironment().apiBaseURL;
  }

  /**
   * 检查是否有环境覆盖
   */
  hasEnvironmentOverride(): boolean {
    const overrideEnv = localStorage.getItem(STORAGE_KEY);
    if (!overrideEnv) return false;

    const defaultEnv = (process.env.NODE_ENV === 'production' || import.meta.env.PROD) 
      ? 'production' 
      : 'development';
    
    return overrideEnv !== defaultEnv;
  }

  /**
   * 切换环境（仅在开发模式下有效）
   */
  switchEnvironment(envName: string): boolean {
    // 只在开发模式下允许切换
    const isProduction = process.env.NODE_ENV === 'production' || import.meta.env.PROD;
    if (isProduction) {
      console.warn('环境切换功能仅在开发模式下可用');
      return false;
    }

    if (!ENVIRONMENTS[envName]) {
      console.error(`未知的环境: ${envName}`);
      return false;
    }

    localStorage.setItem(STORAGE_KEY, envName);
    
    // 通知所有监听器
    const newConfig = ENVIRONMENTS[envName];
    this.listeners.forEach(listener => listener(newConfig));

    console.log(`环境已切换到: ${newConfig.label} (${newConfig.apiBaseURL})`);
    return true;
  }

  /**
   * 重置到默认环境
   */
  resetToDefault(): void {
    localStorage.removeItem(STORAGE_KEY);
    
    // 通知所有监听器
    const defaultConfig = this.getCurrentEnvironment();
    this.listeners.forEach(listener => listener(defaultConfig));

    console.log(`环境已重置到默认: ${defaultConfig.label}`);
  }

  /**
   * 添加环境变化监听器
   */
  addListener(listener: (config: EnvironmentConfig) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除环境变化监听器
   */
  removeListener(listener: (config: EnvironmentConfig) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 获取所有可用环境
   */
  getAllEnvironments(): EnvironmentConfig[] {
    return Object.values(ENVIRONMENTS);
  }

  /**
   * 检查是否为开发模式
   */
  isDevelopmentMode(): boolean {
    return !(process.env.NODE_ENV === 'production' || import.meta.env.PROD);
  }

  /**
   * 获取环境信息用于调试
   */
  getDebugInfo(): object {
    return {
      currentEnvironment: this.getCurrentEnvironment(),
      hasOverride: this.hasEnvironmentOverride(),
      isDevelopmentMode: this.isDevelopmentMode(),
      allEnvironments: this.getAllEnvironments(),
      storageKey: STORAGE_KEY,
      overrideValue: localStorage.getItem(STORAGE_KEY)
    };
  }
}

// 导出单例实例
export const environmentManager = EnvironmentManager.getInstance();

// 在开发环境下将环境管理器挂载到window对象，方便调试
if (environmentManager.isDevelopmentMode()) {
  (window as any).environmentManager = environmentManager;
  console.log('环境管理器已挂载到 window.environmentManager');
  console.log('可用方法:');
  console.log('- environmentManager.getCurrentEnvironment() - 获取当前环境');
  console.log('- environmentManager.switchEnvironment(envName) - 切换环境');
  console.log('- environmentManager.resetToDefault() - 重置到默认环境');
  console.log('- environmentManager.getDebugInfo() - 获取调试信息');
}
