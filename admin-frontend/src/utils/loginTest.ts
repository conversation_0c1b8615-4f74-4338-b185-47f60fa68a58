// 登录循环问题修复验证工具
// 用于验证登录流程是否正常工作

export const loginTestUtils = {
  // 检查认证状态
  checkAuthState: () => {
    const token = localStorage.getItem('admin_token');
    const user = localStorage.getItem('admin_user');
    const loginTimestamp = localStorage.getItem('admin_login_timestamp');

    const now = Date.now();
    const isRecentLogin = loginTimestamp && (now - parseInt(loginTimestamp) < 10000);

    return {
      hasToken: !!token,
      hasUser: !!user,
      tokenPreview: token ? token.substring(0, 20) + '...' : null,
      userInfo: user ? JSON.parse(user) : null,
      loginTimestamp: loginTimestamp ? parseInt(loginTimestamp) : null,
      isRecentLogin: isRecentLogin,
      timeSinceLogin: loginTimestamp ? now - parseInt(loginTimestamp) : null,
      timestamp: new Date().toISOString()
    };
  },

  // 模拟登录成功状态
  simulateLoginSuccess: () => {
    const mockUser = {
      _id: 'test_user_123',
      email: '<EMAIL>',
      fullName: 'Test Admin',
      role: 'admin'
    };

    const mockToken = 'mock_jwt_token_' + Date.now();
    const loginTimestamp = Date.now();

    localStorage.setItem('admin_token', mockToken);
    localStorage.setItem('admin_user', JSON.stringify(mockUser));
    localStorage.setItem('admin_login_timestamp', loginTimestamp.toString());

    console.log('已模拟登录成功状态:', {
      token: mockToken,
      user: mockUser,
      loginTimestamp: loginTimestamp
    });

    return { token: mockToken, user: mockUser, loginTimestamp };
  },

  // 检查登录循环问题
  checkLoginLoop: () => {
    const currentPath = window.location.pathname;
    const authState = loginTestUtils.checkAuthState();
    
    const hasLoginLoop = currentPath === '/login' && authState.hasToken && authState.hasUser;
    
    return {
      currentPath,
      authState,
      hasLoginLoop,
      message: hasLoginLoop ? 
        '⚠️ 检测到可能的登录循环：有认证信息但在登录页' : 
        '✅ 未检测到登录循环问题'
    };
  },

  // 监控路由变化
  monitorRouteChanges: (duration: number = 10000) => {
    const changes: Array<{path: string, timestamp: string, authState: any}> = [];
    let currentPath = window.location.pathname;
    
    const checkInterval = setInterval(() => {
      const newPath = window.location.pathname;
      if (newPath !== currentPath) {
        currentPath = newPath;
        changes.push({
          path: newPath,
          timestamp: new Date().toISOString(),
          authState: loginTestUtils.checkAuthState()
        });
        console.log('路由变化:', newPath);
      }
    }, 100);
    
    setTimeout(() => {
      clearInterval(checkInterval);
      console.log('路由监控结果:', {
        duration: duration + 'ms',
        totalChanges: changes.length,
        changes: changes
      });
    }, duration);
    
    return {
      message: `已开始监控路由变化，持续${duration/1000}秒`,
      stop: () => clearInterval(checkInterval)
    };
  },

  // 测试自动跳转问题
  testAutoLogout: (duration: number = 15000) => {
    console.log('=== 自动跳转测试开始 ===');

    // 1. 模拟登录成功
    const loginResult = loginTestUtils.simulateLoginSuccess();
    console.log('1. 模拟登录成功:', loginResult);

    // 2. 记录初始状态
    const initialState = loginTestUtils.checkAuthState();
    console.log('2. 初始状态:', initialState);

    // 3. 监控状态变化
    const stateChanges: Array<{time: number, state: any, path: string}> = [];
    let currentPath = window.location.pathname;

    const monitor = setInterval(() => {
      const newPath = window.location.pathname;
      const authState = loginTestUtils.checkAuthState();

      if (newPath !== currentPath || stateChanges.length === 0) {
        currentPath = newPath;
        stateChanges.push({
          time: Date.now() - loginResult.loginTimestamp,
          state: authState,
          path: newPath
        });

        console.log(`状态变化 [${stateChanges.length}]:`, {
          timeSinceLogin: `${(Date.now() - loginResult.loginTimestamp)/1000}秒`,
          path: newPath,
          isRecentLogin: authState.isRecentLogin
        });
      }
    }, 1000);

    // 4. 定时结束监控
    setTimeout(() => {
      clearInterval(monitor);

      const finalState = loginTestUtils.checkAuthState();
      const finalPath = window.location.pathname;

      console.log('=== 自动跳转测试结果 ===');
      console.log('测试时长:', duration/1000 + '秒');
      console.log('状态变化次数:', stateChanges.length);
      console.log('最终路径:', finalPath);
      console.log('最终状态:', finalState);

      const hasAutoLogout = finalPath === '/login' && finalState.hasToken;
      console.log('是否发生自动跳转:', hasAutoLogout ? '是' : '否');

      if (hasAutoLogout) {
        console.warn('⚠️ 检测到自动跳转问题！');
      } else {
        console.log('✅ 未检测到自动跳转问题');
      }

    }, duration);

    return {
      message: `已开始自动跳转测试，持续${duration/1000}秒`,
      loginTimestamp: loginResult.loginTimestamp
    };
  },

  // 测试登录流程完整性
  testLoginFlow: async () => {
    console.log('=== 登录流程测试开始 ===');
    
    // 1. 检查初始状态
    const initialState = loginTestUtils.checkAuthState();
    console.log('1. 初始认证状态:', initialState);
    
    // 2. 清除认证信息
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
    console.log('2. 已清除认证信息');
    
    // 3. 模拟登录成功
    const loginResult = loginTestUtils.simulateLoginSuccess();
    console.log('3. 模拟登录成功:', loginResult);
    
    // 4. 检查登录后状态
    await new Promise(resolve => setTimeout(resolve, 1000));
    const afterLoginState = loginTestUtils.checkAuthState();
    console.log('4. 登录后认证状态:', afterLoginState);
    
    // 5. 检查是否有登录循环
    const loopCheck = loginTestUtils.checkLoginLoop();
    console.log('5. 登录循环检查:', loopCheck);
    
    // 6. 监控路由变化
    const routeMonitor = loginTestUtils.monitorRouteChanges(5000);
    console.log('6. 路由监控:', routeMonitor.message);
    
    console.log('=== 登录流程测试完成 ===');
    
    return {
      initialState,
      loginResult,
      afterLoginState,
      loopCheck,
      success: !loopCheck.hasLoginLoop
    };
  },

  // 检查AuthContext状态
  checkAuthContext: () => {
    // 尝试从window对象获取React DevTools信息
    const reactFiber = (document.getElementById('root') as any)?._reactInternalFiber;
    
    return {
      hasReactFiber: !!reactFiber,
      currentPath: window.location.pathname,
      timestamp: new Date().toISOString(),
      message: '请在浏览器控制台中检查AuthContext状态'
    };
  }
};

// 在开发环境下将测试工具挂载到window对象
if (import.meta.env.DEV) {
  (window as any).loginTest = loginTestUtils;
  console.log('登录测试工具已挂载到 window.loginTest');
  console.log('可用方法:');
  console.log('- loginTest.checkAuthState() - 检查认证状态');
  console.log('- loginTest.simulateLoginSuccess() - 模拟登录成功');
  console.log('- loginTest.checkLoginLoop() - 检查登录循环');
  console.log('- loginTest.monitorRouteChanges(duration) - 监控路由变化');
  console.log('- loginTest.testLoginFlow() - 测试完整登录流程');
  console.log('- loginTest.testAutoLogout(duration) - 测试自动跳转问题');
  console.log('- loginTest.checkAuthContext() - 检查AuthContext状态');
}
