// 性能监控工具类

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'timing' | 'counter' | 'gauge';
  tags?: Record<string, string>;
}

interface PerformanceConfig {
  enableLogging: boolean;
  enableReporting: boolean;
  reportingEndpoint?: string;
  sampleRate: number;
  bufferSize: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private config: PerformanceConfig;
  private timers: Map<string, number> = new Map();

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      enableLogging: process.env.NODE_ENV === 'development',
      enableReporting: process.env.NODE_ENV === 'production',
      sampleRate: 1.0,
      bufferSize: 100,
      ...config,
    };

    // 监听页面性能事件
    this.setupPerformanceObserver();
    this.setupNavigationTiming();
    this.setupResourceTiming();
  }

  // 开始计时
  startTimer(name: string): void {
    this.timers.set(name, performance.now());
  }

  // 结束计时并记录
  endTimer(name: string, tags?: Record<string, string>): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      console.warn(`Timer "${name}" was not started`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.timers.delete(name);
    
    this.recordMetric({
      name,
      value: duration,
      timestamp: Date.now(),
      type: 'timing',
      tags,
    });

    return duration;
  }

  // 记录计数器
  incrementCounter(name: string, value: number = 1, tags?: Record<string, string>): void {
    this.recordMetric({
      name,
      value,
      timestamp: Date.now(),
      type: 'counter',
      tags,
    });
  }

  // 记录仪表盘指标
  recordGauge(name: string, value: number, tags?: Record<string, string>): void {
    this.recordMetric({
      name,
      value,
      timestamp: Date.now(),
      type: 'gauge',
      tags,
    });
  }

  // 记录指标
  private recordMetric(metric: PerformanceMetric): void {
    // 采样率控制
    if (Math.random() > this.config.sampleRate) {
      return;
    }

    this.metrics.push(metric);

    // 日志输出
    if (this.config.enableLogging) {
      console.log(`[Performance] ${metric.name}: ${metric.value}${metric.type === 'timing' ? 'ms' : ''}`, metric.tags);
    }

    // 缓冲区管理
    if (this.metrics.length > this.config.bufferSize) {
      this.flush();
    }
  }

  // 刷新缓冲区
  flush(): void {
    if (this.metrics.length === 0) return;

    if (this.config.enableReporting && this.config.reportingEndpoint) {
      this.sendMetrics(this.metrics);
    }

    this.metrics = [];
  }

  // 发送指标到服务器
  private async sendMetrics(metrics: PerformanceMetric[]): Promise<void> {
    try {
      await fetch(this.config.reportingEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metrics,
          userAgent: navigator.userAgent,
          timestamp: Date.now(),
        }),
      });
    } catch (error) {
      console.error('Failed to send performance metrics:', error);
    }
  }

  // 设置性能观察者
  private setupPerformanceObserver(): void {
    if (!('PerformanceObserver' in window)) return;

    try {
      // 观察长任务
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric({
            name: 'long_task',
            value: entry.duration,
            timestamp: Date.now(),
            type: 'timing',
            tags: {
              entryType: entry.entryType,
            },
          });
        }
      });
      longTaskObserver.observe({ entryTypes: ['longtask'] });

      // 观察布局偏移
      const layoutShiftObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if ('value' in entry) {
            this.recordMetric({
              name: 'layout_shift',
              value: (entry as any).value,
              timestamp: Date.now(),
              type: 'gauge',
            });
          }
        }
      });
      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });

      // 观察最大内容绘制
      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric({
            name: 'largest_contentful_paint',
            value: entry.startTime,
            timestamp: Date.now(),
            type: 'timing',
          });
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // 观察首次输入延迟
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric({
            name: 'first_input_delay',
            value: entry.processingStart - entry.startTime,
            timestamp: Date.now(),
            type: 'timing',
          });
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

    } catch (error) {
      console.warn('Failed to setup PerformanceObserver:', error);
    }
  }

  // 设置导航时间监控
  private setupNavigationTiming(): void {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (!navigation) return;

        // 记录各种导航时间
        const timings = {
          dns_lookup: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcp_connect: navigation.connectEnd - navigation.connectStart,
          ssl_handshake: navigation.connectEnd - navigation.secureConnectionStart,
          ttfb: navigation.responseStart - navigation.requestStart,
          response_time: navigation.responseEnd - navigation.responseStart,
          dom_parse: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          page_load: navigation.loadEventEnd - navigation.loadEventStart,
          total_load_time: navigation.loadEventEnd - navigation.navigationStart,
        };

        Object.entries(timings).forEach(([name, value]) => {
          if (value > 0) {
            this.recordMetric({
              name: `navigation_${name}`,
              value,
              timestamp: Date.now(),
              type: 'timing',
            });
          }
        });
      }, 0);
    });
  }

  // 设置资源时间监控
  private setupResourceTiming(): void {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
        
        const resourceStats = {
          total_resources: resources.length,
          total_size: 0,
          avg_load_time: 0,
          slow_resources: 0,
        };

        let totalLoadTime = 0;
        
        resources.forEach((resource) => {
          const loadTime = resource.responseEnd - resource.startTime;
          totalLoadTime += loadTime;
          
          if ('transferSize' in resource) {
            resourceStats.total_size += resource.transferSize || 0;
          }
          
          // 标记慢资源（超过1秒）
          if (loadTime > 1000) {
            resourceStats.slow_resources++;
          }
        });

        resourceStats.avg_load_time = totalLoadTime / resources.length;

        Object.entries(resourceStats).forEach(([name, value]) => {
          this.recordMetric({
            name: `resource_${name}`,
            value,
            timestamp: Date.now(),
            type: 'gauge',
          });
        });
      }, 1000);
    });
  }

  // 获取内存使用情况
  getMemoryUsage(): Record<string, number> | null {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
      };
    }
    return null;
  }

  // 记录内存使用情况
  recordMemoryUsage(): void {
    const memory = this.getMemoryUsage();
    if (memory) {
      Object.entries(memory).forEach(([name, value]) => {
        this.recordMetric({
          name: `memory_${name}`,
          value,
          timestamp: Date.now(),
          type: 'gauge',
        });
      });
    }
  }

  // 获取所有指标
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  // 清除所有指标
  clearMetrics(): void {
    this.metrics = [];
  }

  // 销毁监控器
  destroy(): void {
    this.flush();
    this.timers.clear();
    this.metrics = [];
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();

// React Hook for performance monitoring
export const usePerformanceMonitor = () => {
  return {
    startTimer: performanceMonitor.startTimer.bind(performanceMonitor),
    endTimer: performanceMonitor.endTimer.bind(performanceMonitor),
    incrementCounter: performanceMonitor.incrementCounter.bind(performanceMonitor),
    recordGauge: performanceMonitor.recordGauge.bind(performanceMonitor),
    getMemoryUsage: performanceMonitor.getMemoryUsage.bind(performanceMonitor),
    recordMemoryUsage: performanceMonitor.recordMemoryUsage.bind(performanceMonitor),
  };
};

// 装饰器用于自动监控函数执行时间
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const metricName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      performanceMonitor.startTimer(metricName);
      
      try {
        const result = originalMethod.apply(this, args);
        
        // 处理 Promise
        if (result && typeof result.then === 'function') {
          return result
            .then((value: any) => {
              performanceMonitor.endTimer(metricName, { status: 'success' });
              return value;
            })
            .catch((error: any) => {
              performanceMonitor.endTimer(metricName, { status: 'error' });
              throw error;
            });
        }
        
        performanceMonitor.endTimer(metricName, { status: 'success' });
        return result;
      } catch (error) {
        performanceMonitor.endTimer(metricName, { status: 'error' });
        throw error;
      }
    };

    return descriptor;
  };
}

export default PerformanceMonitor;
