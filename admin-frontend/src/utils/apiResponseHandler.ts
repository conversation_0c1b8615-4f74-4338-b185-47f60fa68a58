/**
 * API响应处理器
 * 统一处理API响应，消除重复的响应处理逻辑
 */

import { ApiResponse } from '@/types/common';

/**
 * API响应处理选项
 */
export interface ResponseHandlerOptions {
  /** 成功时的自定义消息 */
  successMessage?: string;
  /** 失败时的自定义消息 */
  errorMessage?: string;
  /** 是否在成功时显示消息 */
  showSuccessMessage?: boolean;
  /** 是否在失败时显示消息 */
  showErrorMessage?: boolean;
  /** 自定义成功处理函数 */
  onSuccess?: (data: any) => void;
  /** 自定义错误处理函数 */
  onError?: (error: Error) => void;
}

/**
 * API响应处理结果
 */
export interface ResponseHandlerResult<T = any> {
  success: boolean;
  data?: T;
  error?: Error;
  message?: string;
}

/**
 * 通用API响应处理器类
 * 提供统一的响应处理逻辑，减少重复代码
 */
export class ApiResponseHandler {
  /**
   * 处理API响应的通用方法
   * @param response API响应对象
   * @param options 处理选项
   * @returns 处理结果
   */
  static handle<T>(
    response: ApiResponse<T>,
    options: ResponseHandlerOptions = {}
  ): ResponseHandlerResult<T> {
    const {
      successMessage,
      errorMessage,
      showSuccessMessage = false,
      showErrorMessage = true,
      onSuccess,
      onError
    } = options;

    try {
      if (response.success && response.data !== undefined) {
        // 成功处理
        const result: ResponseHandlerResult<T> = {
          success: true,
          data: response.data,
          message: successMessage || response.message || '操作成功'
        };

        // 显示成功消息
        if (showSuccessMessage && result.message) {
          this.showMessage(result.message, 'success');
        }

        // 执行自定义成功处理
        if (onSuccess) {
          onSuccess(response.data);
        }

        return result;
      } else {
        // 失败处理
        const errorMsg = errorMessage || response.message || '操作失败';
        const error = new Error(errorMsg);
        
        const result: ResponseHandlerResult<T> = {
          success: false,
          error,
          message: errorMsg
        };

        // 显示错误消息
        if (showErrorMessage) {
          this.showMessage(errorMsg, 'error');
        }

        // 执行自定义错误处理
        if (onError) {
          onError(error);
        }

        return result;
      }
    } catch (error) {
      // 处理异常
      const errorMsg = errorMessage || '处理响应时发生错误';
      const handlerError = error instanceof Error ? error : new Error(errorMsg);
      
      const result: ResponseHandlerResult<T> = {
        success: false,
        error: handlerError,
        message: handlerError.message
      };

      if (showErrorMessage) {
        this.showMessage(handlerError.message, 'error');
      }

      if (onError) {
        onError(handlerError);
      }

      return result;
    }
  }

  /**
   * 处理API响应并返回数据（成功时）或抛出错误（失败时）
   * @param response API响应对象
   * @param options 处理选项
   * @returns 响应数据
   * @throws 当响应失败时抛出错误
   */
  static handleOrThrow<T>(
    response: ApiResponse<T>,
    options: ResponseHandlerOptions = {}
  ): T {
    const result = this.handle(response, options);
    
    if (result.success && result.data !== undefined) {
      return result.data;
    } else {
      throw result.error || new Error(result.message || '操作失败');
    }
  }

  /**
   * 处理分页响应
   * @param response 分页响应对象
   * @param options 处理选项
   * @returns 分页数据
   */
  static handlePagination<T>(
    response: ApiResponse<{ items: T[]; total: number; page: number; pageSize: number }>,
    options: ResponseHandlerOptions = {}
  ): ResponseHandlerResult<{ items: T[]; total: number; page: number; pageSize: number }> {
    return this.handle(response, {
      ...options,
      errorMessage: options.errorMessage || '获取数据失败'
    });
  }

  /**
   * 处理列表响应
   * @param response 列表响应对象
   * @param options 处理选项
   * @returns 列表数据
   */
  static handleList<T>(
    response: ApiResponse<T[]>,
    options: ResponseHandlerOptions = {}
  ): ResponseHandlerResult<T[]> {
    return this.handle(response, {
      ...options,
      errorMessage: options.errorMessage || '获取列表失败'
    });
  }

  /**
   * 处理创建操作响应
   * @param response 创建响应对象
   * @param options 处理选项
   * @returns 创建结果
   */
  static handleCreate<T>(
    response: ApiResponse<T>,
    options: ResponseHandlerOptions = {}
  ): ResponseHandlerResult<T> {
    return this.handle(response, {
      showSuccessMessage: true,
      successMessage: '创建成功',
      errorMessage: '创建失败',
      ...options
    });
  }

  /**
   * 处理更新操作响应
   * @param response 更新响应对象
   * @param options 处理选项
   * @returns 更新结果
   */
  static handleUpdate<T>(
    response: ApiResponse<T>,
    options: ResponseHandlerOptions = {}
  ): ResponseHandlerResult<T> {
    return this.handle(response, {
      showSuccessMessage: true,
      successMessage: '更新成功',
      errorMessage: '更新失败',
      ...options
    });
  }

  /**
   * 处理删除操作响应
   * @param response 删除响应对象
   * @param options 处理选项
   * @returns 删除结果
   */
  static handleDelete(
    response: ApiResponse<void>,
    options: ResponseHandlerOptions = {}
  ): ResponseHandlerResult<void> {
    return this.handle(response, {
      showSuccessMessage: true,
      successMessage: '删除成功',
      errorMessage: '删除失败',
      ...options
    });
  }

  /**
   * 批量处理多个API响应
   * @param responses 响应数组
   * @param options 处理选项
   * @returns 批量处理结果
   */
  static handleBatch<T>(
    responses: ApiResponse<T>[],
    options: ResponseHandlerOptions = {}
  ): ResponseHandlerResult<T[]> {
    const results: T[] = [];
    const errors: Error[] = [];

    for (const response of responses) {
      const result = this.handle(response, { ...options, showErrorMessage: false });
      
      if (result.success && result.data !== undefined) {
        results.push(result.data);
      } else if (result.error) {
        errors.push(result.error);
      }
    }

    if (errors.length === 0) {
      return {
        success: true,
        data: results,
        message: options.successMessage || `成功处理${results.length}项`
      };
    } else {
      const errorMsg = options.errorMessage || `处理失败：${errors.length}项出错`;
      
      if (options.showErrorMessage !== false) {
        this.showMessage(errorMsg, 'error');
      }

      return {
        success: false,
        error: new Error(errorMsg),
        message: errorMsg
      };
    }
  }

  /**
   * 显示消息（需要根据实际UI框架实现）
   * @param message 消息内容
   * @param type 消息类型
   */
  private static showMessage(message: string, type: 'success' | 'error' | 'warning' | 'info') {
    // 这里应该根据实际使用的UI框架来实现消息显示
    // 例如：使用 antd 的 message，或者 element-ui 的 Message 等
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // 示例：如果使用的是浏览器原生API
    if (typeof window !== 'undefined') {
      // 可以触发自定义事件，让组件监听并显示消息
      window.dispatchEvent(new CustomEvent('show-message', {
        detail: { message, type }
      }));
    }
  }
}

/**
 * 便捷的响应处理函数
 * 为常见场景提供简化的API
 */
export const responseHandlers = {
  /**
   * 处理获取数据的响应
   */
  get: <T>(response: ApiResponse<T>, errorMessage?: string) =>
    ApiResponseHandler.handleOrThrow(response, { errorMessage }),

  /**
   * 处理创建数据的响应
   */
  create: <T>(response: ApiResponse<T>, successMessage?: string) =>
    ApiResponseHandler.handleOrThrow(response, { 
      successMessage: successMessage || '创建成功',
      showSuccessMessage: true 
    }),

  /**
   * 处理更新数据的响应
   */
  update: <T>(response: ApiResponse<T>, successMessage?: string) =>
    ApiResponseHandler.handleOrThrow(response, { 
      successMessage: successMessage || '更新成功',
      showSuccessMessage: true 
    }),

  /**
   * 处理删除数据的响应
   */
  delete: (response: ApiResponse<void>, successMessage?: string) =>
    ApiResponseHandler.handleOrThrow(response, { 
      successMessage: successMessage || '删除成功',
      showSuccessMessage: true 
    }),

  /**
   * 处理列表数据的响应
   */
  list: <T>(response: ApiResponse<T[]>, errorMessage?: string) =>
    ApiResponseHandler.handleOrThrow(response, { 
      errorMessage: errorMessage || '获取列表失败' 
    })
};
