/**
 * 存储管理器
 * 统一管理localStorage操作，提供类型安全和错误处理
 */

/**
 * 存储键名常量
 */
export const STORAGE_KEYS = {
  // 认证相关
  ADMIN_TOKEN: 'admin_token',
  ADMIN_USER: 'admin_user',
  ADMIN_LOGIN_TIMESTAMP: 'admin_login_timestamp',
  
  // 用户偏好设置
  THEME: 'theme',
  LANGUAGE: 'language',
  SIDEBAR_COLLAPSED: 'sidebar_collapsed',
  
  // 表格设置
  TABLE_PAGE_SIZE: 'table_page_size',
  TABLE_COLUMNS: 'table_columns',
  
  // 其他设置
  LAST_VISITED_PAGE: 'last_visited_page',
  NOTIFICATION_SETTINGS: 'notification_settings'
} as const;

/**
 * 存储值类型定义
 */
export type StorageValue = string | number | boolean | object | null;

/**
 * 存储管理器类
 * 提供类型安全的localStorage操作
 */
export class StorageManager {
  /**
   * 设置存储值
   * @param key 存储键
   * @param value 存储值
   * @returns 是否设置成功
   */
  static setItem<T extends StorageValue>(key: string, value: T): boolean {
    try {
      if (typeof window === 'undefined') {
        console.warn('localStorage不可用：非浏览器环境');
        return false;
      }

      if (value === null || value === undefined) {
        this.removeItem(key);
        return true;
      }

      const serializedValue = typeof value === 'string' 
        ? value 
        : JSON.stringify(value);
      
      localStorage.setItem(key, serializedValue);
      return true;
    } catch (error) {
      console.error(`设置localStorage失败 [${key}]:`, error);
      return false;
    }
  }

  /**
   * 获取存储值
   * @param key 存储键
   * @param defaultValue 默认值
   * @returns 存储值或默认值
   */
  static getItem<T extends StorageValue>(key: string, defaultValue?: T): T | null {
    try {
      if (typeof window === 'undefined') {
        return defaultValue ?? null;
      }

      const item = localStorage.getItem(key);
      
      if (item === null) {
        return defaultValue ?? null;
      }

      // 尝试解析JSON，如果失败则返回原始字符串
      try {
        return JSON.parse(item) as T;
      } catch {
        return item as T;
      }
    } catch (error) {
      console.error(`获取localStorage失败 [${key}]:`, error);
      return defaultValue ?? null;
    }
  }

  /**
   * 移除存储值
   * @param key 存储键
   * @returns 是否移除成功
   */
  static removeItem(key: string): boolean {
    try {
      if (typeof window === 'undefined') {
        return false;
      }

      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`移除localStorage失败 [${key}]:`, error);
      return false;
    }
  }

  /**
   * 清空所有存储
   * @returns 是否清空成功
   */
  static clear(): boolean {
    try {
      if (typeof window === 'undefined') {
        return false;
      }

      localStorage.clear();
      return true;
    } catch (error) {
      console.error('清空localStorage失败:', error);
      return false;
    }
  }

  /**
   * 检查存储键是否存在
   * @param key 存储键
   * @returns 是否存在
   */
  static hasItem(key: string): boolean {
    try {
      if (typeof window === 'undefined') {
        return false;
      }

      return localStorage.getItem(key) !== null;
    } catch (error) {
      console.error(`检查localStorage失败 [${key}]:`, error);
      return false;
    }
  }

  /**
   * 获取所有存储键
   * @returns 存储键数组
   */
  static getAllKeys(): string[] {
    try {
      if (typeof window === 'undefined') {
        return [];
      }

      const keys: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          keys.push(key);
        }
      }
      return keys;
    } catch (error) {
      console.error('获取localStorage键列表失败:', error);
      return [];
    }
  }

  /**
   * 获取存储使用情况
   * @returns 存储使用信息
   */
  static getStorageInfo(): {
    used: number;
    total: number;
    available: number;
    percentage: number;
  } {
    try {
      if (typeof window === 'undefined') {
        return { used: 0, total: 0, available: 0, percentage: 0 };
      }

      let used = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }

      // 大多数浏览器localStorage限制为5MB
      const total = 5 * 1024 * 1024; // 5MB in bytes
      const available = total - used;
      const percentage = (used / total) * 100;

      return {
        used,
        total,
        available,
        percentage: Math.round(percentage * 100) / 100
      };
    } catch (error) {
      console.error('获取存储信息失败:', error);
      return { used: 0, total: 0, available: 0, percentage: 0 };
    }
  }
}

/**
 * 认证相关存储操作
 */
export const authStorage = {
  /**
   * 设置认证token
   */
  setToken: (token: string): boolean => {
    return StorageManager.setItem(STORAGE_KEYS.ADMIN_TOKEN, token);
  },

  /**
   * 获取认证token
   */
  getToken: (): string | null => {
    return StorageManager.getItem<string>(STORAGE_KEYS.ADMIN_TOKEN);
  },

  /**
   * 移除认证token
   */
  removeToken: (): boolean => {
    return StorageManager.removeItem(STORAGE_KEYS.ADMIN_TOKEN);
  },

  /**
   * 设置用户信息
   */
  setUser: (user: object): boolean => {
    return StorageManager.setItem(STORAGE_KEYS.ADMIN_USER, user);
  },

  /**
   * 获取用户信息
   */
  getUser: <T = any>(): T | null => {
    return StorageManager.getItem<T>(STORAGE_KEYS.ADMIN_USER);
  },

  /**
   * 移除用户信息
   */
  removeUser: (): boolean => {
    return StorageManager.removeItem(STORAGE_KEYS.ADMIN_USER);
  },

  /**
   * 设置登录时间戳
   */
  setLoginTimestamp: (timestamp: number = Date.now()): boolean => {
    return StorageManager.setItem(STORAGE_KEYS.ADMIN_LOGIN_TIMESTAMP, timestamp);
  },

  /**
   * 获取登录时间戳
   */
  getLoginTimestamp: (): number | null => {
    return StorageManager.getItem<number>(STORAGE_KEYS.ADMIN_LOGIN_TIMESTAMP);
  },

  /**
   * 清除所有认证信息
   */
  clearAuth: (): boolean => {
    const results = [
      StorageManager.removeItem(STORAGE_KEYS.ADMIN_TOKEN),
      StorageManager.removeItem(STORAGE_KEYS.ADMIN_USER),
      StorageManager.removeItem(STORAGE_KEYS.ADMIN_LOGIN_TIMESTAMP)
    ];
    return results.every(result => result);
  },

  /**
   * 检查是否已认证
   */
  isAuthenticated: (): boolean => {
    const token = authStorage.getToken();
    const user = authStorage.getUser();
    return !!(token && user);
  }
};

/**
 * 用户偏好设置存储操作
 */
export const preferencesStorage = {
  /**
   * 设置主题
   */
  setTheme: (theme: 'light' | 'dark'): boolean => {
    return StorageManager.setItem(STORAGE_KEYS.THEME, theme);
  },

  /**
   * 获取主题
   */
  getTheme: (): 'light' | 'dark' => {
    return StorageManager.getItem<'light' | 'dark'>(STORAGE_KEYS.THEME, 'light') || 'light';
  },

  /**
   * 设置语言
   */
  setLanguage: (language: string): boolean => {
    return StorageManager.setItem(STORAGE_KEYS.LANGUAGE, language);
  },

  /**
   * 获取语言
   */
  getLanguage: (): string => {
    return StorageManager.getItem<string>(STORAGE_KEYS.LANGUAGE, 'zh-CN') || 'zh-CN';
  },

  /**
   * 设置侧边栏折叠状态
   */
  setSidebarCollapsed: (collapsed: boolean): boolean => {
    return StorageManager.setItem(STORAGE_KEYS.SIDEBAR_COLLAPSED, collapsed);
  },

  /**
   * 获取侧边栏折叠状态
   */
  getSidebarCollapsed: (): boolean => {
    return StorageManager.getItem<boolean>(STORAGE_KEYS.SIDEBAR_COLLAPSED, false) || false;
  }
};

/**
 * 便捷的存储操作函数
 */
export const storage = {
  set: StorageManager.setItem,
  get: StorageManager.getItem,
  remove: StorageManager.removeItem,
  clear: StorageManager.clear,
  has: StorageManager.hasItem,
  keys: StorageManager.getAllKeys,
  info: StorageManager.getStorageInfo
};
