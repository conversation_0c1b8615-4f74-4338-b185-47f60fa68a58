// 认证测试工具
// 用于测试修复后的认证逻辑是否正常工作

export const authTestUtils = {
  // 模拟token过期
  simulateTokenExpiry: () => {
    // 设置一个无效的token来模拟过期
    localStorage.setItem('admin_token', 'expired_token_for_testing');
    localStorage.setItem('admin_user', JSON.stringify({
      _id: 'test_user_id',
      email: '<EMAIL>',
      fullName: 'Test User',
      role: 'admin'
    }));
    console.log('已设置过期token，刷新页面或点击导航测试认证错误处理');
  },

  // 清除认证信息
  clearAuth: () => {
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
    console.log('已清除认证信息');
  },

  // 设置有效的测试token
  setValidTestToken: () => {
    localStorage.setItem('admin_token', 'valid_test_token_123');
    localStorage.setItem('admin_user', JSON.stringify({
      _id: 'test_user_id',
      email: '<EMAIL>',
      fullName: 'Test User',
      role: 'admin'
    }));
    console.log('已设置有效测试token');
  },

  // 检查当前认证状态
  checkAuthStatus: () => {
    const token = localStorage.getItem('admin_token');
    const user = localStorage.getItem('admin_user');
    console.log('当前认证状态:', {
      hasToken: !!token,
      token: token?.substring(0, 20) + '...',
      hasUser: !!user,
      user: user ? JSON.parse(user) : null
    });
  },

  // 测试API请求
  testApiRequest: async () => {
    try {
      const response = await fetch('/api/admin/auth/me', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('API请求结果:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.log('错误响应内容:', errorText);
      } else {
        const data = await response.json();
        console.log('成功响应数据:', data);
      }
    } catch (error) {
      console.error('API请求失败:', error);
    }
  }
};

// 在开发环境下将测试工具挂载到window对象
if (import.meta.env.DEV) {
  (window as any).authTest = authTestUtils;
  console.log('认证测试工具已挂载到 window.authTest');
  console.log('可用方法:');
  console.log('- authTest.simulateTokenExpiry() - 模拟token过期');
  console.log('- authTest.clearAuth() - 清除认证信息');
  console.log('- authTest.setValidTestToken() - 设置有效测试token');
  console.log('- authTest.checkAuthStatus() - 检查当前认证状态');
  console.log('- authTest.testApiRequest() - 测试API请求');
}
