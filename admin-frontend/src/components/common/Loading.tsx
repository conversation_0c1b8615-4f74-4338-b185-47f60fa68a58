import React from 'react';
import clsx from 'clsx';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  text = '加载中...',
  fullScreen = false,
  className,
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const LoadingSpinner = () => (
    <div className="flex flex-col items-center justify-center space-y-3">
      <div
        className={clsx(
          'spinner border-4',
          sizeClasses[size],
          className
        )}
      />
      {text && (
        <p className={clsx('text-gray-600', textSizeClasses[size])}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center p-8">
      <LoadingSpinner />
    </div>
  );
};

// 页面级加载组件
export const PageLoading: React.FC<{ text?: string }> = ({ text = '页面加载中...' }) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <Loading size="lg" text={text} />
  </div>
);

// 内容区域加载组件
export const ContentLoading: React.FC<{ text?: string }> = ({ text = '内容加载中...' }) => (
  <div className="flex items-center justify-center py-12">
    <Loading size="md" text={text} />
  </div>
);

// 按钮加载组件
export const ButtonLoading: React.FC = () => (
  <div className="flex items-center space-x-2">
    <div className="spinner h-4 w-4 border-2" />
    <span>处理中...</span>
  </div>
);

// 表格加载组件
export const TableLoading: React.FC<{ rows?: number }> = ({ rows = 5 }) => (
  <div className="animate-pulse">
    {Array.from({ length: rows }).map((_, index) => (
      <div key={index} className="border-b border-gray-200 py-4">
        <div className="flex space-x-4">
          <div className="h-4 bg-gray-300 rounded w-1/4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/3"></div>
          <div className="h-4 bg-gray-300 rounded w-1/4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/6"></div>
        </div>
      </div>
    ))}
  </div>
);

export default Loading;
