import React, { useState, useEffect } from 'react';
import { ENV_INFO } from '@/config/api';
import { environmentManager, ENVIRONMENTS, type EnvironmentConfig } from '@/utils/environmentManager';
import { recreateApiInstance } from '@/services/api';

interface EnvironmentIndicatorProps {
  className?: string;
  showDetails?: boolean;
  allowSwitch?: boolean; // 是否允许切换环境
}

/**
 * 环境指示器组件
 * 显示当前运行环境信息，帮助区分开发和生产环境
 * 支持在开发环境下切换不同的API端点
 */
const EnvironmentIndicator: React.FC<EnvironmentIndicatorProps> = ({
  className = '',
  showDetails = false,
  allowSwitch = false
}) => {
  const { IS_PRODUCTION, IS_DEVELOPMENT, API_CONFIG, NODE_ENV } = ENV_INFO;

  // 当前环境状态
  const [currentEnv, setCurrentEnv] = useState<string>(() => {
    return environmentManager.getCurrentEnvironment().name;
  });

  const [showDropdown, setShowDropdown] = useState(false);
  const [isOverrideActive, setIsOverrideActive] = useState(false);

  // 检查是否有环境覆盖
  useEffect(() => {
    setIsOverrideActive(environmentManager.hasEnvironmentOverride());
  }, []);

  // 监听环境变化
  useEffect(() => {
    const handleEnvironmentChange = (newEnvConfig: EnvironmentConfig) => {
      setCurrentEnv(newEnvConfig.name);
      setIsOverrideActive(environmentManager.hasEnvironmentOverride());
    };

    environmentManager.addListener(handleEnvironmentChange);

    return () => {
      environmentManager.removeListener(handleEnvironmentChange);
    };
  }, []);

  // 获取当前环境配置
  const envConfig = environmentManager.getCurrentEnvironment();

  // 切换环境
  const switchEnvironment = (envName: string) => {
    if (environmentManager.switchEnvironment(envName)) {
      setCurrentEnv(envName);
      setIsOverrideActive(envName !== (IS_PRODUCTION ? 'production' : 'development'));
      setShowDropdown(false);

      // 重新创建API实例以应用新配置
      recreateApiInstance();

      // 提示用户刷新页面以完全应用新配置
      if (confirm(`已切换到${ENVIRONMENTS[envName].label}，是否刷新页面以完全应用新配置？`)) {
        window.location.reload();
      }
    }
  };

  // 重置到默认环境
  const resetToDefault = () => {
    environmentManager.resetToDefault();
    const defaultEnv = IS_PRODUCTION ? 'production' : 'development';
    setCurrentEnv(defaultEnv);
    setIsOverrideActive(false);
    setShowDropdown(false);

    // 重新创建API实例
    recreateApiInstance();

    if (confirm('已重置到默认环境，是否刷新页面以完全应用配置？')) {
      window.location.reload();
    }
  };

  // 只在开发环境或需要显示详情时显示
  if (IS_PRODUCTION && !showDetails && !allowSwitch) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* 环境标识徽章 */}
      <div
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${envConfig.color} ${allowSwitch && IS_DEVELOPMENT ? 'cursor-pointer hover:opacity-80' : ''}`}
        onClick={() => allowSwitch && IS_DEVELOPMENT && setShowDropdown(!showDropdown)}
        title={allowSwitch && IS_DEVELOPMENT ? '点击切换环境' : envConfig.description}
      >
        <div className={`w-2 h-2 rounded-full mr-1.5 ${envConfig.dotColor}`} />
        {envConfig.label}
        {isOverrideActive && (
          <span className="ml-1 text-orange-600" title="环境已被覆盖">⚠</span>
        )}
        {allowSwitch && IS_DEVELOPMENT && (
          <svg className="ml-1 w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </div>

      {/* 环境切换下拉菜单 */}
      {showDropdown && allowSwitch && IS_DEVELOPMENT && (
        <div className="absolute bottom-full left-0 mb-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-3 border-b border-gray-200">
            <h4 className="text-sm font-medium text-gray-900">切换环境</h4>
            <p className="text-xs text-gray-500 mt-1">仅在开发环境下可用</p>
          </div>
          <div className="p-2">
            {Object.values(ENVIRONMENTS).map((env) => (
              <button
                key={env.name}
                onClick={() => switchEnvironment(env.name)}
                className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-50 flex items-center justify-between ${
                  currentEnv === env.name ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                }`}
              >
                <div className="flex items-center">
                  <div className={`w-2 h-2 rounded-full mr-2 ${env.dotColor}`} />
                  <div>
                    <div className="font-medium">{env.label}</div>
                    <div className="text-xs text-gray-500">{env.apiBaseURL}</div>
                  </div>
                </div>
                {currentEnv === env.name && (
                  <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            ))}
          </div>
          {isOverrideActive && (
            <div className="p-2 border-t border-gray-200">
              <button
                onClick={resetToDefault}
                className="w-full text-left px-3 py-2 text-sm text-orange-600 hover:bg-orange-50 rounded-md"
              >
                🔄 重置到默认环境
              </button>
            </div>
          )}
        </div>
      )}

      {/* 详细信息（仅在开发环境或明确要求时显示） */}
      {(IS_DEVELOPMENT || showDetails) && (
        <div className="mt-2 text-xs text-gray-500 space-y-1">
          <div>NODE_ENV: {NODE_ENV}</div>
          <div>API: {envConfig.apiBaseURL}</div>
          <div>环境标识: {import.meta.env.VITE_ENV_LABEL || 'unknown'}</div>
          {isOverrideActive && (
            <div className="text-orange-600">⚠ 环境已被手动覆盖</div>
          )}
        </div>
      )}

      {/* 点击外部关闭下拉菜单 */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};

export default EnvironmentIndicator;
