import React, { Component, ErrorInfo, ReactNode } from 'react';
import Button from './Button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // 调用外部错误处理函数
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 这里可以将错误信息发送到错误报告服务
    // reportErrorToService(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义的 fallback UI，则使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认的错误 UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            <div className="text-center">
              <div className="mx-auto h-24 w-24 text-red-500">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                出现了一些问题
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                抱歉，应用程序遇到了意外错误。请尝试刷新页面或联系技术支持。
              </p>
              
              {/* 开发环境下显示错误详情 */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md text-left">
                  <h3 className="text-sm font-medium text-red-800 mb-2">错误详情：</h3>
                  <pre className="text-xs text-red-700 whitespace-pre-wrap overflow-auto max-h-32">
                    {this.state.error.toString()}
                  </pre>
                  {this.state.errorInfo && (
                    <details className="mt-2">
                      <summary className="text-xs text-red-600 cursor-pointer">
                        组件堆栈
                      </summary>
                      <pre className="text-xs text-red-600 whitespace-pre-wrap overflow-auto max-h-32 mt-1">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </details>
                  )}
                </div>
              )}
            </div>
            
            <div className="space-y-3">
              <Button
                variant="primary"
                fullWidth
                onClick={this.handleRetry}
              >
                重试
              </Button>
              <Button
                variant="secondary"
                fullWidth
                onClick={this.handleReload}
              >
                刷新页面
              </Button>
              <Button
                variant="secondary"
                fullWidth
                onClick={() => window.history.back()}
              >
                返回上一页
              </Button>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-gray-500">
                如果问题持续存在，请联系技术支持
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 函数式组件版本的错误边界（使用 react-error-boundary 库的模式）
export const FunctionalErrorBoundary: React.FC<{
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}> = ({ children, fallback, onError }) => {
  return (
    <ErrorBoundary fallback={fallback} onError={onError}>
      {children}
    </ErrorBoundary>
  );
};

// 简化的错误显示组件
export const ErrorFallback: React.FC<{
  error?: Error;
  resetError?: () => void;
  title?: string;
  message?: string;
}> = ({ 
  error, 
  resetError, 
  title = "出现了错误", 
  message = "抱歉，发生了意外错误。请重试或刷新页面。" 
}) => {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="w-16 h-16 text-red-500 mb-4">
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-sm text-gray-600 mb-4 max-w-md">{message}</p>
      
      {process.env.NODE_ENV === 'development' && error && (
        <details className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-left max-w-md">
          <summary className="text-sm text-red-800 cursor-pointer">错误详情</summary>
          <pre className="text-xs text-red-700 mt-2 whitespace-pre-wrap overflow-auto max-h-32">
            {error.toString()}
          </pre>
        </details>
      )}
      
      {resetError && (
        <Button variant="primary" onClick={resetError}>
          重试
        </Button>
      )}
    </div>
  );
};

// 页面级错误边界
export const PageErrorBoundary: React.FC<{
  children: ReactNode;
  pageName?: string;
}> = ({ children, pageName }) => {
  const handleError = (error: Error, errorInfo: ErrorInfo) => {
    // 记录页面级错误
    console.error(`Page Error in ${pageName || 'Unknown Page'}:`, error, errorInfo);
    
    // 这里可以发送错误报告到监控服务
    // sendErrorReport({
    //   page: pageName,
    //   error: error.toString(),
    //   stack: error.stack,
    //   componentStack: errorInfo.componentStack,
    //   timestamp: new Date().toISOString(),
    // });
  };

  return (
    <ErrorBoundary
      onError={handleError}
      fallback={
        <div className="p-8">
          <ErrorFallback
            title={`${pageName || '页面'}加载失败`}
            message="页面遇到了问题，请尝试刷新或返回上一页。"
            resetError={() => window.location.reload()}
          />
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundary;
