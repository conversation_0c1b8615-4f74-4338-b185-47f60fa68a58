import React, { useState, useEffect } from 'react';
import { VersionCheckResponse } from '@/types/system';
import Modal, { ModalBody, ModalFooter } from '@/components/common/Modal';
import Button from '@/components/common/Button';

interface UpdatePromptProps {
  /** 版本检查结果 */
  versionInfo: VersionCheckResponse;
  /** 是否显示提示 */
  isOpen: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 更新回调 */
  onUpdate?: () => void;
  /** 稍后提醒回调 */
  onLater?: () => void;
  /** 跳过版本回调 */
  onSkip?: () => void;
}

const SKIPPED_VERSION_KEY = 'skipped_version';
const LAST_SHOWN_VERSION_KEY = 'last_shown_update_version';

/**
 * 版本更新提示组件
 * 支持强制更新和可选更新两种模式
 */
const UpdatePrompt: React.FC<UpdatePromptProps> = ({
  versionInfo,
  isOpen,
  onClose,
  onUpdate,
  onLater,
  onSkip,
}) => {
  const [countdown, setCountdown] = useState<number | null>(null);

  const isForceUpdate = versionInfo.updateType === 'force';
  const isBelowMinimum = versionInfo.versionInfo?.isBelowMinimum ?? false;

  // 强制更新倒计时
  useEffect(() => {
    if (isForceUpdate && isOpen) {
      setCountdown(30); // 30秒倒计时
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev === null || prev <= 1) {
            clearInterval(timer);
            handleUpdate();
            return null;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isForceUpdate, isOpen]);

  // 处理更新
  const handleUpdate = () => {
    if (onUpdate) {
      onUpdate();
    } else {
      // 默认行为：打开下载链接
      window.open(versionInfo.downloadUrl, '_blank');
    }
    onClose();
  };

  // 处理稍后提醒
  const handleLater = () => {
    if (onLater) {
      onLater();
    }
    onClose();
  };

  // 处理跳过版本
  const handleSkip = () => {
    // 记录跳过的版本
    localStorage.setItem(SKIPPED_VERSION_KEY, versionInfo.latestVersion);
    
    if (onSkip) {
      onSkip();
    }
    onClose();
  };

  // 获取标题
  const getTitle = () => {
    if (isBelowMinimum) {
      return '必须更新';
    }
    if (isForceUpdate) {
      return '强制更新';
    }
    return '发现新版本';
  };

  // 获取图标
  const getIcon = () => {
    if (isBelowMinimum || isForceUpdate) {
      return (
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <svg
            className="h-6 w-6 text-red-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
      );
    }

    return (
      <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
        <svg
          className="h-6 w-6 text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
          />
        </svg>
      </div>
    );
  };

  // 获取描述文本
  const getDescription = () => {
    if (isBelowMinimum) {
      return `您当前使用的版本 ${versionInfo.currentVersion} 已不再受支持，必须更新到 ${versionInfo.latestVersion} 或更高版本才能继续使用。`;
    }
    
    return `发现新版本 ${versionInfo.latestVersion}，当前版本 ${versionInfo.currentVersion}。`;
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={isForceUpdate ? undefined : onClose}
      title={getTitle()}
      size="md"
    >
      <ModalBody>
        <div className="text-center">
          {/* 图标 */}
          {getIcon()}

          {/* 标题和描述 */}
          <div className="mt-3">
            <h3 className="text-lg font-medium text-gray-900">
              {getTitle()}
            </h3>
            <div className="mt-2">
              <p className="text-sm text-gray-500">
                {getDescription()}
              </p>
            </div>
          </div>

          {/* 更新消息 */}
          {versionInfo.updateMessage && (
            <div className="mt-4 p-3 bg-gray-50 rounded-md">
              <p className="text-sm text-gray-700">
                {versionInfo.updateMessage}
              </p>
            </div>
          )}

          {/* 版本信息 */}
          <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">当前版本：</span>
              <span className="text-gray-900">{versionInfo.currentVersion}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">最新版本：</span>
              <span className="text-gray-900">{versionInfo.latestVersion}</span>
            </div>
          </div>

          {/* 强制更新倒计时 */}
          {isForceUpdate && countdown !== null && (
            <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
              <p className="text-sm text-orange-800">
                {countdown > 0 
                  ? `${countdown} 秒后将自动开始更新...`
                  : '正在开始更新...'
                }
              </p>
            </div>
          )}
        </div>
      </ModalBody>

      <ModalFooter>
        <div className="flex justify-end space-x-3">
          {/* 强制更新只显示更新按钮 */}
          {isForceUpdate || isBelowMinimum ? (
            <Button
              onClick={handleUpdate}
              className="bg-red-600 hover:bg-red-700 text-white"
              disabled={countdown !== null && countdown > 0}
            >
              {countdown !== null && countdown > 0 
                ? `更新 (${countdown}s)`
                : '立即更新'
              }
            </Button>
          ) : (
            <>
              {/* 可选更新显示多个按钮 */}
              <Button
                variant="outline"
                onClick={handleSkip}
                className="text-gray-700 border-gray-300 hover:bg-gray-50"
              >
                跳过此版本
              </Button>
              
              <Button
                variant="outline"
                onClick={handleLater}
                className="text-gray-700 border-gray-300 hover:bg-gray-50"
              >
                稍后提醒
              </Button>
              
              <Button
                onClick={handleUpdate}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                立即更新
              </Button>
            </>
          )}
        </div>
      </ModalFooter>
    </Modal>
  );
};

/**
 * 检查是否应该显示更新提示
 * @param versionInfo 版本检查结果
 * @returns 是否应该显示
 */
export const shouldShowUpdatePrompt = (versionInfo: VersionCheckResponse): boolean => {
  if (!versionInfo.needUpdate) {
    return false;
  }

  // 强制更新总是显示
  if (versionInfo.updateType === 'force' || versionInfo.versionInfo?.isBelowMinimum) {
    return true;
  }

  // 检查是否已跳过此版本
  const skippedVersion = localStorage.getItem(SKIPPED_VERSION_KEY);
  if (skippedVersion === versionInfo.latestVersion) {
    return false;
  }

  // 检查是否已为此版本显示过提示
  const lastShownVersion = localStorage.getItem(LAST_SHOWN_VERSION_KEY);
  if (lastShownVersion === versionInfo.latestVersion) {
    return false;
  }

  return true;
};

/**
 * 标记已显示更新提示
 * @param version 版本号
 */
export const markUpdatePromptShown = (version: string): void => {
  localStorage.setItem(LAST_SHOWN_VERSION_KEY, version);
};

export default UpdatePrompt;
