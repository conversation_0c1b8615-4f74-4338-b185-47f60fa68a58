#!/bin/bash

# ChatAdvisor 管理后台构建脚本
# 用于构建React管理后台并部署到后端服务器

set -e  # 遇到错误时退出

echo "🚀 开始构建 ChatAdvisor 管理后台..."

# 检查是否存在admin-frontend目录
if [ ! -d "../admin-frontend" ]; then
    echo "❌ 错误: admin-frontend 目录不存在"
    echo "请确保在 ChatAdvisorServer 目录下运行此脚本，并且 admin-frontend 目录位于同级目录"
    exit 1
fi

# 进入前端项目目录
cd ../admin-frontend

echo "📦 安装依赖..."
npm install

echo "🔨 构建生产版本..."
npm run build

echo "📁 检查构建结果..."
if [ ! -d "dist" ]; then
    echo "❌ 错误: 构建失败，dist 目录不存在"
    exit 1
fi

echo "✅ 构建完成！"
echo "📊 构建统计:"
echo "   - 构建目录: $(pwd)/dist"
echo "   - 文件数量: $(find dist -type f | wc -l)"
echo "   - 总大小: $(du -sh dist | cut -f1)"

echo ""
echo "🎉 管理后台构建成功！"
echo "💡 提示:"
echo "   - 构建文件位于: admin-frontend/dist/"
echo "   - 后端服务器会自动从此目录提供静态文件"
echo "   - 重启后端服务器以应用更改"
echo ""
echo "🔗 访问地址:"
echo "   - 开发环境: http://localhost:3000/admin"
echo "   - 生产环境: http://your-domain/admin"
