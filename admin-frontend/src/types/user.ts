import { PaginationParams, SortParams, SearchParams } from './common';

// 用户基础信息
export interface User {
  _id: string;
  userId: string;
  username?: string;
  email: string;
  fullName?: string;
  avatar?: string;
  birthDate?: string;
  gender?: 'Male' | 'Female' | 'Other';
  phone?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  language?: string;
  timeZone?: string;
  occupation?: string;
  company?: string;
  allergies?: string[];
  medicalConditions?: string[];
  balance: number;
  totalSpent?: number;
  role: 'user' | 'admin' | 'super_admin';
  status: 'active' | 'inactive' | 'suspended';
  lastLoginAt?: string;
  lastLoginIP?: string;
  emailVerified: boolean;
  phoneVerified?: boolean;
  isVip?: boolean;
  vipExpiredAt?: string;
  hasPurchase: boolean;
  isDelete: boolean;
  createdAt: string;
  updatedAt: string;
  externalAccounts?: {
    weChatId?: string;
    qqId?: string;
    appleInfo?: any;
    googleInfo?: any;
    facebookInfo?: any;
    twitterInfo?: any;
    tikTokInfo?: any;
  };
}

// 用户列表查询参数
export interface UserListParams extends PaginationParams, SortParams, SearchParams {
  isVip?: boolean;
  emailVerified?: boolean;
  status?: 'active' | 'inactive' | 'suspended';
  role?: 'user' | 'admin' | 'super_admin';
  dateFrom?: string;
  dateTo?: string;
}

// 用户统计信息
export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  vipUsers: number;
  verifiedUsers: number;
  usersByStatus: {
    active: number;
    inactive: number;
    suspended: number;
  };
  usersByRole: {
    user: number;
    admin: number;
    super_admin: number;
  };
  registrationTrend: {
    date: string;
    count: number;
  }[];
}

// 用户创建/更新表单
export interface UserFormData {
  username?: string;
  email: string;
  fullName?: string;
  password?: string;
  birthDate?: string;
  gender?: 'Male' | 'Female' | 'Other';
  phone?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  language?: string;
  timeZone?: string;
  occupation?: string;
  company?: string;
  allergies?: string[];
  medicalConditions?: string[];
  role: 'user' | 'admin' | 'super_admin';
  status: 'active' | 'inactive' | 'suspended';
  emailVerified?: boolean;
  phoneVerified?: boolean;
  // 余额调整相关字段
  balanceAdjustment?: {
    type: 'add' | 'deduct' | 'set';
    amount: number;
    reason: string;
  };
}

// 用户余额调整
export interface BalanceAdjustment {
  userId: string;
  amount: number;
  reason: string;
  type: 'increase' | 'decrease';
}

// 批量操作
export interface BatchUserOperation {
  userIds: string[];
  operation: 'delete' | 'activate' | 'suspend' | 'verify_email';
  permanent?: boolean;
}

// 用户详情扩展信息
export interface UserDetail extends User {
  balanceHistory?: {
    amount: number;
    reason: string;
    timestamp: string;
    type: number;
  }[];
  chatStats?: {
    totalChats: number;
    totalMessages: number;
    lastChatAt?: string;
  };
  loginHistory?: {
    loginAt: string;
    ip: string;
    userAgent?: string;
  }[];
}
