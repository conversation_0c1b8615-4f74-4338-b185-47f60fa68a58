import { PaginationParams, SortParams, SearchParams } from './common';

// ==================== 系统配置相关 ====================

// 系统配置接口
export interface SystemConfig {
  _id?: string;
  privacyPolicy: string;
  termsOfService: string;
  appVersion: string;
  supportEmail: string;
  featureFlags: Record<string, boolean>;
  mainSolgan: Record<string, string[]>;
  registerSolgan: Record<string, string[]>;
  emailLoginSolgan: Record<string, string[]>;
  rechargeMessages: Record<string, string[]>;
  hideMessage: Record<string, string[]>;
  rechargeDescription: Record<string, string>;
  promotLocal?: Record<string, string>;
  promotCloud?: Record<string, string>;
  compressRate: number;
  // 版本控制相关字段
  latestVersion: string;
  minimumVersion: string;
  forceUpdate: boolean;
  updateMessage: Record<string, string>;
  appStoreUrls: {
    ios: string;
    android: string;
  };
  updateType: 'force' | 'optional';
  versionCheckEnabled: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// 配置项更新数据
export interface ConfigUpdateData {
  privacyPolicy?: string;
  termsOfService?: string;
  appVersion?: string;
  supportEmail?: string;
  featureFlags?: Record<string, boolean>;
  mainSolgan?: Record<string, string[]>;
  registerSolgan?: Record<string, string[]>;
  emailLoginSolgan?: Record<string, string[]>;
  rechargeMessages?: Record<string, string[]>;
  hideMessage?: Record<string, string[]>;
  rechargeDescription?: Record<string, string>;
  promotLocal?: Record<string, string>;
  promotCloud?: Record<string, string>;
  compressRate?: number;
}

// 配置历史记录
export interface ConfigHistory {
  _id: string;
  configId: string;
  changes: Record<string, any>;
  changedBy: string;
  changeReason?: string;
  timestamp: string;
}

// ==================== 问题库相关 ====================

// 问题库接口
export interface Question {
  _id: string;
  sketch: Record<string, string>;
  content: Record<string, string>;
  question: Record<string, string>;
  createdAt?: string;
  updatedAt?: string;
}

// 问题库查询参数
export interface QuestionListParams extends PaginationParams, SortParams, SearchParams {
  language?: 'zh_CN' | 'en';
}

// 问题库表单数据
export interface QuestionFormData {
  sketch: Record<string, string>;
  content: Record<string, string>;
  question: Record<string, string>;
}

// 问题库统计信息
export interface QuestionStats {
  totalQuestions: number;
  languageDistribution: {
    language: string;
    count: number;
    percentage: number;
  }[];
  recentlyAdded: number;
  recentlyUpdated: number;
}

// ==================== 日志相关 ====================

// 请求日志
export interface RequestLog {
  _id: string;
  url: string;
  method: string;
  requestBody?: any;
  requestTime: string;
  userAgent?: string;
  ip?: string;
  userId?: string;
}

// 响应日志
export interface ResponseLog {
  _id: string;
  requestLogId: string;
  requestBody?: any;
  responseBody?: any;
  responseStatus: number;
  responseTime: string;
  duration?: number;
}

// 错误日志
export interface ErrorLog {
  _id: string;
  requestId: string;
  timestamp: string;
  error: string;
  errorCode: number;
  stack?: string;
  level?: 'error' | 'warn' | 'info';
}

// 日志详情（包含关联信息）
export interface LogDetails {
  request: RequestLog;
  response?: ResponseLog;
  errors: ErrorLog[];
  hasResponse: boolean;
  hasErrors: boolean;
  errorCount: number;
}

// 日志查询参数
export interface LogListParams extends PaginationParams, SortParams {
  url?: string;
  method?: string;
  status?: number;
  errorCode?: number;
  error?: string;
  dateFrom?: string;
  dateTo?: string;
}

// 日志统计信息
export interface LogStats {
  overview: {
    totalRequests: number;
    totalResponses: number;
    totalErrors: number;
    avgResponseTime: number;
    errorRate: number;
  };
  statusDistribution: {
    status: number;
    count: number;
    percentage: number;
  }[];
  methodDistribution: {
    method: string;
    count: number;
    percentage: number;
  }[];
  errorDistribution: {
    errorCode: number;
    count: number;
    percentage: number;
  }[];
  timeSeriesData: {
    date: string;
    requests: number;
    responses: number;
    errors: number;
    avgResponseTime: number;
  }[];
  topUrls: {
    url: string;
    count: number;
    avgResponseTime: number;
    errorRate: number;
  }[];
  topErrors: {
    error: string;
    count: number;
    errorCode: number;
  }[];
}

// ==================== 系统状态相关 ====================

// 系统状态
export interface SystemStatus {
  status: 'healthy' | 'warning' | 'error';
  uptime: number;
  version: string;
  environment: string;
  timestamp: string;
  services: {
    database: {
      status: 'connected' | 'disconnected' | 'error';
      responseTime?: number;
      error?: string;
    };
    redis?: {
      status: 'connected' | 'disconnected' | 'error';
      responseTime?: number;
      error?: string;
    };
    external?: {
      name: string;
      status: 'healthy' | 'unhealthy' | 'unknown';
      responseTime?: number;
      error?: string;
    }[];
  };
  resources: {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
    disk?: {
      used: number;
      total: number;
      percentage: number;
    };
  };
}

// 数据库状态
export interface DatabaseStatus {
  status: 'connected' | 'disconnected' | 'error';
  connectionCount: number;
  collections: {
    name: string;
    documentCount: number;
    size: number;
    indexes: number;
  }[];
  performance: {
    avgResponseTime: number;
    slowQueries: number;
    activeConnections: number;
  };
  replication?: {
    status: string;
    lag: number;
  };
}

// ==================== 批量操作相关 ====================

// 批量删除问题
export interface BatchDeleteQuestions {
  questionIds: string[];
}

// 日志清理参数
export interface LogCleanupParams {
  type: 'request' | 'response' | 'error' | 'all';
  daysToKeep?: number;
  batchSize?: number;
}

// 配置导入导出
export interface ConfigExportParams {
  format?: 'json' | 'file';
  includeSecrets?: boolean;
}

export interface ConfigImportParams {
  configData: Record<string, any>;
  mode?: 'merge' | 'replace';
}

// ==================== 版本控制相关 ====================

// 版本控制配置接口
export interface VersionControlConfig {
  latestVersion: string;
  minimumVersion: string;
  forceUpdate: boolean;
  updateMessage: Record<string, string>;
  appStoreUrls: {
    ios: string;
    android: string;
  };
  updateType: 'force' | 'optional';
  versionCheckEnabled: boolean;
}

// 版本控制配置更新数据
export interface VersionControlUpdateData {
  latestVersion?: string;
  minimumVersion?: string;
  forceUpdate?: boolean;
  updateMessage?: Record<string, string>;
  appStoreUrls?: {
    ios?: string;
    android?: string;
  };
  updateType?: 'force' | 'optional';
  versionCheckEnabled?: boolean;
}

// 版本检测响应
export interface VersionCheckResponse {
  needUpdate: boolean;
  updateType: 'force' | 'optional' | 'none';
  currentVersion: string;
  latestVersion: string;
  minimumVersion: string;
  updateMessage: string;
  downloadUrl: string;
  platform: string;
  versionInfo: {
    isLatest: boolean;
    isBelowMinimum: boolean;
    hasNewVersion: boolean;
  };
}

// ==================== 监控和告警相关 ====================

// 系统监控数据
export interface SystemMonitorData {
  timestamp: string;
  metrics: {
    requestsPerMinute: number;
    errorsPerMinute: number;
    avgResponseTime: number;
    activeUsers: number;
    memoryUsage: number;
    cpuUsage: number;
    diskUsage: number;
  };
  alerts: {
    level: 'info' | 'warning' | 'error' | 'critical';
    message: string;
    timestamp: string;
    resolved?: boolean;
  }[];
}

// 性能指标
export interface PerformanceMetrics {
  timeRange: {
    start: string;
    end: string;
  };
  requestMetrics: {
    totalRequests: number;
    requestsPerSecond: number;
    avgResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
  };
  errorMetrics: {
    totalErrors: number;
    errorRate: number;
    topErrors: {
      error: string;
      count: number;
    }[];
  };
  resourceMetrics: {
    avgMemoryUsage: number;
    maxMemoryUsage: number;
    avgCpuUsage: number;
    maxCpuUsage: number;
  };
}
