// 仪表板概览数据
export interface DashboardOverview {
  users: {
    total: number;
    active: number;
    newToday: number;
    growth: number; // 增长率百分比
  };
  chat: {
    totalMessages: number;
    totalSessions: number;
    todayMessages: number;
    todaySessions: number;
    avgMessagesPerSession: number;
    growth: number;
  };
  financial: {
    totalRevenue: number;
    todayRevenue: number;
    totalTransactions: number;
    todayTransactions: number;
    avgTransactionAmount: number;
    growth: number;
  };
  system: {
    uptime: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    errorRate: number;
    avgResponseTime: number;
  };
}

// 趋势数据
export interface TrendData {
  date: string;
  users: number;
  messages: number;
  sessions: number;
  revenue: number;
  transactions: number;
}

// 用户活跃度数据
export interface UserActivityData {
  timeRange: string;
  activeUsers: number;
  newUsers: number;
  returningUsers: number;
}

// 聊天热力图数据
export interface ChatHeatmapData {
  hour: number;
  day: number; // 0-6 (周日到周六)
  value: number; // 消息数量
}

// 收入分布数据
export interface RevenueDistribution {
  category: string;
  amount: number;
  percentage: number;
  color: string;
}

// 热门功能使用数据
export interface FeatureUsage {
  feature: string;
  usage: number;
  growth: number;
  category: 'chat' | 'user' | 'financial' | 'system';
}

// 地理分布数据
export interface GeographicData {
  country: string;
  users: number;
  sessions: number;
  revenue: number;
  coordinates: [number, number]; // [经度, 纬度]
}

// 设备分布数据
export interface DeviceData {
  device: string;
  users: number;
  percentage: number;
  sessions: number;
  avgSessionDuration: number;
}

// 错误统计数据
export interface ErrorStats {
  errorType: string;
  count: number;
  percentage: number;
  trend: number; // 相比上期的变化
  lastOccurrence: string;
}

// 性能指标数据
export interface PerformanceData {
  timestamp: string;
  responseTime: number;
  throughput: number;
  errorRate: number;
  cpuUsage: number;
  memoryUsage: number;
}

// 用户留存数据
export interface RetentionData {
  cohort: string; // 用户群组（注册时间）
  day0: number;   // 注册当天
  day1: number;   // 第1天
  day7: number;   // 第7天
  day30: number;  // 第30天
  day90: number;  // 第90天
}

// 漏斗分析数据
export interface FunnelData {
  step: string;
  users: number;
  conversionRate: number;
  dropOffRate: number;
}

// 实时数据
export interface RealTimeData {
  timestamp: string;
  onlineUsers: number;
  activeChats: number;
  requestsPerSecond: number;
  errorsPerSecond: number;
  avgResponseTime: number;
  systemLoad: {
    cpu: number;
    memory: number;
    disk: number;
  };
}

// 告警数据
export interface AlertData {
  id: string;
  type: 'error' | 'warning' | 'info' | 'critical';
  title: string;
  message: string;
  timestamp: string;
  resolved: boolean;
  source: string;
  severity: number; // 1-5
}

// 仪表板配置
export interface DashboardConfig {
  refreshInterval: number; // 刷新间隔（秒）
  timeRange: '1h' | '6h' | '24h' | '7d' | '30d' | '90d';
  widgets: {
    id: string;
    type: 'overview' | 'chart' | 'table' | 'metric' | 'map' | 'heatmap';
    title: string;
    position: { x: number; y: number; w: number; h: number };
    config: Record<string, any>;
    visible: boolean;
  }[];
  filters: {
    dateRange: {
      start: string;
      end: string;
    };
    userSegment?: string;
    region?: string;
    device?: string;
  };
}

// 报表数据
export interface ReportData {
  id: string;
  name: string;
  type: 'user' | 'chat' | 'financial' | 'system' | 'custom';
  description: string;
  data: any[];
  metadata: {
    generatedAt: string;
    dataRange: {
      start: string;
      end: string;
    };
    totalRecords: number;
    filters: Record<string, any>;
  };
  charts: {
    type: 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'heatmap';
    title: string;
    data: any[];
    config: Record<string, any>;
  }[];
}

// 导出配置
export interface ExportConfig {
  format: 'pdf' | 'excel' | 'csv' | 'json';
  includeCharts: boolean;
  includeRawData: boolean;
  dateRange: {
    start: string;
    end: string;
  };
  filters: Record<string, any>;
  template?: string;
}

// 仪表板查询参数
export interface DashboardQuery {
  timeRange?: '1h' | '6h' | '24h' | '7d' | '30d' | '90d';
  startDate?: string;
  endDate?: string;
  groupBy?: 'hour' | 'day' | 'week' | 'month';
  metrics?: string[];
  filters?: Record<string, any>;
}

// 图表配置
export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter' | 'heatmap' | 'gauge' | 'funnel';
  title: string;
  subtitle?: string;
  xAxis?: {
    title: string;
    type: 'category' | 'time' | 'value';
    format?: string;
  };
  yAxis?: {
    title: string;
    type: 'value' | 'log';
    format?: string;
    min?: number;
    max?: number;
  };
  legend?: {
    show: boolean;
    position: 'top' | 'bottom' | 'left' | 'right';
  };
  colors?: string[];
  animation?: boolean;
  responsive?: boolean;
}

// 小部件数据
export interface WidgetData {
  id: string;
  type: string;
  title: string;
  data: any;
  loading: boolean;
  error?: string;
  lastUpdated: string;
  config: Record<string, any>;
}
