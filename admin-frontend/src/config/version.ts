/**
 * 版本检测配置
 */

// 版本检测配置接口
export interface VersionCheckConfig {
  /** 是否启用版本检测 */
  enabled: boolean;
  /** 检查间隔（毫秒） */
  checkInterval: number;
  /** 是否在应用启动时检查 */
  checkOnMount: boolean;
  /** 平台类型 */
  platform: 'ios' | 'android';
  /** 是否启用自动检查 */
  autoCheck: boolean;
  /** 强制更新倒计时时间（秒） */
  forceUpdateCountdown: number;
  /** 版本检测API端点 */
  apiEndpoint: string;
}

// 默认版本检测配置
export const defaultVersionConfig: VersionCheckConfig = {
  enabled: true,
  checkInterval: 24 * 60 * 60 * 1000, // 24小时
  checkOnMount: true,
  platform: 'ios',
  autoCheck: true,
  forceUpdateCountdown: 30, // 30秒
  apiEndpoint: '/api/admin/system/check-version',
};

// 环境相关配置
export const getVersionConfig = (): VersionCheckConfig => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return {
    ...defaultVersionConfig,
    // 开发环境下可以调整配置
    enabled: !isDevelopment, // 开发环境下默认禁用
    checkInterval: isDevelopment ? 30 * 60 * 1000 : defaultVersionConfig.checkInterval, // 开发环境30分钟检查一次，减少频率
  };
};

// 获取当前应用版本
export const getCurrentAppVersion = (): string => {
  return process.env.REACT_APP_VERSION || '1.0.0';
};

// 版本比较工具
export const compareVersions = (version1: string, version2: string): number => {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);
  
  // 确保版本号都有三个部分（major.minor.patch）
  while (v1Parts.length < 3) v1Parts.push(0);
  while (v2Parts.length < 3) v2Parts.push(0);
  
  for (let i = 0; i < 3; i++) {
    if (v1Parts[i] > v2Parts[i]) return 1;
    if (v1Parts[i] < v2Parts[i]) return -1;
  }
  
  return 0;
};

// 版本格式验证
export const isValidVersion = (version: string): boolean => {
  const versionRegex = /^\d+\.\d+\.\d+$/;
  return versionRegex.test(version);
};

// 本地存储键名
export const STORAGE_KEYS = {
  VERSION_CHECK_DATA: 'admin_version_check_data',
  LAST_CHECK_TIME: 'admin_last_version_check_time',
  SKIPPED_VERSION: 'admin_skipped_version',
  LAST_SHOWN_VERSION: 'admin_last_shown_update_version',
  VERSION_CONFIG: 'admin_version_config',
} as const;

// 版本检测事件类型
export enum VersionCheckEvents {
  CHECK_STARTED = 'version_check_started',
  CHECK_COMPLETED = 'version_check_completed',
  CHECK_FAILED = 'version_check_failed',
  UPDATE_AVAILABLE = 'update_available',
  UPDATE_REQUIRED = 'update_required',
  UPDATE_SKIPPED = 'update_skipped',
  UPDATE_LATER = 'update_later',
  UPDATE_STARTED = 'update_started',
}

// 版本检测状态
export enum VersionCheckStatus {
  IDLE = 'idle',
  CHECKING = 'checking',
  UP_TO_DATE = 'up_to_date',
  UPDATE_AVAILABLE = 'update_available',
  UPDATE_REQUIRED = 'update_required',
  ERROR = 'error',
}

// 更新类型
export enum UpdateType {
  NONE = 'none',
  OPTIONAL = 'optional',
  FORCE = 'force',
}

// 版本检测结果接口
export interface VersionCheckResult {
  status: VersionCheckStatus;
  currentVersion: string;
  latestVersion: string;
  minimumVersion: string;
  updateType: UpdateType;
  updateMessage: string;
  downloadUrl: string;
  needsUpdate: boolean;
  isForceUpdate: boolean;
  isBelowMinimum: boolean;
  checkTime: number;
}

// 版本检测错误类型
export class VersionCheckError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'VersionCheckError';
  }
}

// 版本检测工具类
export class VersionUtils {
  /**
   * 格式化版本号显示
   */
  static formatVersion(version: string): string {
    if (!isValidVersion(version)) {
      return version;
    }
    return `v${version}`;
  }

  /**
   * 获取版本更新描述
   */
  static getUpdateDescription(currentVersion: string, latestVersion: string): string {
    const comparison = compareVersions(latestVersion, currentVersion);
    
    if (comparison > 0) {
      return `发现新版本 ${VersionUtils.formatVersion(latestVersion)}，当前版本 ${VersionUtils.formatVersion(currentVersion)}`;
    } else if (comparison === 0) {
      return `您正在使用最新版本 ${VersionUtils.formatVersion(currentVersion)}`;
    } else {
      return `当前版本 ${VersionUtils.formatVersion(currentVersion)} 高于最新版本 ${VersionUtils.formatVersion(latestVersion)}`;
    }
  }

  /**
   * 检查版本是否需要更新
   */
  static needsUpdate(currentVersion: string, latestVersion: string, minimumVersion: string): {
    needsUpdate: boolean;
    isForceUpdate: boolean;
    isBelowMinimum: boolean;
  } {
    const latestComparison = compareVersions(latestVersion, currentVersion);
    const minimumComparison = compareVersions(currentVersion, minimumVersion);
    
    const isBelowMinimum = minimumComparison < 0;
    const hasNewVersion = latestComparison > 0;
    
    return {
      needsUpdate: hasNewVersion || isBelowMinimum,
      isForceUpdate: isBelowMinimum,
      isBelowMinimum,
    };
  }

  /**
   * 生成版本检测日志
   */
  static createCheckLog(result: VersionCheckResult): string {
    const timestamp = new Date(result.checkTime).toISOString();
    return `[${timestamp}] Version Check: ${result.status} - Current: ${result.currentVersion}, Latest: ${result.latestVersion}, Update Type: ${result.updateType}`;
  }
}

export default {
  defaultVersionConfig,
  getVersionConfig,
  getCurrentAppVersion,
  compareVersions,
  isValidVersion,
  STORAGE_KEYS,
  VersionCheckEvents,
  VersionCheckStatus,
  UpdateType,
  VersionCheckError,
  VersionUtils,
};
