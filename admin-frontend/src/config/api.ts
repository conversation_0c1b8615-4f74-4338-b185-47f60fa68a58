/**
 * API配置文件
 * 管理不同环境下的API端点配置
 */

// API端点配置接口
export interface ApiConfig {
  /** API基础URL */
  baseURL: string;
  /** 超时时间（毫秒） */
  timeout: number;
  /** 是否为生产环境 */
  isProduction: boolean;
}

// 环境检测 - 支持多种生产环境判断方式
const isProduction = process.env.NODE_ENV === 'production' || 
                    import.meta.env.PROD;
const isDevelopment = process.env.NODE_ENV === 'development';

console.log('Environment check:', {
  'import.meta.env.PROD': import.meta.env.PROD,
  'isProduction': isProduction
});

// 获取API配置（支持动态环境切换）
export const getApiConfig = (): ApiConfig => {
  console.log('Getting API config');

  // 在开发环境下，检查是否有环境管理器的覆盖配置
  if (!isProduction && typeof window !== 'undefined') {
    try {
      // 动态导入环境管理器（避免循环依赖）
      const overrideEnv = localStorage.getItem('dev_environment_override');
      if (overrideEnv) {
        const envConfigs = {
          development: { baseURL: 'http://localhost:33001', isProduction: false },
          production: { baseURL: 'https://admin.sanva.top/api', isProduction: true }
        };

        const overrideConfig = envConfigs[overrideEnv as keyof typeof envConfigs];
        if (overrideConfig) {
          const config = {
            baseURL: overrideConfig.baseURL,
            timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
            isProduction: overrideConfig.isProduction,
          };
          console.log(`Using environment override: ${overrideEnv}`, config);
          return config;
        }
      }
    } catch (error) {
      console.warn('Failed to check environment override:', error);
    }
  }

  // 默认配置逻辑
  const envApiUrl = import.meta.env.VITE_API_BASE_URL;
  const envTimeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '10000');

  let baseURL: string;
  let isActualProduction = false;

  if (isProduction) {
    // 生产环境构建时使用admin域名的HTTPS端点
    baseURL = envApiUrl || 'https://admin.sanva.top/api';
    isActualProduction = true;
    console.log('Production build detected');
  } else {
    baseURL = envApiUrl || 'http://localhost:33001';
    isActualProduction = false;
    console.log('Development environment - using localhost API');
  }

  const config = {
    baseURL,
    timeout: envTimeout,
    isProduction: isActualProduction,
  };

  console.log('Final API config:', config);
  return config;
};

// 默认API配置
export const defaultApiConfig = getApiConfig();

// API端点常量
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/admin/auth/login',
    LOGOUT: '/api/admin/auth/logout',
    ME: '/api/admin/auth/me',
    REFRESH: '/api/admin/auth/refresh',
  },
  
  // 用户管理
  USERS: {
    LIST: '/api/admin/users',
    DETAIL: (id: string) => `/api/admin/users/${id}`,
    CREATE: '/api/admin/users',
    UPDATE: (id: string) => `/api/admin/users/${id}`,
    DELETE: (id: string) => `/api/admin/users/${id}`,
  },
  
  // 聊天管理
  CHAT: {
    LIST: '/api/admin/chat',
    DETAIL: (id: string) => `/api/admin/chat/${id}`,
    MESSAGES: (id: string) => `/api/admin/chat/${id}/messages`,
  },
  
  // 财务管理
  FINANCIAL: {
    TRANSACTIONS: '/api/admin/financial/transactions',
    PRODUCTS: '/api/admin/financial/products',
    PRICING: '/api/admin/financial/pricing',
  },
  
  // 系统管理
  SYSTEM: {
    CONFIG: '/api/admin/system/config',
    STATUS: '/api/admin/system/status',
    VERSION: '/api/admin/system/check-version',
  },
  
  // 日志管理
  LOGS: {
    LIST: '/api/admin/logs',
    CLEAR: '/api/admin/logs/clear',
  },
  
  // 统计报表
  REPORTS: {
    DASHBOARD: '/api/admin/reports/dashboard',
    USERS: '/api/admin/reports/users',
    CHAT: '/api/admin/reports/chat',
    FINANCIAL: '/api/admin/reports/financial',
  },
} as const;

// 环境信息
export const ENV_INFO = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  VERSION: process.env.REACT_APP_VERSION || '1.0.0',
  IS_PRODUCTION: isProduction,
  IS_DEVELOPMENT: isDevelopment,
  API_CONFIG: defaultApiConfig,
};

// 调试信息（仅在开发环境下输出）
if (isDevelopment && typeof console !== 'undefined') {
  console.log('🔧 API配置信息:', ENV_INFO);
}
