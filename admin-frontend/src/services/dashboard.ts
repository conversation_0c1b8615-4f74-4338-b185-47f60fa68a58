import { request } from './api';
import { 
  DashboardOverview,
  TrendData,
  UserActivityData,
  ChatHeatmapData,
  RevenueDistribution,
  FeatureUsage,
  GeographicData,
  DeviceData,
  ErrorStats,
  PerformanceData,
  RetentionData,
  FunnelData,
  RealTimeData,
  AlertData,
  DashboardConfig,
  ReportData,
  ExportConfig,
  DashboardQuery
} from '@/types/dashboard';

export const dashboardService = {
  // ==================== 仪表板概览 ====================
  
  // 获取仪表板概览数据
  getOverview: async (query?: DashboardQuery): Promise<DashboardOverview> => {
    const response = await request.get<DashboardOverview>('/admin/dashboard/overview', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取概览数据失败');
  },

  // 获取趋势数据
  getTrendData: async (query?: DashboardQuery): Promise<TrendData[]> => {
    const response = await request.get<TrendData[]>('/admin/dashboard/trends', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取趋势数据失败');
  },

  // 获取实时数据
  getRealTimeData: async (): Promise<RealTimeData> => {
    const response = await request.get<RealTimeData>('/admin/dashboard/realtime');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取实时数据失败');
  },

  // ==================== 用户分析 ====================

  // 获取用户活跃度数据
  getUserActivity: async (query?: DashboardQuery): Promise<UserActivityData[]> => {
    const response = await request.get<UserActivityData[]>('/admin/dashboard/user-activity', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取用户活跃度数据失败');
  },

  // 获取用户留存数据
  getRetentionData: async (query?: DashboardQuery): Promise<RetentionData[]> => {
    const response = await request.get<RetentionData[]>('/admin/dashboard/retention', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取用户留存数据失败');
  },

  // 获取地理分布数据
  getGeographicData: async (query?: DashboardQuery): Promise<GeographicData[]> => {
    const response = await request.get<GeographicData[]>('/admin/dashboard/geographic', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取地理分布数据失败');
  },

  // 获取设备分布数据
  getDeviceData: async (query?: DashboardQuery): Promise<DeviceData[]> => {
    const response = await request.get<DeviceData[]>('/admin/dashboard/devices', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取设备分布数据失败');
  },

  // ==================== 聊天分析 ====================

  // 获取聊天热力图数据
  getChatHeatmap: async (query?: DashboardQuery): Promise<ChatHeatmapData[]> => {
    const response = await request.get<ChatHeatmapData[]>('/admin/dashboard/chat-heatmap', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取聊天热力图数据失败');
  },

  // 获取功能使用数据
  getFeatureUsage: async (query?: DashboardQuery): Promise<FeatureUsage[]> => {
    const response = await request.get<FeatureUsage[]>('/admin/dashboard/feature-usage', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取功能使用数据失败');
  },

  // ==================== 财务分析 ====================

  // 获取收入分布数据
  getRevenueDistribution: async (query?: DashboardQuery): Promise<RevenueDistribution[]> => {
    const response = await request.get<RevenueDistribution[]>('/admin/dashboard/revenue-distribution', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取收入分布数据失败');
  },

  // 获取漏斗分析数据
  getFunnelData: async (query?: DashboardQuery): Promise<FunnelData[]> => {
    const response = await request.get<FunnelData[]>('/admin/dashboard/funnel', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取漏斗分析数据失败');
  },

  // ==================== 系统监控 ====================

  // 获取性能数据
  getPerformanceData: async (query?: DashboardQuery): Promise<PerformanceData[]> => {
    const response = await request.get<PerformanceData[]>('/admin/dashboard/performance', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取性能数据失败');
  },

  // 获取错误统计
  getErrorStats: async (query?: DashboardQuery): Promise<ErrorStats[]> => {
    const response = await request.get<ErrorStats[]>('/admin/dashboard/errors', { params: query });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取错误统计失败');
  },

  // 获取告警数据
  getAlerts: async (params?: { 
    resolved?: boolean; 
    severity?: number; 
    limit?: number 
  }): Promise<AlertData[]> => {
    const response = await request.get<AlertData[]>('/admin/dashboard/alerts', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取告警数据失败');
  },

  // 标记告警为已解决
  resolveAlert: async (alertId: string): Promise<void> => {
    const response = await request.post(`/admin/dashboard/alerts/${alertId}/resolve`);
    if (!response.success) {
      throw new Error(response.message || '标记告警失败');
    }
  },

  // ==================== 仪表板配置 ====================

  // 获取仪表板配置
  getDashboardConfig: async (): Promise<DashboardConfig> => {
    const response = await request.get<DashboardConfig>('/admin/dashboard/config');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取仪表板配置失败');
  },

  // 保存仪表板配置
  saveDashboardConfig: async (config: DashboardConfig): Promise<DashboardConfig> => {
    const response = await request.post<DashboardConfig>('/admin/dashboard/config', config);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '保存仪表板配置失败');
  },

  // 重置仪表板配置
  resetDashboardConfig: async (): Promise<DashboardConfig> => {
    const response = await request.post<DashboardConfig>('/admin/dashboard/config/reset');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '重置仪表板配置失败');
  },

  // ==================== 报表管理 ====================

  // 获取报表列表
  getReports: async (params?: {
    type?: string;
    page?: number;
    limit?: number;
  }): Promise<{ reports: ReportData[]; total: number }> => {
    const response = await request.get<{ reports: ReportData[]; total: number }>('/admin/reports', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取报表列表失败');
  },

  // 获取报表详情
  getReport: async (reportId: string): Promise<ReportData> => {
    const response = await request.get<ReportData>(`/admin/reports/${reportId}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取报表详情失败');
  },

  // 生成报表
  generateReport: async (config: {
    type: string;
    name: string;
    description?: string;
    query: DashboardQuery;
    charts?: any[];
  }): Promise<ReportData> => {
    const response = await request.post<ReportData>('/admin/reports/generate', config);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '生成报表失败');
  },

  // 删除报表
  deleteReport: async (reportId: string): Promise<void> => {
    const response = await request.delete(`/admin/reports/${reportId}`);
    if (!response.success) {
      throw new Error(response.message || '删除报表失败');
    }
  },

  // 导出报表
  exportReport: async (reportId: string, config: ExportConfig): Promise<Blob> => {
    const response = await request.post(`/admin/reports/${reportId}/export`, config, {
      responseType: 'blob',
    });
    return response as any;
  },

  // 导出仪表板数据
  exportDashboard: async (config: ExportConfig & { widgets?: string[] }): Promise<Blob> => {
    const response = await request.post('/admin/dashboard/export', config, {
      responseType: 'blob',
    });
    return response as any;
  },

  // ==================== 数据刷新 ====================

  // 刷新缓存
  refreshCache: async (type?: 'all' | 'overview' | 'trends' | 'realtime'): Promise<void> => {
    const response = await request.post('/admin/dashboard/refresh-cache', { type });
    if (!response.success) {
      throw new Error(response.message || '刷新缓存失败');
    }
  },

  // 预热数据
  warmupData: async (metrics?: string[]): Promise<void> => {
    const response = await request.post('/admin/dashboard/warmup', { metrics });
    if (!response.success) {
      throw new Error(response.message || '预热数据失败');
    }
  },
};
