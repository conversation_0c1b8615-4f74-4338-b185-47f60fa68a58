import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '@/types/common';
import { getApiConfig } from '@/config/api';

// 获取API配置
const apiConfig = getApiConfig();

// 创建axios实例 - 支持动态环境切换
const createApiInstance = () => {
  const currentConfig = getApiConfig();
  return axios.create({
    baseURL: currentConfig.isProduction ? currentConfig.baseURL : '/api',  // 生产环境使用完整URL，开发环境使用代理
    timeout: currentConfig.timeout,
    headers: {
      'Content-Type': 'application/json',
      'X-Environment': currentConfig.isProduction ? 'production' : 'development', // 添加环境标识
    },
  });
};

// 初始API实例
let api: AxiosInstance = createApiInstance();

// 重新创建API实例的函数（用于环境切换后更新配置）
export const recreateApiInstance = () => {
  api = createApiInstance();
  console.log('API实例已重新创建，使用新的环境配置');
  return api;
};

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 全局认证错误处理器
let globalAuthErrorHandler: ((error: Error) => void) | null = null;
let authErrorTimeout: NodeJS.Timeout | null = null;
let isHandlingAuthError = false;

export const setGlobalAuthErrorHandler = (handler: (error: Error) => void) => {
  globalAuthErrorHandler = handler;
};

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    // 处理认证错误
    if (error.response?.status === 401) {
      // 防抖机制：避免多个API同时失败时重复处理认证错误
      if (isHandlingAuthError) {
        return Promise.reject(new Error('认证失败，请重新登录'));
      }

      isHandlingAuthError = true;

      // 清除之前的超时
      if (authErrorTimeout) {
        clearTimeout(authErrorTimeout);
      }

      // 设置超时，在500ms后重置标志，允许后续的认证错误处理
      authErrorTimeout = setTimeout(() => {
        isHandlingAuthError = false;
      }, 500);

      // 清除本地存储
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');

      // 调用全局认证错误处理器，而不是直接跳转
      const authError = new Error('认证失败，请重新登录');
      if (globalAuthErrorHandler) {
        globalAuthErrorHandler(authError);
      }

      return Promise.reject(authError);
    }

    // 处理网络错误
    if (!error.response) {
      return Promise.reject(new Error('网络连接失败，请检查网络设置'));
    }

    // 处理服务器错误
    const message = error.response?.data?.message || error.message || '请求失败';
    return Promise.reject(new Error(message));
  }
);

// 通用请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.get(url, config).then(res => res.data),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.post(url, data, config).then(res => res.data),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.put(url, data, config).then(res => res.data),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.delete(url, config).then(res => res.data),
    
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    api.patch(url, data, config).then(res => res.data),
};

export default api;
