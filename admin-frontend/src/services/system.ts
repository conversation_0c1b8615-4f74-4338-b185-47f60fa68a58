import { request } from './api';
import {
  SystemConfig,
  ConfigUpdateData,
  ConfigHistory,
  Question,
  QuestionListParams,
  QuestionFormData,
  QuestionStats,
  RequestLog,
  ResponseLog,
  ErrorLog,
  LogDetails,
  LogListParams,
  LogStats,
  SystemStatus,
  DatabaseStatus,
  BatchDeleteQuestions,
  LogCleanupParams,
  ConfigExportParams,
  ConfigImportParams,
  SystemMonitorData,
  PerformanceMetrics,
  VersionControlConfig,
  VersionControlUpdateData,
  VersionCheckResponse
} from '@/types/system';
import { PaginationResponse } from '@/types/common';

export const systemService = {
  // ==================== 系统状态 ====================
  
  // 获取系统状态
  getSystemStatus: async (): Promise<SystemStatus> => {
    const response = await request.get<SystemStatus>('/admin/system/status');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取系统状态失败');
  },

  // 获取数据库状态
  getDatabaseStatus: async (): Promise<DatabaseStatus> => {
    const response = await request.get<DatabaseStatus>('/admin/system/database');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取数据库状态失败');
  },

  // 获取系统监控数据
  getSystemMonitorData: async (): Promise<SystemMonitorData> => {
    const response = await request.get<SystemMonitorData>('/admin/system/monitor');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取监控数据失败');
  },

  // 获取性能指标
  getPerformanceMetrics: async (params?: {
    dateFrom?: string;
    dateTo?: string;
    groupBy?: 'hour' | 'day' | 'week';
  }): Promise<PerformanceMetrics> => {
    const response = await request.get<PerformanceMetrics>('/admin/system/performance', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取性能指标失败');
  },

  // ==================== 系统配置管理 ====================

  // 获取系统配置
  getConfig: async (): Promise<SystemConfig> => {
    const response = await request.get<SystemConfig>('/admin/system/config');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取系统配置失败');
  },

  // 更新系统配置
  updateConfig: async (data: ConfigUpdateData): Promise<SystemConfig> => {
    const response = await request.put<SystemConfig>('/admin/system/config', data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新系统配置失败');
  },

  // 获取特定配置项
  getConfigItem: async (key: string): Promise<any> => {
    const response = await request.get(`/admin/system/config/${key}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取配置项失败');
  },

  // 更新特定配置项
  updateConfigItem: async (key: string, value: any): Promise<void> => {
    const response = await request.put(`/admin/system/config/${key}`, { value });
    if (!response.success) {
      throw new Error(response.message || '更新配置项失败');
    }
  },

  // ==================== 版本控制管理 ====================

  // 获取版本控制配置
  getVersionConfig: async (): Promise<VersionControlConfig> => {
    const response = await request.get<VersionControlConfig>('/admin/system/version-config');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取版本控制配置失败');
  },

  // 更新版本控制配置
  updateVersionConfig: async (data: VersionControlUpdateData): Promise<VersionControlConfig> => {
    const response = await request.put<VersionControlConfig>('/admin/system/version-config', data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新版本控制配置失败');
  },

  // 检查版本更新
  checkVersion: async (platform: 'ios' | 'android' = 'ios'): Promise<VersionCheckResponse> => {
    const response = await request.get<VersionCheckResponse>('/admin/system/check-version', {
      params: { platform }
    });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '版本检查失败');
  },

  // 获取版本信息
  getVersionInfo: async (): Promise<any> => {
    const response = await request.get('/admin/system/version-info');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取版本信息失败');
  },

  // 批量更新配置
  batchUpdateConfig: async (updates: Record<string, any>): Promise<SystemConfig> => {
    const response = await request.post<SystemConfig>('/admin/system/config/batch-update', { updates });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '批量更新配置失败');
  },

  // 导出配置
  exportConfig: async (params: ConfigExportParams): Promise<Blob> => {
    const response = await request.get('/admin/system/config/export', {
      params,
      responseType: 'blob',
    });
    return response as any;
  },

  // 导入配置
  importConfig: async (params: ConfigImportParams): Promise<SystemConfig> => {
    const response = await request.post<SystemConfig>('/admin/system/config/import', params);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '导入配置失败');
  },

  // 重置配置
  resetConfig: async (keys?: string[]): Promise<SystemConfig> => {
    const response = await request.post<SystemConfig>('/admin/system/config/reset', { keys });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '重置配置失败');
  },

  // 验证配置
  validateConfig: async (configData: Record<string, any>): Promise<{ valid: boolean; errors?: string[] }> => {
    const response = await request.post('/admin/system/config/validate', { configData });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '验证配置失败');
  },

  // 获取配置历史
  getConfigHistory: async (params?: { page?: number; limit?: number }): Promise<PaginationResponse<ConfigHistory>> => {
    const response = await request.get<{ history: ConfigHistory[]; pagination: any }>('/admin/system/config/history', { params });
    if (response.success && response.data) {
      return {
        items: response.data.history,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取配置历史失败');
  },

  // ==================== 问题库管理 ====================

  // 获取问题列表
  getQuestions: async (params: QuestionListParams): Promise<PaginationResponse<Question>> => {
    const response = await request.get<{ questions: Question[]; pagination: any }>('/admin/system/questions', { params });
    if (response.success && response.data) {
      return {
        items: response.data.questions,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取问题列表失败');
  },

  // 获取问题详情
  getQuestionById: async (id: string): Promise<Question> => {
    const response = await request.get<Question>(`/admin/system/questions/${id}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取问题详情失败');
  },

  // 创建问题
  createQuestion: async (data: QuestionFormData): Promise<Question> => {
    const response = await request.post<Question>('/admin/system/questions', data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '创建问题失败');
  },

  // 更新问题
  updateQuestion: async (id: string, data: Partial<QuestionFormData>): Promise<Question> => {
    const response = await request.put<Question>(`/admin/system/questions/${id}`, data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新问题失败');
  },

  // 删除问题
  deleteQuestion: async (id: string): Promise<void> => {
    const response = await request.delete(`/admin/system/questions/${id}`);
    if (!response.success) {
      throw new Error(response.message || '删除问题失败');
    }
  },

  // 批量删除问题
  batchDeleteQuestions: async (operation: BatchDeleteQuestions): Promise<void> => {
    const response = await request.post('/admin/system/questions/batch-delete', operation);
    if (!response.success) {
      throw new Error(response.message || '批量删除问题失败');
    }
  },

  // 获取问题统计
  getQuestionStats: async (): Promise<QuestionStats> => {
    const response = await request.get<QuestionStats>('/admin/system/questions/stats');
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取问题统计失败');
  },

  // 搜索问题
  searchQuestions: async (params: {
    keyword: string;
    language?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginationResponse<Question>> => {
    const response = await request.get<{ questions: Question[]; pagination: any }>('/admin/system/questions/search', { params });
    if (response.success && response.data) {
      return {
        items: response.data.questions,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '搜索问题失败');
  },

  // 复制问题
  duplicateQuestion: async (id: string): Promise<Question> => {
    const response = await request.post<Question>(`/admin/system/questions/${id}/duplicate`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '复制问题失败');
  },

  // 导出问题
  exportQuestions: async (params?: {
    format?: 'json' | 'csv';
    language?: string;
    includeAllLanguages?: boolean;
  }): Promise<Blob> => {
    const response = await request.get('/admin/system/questions/export', {
      params,
      responseType: 'blob',
    });
    return response as any;
  },

  // 导入问题
  importQuestions: async (data: {
    questions: QuestionFormData[];
    mode?: 'add' | 'replace';
  }): Promise<{ imported: number; errors?: string[] }> => {
    const response = await request.post('/admin/system/questions/import', data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '导入问题失败');
  },

  // ==================== 日志管理 ====================

  // 获取日志概览
  getLogsOverview: async () => {
    const response = await request.get('/admin/logs/overview');
    return responseHandlers.get(response, '获取日志概览失败');
  },

  // 获取请求日志列表
  getRequestLogs: async (params: LogListParams): Promise<PaginationResponse<RequestLog>> => {
    const response = await request.get<{ logs: RequestLog[]; pagination: any }>('/admin/logs/requests', { params });
    if (response.success && response.data) {
      return {
        items: response.data.logs,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取请求日志失败');
  },

  // 获取响应日志列表
  getResponseLogs: async (params: LogListParams): Promise<PaginationResponse<ResponseLog>> => {
    const response = await request.get<{ logs: ResponseLog[]; pagination: any }>('/admin/logs/responses', { params });
    if (response.success && response.data) {
      return {
        items: response.data.logs,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取响应日志失败');
  },

  // 获取错误日志列表
  getErrorLogs: async (params: LogListParams): Promise<PaginationResponse<ErrorLog>> => {
    const response = await request.get<{ logs: ErrorLog[]; pagination: any }>('/admin/logs/errors', { params });
    if (response.success && response.data) {
      return {
        items: response.data.logs,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '获取错误日志失败');
  },

  // 获取日志详情
  getLogDetails: async (requestId: string): Promise<LogDetails> => {
    const response = await request.get<LogDetails>(`/admin/logs/details/${requestId}`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取日志详情失败');
  },

  // 获取日志统计
  getLogStats: async (params?: {
    dateFrom?: string;
    dateTo?: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<LogStats> => {
    const response = await request.get<LogStats>('/admin/logs/stats', { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取日志统计失败');
  },

  // 清理日志
  cleanupLogs: async (params: LogCleanupParams): Promise<{
    results: any;
    cutoffDate: string;
    daysToKeep: number;
  }> => {
    const response = await request.post('/admin/logs/cleanup', params);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '清理日志失败');
  },

  // 导出日志
  exportLogs: async (params: {
    type: 'request' | 'response' | 'error';
    format?: 'json' | 'csv';
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
  }): Promise<Blob> => {
    const response = await request.get('/admin/logs/export', {
      params,
      responseType: 'blob',
    });
    return response as any;
  },

  // 搜索日志
  searchLogs: async (params: {
    keyword: string;
    type?: 'all' | 'request' | 'response' | 'error';
    page?: number;
    limit?: number;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<PaginationResponse<RequestLog | ResponseLog | ErrorLog>> => {
    const response = await request.get<{ logs: any[]; pagination: any }>('/admin/logs/search', { params });
    if (response.success && response.data) {
      return {
        items: response.data.logs,
        total: response.data.pagination.total,
        page: response.data.pagination.page,
        limit: response.data.pagination.limit,
        totalPages: response.data.pagination.pages,
      };
    }
    throw new Error(response.message || '搜索日志失败');
  },
};
