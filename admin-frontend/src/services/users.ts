import { request } from './api';
import {
  User,
  UserListParams,
  UserStats,
  UserFormData,
  UserDetail,
  BalanceAdjustment,
  BatchUserOperation
} from '@/types/user';
import { PaginationResponse } from '@/types/common';
import { responseHandlers } from '@/utils/apiResponseHandler';

export const userService = {
  // 获取用户列表
  getUsers: async (params: UserListParams): Promise<PaginationResponse<User>> => {
    const response = await request.get<{users: User[], pagination: {page: number, limit: number, total: number, pages: number}}>('/admin/users', { params });
    const data = responseHandlers.get(response, '获取用户列表失败');

    // 转换后端数据格式为前端期望的格式
    return {
      items: data.users,
      total: data.pagination.total,
      page: data.pagination.page,
      limit: data.pagination.limit,
      totalPages: data.pagination.pages
    };
  },

  // 获取用户详情
  getUserById: async (id: string): Promise<UserDetail> => {
    const response = await request.get<UserDetail>(`/admin/users/${id}`);
    return responseHandlers.get(response, '获取用户详情失败');
  },

  // 创建用户
  createUser: async (userData: UserFormData): Promise<User> => {
    const response = await request.post<User>('/admin/users', userData);
    return responseHandlers.create(response, '用户创建成功');
  },

  // 更新用户
  updateUser: async (id: string, userData: Partial<UserFormData>): Promise<User> => {
    const response = await request.put<User>(`/admin/users/${id}`, userData);
    return responseHandlers.update(response, '用户更新成功');
  },

  // 删除用户（软删除）
  deleteUser: async (id: string): Promise<void> => {
    const response = await request.delete(`/admin/users/${id}`);
    return responseHandlers.delete(response, '用户删除成功');
  },

  // 恢复用户
  restoreUser: async (id: string): Promise<void> => {
    const response = await request.post(`/admin/users/${id}/restore`);
    return responseHandlers.update(response, '用户恢复成功');
  },

  // 批量删除用户
  batchDeleteUsers: async (operation: BatchUserOperation): Promise<void> => {
    const response = await request.post('/admin/users/batch-delete', operation);
    return responseHandlers.delete(response, '批量删除成功');
  },

  // 调整用户余额
  adjustBalance: async (adjustment: BalanceAdjustment): Promise<void> => {
    const response = await request.post(`/admin/users/${adjustment.userId}/balance`, {
      amount: adjustment.amount,
      type: adjustment.type === 'decrease' ? 'deduct' : adjustment.type === 'increase' ? 'add' : 'set',
      reason: adjustment.reason,
    });
    return responseHandlers.update(response, '余额调整成功');
  },

  // 调整用户余额（新版本，直接支持后端API格式）
  adjustUserBalance: async (userId: string, data: { type: 'add' | 'deduct' | 'set'; amount: number; reason: string }): Promise<void> => {
    const response = await request.post(`/admin/users/${userId}/balance`, data);
    return responseHandlers.update(response, '余额调整成功');
  },

  // 重置用户密码
  resetPassword: async (id: string, newPassword: string): Promise<void> => {
    const response = await request.post(`/admin/users/${id}/reset-password`, {
      password: newPassword,
    });
    return responseHandlers.update(response, '密码重置成功');
  },

  // 验证用户邮箱
  verifyEmail: async (id: string): Promise<void> => {
    const response = await request.post(`/admin/users/${id}/verify-email`);
    return responseHandlers.update(response, '邮箱验证成功');
  },

  // 获取用户统计信息
  getUserStats: async (): Promise<UserStats> => {
    const response = await request.get<UserStats>('/admin/users/stats');
    return responseHandlers.get(response, '获取用户统计失败');
  },

  // 导出用户数据
  exportUsers: async (params: UserListParams): Promise<Blob> => {
    const response = await request.get('/admin/users/export', {
      params: { ...params, format: 'csv' },
      responseType: 'blob',
    });
    // 对于blob响应，直接返回，不使用响应处理器
    // TODO: 需要根据实际API响应格式调整
    return response as any;
  },

  // 搜索用户
  searchUsers: async (keyword: string, limit = 10): Promise<User[]> => {
    const response = await request.get<User[]>('/admin/users/search', {
      params: { keyword, limit },
    });
    return responseHandlers.list(response, '搜索用户失败');
  },
};
