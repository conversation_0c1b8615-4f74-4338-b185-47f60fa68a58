import { request } from './api';
import { responseHandlers } from '@/utils/apiResponseHandler';

export const reportsService = {
  // 获取报表概览
  getOverview: async () => {
    const response = await request.get('/admin/reports/overview');
    return responseHandlers.get(response, '获取报表概览失败');
  },

  // 用户统计报表
  getUserReports: async (params?: any) => {
    const response = await request.get('/admin/reports/users', { params });
    return responseHandlers.get(response, '获取用户统计失败');
  },

  // 财务统计报表
  getFinancialReports: async (params?: any) => {
    const response = await request.get('/admin/reports/financial', { params });
    return responseHandlers.get(response, '获取财务统计失败');
  },

  // 系统统计报表
  getSystemReports: async (params?: any) => {
    const response = await request.get('/admin/reports/system', { params });
    return responseHandlers.get(response, '获取系统统计失败');
  },

  // 导出报表
  exportReport: async (type: string, params?: any) => {
    const response = await request.get(`/admin/reports/${type}/export`, {
      params,
      responseType: 'blob',
    });
    return response as any;
  },
};
