import { request } from '@/utils/request';

// 模型价格管理服务（基于AI配置系统）
const API_BASE = '/admin/model-pricing';

// 模型价格接口
export interface ModelPricing {
  _id: string;
  modelName: string;
  displayName: string;
  description?: string;
  maxTokens: number;
  supportedFeatures: string[];
  pricing: {
    inputPrice: number;
    outputPrice: number;
    currency: string;
  };
  configId: string;
  configName?: string;
  provider?: string;
  isCurrentModel?: boolean;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

// 查询参数
export interface ModelPricingListParams {
  page?: number;
  limit?: number;
  search?: string;
  configId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 列表响应
export interface ModelPricingListResponse {
  success: boolean;
  data: {
    items: ModelPricing[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message?: string;
}

// 更新数据
export interface ModelPricingUpdateData {
  pricing?: {
    inputPrice?: number;
    outputPrice?: number;
    currency?: string;
  };
  displayName?: string;
  description?: string;
  maxTokens?: number;
}

// 同步结果
export interface SyncModelsResult {
  configName: string;
  totalModels: number;
  defaultPrice: number;
  results: {
    created: number;
    updated: number;
    errors: number;
    models: Array<{
      modelName: string;
      action: 'created' | 'updated' | 'error';
      pricing?: ModelPricing['pricing'];
      error?: string;
    }>;
  };
}

// 修复零价格结果
export interface FixZeroPricingResult {
  fixed: number;
  defaultPrice: number;
  models: string[];
}

export const modelPricingService = {
  // 获取模型价格列表
  getModelPricings: async (params?: ModelPricingListParams): Promise<ModelPricingListResponse['data']> => {
    const response = await request.get<ModelPricingListResponse['data']>(API_BASE, { params });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取模型价格列表失败');
  },

  // 更新模型价格
  updateModelPricing: async (id: string, data: ModelPricingUpdateData): Promise<ModelPricing> => {
    const response = await request.put<ModelPricing>(`${API_BASE}/${id}`, data);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '更新模型价格失败');
  },

  // 批量修复零价格模型
  fixZeroModelPricing: async (): Promise<FixZeroPricingResult> => {
    const response = await request.post<FixZeroPricingResult>(`${API_BASE}/fix-zero-pricing`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '修复零价格失败');
  },

  // 同步模型并设置默认价格
  syncModelsWithPricing: async (configId: string): Promise<SyncModelsResult> => {
    const response = await request.post<SyncModelsResult>(`${API_BASE}/sync-models`, { configId });
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '同步模型失败');
  },

  // 获取当前启用模型的价格信息
  getCurrentModelPricing: async (): Promise<{
    model: ModelPricing;
    configName: string;
    provider: string;
    isActiveModel: boolean;
  }> => {
    const response = await request.get<{
      model: ModelPricing;
      configName: string;
      provider: string;
      isActiveModel: boolean;
    }>(`${API_BASE}/current`);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error(response.message || '获取当前模型价格失败');
  },
};
