import { request } from './api';
import {
  AIServiceConfig,
  AIServiceModel,
  CreateConfigRequest,
  UpdateConfigRequest,
  ConfigListResponse,
  ConfigResponse,
  ModelListResponse,
  TestResponse,
  UsageStatsResponse,
  ActiveAIConfig,
  ActiveConfigStats,
  ActivateConfigRequest,
  ActiveConfigResponse,
  ActiveConfigStatsResponse,
  ApiResponse
} from '../types/aiConfig';

const API_BASE = '/admin/ai';

// 获取AI配置列表
export const getConfigs = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
  provider?: string;
  isActive?: boolean;
}): Promise<ConfigListResponse> => {
  return await request.get(`${API_BASE}/configs`, { params });
};

// 获取单个AI配置
export const getConfig = async (id: string): Promise<ConfigResponse> => {
  return await request.get(`${API_BASE}/configs/${id}`);
};

// 创建AI配置
export const createConfig = async (data: CreateConfigRequest): Promise<ConfigResponse> => {
  return await request.post(`${API_BASE}/configs`, data);
};

// 更新AI配置
export const updateConfig = async (id: string, data: UpdateConfigRequest): Promise<ConfigResponse> => {
  return await request.put(`${API_BASE}/configs/${id}`, data);
};

// 删除AI配置
export const deleteConfig = async (id: string): Promise<ApiResponse> => {
  return await request.delete(`${API_BASE}/configs/${id}`);
};

// 测试AI配置连接
export const testConfig = async (id: string): Promise<TestResponse> => {
  return await request.post(`${API_BASE}/configs/${id}/test`);
};

// 设置默认配置
export const setDefaultConfig = async (id: string): Promise<ApiResponse> => {
  return await request.post(`${API_BASE}/configs/${id}/set-default`);
};

// 同步模型列表
export const syncModels = async (configId: string): Promise<ApiResponse<AIServiceModel[]>> => {
  return await request.post(`${API_BASE}/configs/${configId}/sync-models`);
};

// 获取模型列表
export const getModels = async (params?: {
  configId?: string;
  page?: number;
  limit?: number;
  search?: string;
}): Promise<ModelListResponse> => {
  return await request.get(`${API_BASE}/models`, { params });
};

// 更新模型配置
export const updateModel = async (id: string, data: Partial<AIServiceModel>): Promise<ApiResponse<AIServiceModel>> => {
  return await request.put(`${API_BASE}/models/${id}`, data);
};

// 获取使用统计
export const getUsageStats = async (
  configId: string,
  params?: {
    startDate?: string;
    endDate?: string;
  }
): Promise<UsageStatsResponse> => {
  return await request.get(`${API_BASE}/configs/${configId}/usage-stats`, { params });
};

// 启用AI配置
export const activateConfig = async (configId: string, data: ActivateConfigRequest): Promise<ApiResponse> => {
  return await request.post(`${API_BASE}/configs/${configId}/activate`, data);
};

// 获取当前启用的配置
export const getActiveConfig = async (): Promise<ActiveConfigResponse> => {
  return await request.get(`${API_BASE}/active-config`);
};

// 禁用当前配置
export const deactivateConfig = async (): Promise<ApiResponse> => {
  return await request.delete(`${API_BASE}/active-config`);
};

// 获取配置的可用模型
export const getConfigModels = async (configId: string): Promise<ApiResponse<AIServiceModel[]>> => {
  return await request.get(`${API_BASE}/configs/${configId}/models`);
};

// 获取启用配置统计
export const getActiveConfigStats = async (): Promise<ActiveConfigStatsResponse> => {
  return await request.get(`${API_BASE}/active-config/stats`);
};

// 导出所有服务
export const aiConfigService = {
  getConfigs,
  getConfig,
  createConfig,
  updateConfig,
  deleteConfig,
  testConfig,
  setDefaultConfig,
  syncModels,
  getModels,
  updateModel,
  getUsageStats,
  activateConfig,
  getActiveConfig,
  deactivateConfig,
  getConfigModels,
  getActiveConfigStats,
};

export default aiConfigService;
