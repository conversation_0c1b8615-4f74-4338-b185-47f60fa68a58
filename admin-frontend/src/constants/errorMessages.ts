/**
 * 统一错误消息管理
 * 消除硬编码的错误消息，提供一致的错误提示
 */

/**
 * 通用错误消息
 */
export const COMMON_ERRORS = {
  // 网络相关错误
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  REQUEST_TIMEOUT: '请求超时，请稍后重试',
  SERVER_ERROR: '服务器错误，请稍后重试',
  
  // 认证相关错误
  AUTH_FAILED: '认证失败，请重新登录',
  TOKEN_EXPIRED: '登录已过期，请重新登录',
  PERMISSION_DENIED: '权限不足，无法执行此操作',
  LOGIN_FAILED: '登录失败，请检查用户名和密码',
  LOGOUT_FAILED: '登出失败',
  
  // 数据验证错误
  VALIDATION_ERROR: '数据验证失败',
  REQUIRED_FIELD_MISSING: '必填字段不能为空',
  INVALID_FORMAT: '数据格式不正确',
  INVALID_EMAIL: '邮箱格式不正确',
  INVALID_PASSWORD: '密码格式不正确',
  PASSWORD_MISMATCH: '两次输入的密码不一致',
  
  // 操作相关错误
  OPERATION_FAILED: '操作失败',
  SAVE_FAILED: '保存失败',
  DELETE_FAILED: '删除失败',
  UPDATE_FAILED: '更新失败',
  CREATE_FAILED: '创建失败',
  LOAD_FAILED: '加载失败',
  
  // 数据相关错误
  DATA_NOT_FOUND: '数据不存在',
  DATA_ALREADY_EXISTS: '数据已存在',
  DATA_CORRUPTED: '数据已损坏',
  
  // 文件相关错误
  FILE_UPLOAD_FAILED: '文件上传失败',
  FILE_TOO_LARGE: '文件大小超出限制',
  INVALID_FILE_TYPE: '不支持的文件类型',
  
  // 其他错误
  UNKNOWN_ERROR: '未知错误',
  FEATURE_NOT_AVAILABLE: '功能暂不可用',
  MAINTENANCE_MODE: '系统维护中，请稍后访问'
} as const;

/**
 * 用户管理相关错误消息
 */
export const USER_ERRORS = {
  // 获取用户信息
  GET_USER_LIST_FAILED: '获取用户列表失败',
  GET_USER_DETAIL_FAILED: '获取用户详情失败',
  GET_USER_STATS_FAILED: '获取用户统计失败',
  SEARCH_USER_FAILED: '搜索用户失败',
  
  // 用户操作
  CREATE_USER_FAILED: '创建用户失败',
  UPDATE_USER_FAILED: '更新用户失败',
  DELETE_USER_FAILED: '删除用户失败',
  RESTORE_USER_FAILED: '恢复用户失败',
  BATCH_DELETE_FAILED: '批量删除失败',
  
  // 用户状态
  USER_NOT_FOUND: '用户不存在',
  USER_ALREADY_EXISTS: '用户已存在',
  USER_DISABLED: '用户已被禁用',
  
  // 用户余额
  ADJUST_BALANCE_FAILED: '调整余额失败',
  INSUFFICIENT_BALANCE: '余额不足',
  INVALID_AMOUNT: '金额无效',
  
  // 用户密码
  RESET_PASSWORD_FAILED: '重置密码失败',
  CHANGE_PASSWORD_FAILED: '修改密码失败',
  WEAK_PASSWORD: '密码强度不够',
  
  // 用户邮箱
  VERIFY_EMAIL_FAILED: '验证邮箱失败',
  EMAIL_ALREADY_VERIFIED: '邮箱已验证',
  INVALID_EMAIL_CODE: '邮箱验证码无效',
  
  // 导出功能
  EXPORT_USERS_FAILED: '导出用户数据失败',
  EXPORT_FORMAT_ERROR: '导出格式错误'
} as const;

/**
 * 聊天管理相关错误消息
 */
export const CHAT_ERRORS = {
  GET_CHAT_LIST_FAILED: '获取聊天列表失败',
  GET_CHAT_DETAIL_FAILED: '获取聊天详情失败',
  DELETE_CHAT_FAILED: '删除聊天失败',
  EXPORT_CHAT_FAILED: '导出聊天记录失败',
  CHAT_NOT_FOUND: '聊天记录不存在',
  MESSAGE_TOO_LONG: '消息内容过长',
  INVALID_MESSAGE_FORMAT: '消息格式无效'
} as const;

/**
 * 财务管理相关错误消息
 */
export const FINANCIAL_ERRORS = {
  GET_TRANSACTION_LIST_FAILED: '获取交易记录失败',
  GET_FINANCIAL_STATS_FAILED: '获取财务统计失败',
  PROCESS_REFUND_FAILED: '处理退款失败',
  INVALID_TRANSACTION: '无效的交易记录',
  TRANSACTION_NOT_FOUND: '交易记录不存在',
  REFUND_ALREADY_PROCESSED: '退款已处理',
  AMOUNT_MISMATCH: '金额不匹配'
} as const;

/**
 * 系统管理相关错误消息
 */
export const SYSTEM_ERRORS = {
  GET_CONFIG_FAILED: '获取系统配置失败',
  UPDATE_CONFIG_FAILED: '更新系统配置失败',
  GET_LOGS_FAILED: '获取系统日志失败',
  CLEAR_LOGS_FAILED: '清理日志失败',
  BACKUP_FAILED: '备份失败',
  RESTORE_FAILED: '恢复失败',
  CONFIG_VALIDATION_FAILED: '配置验证失败',
  INVALID_CONFIG_VALUE: '配置值无效'
} as const;

/**
 * 成功消息
 */
export const SUCCESS_MESSAGES = {
  // 通用成功消息
  OPERATION_SUCCESS: '操作成功',
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  UPDATE_SUCCESS: '更新成功',
  CREATE_SUCCESS: '创建成功',
  
  // 用户相关成功消息
  USER_CREATED: '用户创建成功',
  USER_UPDATED: '用户更新成功',
  USER_DELETED: '用户删除成功',
  USER_RESTORED: '用户恢复成功',
  BALANCE_ADJUSTED: '余额调整成功',
  PASSWORD_RESET: '密码重置成功',
  EMAIL_VERIFIED: '邮箱验证成功',
  
  // 认证相关成功消息
  LOGIN_SUCCESS: '登录成功',
  LOGOUT_SUCCESS: '登出成功',
  PASSWORD_CHANGED: '密码修改成功',
  
  // 系统相关成功消息
  CONFIG_UPDATED: '配置更新成功',
  BACKUP_CREATED: '备份创建成功',
  LOGS_CLEARED: '日志清理成功'
} as const;

/**
 * 错误消息类型定义
 */
export type ErrorMessageKey = 
  | keyof typeof COMMON_ERRORS
  | keyof typeof USER_ERRORS
  | keyof typeof CHAT_ERRORS
  | keyof typeof FINANCIAL_ERRORS
  | keyof typeof SYSTEM_ERRORS;

export type SuccessMessageKey = keyof typeof SUCCESS_MESSAGES;

/**
 * 错误消息工具类
 */
export class ErrorMessageHelper {
  /**
   * 获取错误消息
   * @param key 错误消息键
   * @param defaultMessage 默认消息
   * @returns 错误消息
   */
  static getErrorMessage(key: ErrorMessageKey, defaultMessage?: string): string {
    // 按优先级查找错误消息
    const message = 
      COMMON_ERRORS[key as keyof typeof COMMON_ERRORS] ||
      USER_ERRORS[key as keyof typeof USER_ERRORS] ||
      CHAT_ERRORS[key as keyof typeof CHAT_ERRORS] ||
      FINANCIAL_ERRORS[key as keyof typeof FINANCIAL_ERRORS] ||
      SYSTEM_ERRORS[key as keyof typeof SYSTEM_ERRORS] ||
      defaultMessage ||
      COMMON_ERRORS.UNKNOWN_ERROR;
    
    return message;
  }

  /**
   * 获取成功消息
   * @param key 成功消息键
   * @param defaultMessage 默认消息
   * @returns 成功消息
   */
  static getSuccessMessage(key: SuccessMessageKey, defaultMessage?: string): string {
    return SUCCESS_MESSAGES[key] || defaultMessage || SUCCESS_MESSAGES.OPERATION_SUCCESS;
  }

  /**
   * 根据HTTP状态码获取错误消息
   * @param statusCode HTTP状态码
   * @returns 错误消息
   */
  static getErrorByStatusCode(statusCode: number): string {
    switch (statusCode) {
      case 400:
        return COMMON_ERRORS.VALIDATION_ERROR;
      case 401:
        return COMMON_ERRORS.AUTH_FAILED;
      case 403:
        return COMMON_ERRORS.PERMISSION_DENIED;
      case 404:
        return COMMON_ERRORS.DATA_NOT_FOUND;
      case 408:
        return COMMON_ERRORS.REQUEST_TIMEOUT;
      case 500:
        return COMMON_ERRORS.SERVER_ERROR;
      default:
        return COMMON_ERRORS.UNKNOWN_ERROR;
    }
  }

  /**
   * 格式化错误消息（支持参数替换）
   * @param template 消息模板
   * @param params 参数对象
   * @returns 格式化后的消息
   */
  static formatMessage(template: string, params: Record<string, string | number>): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key]?.toString() || match;
    });
  }
}

/**
 * 便捷的错误消息获取函数
 */
export const getErrorMessage = ErrorMessageHelper.getErrorMessage;
export const getSuccessMessage = ErrorMessageHelper.getSuccessMessage;
