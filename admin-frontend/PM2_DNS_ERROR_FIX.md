# Admin Frontend PM2 DNS 错误修复总结

## 🐛 问题描述

admin-frontend 应用在使用 PM2 启动时出现 DNS 解析错误：

```
Error: getaddrinfo ENOTFOUND -l
Error: getaddrinfo ENOTFOUND -p
```

应用构建成功但 PM2 服务立即进入 "errored" 状态，重启次数达到 15 次。

## 🔍 问题分析

### 根本原因
PM2 配置文件中 `serve` 命令的参数格式不正确，导致参数解析错误：

1. **原始错误配置**：
   ```javascript
   script: 'serve',
   args: `-s dist -l 54001`,  // ❌ -l 参数被误用
   ```

2. **第一次修复尝试**：
   ```javascript
   script: 'serve',
   args: `-s dist -p 54001`,  // ❌ 字符串格式仍有解析问题
   ```

3. **第二次修复尝试**：
   ```javascript
   script: 'serve',
   args: ['-s', 'dist', '-p', '54001'],  // ❌ 数组格式但仍有问题
   ```

### 问题详解
- `serve` 命令中 `-l` 参数用于指定监听地址，不是端口
- 正确的端口参数应该是 `-p`
- PM2 在直接执行 `serve` 时存在参数解析问题
- 需要使用 `npx` 来确保正确执行

## ✅ 解决方案

### 最终修复配置
```javascript
// pm2.config.cjs
{
  script: 'npx',                           // 使用 npx 执行
  args: ['serve', '-s', 'dist', '-p', '54001'],  // 数组格式参数
}
```

### 修复步骤
1. **停止并删除错误的 PM2 进程**：
   ```bash
   pm2 stop admin-frontend
   pm2 delete admin-frontend
   ```

2. **修改 PM2 配置文件**：
   - 将 `script: 'serve'` 改为 `script: 'npx'`
   - 将参数改为数组格式：`['serve', '-s', 'dist', '-p', '54001']`

3. **重新启动应用**：
   ```bash
   pm2 start pm2.config.cjs --only admin-frontend
   ```

## 🧪 验证结果

### 应用状态
```
┌────┬───────────────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name                      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 4  │ admin-frontend            │ default     │ N/A     │ fork    │ 31636    │ 16s    │ 0    │ online    │ 0%       │ 101.6mb  │ zha… │ disabled │
└────┴───────────────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
```

### HTTP 响应测试
```bash
$ curl -I http://localhost:54001
HTTP/1.1 200 OK
Content-Length: 724
Content-Type: text/html; charset=utf-8
```

### 正常日志输出
```
INFO  Accepting connections at http://localhost:54001
HTTP  2025/8/6 20:51:42 ::1 HEAD /
HTTP  2025/8/6 20:51:42 ::1 Returned 200 in 15 ms
```

## 📚 技术要点

### serve 命令参数说明
- `-s, --single`: 单页应用模式，将所有 404 请求重写到 index.html
- `-p`: 指定端口号
- `-l, --listen`: 指定监听地址（不是端口）

### PM2 参数传递最佳实践
1. **使用数组格式**：避免字符串解析问题
2. **使用 npx**：确保正确执行 npm 包命令
3. **明确参数顺序**：按照命令行工具的要求传递参数

## 🔧 相关文件修改

### 修改的文件
- `admin-frontend/pm2.config.cjs` - PM2 配置文件

### 修改内容
```diff
- script: 'serve',
- args: `-s dist -l 54001`,
+ script: 'npx',
+ args: ['serve', '-s', 'dist', '-p', '54001'],
```

## 🎉 总结

通过正确配置 PM2 的 `script` 和 `args` 参数，成功解决了 DNS 解析错误问题：

1. **问题根源**：参数格式和解析错误
2. **解决方案**：使用 `npx` + 数组格式参数
3. **验证结果**：应用稳定运行，HTTP 服务正常

现在 admin-frontend 应用可以通过 `yarn pm-release` 正常启动并稳定运行在端口 54001 上。
