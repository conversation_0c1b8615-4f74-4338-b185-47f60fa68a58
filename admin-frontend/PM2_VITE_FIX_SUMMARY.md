# PM2 配置和 Vite 构建警告修复总结

## 问题描述

1. **PM2 日志目录问题**：PM2 无法创建 `/home/<USER>/gitlab/production/admin-frontend/logs` 目录
2. **Vite 构建警告**：`NODE_ENV=production is not supported in the .env file`

## 解决方案

### 1. PM2 配置优化 (`pm2.config.cjs`)

**修复内容**：
- 实现智能路径解析：优先使用生产路径，失败时回退到当前目录
- 添加日志目录自动创建和权限检查
- 实现多级回退机制：生产路径 → 当前目录 → 系统临时目录
- 添加详细的日志输出，便于调试

**核心功能**：
```javascript
// 智能路径解析函数
function resolveWorkingPath() {
  const preferredPath = '/home/<USER>/gitlab/production/admin-frontend';
  const fallbackPath = process.cwd();
  
  // 检查路径存在性和写权限
  // 自动回退到可用路径
}

// 确保日志目录存在
function ensureLogsDirectory(basePath) {
  // 创建日志目录，失败时使用临时目录
}
```

### 2. Vite 环境变量修复

**问题原因**：
- Vite 不支持在 `.env` 文件中设置 `NODE_ENV`
- 该变量由 Vite 根据构建模式自动管理

**修复内容**：
- 从 `.env.production` 和 `.env.development` 中移除 `NODE_ENV` 设置
- 更新 `vite.config.ts` 使用 `mode` 参数而不是 `process.env.NODE_ENV`
- 修改构建脚本使用 `--mode production` 参数

**修改的文件**：
- `.env.production`: 移除 `NODE_ENV=production`
- `.env.development`: 移除 `NODE_ENV=development`
- `vite.config.ts`: 使用 `mode === 'production'` 判断环境
- `package.json`: 更新构建脚本为 `vite build --mode production`

### 3. 新增工具脚本

**测试脚本** (`scripts/test-pm2-config.cjs`)：
- 验证 PM2 配置的路径解析
- 检查工作目录和日志目录权限
- 验证 dist 目录和必要文件
- 显示环境变量配置

**安装脚本** (`scripts/setup-pm2.sh`)：
- 自动安装 PM2 和 serve
- 检查 Node.js 环境
- 运行配置测试

**新增 package.json 脚本**：
```json
{
  "pm-test": "node scripts/test-pm2-config.cjs",
  "pm-stop": "pm2 stop admin-frontend",
  "pm-restart": "pm2 restart admin-frontend",
  "pm-logs": "pm2 logs admin-frontend"
}
```

## 使用方法

### 开发环境测试
```bash
# 测试 PM2 配置
yarn pm-test

# 构建项目（无警告）
yarn build:prod

# 启动 PM2 应用
yarn pm-release
```

### 生产环境部署
```bash
# 在生产服务器上，PM2 会自动使用 /home/<USER>/gitlab/production/admin-frontend
# 在其他环境中，会自动回退到当前目录

# 查看应用状态
pm2 status

# 查看日志
yarn pm-logs
```

## 技术要点

### 环境变量处理
- **开发环境**: Vite 自动设置 `NODE_ENV=development`
- **生产环境**: 使用 `--mode production` 触发生产模式
- **自定义变量**: 使用 `VITE_` 前缀的变量可在代码中访问

### 路径回退策略
1. **首选路径**: `/home/<USER>/gitlab/production/admin-frontend`
2. **回退路径**: 当前工作目录 (`process.cwd()`)
3. **最终回退**: 系统临时目录 (`os.tmpdir()`)

### 权限检查
- 创建测试文件验证写权限
- 自动创建必要的目录结构
- 详细的错误日志和回退信息

## 验证结果

✅ **Vite 构建警告已消除**
✅ **PM2 配置支持多环境部署**
✅ **日志目录自动创建和权限处理**
✅ **完整的测试和调试工具**

## 后续建议

1. **代码分割优化**: 当前 JS 包较大 (747KB)，建议实现动态导入
2. **监控集成**: 可考虑集成 PM2 监控面板
3. **自动化部署**: 可结合 CI/CD 流水线自动化部署流程
