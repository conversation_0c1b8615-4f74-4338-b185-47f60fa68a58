# ChatAdvisorServer V2版本流水线部署指南

## 概述

参考admin-frontend的最佳实践，ChatAdvisorServer现已支持通过GitLab CI/CD流水线自动构建和部署v2版本到线上环境。

## 核心改进

### 1. 流水线优化 (参考admin-frontend)
- **合并构建与部署**：将构建和部署合并为一个原子阶段，提升可靠性
- **分离环境**：临时构建环境与持久生产环境完全分离
- **权限管理**：使用`sudo -u ubuntu`确保正确的文件所有权
- **绝对路径**：所有配置使用绝对路径，避免路径问题

### 2. 版本支持
- **V1版本**：`/home/<USER>/gitlab/production/chat-advisor-server` (端口53011)
- **V2版本**：`/home/<USER>/gitlab/production/chat-advisor-server-v2` (端口53012)
- **独立部署**：两个版本完全独立，互不影响

### 3. PM2配置升级
- **动态路径检测**：自动识别v1/v2部署环境
- **绝对路径配置**：日志文件、工作目录使用绝对路径
- **版本自适应**：根据部署路径自动调整配置

## 部署方法

### 方法1：通过CI/CD变量
在GitLab项目的CI/CD设置中添加变量：
- 变量名：`SERVICE_VERSION`
- 变量值：`v2`
- 作用域：所有环境

### 方法2：在提交信息中指定
```bash
git commit -m "release: 部署v2版本新功能"
# 然后在GitLab Web界面中手动触发，并设置SERVICE_VERSION=v2
```

### 方法3：修改默认版本（临时）
在`.gitlab-ci.yml`中修改：
```yaml
variables:
  SERVICE_VERSION: "v2"  # 临时修改默认版本
```

## 部署流程

### 自动触发条件
- 提交信息包含`release:`关键字
- 或通过GitLab Web界面手动触发

### 流水线阶段
1. **build-and-deploy**：构建TypeScript并部署到生产目录
2. **test**：健康检查，确保服务正常运行
3. **notify**：发送部署结果通知

### 部署目录结构
```
/home/<USER>/gitlab/production/
├── chat-advisor-server/          # V1版本
│   ├── dist.current/            # 当前运行的构建产物
│   ├── dist.backup.YYYYMMDD_HHMMSS/  # 自动备份
│   ├── logs/                    # 日志文件
│   ├── package.json
│   └── pm2.config.cjs
└── chat-advisor-server-v2/       # V2版本
    ├── dist.current/            # 当前运行的构建产物
    ├── dist.backup.YYYYMMDD_HHMMSS/  # 自动备份
    ├── logs/                    # 独立的日志文件
    ├── package.json
    └── pm2.config.cjs
```

## 测试验证

### 1. 部署v2版本
```bash
# 设置GitLab CI/CD变量 SERVICE_VERSION=v2
# 提交代码触发流水线
git commit -m "release: 测试v2版本部署"
git push origin main
```

### 2. 验证部署结果
```bash
# 检查PM2状态
sudo -u ubuntu pm2 status

# 确认v2服务运行
sudo -u ubuntu pm2 show chat-advisor-release-v2

# 测试API响应
curl http://localhost:53012/health

# 查看v2版本日志
sudo -u ubuntu pm2 logs chat-advisor-release-v2
```

### 3. 验证v1版本未受影响
```bash
# 确认v1服务仍在运行
sudo -u ubuntu pm2 show chat-advisor-release

# 测试v1版本API
curl http://localhost:53011/health
```

## 监控和管理

### PM2命令
```bash
# 查看所有服务状态
sudo -u ubuntu pm2 status

# 重启v2版本
sudo -u ubuntu pm2 restart chat-advisor-release-v2

# 停止v2版本
sudo -u ubuntu pm2 stop chat-advisor-release-v2

# 查看v2版本日志
sudo -u ubuntu pm2 logs chat-advisor-release-v2

# 实时监控
sudo -u ubuntu pm2 monit
```

### 日志文件位置
- **V1版本**：`/home/<USER>/gitlab/production/chat-advisor-server/logs/`
- **V2版本**：`/home/<USER>/gitlab/production/chat-advisor-server-v2/logs/`

## 回滚策略

### 快速回滚
如果v2版本出现问题：
```bash
# 停止v2版本
sudo -u ubuntu pm2 stop chat-advisor-release-v2

# 确保v1版本运行正常
sudo -u ubuntu pm2 restart chat-advisor-release
```

### 版本切换
```bash
# 从v2切换回v1版本部署
# 在GitLab中设置 SERVICE_VERSION=v1 或删除该变量
# 然后重新触发流水线
```

## 注意事项

1. **端口隔离**：v1使用53011，v2使用53012，避免冲突
2. **数据库共享**：两个版本共享同一数据库，注意兼容性
3. **备份机制**：每次部署自动备份前一版本，保留最近5个备份
4. **权限一致**：所有文件由ubuntu用户所有，避免权限问题
5. **健康检查**：部署后自动进行健康检查，失败时终止流水线

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   # 修复部署目录权限
   sudo chown -R ubuntu:ubuntu /home/<USER>/gitlab/production/chat-advisor-server-v2
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep 53012
   ```

3. **PM2服务异常**
   ```bash
   # 查看详细错误日志
   sudo -u ubuntu pm2 logs chat-advisor-release-v2 --err
   ```

## 迁移计划

### 推荐步骤
1. **部署v2版本**：确保v2版本稳定运行
2. **并行测试**：同时测试v1和v2版本功能
3. **流量切换**：将负载均衡器指向v2端口
4. **监控观察**：观察v2版本运行状况
5. **停止v1版本**：确认v2稳定后停止v1

---

*本指南基于admin-frontend的成功实践，确保ChatAdvisorServer v2版本的安全可靠部署。*