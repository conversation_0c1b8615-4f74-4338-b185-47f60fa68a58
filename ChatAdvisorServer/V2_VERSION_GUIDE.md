# ChatAdvisorServer V2版本使用指南

## 概述

我们已成功为ChatAdvisorServer添加了v2版本支持，实现了v1和v2版本的并行运行。这允许您在不影响现有v1服务的情况下，安全地部署和测试v2版本。

## 版本对比

| 项目 | V1版本 | V2版本 |
|------|--------|--------|
| **生产服务名** | `chat-advisor-release` | `chat-advisor-release-v2` |
| **调试服务名** | `chat-advisor-debug` | `chat-advisor-debug-v2` |
| **生产端口** | 53011 | 53012 |
| **调试端口** | 33001 | 33002 |
| **日志文件** | `logs/release-*.log` | `logs/release-v2-*.log` |
| **健康检查** | `http://localhost:53011/health` | `http://localhost:53012/health` |

## 使用方法

### 1. 本地开发

#### 启动v2版本服务
```bash
# 生产环境v2
npm run pm-release-v2

# 调试环境v2  
npm run pm-dev-v2

# 查看服务状态
pm2 status
```

### 2. 部署脚本

#### 使用pm2-deploy.sh脚本
```bash
# 部署后端v2版本到生产环境
./scripts/pm2-deploy.sh backend deploy production v2

# 启动后端v2版本
./scripts/pm2-deploy.sh backend start production v2

# 查看v2版本状态
./scripts/pm2-deploy.sh backend status production v2

# 查看v2版本日志
./scripts/pm2-deploy.sh backend logs production v2

# 健康检查v2版本
./scripts/pm2-deploy.sh backend health production v2
```

### 3. CI/CD部署

#### 使用GitLab CI/CD部署v2版本

在GitLab中设置CI/CD变量：
- 变量名：`SERVICE_VERSION`  
- 变量值：`v2`

或者在`.gitlab-ci.yml`文件中临时设置：
```yaml
variables:
  SERVICE_VERSION: "v2"
```

### 4. 手动PM2操作

```bash
# 启动v2版本
pm2 start pm2.config.cjs --only chat-advisor-release-v2

# 停止v2版本  
pm2 stop chat-advisor-release-v2

# 重启v2版本
pm2 restart chat-advisor-release-v2

# 查看v2版本详情
pm2 show chat-advisor-release-v2

# 查看v2版本日志
pm2 logs chat-advisor-release-v2

# 删除v2版本进程
pm2 delete chat-advisor-release-v2
```

## 迁移策略

### 安全迁移流程
1. **启动v2版本**：在新端口启动v2服务
2. **测试验证**：确保v2版本功能正常
3. **流量切换**：将负载均衡或反向代理指向v2端口
4. **监控观察**：观察v2版本运行状态
5. **停止v1版本**：确认v2稳定后停止v1

### 回滚方案
如果v2版本出现问题，可以快速回滚：
```bash
# 停止v2版本
pm2 stop chat-advisor-release-v2

# 确保v1版本运行
pm2 start chat-advisor-release
```

## 环境变量支持

所有脚本都支持通过环境变量指定版本：

```bash
# 设置使用v2版本
export SERVICE_VERSION=v2

# 运行任何脚本都将使用v2版本
./scripts/deploy-backend.sh
```

## 监控和日志

### 日志文件位置
- **V1版本日志**：`logs/release-*.log`, `logs/debug-*.log`
- **V2版本日志**：`logs/release-v2-*.log`, `logs/debug-v2-*.log`

### 同时监控两个版本
```bash
# 监控所有服务
pm2 logs

# 只监控v1版本
pm2 logs chat-advisor-release

# 只监控v2版本  
pm2 logs chat-advisor-release-v2

# 实时监控界面
pm2 monit
```

## 注意事项

1. **端口冲突**：v1和v2使用不同端口，避免冲突
2. **数据库**：两个版本共享同一数据库，注意数据兼容性
3. **配置文件**：确保配置文件在两个版本间保持一致
4. **资源占用**：同时运行两个版本会占用更多系统资源
5. **日志空间**：注意监控日志文件大小，及时清理

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :53012
   
   # 强制停止占用进程
   pm2 delete chat-advisor-release-v2
   ```

2. **服务启动失败**
   ```bash
   # 查看详细错误日志
   pm2 logs chat-advisor-release-v2 --err
   
   # 检查配置文件
   node -c pm2.config.cjs
   ```

3. **健康检查失败**
   ```bash
   # 手动检查健康状态
   curl http://localhost:53012/health
   
   # 检查服务是否真正启动
   pm2 show chat-advisor-release-v2
   ```

## 联系支持

如果在使用v2版本过程中遇到问题，请：
1. 查看上述故障排除指南
2. 检查相关日志文件
3. 联系开发团队获取支持

---

*该指南涵盖了ChatAdvisorServer v2版本的所有使用场景。请根据实际需要选择合适的操作方式。*