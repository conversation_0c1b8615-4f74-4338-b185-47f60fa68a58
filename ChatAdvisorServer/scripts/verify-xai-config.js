#!/usr/bin/env node

/**
 * xAI 配置验证脚本
 * 验证环境变量配置是否符合 XAI_CONFIGURATION.md 的要求
 */

require('dotenv').config();

const chalk = require('chalk');

console.log(chalk.blue.bold('\n🔍 xAI 配置验证\n'));

// 必需的配置项
const requiredConfigs = {
    'OPENAI_API_KEY': {
        expected: '************************************************************************************',
        description: 'xAI API 密钥'
    },
    'OPENAI_BASE_URL': {
        expected: 'https://api.x.ai/v1/',
        description: 'xAI API 基础 URL'
    },
    'NODE_ENV': {
        expected: ['development', 'production', 'test'],
        description: '运行环境'
    },
    'PORT': {
        expected: {
            development: '33001',
            production: '53011'
        },
        description: '服务端口'
    }
};

let allValid = true;

// 验证配置
Object.entries(requiredConfigs).forEach(([key, config]) => {
    const value = process.env[key];
    const { expected, description } = config;
    
    console.log(chalk.cyan(`📋 ${key} (${description}):`));
    
    if (!value) {
        console.log(chalk.red(`   ❌ 未设置`));
        allValid = false;
        return;
    }
    
    console.log(chalk.gray(`   当前值: ${value}`));
    
    if (key === 'PORT') {
        const nodeEnv = process.env.NODE_ENV || 'development';
        const expectedPort = expected[nodeEnv];
        if (value === expectedPort) {
            console.log(chalk.green(`   ✅ 正确 (${nodeEnv} 环境)`));
        } else {
            console.log(chalk.yellow(`   ⚠️  建议值: ${expectedPort} (${nodeEnv} 环境)`));
        }
    } else if (Array.isArray(expected)) {
        if (expected.includes(value)) {
            console.log(chalk.green(`   ✅ 正确`));
        } else {
            console.log(chalk.red(`   ❌ 期望值: ${expected.join(' | ')}`));
            allValid = false;
        }
    } else {
        if (value === expected) {
            console.log(chalk.green(`   ✅ 正确`));
        } else {
            console.log(chalk.red(`   ❌ 期望值: ${expected}`));
            allValid = false;
        }
    }
    
    console.log();
});

// 额外检查
console.log(chalk.blue.bold('🔧 额外检查:\n'));

// 检查 API 密钥格式
const apiKey = process.env.OPENAI_API_KEY;
if (apiKey && apiKey.startsWith('xai-')) {
    console.log(chalk.green('✅ API 密钥格式正确 (xAI 格式)'));
} else if (apiKey && apiKey.startsWith('sk-')) {
    console.log(chalk.yellow('⚠️  检测到 OpenAI 格式的 API 密钥，请确认是否需要切换到 xAI'));
} else {
    console.log(chalk.red('❌ API 密钥格式不正确'));
    allValid = false;
}

// 检查 URL 格式
const baseUrl = process.env.OPENAI_BASE_URL;
if (baseUrl && baseUrl.includes('x.ai')) {
    console.log(chalk.green('✅ 基础 URL 指向 xAI'));
} else if (baseUrl && baseUrl.includes('openai.com')) {
    console.log(chalk.yellow('⚠️  基础 URL 指向 OpenAI，请确认是否需要切换到 xAI'));
} else {
    console.log(chalk.red('❌ 基础 URL 格式不正确'));
    allValid = false;
}

console.log();

// 总结
if (allValid) {
    console.log(chalk.green.bold('🎉 所有配置验证通过！'));
    console.log(chalk.green('您可以启动服务了：'));
    console.log(chalk.cyan('   npm run dev    # 开发环境'));
    console.log(chalk.cyan('   npm start      # 生产环境'));
} else {
    console.log(chalk.red.bold('❌ 配置验证失败！'));
    console.log(chalk.yellow('请根据上述提示修正配置后重新验证。'));
    process.exit(1);
}

console.log();

// 显示当前配置摘要
console.log(chalk.blue.bold('📊 当前配置摘要:'));
console.log(chalk.gray(`   环境: ${process.env.NODE_ENV || 'development'}`));
console.log(chalk.gray(`   端口: ${process.env.PORT || '未设置'}`));
console.log(chalk.gray(`   API: ${baseUrl || '未设置'}`));
console.log(chalk.gray(`   密钥: ${apiKey ? apiKey.substring(0, 10) + '...' : '未设置'}`));
console.log();
