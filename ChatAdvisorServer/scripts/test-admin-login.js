const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config({ path: '.env.production' });

// 用户模型定义
const userSchema = new mongoose.Schema({
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    fullName: String,
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    username: String,
    language: String,
    balance: { type: Number, default: 0 },
    isDelete: { type: Boolean, default: false }
});

const User = mongoose.model('User', userSchema);

async function testAdminLogin() {
    try {
        console.log('🔗 连接到数据库...');
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor');
        console.log('✅ 数据库连接成功');

        // 查找管理员用户
        const adminUser = await User.findOne({ 
            email: '<EMAIL>' 
        }).select('+password');

        if (!adminUser) {
            console.log('❌ 未找到管理员用户');
            return;
        }

        console.log('✅ 找到管理员用户:');
        console.log(`📧 邮箱: ${adminUser.email}`);
        console.log(`👤 用户名: ${adminUser.username || 'N/A'}`);
        console.log(`👑 角色: ${adminUser.role}`);
        console.log(`📊 状态: ${adminUser.status}`);
        console.log(`🗑️ 已删除: ${adminUser.isDelete}`);

        // 测试不同的密码
        const testPasswords = [
            'admin123456',
            'HwgZFWHn17U7zaw+',
            process.env.ADMIN_PASSWORD || 'admin123456'
        ];

        console.log('\n🔐 测试密码:');
        for (const password of testPasswords) {
            if (password) {
                try {
                    const isMatch = await bcrypt.compare(password, adminUser.password);
                    console.log(`   "${password}": ${isMatch ? '✅ 匹配' : '❌ 不匹配'}`);
                    if (isMatch) {
                        console.log(`🎉 正确密码是: ${password}`);
                    }
                } catch (error) {
                    console.log(`   "${password}": ❌ 验证错误 - ${error.message}`);
                }
            }
        }

        // 如果需要，重置密码为 admin123456
        console.log('\n🔄 是否需要重置密码为 admin123456？');
        const resetPassword = 'admin123456';
        const hashedNewPassword = await bcrypt.hash(resetPassword, 12);
        
        await User.findByIdAndUpdate(adminUser._id, {
            password: hashedNewPassword
        });
        
        console.log('✅ 密码已重置为: admin123456');

    } catch (error) {
        console.error('❌ 错误:', error.message);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 数据库连接已关闭');
    }
}

testAdminLogin();
