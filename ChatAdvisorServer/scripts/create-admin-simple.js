const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config({ path: '.env.production' });

// 用户模型定义
const userSchema = new mongoose.Schema({
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    fullName: String,
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    username: String,
    language: { type: String, default: 'zh_CN' },
    balance: { type: Number, default: 0 },
    isDelete: { type: Boolean, default: false },
    emailVerified: { type: Boolean, default: true },
    isVip: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

async function createAdmin() {
    try {
        console.log('🔗 连接到数据库...');
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor');
        console.log('✅ 数据库连接成功');

        // 检查是否已存在管理员
        const existingAdmin = await User.findOne({ email: '<EMAIL>' });
        if (existingAdmin) {
            console.log('⚠️  管理员用户已存在，跳过创建');
            return;
        }

        // 创建管理员用户
        const adminPassword = 'admin123456'; // 使用简单密码进行测试
        const hashedPassword = await bcrypt.hash(adminPassword, 12);

        const adminUser = new User({
            email: '<EMAIL>',
            password: hashedPassword,
            username: 'admin',
            fullName: '系统管理员',
            role: 'super_admin',
            status: 'active',
            language: 'zh_CN',
            balance: 0,
            emailVerified: true,
            isVip: true,
            isDelete: false
        });

        await adminUser.save();
        console.log('✅ 管理员用户创建成功！');
        console.log(`📧 邮箱: <EMAIL>`);
        console.log(`🔑 密码: ${adminPassword}`);
        console.log(`👑 角色: super_admin`);

    } catch (error) {
        console.error('❌ 创建管理员用户失败:', error.message);
        if (error.code === 11000) {
            console.log('⚠️  用户已存在');
        }
    } finally {
        await mongoose.disconnect();
        console.log('🔌 数据库连接已关闭');
    }
}

createAdmin();
