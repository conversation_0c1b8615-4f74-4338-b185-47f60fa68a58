#!/usr/bin/env node

/**
 * 新环境配置测试脚本
 * 验证方案A的 dotenv 配置加载方式
 */

const path = require('path');
const fs = require('fs');

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(color, message) {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function testEnvironment(env) {
    log('blue', `\n🔧 测试 ${env.toUpperCase()} 环境`);
    log('blue', '='.repeat(40));
    
    // 清除相关环境变量
    const envKeys = ['MONGODB_URI', 'PORT', 'JWT_SECRET', 'ADMIN_PASSWORD', 'BASE_URL', 'NEED_TO_ENCRYPT'];
    envKeys.forEach(key => delete process.env[key]);
    
    // 设置NODE_ENV
    process.env.NODE_ENV = env;
    
    // 模拟新的dotenv加载方式
    const dotenv = require('dotenv');
    
    // 先加载环境特定文件
    const envFile = `.env.${env}`;
    const envPath = path.resolve(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
        dotenv.config({ path: envPath });
        log('green', `✅ 加载 ${envFile}`);
    } else {
        log('red', `❌ ${envFile} 不存在`);
    }
    
    // 再加载通用文件
    const commonPath = path.resolve(process.cwd(), '.env');
    if (fs.existsSync(commonPath)) {
        dotenv.config({ path: commonPath });
        log('green', `✅ 加载 .env (后备配置)`);
    }
    
    // 显示配置
    log('cyan', `NODE_ENV: ${process.env.NODE_ENV}`);
    log('cyan', `数据库: ${process.env.MONGODB_URI || '未设置'}`);
    log('cyan', `端口: ${process.env.PORT || '未设置'}`);
    log('cyan', `JWT密钥: ${process.env.JWT_SECRET ? '已设置' : '未设置'}`);
    log('cyan', `API密钥: ${process.env.OPENAI_API_KEY ? '已设置' : '未设置'}`);
    log('cyan', `管理员密码: ${process.env.ADMIN_PASSWORD ? '已设置' : '未设置'}`);
    
    // 验证配置正确性
    if (env === 'development') {
        const expectedDb = 'ChatAdvisor_test';
        if (process.env.MONGODB_URI && process.env.MONGODB_URI.includes(expectedDb)) {
            log('green', '✅ 开发环境数据库配置正确');
        } else {
            log('red', '❌ 开发环境数据库配置错误');
        }
        
        if (process.env.PORT === '33001') {
            log('green', '✅ 开发环境端口配置正确');
        }
        
        if (process.env.ADMIN_PASSWORD === 'admin123') {
            log('green', '✅ 开发环境管理员密码正确');
        }
    } else if (env === 'production') {
        const expectedDb = 'ChatAdvisor?replicaSet=rs0';
        if (process.env.MONGODB_URI && process.env.MONGODB_URI.includes('ChatAdvisor')) {
            log('green', '✅ 生产环境数据库配置正确');
        } else {
            log('red', '❌ 生产环境数据库配置错误');
        }
        
        if (process.env.PORT === '53011') {
            log('green', '✅ 生产环境端口配置正确');
        }
        
        if (process.env.NEED_TO_ENCRYPT === '1') {
            log('green', '✅ 生产环境加密配置正确');
        }
    }
}

function checkFiles() {
    log('blue', '\n📁 检查配置文件');
    log('blue', '='.repeat(30));
    
    const files = [
        { name: '.env', desc: '通用配置' },
        { name: '.env.development', desc: '开发环境' },
        { name: '.env.production', desc: '生产环境' },
        { name: 'src/config/env.ts', desc: '配置加载器' }
    ];
    
    files.forEach(({ name, desc }) => {
        if (fs.existsSync(path.resolve(process.cwd(), name))) {
            log('green', `✅ ${name} (${desc})`);
        } else {
            log('red', `❌ ${name} (${desc})`);
        }
    });
}

function showSummary() {
    log('cyan', '\n📋 配置方案A总结');
    log('cyan', '='.repeat(35));
    console.log(`
🎯 实现目标:
   ✅ 根据NODE_ENV自动加载对应环境文件
   ✅ 第三方API密钥统一管理
   ✅ 环境特定配置分离
   ✅ 避免数据库名称冲突

📁 文件结构:
   .env              → 通用配置（API密钥等）
   .env.development  → 开发环境配置
   .env.production   → 生产环境配置

🔄 加载顺序:
   1. .env.development (如果NODE_ENV=development)
   2. .env (后备配置)

🚀 使用方法:
   yarn dev    → 自动加载开发环境配置
   yarn release → 自动加载生产环境配置

✨ 优势:
   - 配置清晰分离
   - 避免环境变量冲突
   - 第三方密钥统一管理
   - 支持环境特定覆盖
`);
}

function main() {
    log('blue', '🔧 新环境配置测试 (方案A)');
    log('blue', '==========================');
    
    checkFiles();
    testEnvironment('development');
    testEnvironment('production');
    showSummary();
    
    log('green', '\n✅ 测试完成！现在可以运行 yarn dev 验证');
}

if (require.main === module) {
    main();
}
