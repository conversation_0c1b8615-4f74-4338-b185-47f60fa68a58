const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config({ path: '.env.production' });

// 用户模型定义
const userSchema = new mongoose.Schema({
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    fullName: String,
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    username: String,
    balance: { type: Number, default: 0 },
    isDelete: { type: Boolean, default: false },
    emailVerified: { type: Boolean, default: true },
    isVip: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

async function updateAdminPassword() {
    try {
        console.log('🔗 连接到数据库...');
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor');
        console.log('✅ 数据库连接成功');

        // 查找管理员用户
        const adminUser = await User.findOne({ 
            email: '<EMAIL>' 
        });

        if (!adminUser) {
            console.log('❌ 未找到管理员用户');
            return;
        }

        console.log('✅ 找到管理员用户:');
        console.log(`📧 邮箱: ${adminUser.email}`);
        console.log(`👤 用户名: ${adminUser.username}`);
        console.log(`👑 角色: ${adminUser.role}`);
        console.log(`📊 状态: ${adminUser.status}`);
        console.log(`🆔 ID: ${adminUser._id}`);

        // 新密码
        const newPassword = 'HwgZFWHn17U7zaw+';
        console.log(`\n🔄 正在更新密码为: ${newPassword}`);

        // 加密新密码
        const hashedNewPassword = await bcrypt.hash(newPassword, 12);
        console.log('🔐 密码已加密');

        // 更新密码
        const updateResult = await User.findByIdAndUpdate(
            adminUser._id,
            { 
                password: hashedNewPassword,
                updatedAt: new Date()
            },
            { new: true }
        );

        if (updateResult) {
            console.log('✅ 密码更新成功！');
            console.log(`📧 邮箱: ${updateResult.email}`);
            console.log(`🔑 新密码: ${newPassword}`);
            console.log(`🕒 更新时间: ${updateResult.updatedAt}`);

            // 验证新密码
            console.log('\n🔍 验证新密码...');
            const isValid = await bcrypt.compare(newPassword, updateResult.password);
            console.log(`验证结果: ${isValid ? '✅ 密码正确' : '❌ 密码错误'}`);

            // 同时测试旧密码确保已更改
            const oldPasswordTest = await bcrypt.compare('admin123456', updateResult.password);
            console.log(`旧密码测试: ${oldPasswordTest ? '❌ 旧密码仍有效' : '✅ 旧密码已失效'}`);

        } else {
            console.log('❌ 密码更新失败');
        }

    } catch (error) {
        console.error('❌ 错误:', error.message);
        console.error('详细错误:', error);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 数据库连接已关闭');
    }
}

updateAdminPassword();
