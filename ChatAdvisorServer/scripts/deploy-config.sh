#!/bin/bash

# 生产环境配置部署脚本
# 用于自动化部署时的环境变量配置管理

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "生产环境配置部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV_FILE        指定环境变量文件路径"
    echo "  -t, --template            使用模板创建配置文件"
    echo "  -v, --validate            验证配置文件"
    echo "  -b, --backup              备份现有配置"
    echo "  -r, --restore BACKUP      恢复配置备份"
    echo "  -h, --help                显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 --template             # 创建配置模板"
    echo "  $0 --env .env.production  # 部署生产环境配置"
    echo "  $0 --validate             # 验证当前配置"
    echo "  $0 --backup               # 备份当前配置"
}

# 创建配置模板
create_template() {
    local template_file=".env.production.template"
    
    log_info "创建生产环境配置模板: $template_file"
    
    cat > "$template_file" << 'EOF'
# xAI API 配置
OPENAI_API_KEY=xai-your-production-api-key-here
OPENAI_BASE_URL=https://api.x.ai/v1/

# 服务器配置
NODE_ENV=production
PORT=53011

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ChatAdvisor
MONGODB_URI_PRODUCTION=mongodb://localhost:27017/ChatAdvisor

# JWT 配置 - 请使用强密码
JWT_SECRET=your-super-secure-jwt-secret-key-for-production-change-this
JWT_EXPIRES_IN=604800

# 第三方登录配置
GOOGLE_CLIENT_ID=873925125344-hktsn356sutu326jf1el2td6lfsokbdf.apps.googleusercontent.com

TWITTER_API_KEY=*************************
TWITTER_API_SECRET=TmMqViKTVqgFAfTRwGNuXcKzbulPH7h4dPtUtudKE2DJyEJgUu

TIKTOK_CLIENT_ID=awk9nx195kycy67u
TIKTOK_CLIENT_SECRET=J7kZUogyDrqt4qIEQDjInGd9B2pbrkOr

FACEBOOK_APP_ID=your-production-facebook-app-id
FACEBOOK_APP_SECRET=your-production-facebook-app-secret

# 应用配置
BASE_URL=https://your-domain.com
APP_VERSION=1.0
SUPPORT_EMAIL=<EMAIL>
DEFAULT_BALANCE=50
PROFIT_RATE=0.045
EXCHANGE_RATE=100
NEED_TO_ENCRYPT=1

# CORS配置
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# 邮件配置（生产环境）
SMTP_HOST=your-production-smtp-host
SMTP_PORT=587
SMTP_USER=your-production-smtp-user
SMTP_PASS=your-production-smtp-password
EOF

    log_success "配置模板已创建: $template_file"
    log_warning "请编辑模板文件，填入真实的生产环境配置值"
}

# 验证配置文件
validate_config() {
    local env_file="${1:-.env}"
    
    if [[ ! -f "$env_file" ]]; then
        log_error "配置文件不存在: $env_file"
        return 1
    fi
    
    log_info "验证配置文件: $env_file"
    
    # 加载环境变量
    source "$env_file"
    
    local errors=0
    
    # 检查必需的配置项
    local required_vars=(
        "OPENAI_API_KEY"
        "OPENAI_BASE_URL"
        "NODE_ENV"
        "PORT"
        "JWT_SECRET"
        "MONGODB_URI"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            log_error "缺少必需的环境变量: $var"
            ((errors++))
        fi
    done
    
    # 检查xAI配置
    if [[ "$OPENAI_API_KEY" != xai-* ]]; then
        log_warning "API密钥格式可能不正确，应以 'xai-' 开头"
    fi
    
    if [[ "$OPENAI_BASE_URL" != *"x.ai"* ]]; then
        log_warning "基础URL可能不正确，应包含 'x.ai'"
    fi
    
    # 检查端口配置
    if [[ "$NODE_ENV" == "production" && "$PORT" != "53011" ]]; then
        log_warning "生产环境建议使用端口 53011"
    fi
    
    if [[ "$NODE_ENV" == "development" && "$PORT" != "33001" ]]; then
        log_warning "开发环境建议使用端口 33001"
    fi
    
    # 检查JWT密钥强度
    if [[ ${#JWT_SECRET} -lt 32 ]]; then
        log_warning "JWT密钥长度建议至少32个字符"
    fi
    
    if [[ $errors -eq 0 ]]; then
        log_success "配置验证通过"
        return 0
    else
        log_error "配置验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 备份配置
backup_config() {
    local backup_dir="config-backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="$backup_dir/env_backup_$timestamp.tar.gz"
    
    mkdir -p "$backup_dir"
    
    log_info "备份当前配置到: $backup_file"
    
    tar -czf "$backup_file" .env* 2>/dev/null || true
    
    log_success "配置已备份到: $backup_file"
    echo "$backup_file"
}

# 恢复配置
restore_config() {
    local backup_file="$1"
    
    if [[ ! -f "$backup_file" ]]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    log_info "从备份恢复配置: $backup_file"
    
    # 先备份当前配置
    backup_config > /dev/null
    
    # 恢复配置
    tar -xzf "$backup_file"
    
    log_success "配置已从备份恢复"
}

# 部署配置
deploy_config() {
    local env_file="$1"
    
    if [[ ! -f "$env_file" ]]; then
        log_error "环境配置文件不存在: $env_file"
        return 1
    fi
    
    log_info "部署环境配置: $env_file"
    
    # 验证配置
    if ! validate_config "$env_file"; then
        log_error "配置验证失败，部署中止"
        return 1
    fi
    
    # 备份现有配置
    if [[ -f ".env" ]]; then
        backup_config > /dev/null
    fi
    
    # 复制配置文件
    cp "$env_file" ".env"
    
    log_success "环境配置部署完成"
    log_info "请重启应用以使配置生效"
}

# 主函数
main() {
    case "$1" in
        -t|--template)
            create_template
            ;;
        -v|--validate)
            validate_config "$2"
            ;;
        -b|--backup)
            backup_config
            ;;
        -r|--restore)
            if [[ -z "$2" ]]; then
                log_error "请指定备份文件路径"
                exit 1
            fi
            restore_config "$2"
            ;;
        -e|--env)
            if [[ -z "$2" ]]; then
                log_error "请指定环境配置文件路径"
                exit 1
            fi
            deploy_config "$2"
            ;;
        -h|--help)
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 如果没有参数，显示帮助
if [[ $# -eq 0 ]]; then
    show_help
    exit 1
fi

main "$@"
