const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config({ path: '.env.production' });

// 使用与AuthController相同的User模型定义
const AppleInfoSchema = new mongoose.Schema({
    iss: String,
    aud: String,
    exp: Number,
    iat: Number,
    sub: String,
    c_hash: String,
    email: String,
    email_verified: Boolean,
    is_private_email: Boolean,
    auth_time: Number,
    nonce_supported: Boolean
});

const GoogleInfoSchema = new mongoose.Schema({
    iss: String,
    azp: String,
    aud: String,
    sub: String,
    email: String,
    email_verified: Boolean,
    at_hash: String,
    nonce: String,
    name: String,
    picture: String,
    given_name: String,
    family_name: String,
    iat: Number,
    exp: Number
});

const TikTokInfoSchema = new mongoose.Schema({
    access_token: String,
    expires_in: Number,
    open_id: String,
    refresh_expires_in: Number,
    refresh_token: String,
    scope: String,
    token_type: String
});

const FacebookInfoSchema = new mongoose.Schema({
    id: String,
    email: String
});

const TwitterInfoSchema = new mongoose.Schema({
    id: String,
    username: String,
    name: String,
    email: String
});

// 完整的User模型定义（与AuthController兼容）
const userSchema = new mongoose.Schema({
    username: { type: String },
    password: { type: String },
    email: { type: String, required: true, unique: true },
    fullName: { type: String },
    birthDate: { type: Date },
    gender: { type: String, enum: ['Male', 'Female', 'Other'] },
    phone: { type: String },
    address: {
        street: { type: String },
        city: { type: String },
        state: { type: String },
        country: { type: String },
        postalCode: { type: String }
    },
    language: { type: String, default: 'zh_CN' },
    timeZone: { type: String },
    occupation: { type: String },
    company: { type: String },
    allergies: [String],
    medicalConditions: [String],
    balance: { type: Number, default: 50 },
    // 管理员相关字段
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    lastLoginAt: { type: Date },
    lastLoginIP: { type: String },
    avatar: { type: String },
    isDelete: { type: Boolean, default: false },
    // 外部账户绑定
    externalAccounts: {
        weChatId: { type: String, unique: true, sparse: true },
        qqId: { type: String, unique: true, sparse: true },
        appleInfo: { type: AppleInfoSchema },
        googleInfo: { type: GoogleInfoSchema },
        facebookInfo: { type: FacebookInfoSchema },
        twitterInfo: { type: TwitterInfoSchema },
        tikTokInfo: { type: TikTokInfoSchema }
    },
    hasPurchase: { type: Boolean, default: false},
    // 两步验证相关
    twoFactorSecret: {
        type: String,
        select: false
    },
    twoFactorEnabled: { type: Boolean, default: false },
    twoFactorBackupCodes: {
        type: [String],
        select: false
    }
}, {
    timestamps: true
});

const User = mongoose.model('User', userSchema);

async function createCorrectAdmin() {
    try {
        console.log('🔗 连接到数据库...');
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor');
        console.log('✅ 数据库连接成功');

        // 删除所有现有的管理员用户
        await User.deleteMany({ email: '<EMAIL>' });
        console.log('🗑️ 清理旧的管理员用户');

        // 创建新的管理员用户
        const adminPassword = 'HwgZFWHn17U7zaw+';
        const hashedPassword = await bcrypt.hash(adminPassword, 12);

        const adminUser = new User({
            email: '<EMAIL>',
            password: hashedPassword,
            username: 'admin',
            fullName: '系统管理员',
            role: 'super_admin',
            status: 'active',
            language: 'zh_CN',
            balance: 0,
            isDelete: false,
            hasPurchase: false,
            twoFactorEnabled: false
        });

        await adminUser.save();
        console.log('✅ 管理员用户创建成功！');
        console.log(`📧 邮箱: <EMAIL>`);
        console.log(`🔑 密码: ${adminPassword}`);
        console.log(`👑 角色: super_admin`);

        // 验证创建是否成功
        const createdUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
        if (createdUser) {
            console.log('✅ 验证：用户已成功创建');
            console.log(`   ID: ${createdUser._id}`);
            console.log(`   角色: ${createdUser.role}`);
            console.log(`   状态: ${createdUser.status}`);
            
            // 验证密码
            const isPasswordValid = await bcrypt.compare(adminPassword, createdUser.password);
            console.log(`   密码验证: ${isPasswordValid ? '✅ 正确' : '❌ 错误'}`);
        }

    } catch (error) {
        console.error('❌ 创建管理员用户失败:', error.message);
        console.error('详细错误:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 数据库连接已关闭');
    }
}

createCorrectAdmin();
