const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.production' });

// 用户模型定义
const userSchema = new mongoose.Schema({
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    fullName: String,
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    username: String,
    language: String,
    balance: { type: Number, default: 0 },
    isDelete: { type: Boolean, default: false }
});

const User = mongoose.model('User', userSchema);

async function listUsers() {
    try {
        console.log('🔗 连接到数据库...');
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor');
        console.log('✅ 数据库连接成功');

        // 查找所有用户
        const users = await User.find({}).select('-password');
        
        console.log(`\n📊 数据库中共有 ${users.length} 个用户:`);
        
        if (users.length === 0) {
            console.log('❌ 数据库中没有用户');
        } else {
            users.forEach((user, index) => {
                console.log(`\n${index + 1}. 用户信息:`);
                console.log(`   📧 邮箱: ${user.email}`);
                console.log(`   👤 用户名: ${user.username || 'N/A'}`);
                console.log(`   👑 角色: ${user.role}`);
                console.log(`   📊 状态: ${user.status}`);
                console.log(`   💰 余额: ${user.balance}`);
                console.log(`   🗑️ 已删除: ${user.isDelete}`);
            });
        }

        // 专门查找管理员用户
        const adminUsers = await User.find({ 
            role: { $in: ['admin', 'super_admin'] } 
        }).select('-password');
        
        console.log(`\n👑 管理员用户 (${adminUsers.length} 个):`);
        if (adminUsers.length === 0) {
            console.log('❌ 没有管理员用户');
        } else {
            adminUsers.forEach((admin, index) => {
                console.log(`   ${index + 1}. ${admin.email} (${admin.role})`);
            });
        }

    } catch (error) {
        console.error('❌ 错误:', error.message);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 数据库连接已关闭');
    }
}

listUsers();
