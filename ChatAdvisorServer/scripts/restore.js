const { exec } = require('child_process');
const path = require('path');
const backupPath = path.join(__dirname, '..', 'backup');
const dbName = 'yourDatabaseName';
const backupFileName = `${dbName}_backup`;

exec(`mongorestore --db ${dbName} ${path.join(backupPath, dbName)}`, (err, stdout, stderr) => {
    if (err) {
        console.error(`Error during restore: ${stderr}`);
        process.exit(1);
    }
    // console.log(`Restore successful: ${stdout}`);
});
