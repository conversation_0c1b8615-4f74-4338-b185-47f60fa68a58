#!/usr/bin/env node

/**
 * 创建默认管理员用户脚本
 * 使用方法: node scripts/create-admin.js
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const path = require('path');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

// 加载环境配置
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/junshi';

// 用户Schema定义（简化版）
const userSchema = new mongoose.Schema({
    username: { type: String },
    password: { type: String },
    email: { type: String, required: true, unique: true },
    fullName: { type: String },
    language: { type: String, default: 'zh_CN' },
    balance: { type: Number, default: 0 },
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    lastLoginAt: { type: Date },
    lastLoginIP: { type: String },
    avatar: { type: String },
    isDelete: { type: Boolean, default: false },
    emailVerified: { type: Boolean, default: false },
    phoneVerified: { type: Boolean, default: false },
    isVip: { type: Boolean, default: false },
    totalSpent: { type: Number, default: 0 },
    loginCount: { type: Number, default: 0 },
    chatCount: { type: Number, default: 0 }
}, {
    timestamps: true
});

const User = mongoose.model('User', userSchema);

async function createAdminUsers() {
    try {
        console.log('🔗 连接到数据库...');
        await mongoose.connect(MONGODB_URI);
        console.log('✅ 数据库连接成功');

        // 检查是否已存在管理员用户
        const existingAdmin = await User.findOne({
            email: '<EMAIL>'
        });

        if (existingAdmin) {
            console.log('⚠️  管理员用户已存在，跳过创建');
            console.log(`📧 邮箱: <EMAIL>`);
            console.log(`🔑 如需重置密码，请删除现有用户后重新运行此脚本`);
        } else {
            // 创建超级管理员
            const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456';
            const hashedAdminPassword = await bcrypt.hash(adminPassword, 12);

            const adminUser = new User({
                email: '<EMAIL>',
                password: hashedAdminPassword,
                username: 'admin',
                fullName: '系统管理员',
                role: 'super_admin',
                status: 'active',
                language: 'zh_CN',
                balance: 0,
                emailVerified: true,
                isVip: true,
                isDelete: false
            });

            await adminUser.save();
            console.log('✅ 超级管理员创建成功！');
            console.log(`📧 邮箱: <EMAIL>`);
            console.log(`🔑 密码: ${adminPassword}`);
        }

        // 检查是否已存在普通管理员
        const existingModerator = await User.findOne({
            email: '<EMAIL>'
        });

        if (existingModerator) {
            console.log('⚠️  普通管理员用户已存在，跳过创建');
        } else {
            // 创建普通管理员
            const moderatorPassword = process.env.MODERATOR_PASSWORD || 'mod123456';
            const hashedModPassword = await bcrypt.hash(moderatorPassword, 12);

            const moderatorUser = new User({
                email: '<EMAIL>',
                password: hashedModPassword,
                username: 'moderator',
                fullName: '内容管理员',
                role: 'admin',
                status: 'active',
                language: 'zh_CN',
                balance: 0,
                emailVerified: true,
                isDelete: false
            });

            await moderatorUser.save();
            console.log('✅ 普通管理员创建成功！');
            console.log(`📧 邮箱: <EMAIL>`);
            console.log(`🔑 密码: ${moderatorPassword}`);
        }

        console.log('\n🎉 管理员用户创建完成！');
        console.log('\n🔗 登录信息:');
        console.log('   管理后台地址: http://localhost:3000/admin');
        console.log('   超级管理员: <EMAIL>');
        console.log('   普通管理员: <EMAIL>');
        console.log('\n💡 提示: 请在生产环境中修改默认密码！');

    } catch (error) {
        console.error('❌ 创建管理员用户失败:', error);
        
        if (error.code === 11000) {
            console.log('💡 提示: 用户已存在，这是正常的。');
        }
    } finally {
        await mongoose.disconnect();
        console.log('🔌 数据库连接已关闭');
        process.exit(0);
    }
}

// 运行脚本
if (require.main === module) {
    createAdminUsers().catch(console.error);
}

module.exports = { createAdminUsers };
