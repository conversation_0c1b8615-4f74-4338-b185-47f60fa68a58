#!/bin/bash

# ChatAdvisorServer PM2 环境设置脚本
# 用于安装和配置PM2及相关依赖

echo "🚀 开始设置 ChatAdvisorServer PM2环境..."

# 检查Node.js版本
echo "📋 检查Node.js版本..."
node_version=$(node --version)
echo "   Node.js版本: $node_version"

# 检查npm/yarn
if command -v yarn &> /dev/null; then
    echo "✅ 发现yarn包管理器"
    PKG_MANAGER="yarn"
elif command -v npm &> /dev/null; then
    echo "✅ 发现npm包管理器"
    PKG_MANAGER="npm"
else
    echo "❌ 未找到包管理器，请先安装Node.js和npm"
    exit 1
fi

# 全局安装PM2
echo "📦 安装PM2..."
if command -v pm2 &> /dev/null; then
    echo "✅ PM2已安装"
    pm2 --version
else
    echo "   正在全局安装PM2..."
    if [ "$PKG_MANAGER" = "yarn" ]; then
        yarn global add pm2
    else
        npm install -g pm2
    fi
    
    if command -v pm2 &> /dev/null; then
        echo "✅ PM2安装成功"
        pm2 --version
    else
        echo "❌ PM2安装失败"
        exit 1
    fi
fi

# 检查TypeScript相关依赖
echo "📦 检查TypeScript环境..."
if command -v tsc &> /dev/null; then
    echo "✅ TypeScript已安装"
    tsc --version
else
    echo "   正在全局安装TypeScript..."
    if [ "$PKG_MANAGER" = "yarn" ]; then
        yarn global add typescript
    else
        npm install -g typescript
    fi
fi

# 检查ts-node
if command -v ts-node &> /dev/null; then
    echo "✅ ts-node已安装"
    ts-node --version
else
    echo "   正在全局安装ts-node..."
    if [ "$PKG_MANAGER" = "yarn" ]; then
        yarn global add ts-node
    else
        npm install -g ts-node
    fi
fi

# 安装项目依赖
echo "📦 安装项目依赖..."
if [ "$PKG_MANAGER" = "yarn" ]; then
    yarn install
else
    npm install
fi

# 构建项目
echo "🔨 构建项目..."
if [ "$PKG_MANAGER" = "yarn" ]; then
    yarn build
else
    npm run build
fi

# 测试PM2配置
echo "🔍 测试PM2配置..."
if [ -f "scripts/test-pm2-config.cjs" ]; then
    node scripts/test-pm2-config.cjs
else
    echo "⚠️  测试脚本不存在，跳过配置测试"
fi

echo ""
echo "🎉 ChatAdvisorServer PM2环境设置完成！"
echo ""
echo "💡 接下来可以使用以下命令："
echo "   npm run pm-test        - 测试PM2配置"
echo "   npm run pm-release     - 构建并启动生产应用"
echo "   npm run pm-dev         - 构建并启动调试应用"
echo "   npm run pm-all         - 启动所有应用"
echo "   npm run pm-stop        - 停止所有应用"
echo "   npm run pm-restart     - 重启所有应用"
echo "   npm run pm-logs        - 查看生产应用日志"
echo "   npm run pm-logs-debug  - 查看调试应用日志"
echo ""
echo "📚 PM2常用命令："
echo "   pm2 list               - 查看所有应用状态"
echo "   pm2 monit              - 监控面板"
echo "   pm2 stop all           - 停止所有应用"
echo "   pm2 delete all         - 删除所有应用"
echo "   pm2 save               - 保存当前PM2配置"
echo "   pm2 startup            - 设置开机自启"
