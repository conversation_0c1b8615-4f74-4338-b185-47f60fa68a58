#!/bin/bash

# ChatAdvisor Admin Frontend PM2 管理脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}ChatAdvisor Admin Frontend PM2 管理脚本${NC}"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start-release    启动生产环境前端 (端口 34001)"
    echo "  start-debug      启动开发环境前端 (端口 54001)"
    echo "  start-all        启动所有前端服务"
    echo "  stop-release     停止生产环境前端"
    echo "  stop-debug       停止开发环境前端"
    echo "  stop-all         停止所有前端服务"
    echo "  restart-release  重启生产环境前端"
    echo "  restart-debug    重启开发环境前端"
    echo "  restart-all      重启所有前端服务"
    echo "  status           查看所有服务状态"
    echo "  logs-release     查看生产环境日志"
    echo "  logs-debug       查看开发环境日志"
    echo "  build            构建前端项目"
    echo "  help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start-release    # 启动生产环境前端"
    echo "  $0 status           # 查看服务状态"
    echo "  $0 logs-debug       # 查看开发环境日志"
}

# 检查是否在正确的目录
check_directory() {
    if [ ! -f "pm2.config.js" ]; then
        echo -e "${RED}错误: 请在 ChatAdvisorServer 目录下运行此脚本${NC}"
        exit 1
    fi
}

# 构建前端项目
build_frontend() {
    echo -e "${BLUE}构建前端项目...${NC}"
    cd ../admin-frontend
    if npm run build:prod; then
        echo -e "${GREEN}前端项目构建成功${NC}"
        cd ../ChatAdvisorServer
    else
        echo -e "${RED}前端项目构建失败${NC}"
        cd ../ChatAdvisorServer
        exit 1
    fi
}

# 启动生产环境前端
start_release() {
    echo -e "${BLUE}启动生产环境前端 (端口 34001)...${NC}"
    build_frontend
    pm2 start pm2.config.js --only admin-frontend-release
    echo -e "${GREEN}生产环境前端已启动${NC}"
    echo -e "${YELLOW}访问地址: http://localhost:34001${NC}"
}

# 启动开发环境前端
start_debug() {
    echo -e "${BLUE}启动开发环境前端 (端口 54001)...${NC}"
    pm2 start pm2.config.js --only admin-frontend-debug
    echo -e "${GREEN}开发环境前端已启动${NC}"
    echo -e "${YELLOW}访问地址: http://localhost:54001${NC}"
}

# 启动所有前端服务
start_all() {
    echo -e "${BLUE}启动所有前端服务...${NC}"
    build_frontend
    pm2 start pm2.config.js --only admin-frontend-release
    pm2 start pm2.config.js --only admin-frontend-debug
    echo -e "${GREEN}所有前端服务已启动${NC}"
    echo -e "${YELLOW}生产环境: http://localhost:34001${NC}"
    echo -e "${YELLOW}开发环境: http://localhost:54001${NC}"
}

# 停止服务
stop_release() {
    echo -e "${BLUE}停止生产环境前端...${NC}"
    pm2 stop admin-frontend-release
    echo -e "${GREEN}生产环境前端已停止${NC}"
}

stop_debug() {
    echo -e "${BLUE}停止开发环境前端...${NC}"
    pm2 stop admin-frontend-debug
    echo -e "${GREEN}开发环境前端已停止${NC}"
}

stop_all() {
    echo -e "${BLUE}停止所有前端服务...${NC}"
    pm2 stop admin-frontend-release admin-frontend-debug
    echo -e "${GREEN}所有前端服务已停止${NC}"
}

# 重启服务
restart_release() {
    echo -e "${BLUE}重启生产环境前端...${NC}"
    build_frontend
    pm2 restart admin-frontend-release
    echo -e "${GREEN}生产环境前端已重启${NC}"
}

restart_debug() {
    echo -e "${BLUE}重启开发环境前端...${NC}"
    pm2 restart admin-frontend-debug
    echo -e "${GREEN}开发环境前端已重启${NC}"
}

restart_all() {
    echo -e "${BLUE}重启所有前端服务...${NC}"
    build_frontend
    pm2 restart admin-frontend-release admin-frontend-debug
    echo -e "${GREEN}所有前端服务已重启${NC}"
}

# 查看状态
show_status() {
    echo -e "${BLUE}服务状态:${NC}"
    pm2 status admin-frontend-release admin-frontend-debug
}

# 查看日志
logs_release() {
    echo -e "${BLUE}查看生产环境日志:${NC}"
    pm2 logs admin-frontend-release
}

logs_debug() {
    echo -e "${BLUE}查看开发环境日志:${NC}"
    pm2 logs admin-frontend-debug
}

# 主逻辑
check_directory

case "$1" in
    start-release)
        start_release
        ;;
    start-debug)
        start_debug
        ;;
    start-all)
        start_all
        ;;
    stop-release)
        stop_release
        ;;
    stop-debug)
        stop_debug
        ;;
    stop-all)
        stop_all
        ;;
    restart-release)
        restart_release
        ;;
    restart-debug)
        restart_debug
        ;;
    restart-all)
        restart_all
        ;;
    status)
        show_status
        ;;
    logs-release)
        logs_release
        ;;
    logs-debug)
        logs_debug
        ;;
    build)
        build_frontend
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${RED}未知命令: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
