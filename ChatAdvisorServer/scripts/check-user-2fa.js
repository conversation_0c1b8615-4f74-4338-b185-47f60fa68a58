const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config({ path: '.env.production' });

// 使用与AuthController相同的User模型定义
const AppleInfoSchema = new mongoose.Schema({
    iss: String,
    aud: String,
    exp: Number,
    iat: Number,
    sub: String,
    c_hash: String,
    email: String,
    email_verified: Boolean,
    is_private_email: Boolean,
    auth_time: Number,
    nonce_supported: Boolean
});

const GoogleInfoSchema = new mongoose.Schema({
    iss: String,
    azp: String,
    aud: String,
    sub: String,
    email: String,
    email_verified: Boolean,
    at_hash: String,
    nonce: String,
    name: String,
    picture: String,
    given_name: String,
    family_name: String,
    iat: Number,
    exp: Number
});

const TikTokInfoSchema = new mongoose.Schema({
    access_token: String,
    expires_in: Number,
    open_id: String,
    refresh_expires_in: Number,
    refresh_token: String,
    scope: String,
    token_type: String
});

const FacebookInfoSchema = new mongoose.Schema({
    id: String,
    email: String
});

const TwitterInfoSchema = new mongoose.Schema({
    id: String,
    username: String,
    name: String,
    email: String
});

// 完整的User模型定义（与AuthController兼容）
const userSchema = new mongoose.Schema({
    username: { type: String },
    password: { type: String },
    email: { type: String, required: true, unique: true },
    fullName: { type: String },
    birthDate: { type: Date },
    gender: { type: String, enum: ['Male', 'Female', 'Other'] },
    phone: { type: String },
    address: {
        street: { type: String },
        city: { type: String },
        state: { type: String },
        country: { type: String },
        postalCode: { type: String }
    },
    language: { type: String, default: 'zh_CN' },
    timeZone: { type: String },
    occupation: { type: String },
    company: { type: String },
    allergies: [String],
    medicalConditions: [String],
    balance: { type: Number, default: 50 },
    // 管理员相关字段
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    lastLoginAt: { type: Date },
    lastLoginIP: { type: String },
    avatar: { type: String },
    isDelete: { type: Boolean, default: false },
    // 外部账户绑定
    externalAccounts: {
        weChatId: { type: String, unique: true, sparse: true },
        qqId: { type: String, unique: true, sparse: true },
        appleInfo: { type: AppleInfoSchema },
        googleInfo: { type: GoogleInfoSchema },
        facebookInfo: { type: FacebookInfoSchema },
        twitterInfo: { type: TwitterInfoSchema },
        tikTokInfo: { type: TikTokInfoSchema }
    },
    hasPurchase: { type: Boolean, default: false},
    // 两步验证相关
    twoFactorSecret: {
        type: String,
        select: false
    },
    twoFactorEnabled: { type: Boolean, default: false },
    twoFactorBackupCodes: {
        type: [String],
        select: false
    }
}, {
    timestamps: true
});

const User = mongoose.model('User', userSchema);

async function checkUser2FA() {
    try {
        console.log('🔗 连接到数据库...');
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor');
        console.log('✅ 数据库连接成功');

        const email = '<EMAIL>';
        console.log(`\n🔍 检查用户两步验证设置: ${email}`);

        // 查找用户并包含所有2FA相关字段
        const user = await User.findOne({
            email: email.toLowerCase(),
            role: { $in: ['admin', 'super_admin'] },
            isDelete: { $ne: true }
        }).select('+password +twoFactorSecret +twoFactorBackupCodes');

        if (!user) {
            console.log('❌ 未找到用户');
            return;
        }

        console.log('✅ 找到用户:');
        console.log(`   ID: ${user._id}`);
        console.log(`   邮箱: ${user.email}`);
        console.log(`   角色: ${user.role}`);
        console.log(`   状态: ${user.status}`);
        console.log(`   密码字段: ${user.password ? '存在' : '不存在'}`);
        console.log(`   两步验证启用: ${user.twoFactorEnabled}`);
        console.log(`   两步验证密钥: ${user.twoFactorSecret ? '存在' : '不存在'}`);
        console.log(`   备用恢复码: ${user.twoFactorBackupCodes ? user.twoFactorBackupCodes.length + '个' : '不存在'}`);

        // 模拟AuthController的2FA检查逻辑
        console.log('\n🔍 模拟AuthController的2FA检查:');
        const requires2FA = user.twoFactorEnabled && user.twoFactorSecret;
        console.log(`   需要两步验证: ${requires2FA ? '是' : '否'}`);
        
        if (requires2FA) {
            console.log('   ⚠️  用户启用了两步验证，登录时需要提供2FA token');
        } else {
            console.log('   ✅ 用户未启用两步验证，可以直接使用密码登录');
        }

        // 测试密码验证
        if (user.password) {
            const testPassword = 'HwgZFWHn17U7zaw+';
            const isValid = await bcrypt.compare(testPassword, user.password);
            console.log(`\n🔐 密码验证 (${testPassword}): ${isValid ? '✅ 正确' : '❌ 错误'}`);
        }

        // 如果启用了2FA，尝试禁用它
        if (user.twoFactorEnabled) {
            console.log('\n🔄 禁用两步验证...');
            await User.findByIdAndUpdate(user._id, {
                twoFactorEnabled: false,
                twoFactorSecret: null,
                twoFactorBackupCodes: []
            });
            console.log('✅ 两步验证已禁用');
        }

    } catch (error) {
        console.error('❌ 错误:', error.message);
        console.error('详细错误:', error);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 数据库连接已关闭');
    }
}

checkUser2FA();
