const { exec } = require('child_process');
const path = require('path');
const backupPath = path.join(__dirname, '..', 'backup');
const dbName = 'junshi';
const backupFileName = `${dbName}_backup`;

exec(`mongodump --db ${dbName} --out ${backupPath}`, (err, stdout, stderr) => {
    if (err) {
        console.error(`Error during backup: ${stderr}`);
        process.exit(1);
    }
    // console.log(`Backup successful: ${stdout}`);
});
