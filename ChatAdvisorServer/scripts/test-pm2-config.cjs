#!/usr/bin/env node

/**
 * PM2配置测试脚本
 * 用于验证 ChatAdvisorServer PM2配置的路径解析和目录创建功能
 */

const path = require('path');
const fs = require('fs');

console.log('🔍 测试 ChatAdvisorServer PM2配置...\n');

// 加载PM2配置
let pm2Config;
try {
  pm2Config = require('../pm2.config.cjs');
  console.log('✅ PM2配置文件加载成功');
} catch (error) {
  console.error('❌ PM2配置文件加载失败:', error.message);
  process.exit(1);
}

// 获取应用配置
const apps = pm2Config.apps;
console.log(`\n📋 发现 ${apps.length} 个应用配置:`);

apps.forEach((appConfig, index) => {
  console.log(`\n--- 应用 ${index + 1}: ${appConfig.name} ---`);
  console.log(`   脚本路径: ${appConfig.script}`);
  console.log(`   工作目录: ${appConfig.cwd}`);
  console.log(`   错误日志: ${appConfig.error_file}`);
  console.log(`   输出日志: ${appConfig.out_file}`);
  console.log(`   环境变量: NODE_ENV=${appConfig.env.NODE_ENV}, PORT=${appConfig.env.PORT}`);

  // 测试工作目录
  console.log(`\n🔍 测试工作目录访问权限...`);
  try {
    const stats = fs.statSync(appConfig.cwd);
    if (stats.isDirectory()) {
      console.log('✅ 工作目录存在且可访问');
      
      // 测试写权限
      const testFile = path.join(appConfig.cwd, '.pm2-write-test');
      try {
        fs.writeFileSync(testFile, 'write test');
        fs.unlinkSync(testFile);
        console.log('✅ 工作目录具有写权限');
      } catch (writeError) {
        console.warn('⚠️  工作目录缺少写权限:', writeError.message);
      }
    } else {
      console.error('❌ 工作目录路径不是目录');
    }
  } catch (error) {
    console.error('❌ 工作目录不可访问:', error.message);
  }

  // 测试日志目录
  console.log(`\n🔍 测试日志目录...`);
  const logDir = path.dirname(appConfig.error_file);
  try {
    const stats = fs.statSync(logDir);
    if (stats.isDirectory()) {
      console.log('✅ 日志目录存在且可访问');
      
      // 测试日志文件写权限
      const testLogFile = path.join(logDir, '.test-log');
      try {
        fs.writeFileSync(testLogFile, 'log test');
        fs.unlinkSync(testLogFile);
        console.log('✅ 日志目录具有写权限');
      } catch (writeError) {
        console.warn('⚠️  日志目录缺少写权限:', writeError.message);
      }
    } else {
      console.error('❌ 日志目录路径不是目录');
    }
  } catch (error) {
    console.error('❌ 日志目录不可访问:', error.message);
  }

  // 测试应用脚本
  console.log(`\n🔍 测试应用脚本...`);
  try {
    const stats = fs.statSync(appConfig.script);
    if (stats.isFile()) {
      console.log('✅ 应用脚本存在');
    } else {
      console.warn('⚠️  应用脚本路径不是文件');
    }
  } catch (error) {
    console.warn('⚠️  应用脚本不存在，需要先运行构建: npm run build');
  }

  // 测试dist目录结构
  console.log(`\n🔍 测试dist目录结构...`);
  const distPath = path.join(appConfig.cwd, 'dist');
  try {
    const stats = fs.statSync(distPath);
    if (stats.isDirectory()) {
      console.log('✅ dist目录存在');
      
      // 检查关键文件
      const srcDir = path.join(distPath, 'src');
      const appJs = path.join(srcDir, 'app.js');
      const indexJs = path.join(srcDir, 'index.js');
      
      if (fs.existsSync(srcDir)) {
        console.log('✅ dist/src目录存在');
      } else {
        console.warn('⚠️  dist/src目录不存在');
      }
      
      if (fs.existsSync(appJs)) {
        console.log('✅ dist/src/app.js文件存在');
      } else {
        console.warn('⚠️  dist/src/app.js文件不存在');
      }
      
      if (fs.existsSync(indexJs)) {
        console.log('✅ dist/src/index.js文件存在');
      } else {
        console.warn('⚠️  dist/src/index.js文件不存在');
      }
    } else {
      console.warn('⚠️  dist目录不存在，需要先运行构建: npm run build');
    }
  } catch (error) {
    console.warn('⚠️  dist目录不存在，需要先运行构建: npm run build');
  }
});

console.log('\n🎉 PM2配置测试完成！');
console.log('\n💡 使用建议:');
console.log('   1. 如果dist目录不存在，请先运行: npm run build');
console.log('   2. 启动生产应用: npm run pm-release');
console.log('   3. 启动调试应用: npm run pm-dev');
console.log('   4. 查看PM2状态: pm2 status');
console.log('   5. 查看应用日志: pm2 logs chat-advisor-server-v2');
console.log('   6. 查看调试日志: pm2 logs chat-advisor-debug');
