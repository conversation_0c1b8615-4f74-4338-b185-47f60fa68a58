const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config({ path: '.env.production' });

// 简化的用户模型定义，避免语言问题
const userSchema = new mongoose.Schema({
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    fullName: String,
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    username: String,
    balance: { type: Number, default: 0 },
    isDelete: { type: Boolean, default: false },
    emailVerified: { type: Boolean, default: true },
    isVip: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

async function forceCreateAdmin() {
    try {
        console.log('🔗 连接到数据库...');
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor');
        console.log('✅ 数据库连接成功');

        // 先删除可能存在的管理员用户
        await User.deleteMany({ email: '<EMAIL>' });
        console.log('🗑️ 清理旧的管理员用户');

        // 创建新的管理员用户
        const adminPassword = 'admin123456';
        const hashedPassword = await bcrypt.hash(adminPassword, 12);

        const adminUser = new User({
            email: '<EMAIL>',
            password: hashedPassword,
            username: 'admin',
            fullName: '系统管理员',
            role: 'super_admin',
            status: 'active',
            balance: 0,
            emailVerified: true,
            isVip: true,
            isDelete: false
        });

        await adminUser.save();
        console.log('✅ 管理员用户创建成功！');
        console.log(`📧 邮箱: <EMAIL>`);
        console.log(`🔑 密码: ${adminPassword}`);
        console.log(`👑 角色: super_admin`);

        // 验证创建是否成功
        const createdUser = await User.findOne({ email: '<EMAIL>' });
        if (createdUser) {
            console.log('✅ 验证：用户已成功创建');
            console.log(`   ID: ${createdUser._id}`);
            console.log(`   角色: ${createdUser.role}`);
            console.log(`   状态: ${createdUser.status}`);
        }

    } catch (error) {
        console.error('❌ 创建管理员用户失败:', error.message);
        console.error('详细错误:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 数据库连接已关闭');
    }
}

forceCreateAdmin();
