#!/usr/bin/env node

/**
 * 修复MongoDB文本索引问题
 * 删除有问题的zh_CN语言索引，重新创建支持中文的索引
 */

const mongoose = require('mongoose');

// 直接使用环境变量或默认值
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor_test';

async function fixTextIndexes() {
    try {
        console.log('连接到MongoDB...');
        await mongoose.connect(MONGODB_URI);
        console.log('✅ 已连接到MongoDB');
        
        const db = mongoose.connection.db;
        const collection = db.collection('users');
        
        console.log('\n📋 检查当前索引...');
        const indexes = await collection.indexes();
        
        // 查找并删除有问题的文本索引
        let hasTextIndex = false;
        for (const index of indexes) {
            if (index.key && Object.values(index.key).includes('text')) {
                hasTextIndex = true;
                console.log(`🗑️  删除有问题的文本索引: ${index.name}`);
                try {
                    await collection.dropIndex(index.name);
                    console.log(`✅ 成功删除索引: ${index.name}`);
                } catch (error) {
                    console.log(`❌ 删除索引失败: ${index.name} - ${error.message}`);
                }
            }
        }
        
        if (!hasTextIndex) {
            console.log('ℹ️  未找到现有的文本索引');
        }
        
        console.log('\n🔨 创建新的文本索引...');
        
        // 创建新的文本索引，使用 'none' 作为默认语言
        try {
            await collection.createIndex(
                {
                    email: 'text',
                    fullName: 'text',
                    username: 'text'
                },
                {
                    background: true,
                    default_language: 'none', // 避免语言特定处理，支持中文等非支持语言
                    weights: {
                        email: 10,
                        fullName: 5,
                        username: 3
                    },
                    name: 'user_text_search_index'
                }
            );
            console.log('✅ 成功创建新的文本搜索索引');
        } catch (error) {
            console.error('❌ 创建文本索引失败:', error.message);
            throw error;
        }
        
        console.log('\n📋 验证新索引...');
        const newIndexes = await collection.indexes();
        const textIndex = newIndexes.find(idx => idx.name === 'user_text_search_index');
        
        if (textIndex) {
            console.log('✅ 文本索引验证成功');
            console.log('索引详情:', JSON.stringify(textIndex, null, 2));
        } else {
            console.log('❌ 文本索引验证失败');
        }
        
        console.log('\n🎉 索引修复完成！');
        
    } catch (error) {
        console.error('❌ 修复过程中出现错误:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('🔌 已断开MongoDB连接');
    }
}

// 运行修复脚本
if (require.main === module) {
    fixTextIndexes()
        .then(() => {
            console.log('\n✨ 脚本执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = { fixTextIndexes };
