#!/usr/bin/env node

/**
 * 配置调试脚本
 * 用于诊断环境变量加载和API连接问题
 */

require('dotenv').config();
const https = require('https');
const http = require('http');

console.log('🔍 配置调试和网络诊断');
console.log('='.repeat(50));

// 1. 检查环境变量
console.log('\n📋 环境变量检查:');
console.log('-'.repeat(30));

const envVars = [
    'NODE_ENV',
    'PORT',
    'OPENAI_API_KEY',
    'OPENAI_BASE_URL',
    'MONGODB_URI',
    'JWT_SECRET'
];

envVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
        if (varName === 'OPENAI_API_KEY') {
            console.log(`✅ ${varName}: ${value.substring(0, 10)}...${value.substring(value.length - 4)} (长度: ${value.length})`);
        } else if (varName === 'JWT_SECRET') {
            console.log(`✅ ${varName}: ${'*'.repeat(10)}... (长度: ${value.length})`);
        } else {
            console.log(`✅ ${varName}: ${value}`);
        }
    } else {
        console.log(`❌ ${varName}: 未设置`);
    }
});

// 2. 检查API密钥格式
console.log('\n🔑 API密钥格式检查:');
console.log('-'.repeat(30));

const apiKey = process.env.OPENAI_API_KEY;
if (apiKey) {
    if (apiKey.startsWith('xai-')) {
        console.log('✅ API密钥格式正确 (xAI格式)');
    } else if (apiKey.startsWith('sk-')) {
        console.log('⚠️  检测到OpenAI格式的API密钥');
    } else {
        console.log('❌ API密钥格式不正确');
    }
} else {
    console.log('❌ API密钥未设置');
}

// 3. 检查URL格式
console.log('\n🌐 URL格式检查:');
console.log('-'.repeat(30));

const baseURL = process.env.OPENAI_BASE_URL;
if (baseURL) {
    console.log(`✅ Base URL: ${baseURL}`);
    
    if (baseURL.includes('x.ai')) {
        console.log('✅ URL指向xAI服务');
    } else if (baseURL.includes('openai.com')) {
        console.log('⚠️  URL指向OpenAI服务');
    } else {
        console.log('❓ 未知的API服务');
    }
    
    // 解析URL
    try {
        const url = new URL(baseURL);
        console.log(`   协议: ${url.protocol}`);
        console.log(`   主机: ${url.hostname}`);
        console.log(`   端口: ${url.port || (url.protocol === 'https:' ? '443' : '80')}`);
        console.log(`   路径: ${url.pathname}`);
    } catch (error) {
        console.log(`❌ URL格式错误: ${error.message}`);
    }
} else {
    console.log('❌ Base URL未设置');
}

// 4. 网络连接测试
async function testConnection() {
    console.log('\n🔌 网络连接测试:');
    console.log('-'.repeat(30));
    
    if (!baseURL || !apiKey) {
        console.log('❌ 缺少必要的配置，跳过网络测试');
        return;
    }
    
    try {
        const url = new URL(baseURL);
        const isHttps = url.protocol === 'https:';
        const port = url.port || (isHttps ? 443 : 80);
        
        console.log(`测试连接到: ${url.hostname}:${port}`);
        
        // 测试基本连接
        await testBasicConnection(url.hostname, port, isHttps);
        
        // 测试API端点
        await testAPIEndpoint();
        
    } catch (error) {
        console.log(`❌ 网络测试失败: ${error.message}`);
    }
}

// 测试基本网络连接
function testBasicConnection(hostname, port, isHttps) {
    return new Promise((resolve, reject) => {
        const module = isHttps ? https : http;
        
        console.log(`  测试基本连接...`);
        
        const req = module.request({
            hostname: hostname,
            port: port,
            method: 'HEAD',
            path: '/',
            timeout: 5000
        }, (res) => {
            console.log(`  ✅ 基本连接成功 (状态码: ${res.statusCode})`);
            resolve();
        });
        
        req.on('error', (error) => {
            console.log(`  ❌ 基本连接失败: ${error.message}`);
            console.log(`     错误代码: ${error.code || 'N/A'}`);
            reject(error);
        });
        
        req.on('timeout', () => {
            console.log(`  ❌ 连接超时`);
            req.destroy();
            reject(new Error('Connection timeout'));
        });
        
        req.end();
    });
}

// 测试API端点
function testAPIEndpoint() {
    return new Promise((resolve, reject) => {
        console.log(`  测试API端点...`);
        
        const url = new URL(baseURL + 'models');
        const isHttps = url.protocol === 'https:';
        const module = isHttps ? https : http;
        
        const postData = JSON.stringify({
            model: 'grok-2-latest',
            messages: [{ role: 'user', content: 'test' }],
            max_tokens: 1
        });
        
        const req = module.request({
            hostname: url.hostname,
            port: url.port || (isHttps ? 443 : 80),
            path: url.pathname.replace('models', 'chat/completions'),
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            },
            timeout: 10000
        }, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log(`  ✅ API端点连接成功`);
                } else if (res.statusCode === 401) {
                    console.log(`  ❌ API密钥无效 (状态码: ${res.statusCode})`);
                } else {
                    console.log(`  ⚠️  API响应异常 (状态码: ${res.statusCode})`);
                    console.log(`     响应: ${data.substring(0, 200)}...`);
                }
                resolve();
            });
        });
        
        req.on('error', (error) => {
            console.log(`  ❌ API端点连接失败: ${error.message}`);
            console.log(`     错误代码: ${error.code || 'N/A'}`);
            console.log(`     错误类型: ${error.type || 'N/A'}`);
            
            if (error.code === 'ECONNRESET') {
                console.log(`     这通常表示：`);
                console.log(`     1. API密钥无效`);
                console.log(`     2. 网络连接不稳定`);
                console.log(`     3. 服务器拒绝连接`);
            }
            
            reject(error);
        });
        
        req.on('timeout', () => {
            console.log(`  ❌ API请求超时`);
            req.destroy();
            reject(new Error('API request timeout'));
        });
        
        req.write(postData);
        req.end();
    });
}

// 5. 系统信息
console.log('\n💻 系统信息:');
console.log('-'.repeat(30));
console.log(`Node.js版本: ${process.version}`);
console.log(`平台: ${process.platform}`);
console.log(`架构: ${process.arch}`);
console.log(`当前工作目录: ${process.cwd()}`);

// 6. 建议
function showRecommendations() {
    console.log('\n💡 故障排除建议:');
    console.log('-'.repeat(30));
    
    if (!apiKey || !apiKey.startsWith('xai-')) {
        console.log('1. 检查API密钥是否正确设置为xAI格式');
    }
    
    if (!baseURL || !baseURL.includes('x.ai')) {
        console.log('2. 检查Base URL是否指向xAI服务');
    }
    
    console.log('3. 确认网络连接正常');
    console.log('4. 检查防火墙设置');
    console.log('5. 验证API密钥是否有效');
    console.log('6. 尝试重启应用服务');
}

// 运行诊断
async function runDiagnostics() {
    try {
        await testConnection();
    } catch (error) {
        // 错误已在testConnection中处理
    }
    
    showRecommendations();
    
    console.log('\n' + '='.repeat(50));
    console.log('诊断完成');
}

// 执行诊断
runDiagnostics();
