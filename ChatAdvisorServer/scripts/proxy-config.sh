#!/bin/bash

# 代理配置脚本
# 用于配置HTTP/HTTPS代理来访问xAI API

echo "🔧 xAI API 代理配置脚本"
echo "========================"

# 检查是否有代理配置
check_proxy() {
    echo "当前代理配置："
    echo "HTTP_PROXY: ${HTTP_PROXY:-未设置}"
    echo "HTTPS_PROXY: ${HTTPS_PROXY:-未设置}"
    echo "NO_PROXY: ${NO_PROXY:-未设置}"
}

# 设置代理
set_proxy() {
    local proxy_url="$1"
    
    if [[ -z "$proxy_url" ]]; then
        echo "请提供代理URL，格式：http://proxy-server:port"
        echo "示例：http://127.0.0.1:7890"
        return 1
    fi
    
    echo "设置代理为: $proxy_url"
    
    # 设置环境变量
    export HTTP_PROXY="$proxy_url"
    export HTTPS_PROXY="$proxy_url"
    export http_proxy="$proxy_url"
    export https_proxy="$proxy_url"
    
    # 添加到.env文件
    echo "" >> .env
    echo "# 代理配置" >> .env
    echo "HTTP_PROXY=$proxy_url" >> .env
    echo "HTTPS_PROXY=$proxy_url" >> .env
    
    echo "✅ 代理配置已设置"
}

# 测试代理连接
test_proxy() {
    echo "🔍 测试代理连接..."
    
    # 测试基本连接
    if curl -s --connect-timeout 10 --proxy "$HTTPS_PROXY" https://api.x.ai/v1/ > /dev/null; then
        echo "✅ 代理连接测试成功"
        return 0
    else
        echo "❌ 代理连接测试失败"
        return 1
    fi
}

# 清除代理
clear_proxy() {
    echo "清除代理配置..."
    
    unset HTTP_PROXY
    unset HTTPS_PROXY
    unset http_proxy
    unset https_proxy
    
    # 从.env文件中移除代理配置
    if [[ -f ".env" ]]; then
        sed -i '' '/^HTTP_PROXY=/d' .env
        sed -i '' '/^HTTPS_PROXY=/d' .env
        sed -i '' '/^# 代理配置/d' .env
    fi
    
    echo "✅ 代理配置已清除"
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  check                    检查当前代理配置"
    echo "  set <proxy_url>          设置代理"
    echo "  test                     测试代理连接"
    echo "  clear                    清除代理配置"
    echo "  help                     显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 set http://127.0.0.1:7890"
    echo "  $0 test"
    echo "  $0 clear"
}

# 主函数
case "$1" in
    "check")
        check_proxy
        ;;
    "set")
        set_proxy "$2"
        ;;
    "test")
        test_proxy
        ;;
    "clear")
        clear_proxy
        ;;
    "help"|"")
        show_help
        ;;
    *)
        echo "未知选项: $1"
        show_help
        exit 1
        ;;
esac
