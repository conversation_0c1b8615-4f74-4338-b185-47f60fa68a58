#!/usr/bin/env node

/**
 * 验证修复脚本
 * 检查MongoDB文本索引和服务器状态
 */

const mongoose = require('mongoose');
const http = require('http');

// 直接使用环境变量或默认值
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor_test';
const SERVER_PORT = process.env.PORT || 53011;

async function verifyFix() {
    console.log('🔍 开始验证修复结果...\n');
    
    let allPassed = true;
    
    try {
        // 1. 检查MongoDB连接和索引
        console.log('1️⃣ 检查MongoDB连接和索引...');
        await mongoose.connect(MONGODB_URI);
        console.log('✅ MongoDB连接成功');
        
        const db = mongoose.connection.db;
        const collection = db.collection('users');
        
        // 检查文本索引
        const indexes = await collection.indexes();
        const textIndex = indexes.find(idx => idx.name === 'user_text_search_index');
        
        if (textIndex) {
            console.log('✅ 文本搜索索引存在');
            console.log(`   索引名称: ${textIndex.name}`);
            console.log(`   默认语言: ${textIndex.default_language || 'english'}`);
            
            if (textIndex.default_language === 'none') {
                console.log('✅ 索引语言设置正确 (none)');
            } else {
                console.log('⚠️  索引语言设置可能需要检查');
            }
        } else {
            console.log('❌ 文本搜索索引不存在');
            allPassed = false;
        }
        
        // 检查管理员用户
        const adminUser = await collection.findOne({ email: '<EMAIL>' });
        if (adminUser) {
            console.log('✅ 管理员用户存在');
            console.log(`   用户ID: ${adminUser._id}`);
            console.log(`   邮箱: ${adminUser.email}`);
        } else {
            console.log('❌ 管理员用户不存在');
            allPassed = false;
        }
        
        await mongoose.disconnect();
        console.log('✅ MongoDB连接已关闭\n');
        
    } catch (error) {
        console.error('❌ MongoDB检查失败:', error.message);
        allPassed = false;
    }
    
    // 2. 检查服务器状态
    console.log('2️⃣ 检查服务器状态...');
    
    try {
        const response = await new Promise((resolve, reject) => {
            const req = http.request({
                hostname: 'localhost',
                port: SERVER_PORT,
                path: '/',
                method: 'GET',
                timeout: 5000
            }, (res) => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers
                });
            });
            
            req.on('error', reject);
            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });
            
            req.end();
        });
        
        console.log(`✅ 服务器响应正常 (端口 ${SERVER_PORT})`);
        console.log(`   HTTP状态码: ${response.statusCode}`);
        
        if (response.statusCode === 404) {
            console.log('✅ 404响应正常 (根路径未定义路由)');
        }
        
    } catch (error) {
        console.error(`❌ 服务器检查失败 (端口 ${SERVER_PORT}):`, error.message);
        allPassed = false;
    }
    
    // 3. 总结
    console.log('\n📊 验证结果总结:');
    if (allPassed) {
        console.log('🎉 所有检查都通过了！');
        console.log('✅ MongoDB zh_CN语言问题已修复');
        console.log('✅ 文本索引正常工作');
        console.log('✅ 管理员用户创建成功');
        console.log(`✅ ChatAdvisorServer正在端口 ${SERVER_PORT} 上正常运行`);
        console.log('\n🚀 yarn pm-release 命令现在应该可以正常工作了！');
    } else {
        console.log('❌ 部分检查失败，请检查上述错误信息');
        process.exit(1);
    }
}

// 运行验证
if (require.main === module) {
    verifyFix()
        .then(() => {
            console.log('\n✨ 验证完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 验证过程中出现错误:', error);
            process.exit(1);
        });
}

module.exports = { verifyFix };
