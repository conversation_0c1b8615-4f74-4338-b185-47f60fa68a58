#!/usr/bin/env node

/**
 * AI配置环境变量检查脚本
 * 检查和生成AI配置管理所需的环境变量
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// 生成加密密钥
function generateEncryptionKey() {
    return crypto.randomBytes(32).toString('hex');
}

// 验证加密密钥格式
function validateEncryptionKey(key) {
    try {
        const buffer = Buffer.from(key, 'hex');
        return buffer.length === 32;
    } catch {
        return false;
    }
}

// 检查环境变量
function checkEnvironmentVariables() {
    console.log('🔍 检查AI配置管理环境变量...\n');
    
    const requiredVars = {
        'AI_CONFIG_ENCRYPTION_KEY': {
            description: 'AI配置API密钥加密密钥',
            validator: validateEncryptionKey,
            generator: generateEncryptionKey
        }
    };
    
    const missingVars = [];
    const invalidVars = [];
    
    for (const [varName, config] of Object.entries(requiredVars)) {
        const value = process.env[varName];
        
        if (!value) {
            missingVars.push({ name: varName, config });
            console.log(`❌ ${varName}: 未设置`);
        } else if (config.validator && !config.validator(value)) {
            invalidVars.push({ name: varName, config, value });
            console.log(`⚠️  ${varName}: 格式无效`);
        } else {
            console.log(`✅ ${varName}: 已正确设置`);
        }
    }
    
    return { missingVars, invalidVars };
}

// 生成环境变量配置
function generateEnvConfig(missingVars, invalidVars) {
    if (missingVars.length === 0 && invalidVars.length === 0) {
        console.log('\n🎉 所有环境变量都已正确配置！');
        return;
    }
    
    console.log('\n📝 生成环境变量配置...\n');
    
    const envLines = [];
    envLines.push('# AI配置管理环境变量');
    envLines.push('# 由 check-ai-config-env.js 自动生成');
    envLines.push('');
    
    // 处理缺失的变量
    for (const { name, config } of missingVars) {
        const value = config.generator ? config.generator() : '';
        envLines.push(`# ${config.description}`);
        envLines.push(`${name}=${value}`);
        envLines.push('');
        console.log(`✨ 生成 ${name}`);
    }
    
    // 处理无效的变量
    for (const { name, config } of invalidVars) {
        const value = config.generator ? config.generator() : '';
        envLines.push(`# ${config.description} (替换无效值)`);
        envLines.push(`${name}=${value}`);
        envLines.push('');
        console.log(`🔄 重新生成 ${name}`);
    }
    
    // 写入文件
    const envContent = envLines.join('\n');
    const envFile = path.join(__dirname, '../.env.ai-config');
    
    fs.writeFileSync(envFile, envContent);
    console.log(`\n📄 配置已保存到: ${envFile}`);
    console.log('\n请将以上配置添加到您的 .env 文件中，或者：');
    console.log(`cat ${envFile} >> .env`);
}

// 验证数据库连接
async function validateDatabaseConnection() {
    console.log('\n🔗 验证数据库连接...');
    
    try {
        // 这里需要实际的数据库连接逻辑
        // 暂时跳过，因为需要完整的应用上下文
        console.log('⏭️  跳过数据库连接验证（需要在应用上下文中运行）');
    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
        process.exit(1);
    }
}

// 主函数
async function main() {
    console.log('🚀 AI配置管理环境检查工具\n');
    
    // 检查环境变量
    const { missingVars, invalidVars } = checkEnvironmentVariables();
    
    // 生成配置
    generateEnvConfig(missingVars, invalidVars);
    
    // 验证数据库连接
    await validateDatabaseConnection();
    
    console.log('\n✅ 环境检查完成！');
    
    if (missingVars.length > 0 || invalidVars.length > 0) {
        console.log('\n⚠️  请配置环境变量后重启应用');
        process.exit(1);
    }
}

// 运行脚本
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = {
    generateEncryptionKey,
    validateEncryptionKey,
    checkEnvironmentVariables
};
