#!/usr/bin/env node

/**
 * 修复管理员用户脚本
 * 检查并修复现有管理员用户的密码和角色设置
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const path = require('path');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

// 加载环境配置
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// 数据库连接配置
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/junshi';

// 用户Schema定义（简化版）
const userSchema = new mongoose.Schema({
    username: { type: String },
    password: { type: String },
    email: { type: String, required: true, unique: true },
    fullName: { type: String },
    language: { type: String, default: 'zh_CN' },
    balance: { type: Number, default: 0 },
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    lastLoginAt: { type: Date },
    lastLoginIP: { type: String },
    avatar: { type: String },
    isDelete: { type: Boolean, default: false },
    emailVerified: { type: Boolean, default: false },
    phoneVerified: { type: Boolean, default: false },
    isVip: { type: Boolean, default: false },
    totalSpent: { type: Number, default: 0 },
    loginCount: { type: Number, default: 0 },
    chatCount: { type: Number, default: 0 }
}, {
    timestamps: true
});

const User = mongoose.model('User', userSchema);

async function fixAdminUser() {
    try {
        console.log('🔗 连接到数据库...');
        await mongoose.connect(MONGODB_URI);
        console.log('✅ 数据库连接成功');

        // 查找现有的管理员用户
        const adminUser = await User.findOne({
            email: '<EMAIL>'
        });

        if (!adminUser) {
            console.log('❌ 未找到管理员用户，正在创建...');
            
            const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456';
            const hashedPassword = await bcrypt.hash(adminPassword, 12);

            const newAdminUser = new User({
                email: '<EMAIL>',
                password: hashedPassword,
                username: 'admin',
                fullName: '系统管理员',
                role: 'super_admin',
                status: 'active',
                language: 'zh_CN',
                balance: 0,
                emailVerified: true,
                isVip: true,
                isDelete: false
            });

            await newAdminUser.save();
            console.log('✅ 管理员用户创建成功！');
            console.log(`📧 邮箱: <EMAIL>`);
            console.log(`🔑 密码: ${adminPassword}`);
            return;
        }

        console.log('🔍 检查现有管理员用户...');
        console.log(`📧 邮箱: ${adminUser.email}`);
        console.log(`👤 用户名: ${adminUser.username || '未设置'}`);
        console.log(`🎭 角色: ${adminUser.role || '未设置'}`);
        console.log(`📊 状态: ${adminUser.status || '未设置'}`);
        console.log(`🔒 有密码: ${adminUser.password ? '是' : '否'}`);
        console.log(`✉️ 邮箱验证: ${adminUser.emailVerified ? '是' : '否'}`);
        console.log(`🗑️ 已删除: ${adminUser.isDelete ? '是' : '否'}`);

        // 修复用户信息
        let needUpdate = false;
        const updates = {};

        // 确保有密码
        if (!adminUser.password) {
            const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456';
            updates.password = await bcrypt.hash(adminPassword, 12);
            needUpdate = true;
            console.log('🔧 设置密码...');
        }

        // 确保角色正确
        if (adminUser.role !== 'super_admin') {
            updates.role = 'super_admin';
            needUpdate = true;
            console.log('🔧 设置角色为 super_admin...');
        }

        // 确保状态激活
        if (adminUser.status !== 'active') {
            updates.status = 'active';
            needUpdate = true;
            console.log('🔧 设置状态为 active...');
        }

        // 确保邮箱已验证
        if (!adminUser.emailVerified) {
            updates.emailVerified = true;
            needUpdate = true;
            console.log('🔧 设置邮箱为已验证...');
        }

        // 确保未被删除
        if (adminUser.isDelete) {
            updates.isDelete = false;
            needUpdate = true;
            console.log('🔧 恢复用户（设置为未删除）...');
        }

        // 设置用户名
        if (!adminUser.username) {
            updates.username = 'admin';
            needUpdate = true;
            console.log('🔧 设置用户名...');
        }

        // 设置全名
        if (!adminUser.fullName) {
            updates.fullName = '系统管理员';
            needUpdate = true;
            console.log('🔧 设置全名...');
        }

        if (needUpdate) {
            await User.updateOne({ email: '<EMAIL>' }, updates);
            console.log('✅ 管理员用户修复完成！');
        } else {
            console.log('✅ 管理员用户状态正常，无需修复');
        }

        // 显示最终信息
        const updatedAdmin = await User.findOne({ email: '<EMAIL>' });
        console.log('\n📋 最终用户信息:');
        console.log(`📧 邮箱: ${updatedAdmin.email}`);
        console.log(`👤 用户名: ${updatedAdmin.username}`);
        console.log(`🎭 角色: ${updatedAdmin.role}`);
        console.log(`📊 状态: ${updatedAdmin.status}`);
        console.log(`🔑 密码: ${process.env.ADMIN_PASSWORD || 'admin123456'}`);
        console.log(`✉️ 邮箱验证: ${updatedAdmin.emailVerified ? '是' : '否'}`);

        console.log('\n🎉 现在可以使用以下信息登录:');
        console.log('   管理后台: http://localhost:3000/admin');
        console.log('   邮箱: <EMAIL>');
        console.log(`   密码: ${process.env.ADMIN_PASSWORD || 'admin123456'}`);

    } catch (error) {
        console.error('❌ 修复管理员用户失败:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 数据库连接已关闭');
        process.exit(0);
    }
}

// 运行脚本
if (require.main === module) {
    fixAdminUser().catch(console.error);
}

module.exports = { fixAdminUser };
