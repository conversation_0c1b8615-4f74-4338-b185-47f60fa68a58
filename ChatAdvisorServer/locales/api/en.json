{"data_not_found": "Data not found", "register_success": "Register successfully", "register_fail": "Register failed", "login_success": "<PERSON><PERSON> successfully", "login_fail": "<PERSON><PERSON> failed", "password_incorrect": "Password is incorrect", "login_error": "Username or password error", "user_not_exist": "User does not exist", "user_exist": "User already exists, please use another email to register", "email_conflict": "Email is already registered with a different account", "invalid_signature": "Invalid signatur", "invalid_apple_id_token": "Invalid apple id token", "signature_required": "Signature is required", "decryption_failed": "Decryption failed", "internal_server_error": "Internal server error", "speech_recognition_failure": "Speech recognition failed", "verification_code_sent": "Verification code sent successfully", "invalid_apple_purchase": "Invalid apple purchase", "no_balance_1": "Your balance is insufficient, the session has been paused,", "no_balance_2": "You can click the menu in the upper left corner to enter the settings for recharge.", "un-suppored-langue": "Unsupported language", "email": {"sender": "Chat Advisor", "subject": "Chat Advisor - Your Verification Code", "body": "Use the code below to complete your verification:", "purpose": "This email is for verifying your identity", "common_content": "The verification code is crucial for completing your operation. Please do not share this code with anyone."}, "generate_title_content": "Please generate a title for our conversation based on the message I will send you next. Just respond with the generated title, without quotation marks.", "generate_title": "Title: the title you generated", "generate_help": "Please tell me the types of help you can provide me with in JSON format, for example: [{\"title\": \"Plan a 'Mental Health Day'\", \"content\": \"to help me relax\", question: \"Can you help me plan a day specifically for rejuvenation and relaxation? First, can you ask me what my favorite way to relax is?\"}]", "welcome": "Welcome to use our application"}