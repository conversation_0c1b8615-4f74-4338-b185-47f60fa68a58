pipeline {
    agent any
    
    tools {
        nodejs "NodeJS"
    }
    
    parameters {
        string(name: 'UPDATE_CONTENT', defaultValue: 'debug-无更新内容', description: 'prod和debug, prod使用master打包，debug使用develop打包，auto-pack分支仅触发打包')
        string(name: 'TAG', defaultValue: '', description: '可选的构建标签')
        string(name: 'REMOTE_USER', defaultValue: 'ubuntu', description: '远程服务器用户名')
        string(name: 'REMOTE_HOST', defaultValue: '**********', description: '远程服务器地址')
        string(name: 'GIT_KEY_CREDENTIALS_ID', defaultValue: '775e39dd-2b13-4aae-a47e-bf6d3b095a8e', description: 'Git凭据ID')
        string(name: 'SSH_KEY_CREDENTIALS_ID', defaultValue: '04ae6ac7-e2ac-4415-b216-4bc53b901d57', description: 'SSH凭据ID')
        string(name: 'REMOTE_SUBDIR', defaultValue: 'ChatAdvisor', description: '部署服务器中的子目录名')
        string(name: 'REMOTE_REPO_URL', defaultValue: 'git@**********:server/ChatAdvisorServer.git', description: '远程仓库地址')
        string(name: 'PM2_APP_PROD', defaultValue: 'chat-advisor-release', description: '生产环境的PM2应用名称')
        string(name: 'PM2_APP_DEBUG', defaultValue: 'chat-advisor-debug', description: '调试环境的PM2应用名称')
        string(name: 'BRANCH_PROD', defaultValue: 'main', description: '生产环境使用的Git分支名称')
        string(name: 'BRANCH_DEBUG', defaultValue: 'develop', description: '调试环境使用的Git分支名称')
    }

    environment {
        NODE_ENV = 'production'
        PATH = "/usr/local/bin:${env.PATH}" 
    }

    stages {
        
        stage('Setup Environment') {
            steps {
                script {
                    def updateContent = params.UPDATE_CONTENT
                    if (env.update_content_0) {
                        echo 'The variable env.update_content_0 exists.'
                        updateContent = env.update_content_0
                    }
                    if (params.containsKey('UPDATE_CONTENT_0') && params.UPDATE_CONTENT_0) {
                        echo 'The variable params.update_content_0 exists.'
                        updateContent = params.UPDATE_CONTENT_0
                    }
                    echo "updateContent: ${updateContent}"

                    def updateContentParts = updateContent.split('-')
                    if (updateContentParts.size() > 1) {
                        def updateType = updateContentParts[0]
                        if (updateType == 'prod') {
                            env.REMOTE_DIR = "/home/<USER>/jenkins/production/${params.REMOTE_SUBDIR}"
                            env.PM2_APP_NAME = params.PM2_APP_PROD
                            env.BRANCH_NAME = params.BRANCH_PROD
                            env.IS_PROD = true
                        } else if (updateType == 'debug') {
                            env.REMOTE_DIR = "/home/<USER>/jenkins/debug/${params.REMOTE_SUBDIR}"
                            env.PM2_APP_NAME = params.PM2_APP_DEBUG
                            env.BRANCH_NAME = params.BRANCH_DEBUG // Override the branch name for debug
                            env.IS_PROD = false
                        } else {
                            error('Invalid update type. Skipping the build.')
                        }
                    } else {
                        error('Invalid UPDATE_CONTENT format. Skipping the build.')
                    }
                    echo "REMOTE_DIR: ${env.REMOTE_DIR}"
                    echo "PM2_APP_NAME: ${env.PM2_APP_NAME}"
                    echo "BRANCH_NAME: ${env.BRANCH_NAME}"
                    echo "IS_PROD: ${env.IS_PROD}"
                }
            }
        }

        stage('Checkout Target Code') {
            steps {
                script {
                    if (params.TAG) {
                        echo "Checking out tag: ${params.TAG}"
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: "refs/tags/${params.TAG}"]],
                            extensions: [],
                            userRemoteConfigs: [[
                                credentialsId: env.GIT_KEY_CREDENTIALS_ID,
                                url: params.REMOTE_REPO_URL
                            ]]
                        ])
                    } else {
                        echo "Checking out branch: ${env.BRANCH_NAME}"
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: "*/${env.BRANCH_NAME}"]],
                            extensions: [],
                            userRemoteConfigs: [[
                                credentialsId: env.GIT_KEY_CREDENTIALS_ID,
                                url: params.REMOTE_REPO_URL
                            ]]
                        ])
                    }
                }
            }
        }
        
        stage('Install Dependencies') {
            steps {
                script {
                    echo "Installing dependencies"
                }
                sh 'sudo chmod -R 777 .'
                sh 'npm install -g yarn typescript'
                sh 'yarn add @types/bcrypt @types/nodemailer @types/luxon @types/multer @types/morgan @types/axios --dev'
                sh 'yarn'
            }
        }

        stage('Build Project') {
            steps {
                script {
                    echo "Building project"
                }
                sh 'yarn run build' 
            }
        }

        stage('Backup Existing Deployment') {
            when {
                expression { return env.IS_PROD.toBoolean() }
            }
            steps {
                script {
                    echo "Backing up existing deployment"
                    sshagent (credentials: [params.SSH_KEY_CREDENTIALS_ID]) {
                        sh """
                        ssh -o StrictHostKeyChecking=no ${params.REMOTE_USER}@${params.REMOTE_HOST} '
                        if [ -d "${env.REMOTE_DIR}" ]; then
                            tar -czf ${env.REMOTE_DIR}_backup_\$(date +%Y%m%d%H%M%S).tar.gz -C ${env.REMOTE_DIR} .
                        fi
                        mkdir -p ${env.REMOTE_DIR}/logs
                        touch ${env.REMOTE_DIR}/logs/release-err.log ${env.REMOTE_DIR}/logs/release-out.log
                        '
                        """
                    }
                }
            }
        }

        stage('Transfer Files to Remote Server') {
            steps {
                script {
                    echo "Stopping PM2 process: ${env.PM2_APP_NAME}"
                    sshagent (credentials: [params.SSH_KEY_CREDENTIALS_ID]) {
                        sh """
                        ssh -o StrictHostKeyChecking=no ${params.REMOTE_USER}@${params.REMOTE_HOST} '
                        '
                        """
                    }

                    echo "Transferring files to remote server: ${params.REMOTE_USER}@${params.REMOTE_HOST}:${env.REMOTE_DIR}"
                    sshagent (credentials: [params.SSH_KEY_CREDENTIALS_ID]) {
                        sh """
                        ssh -o StrictHostKeyChecking=no ${params.REMOTE_USER}@${params.REMOTE_HOST} 'mkdir -p ${env.REMOTE_DIR} && sudo chmod -R 777 ${env.REMOTE_DIR}'
                        rsync -avz --exclude 'node_modules' --exclude '.git' ./ ${params.REMOTE_USER}@${params.REMOTE_HOST}:${env.REMOTE_DIR}
                        """
                    }
                }
            }
        }

        stage('Install Dependencies on Remote Server') {
            steps {
                script {
                    echo "Installing dependencies on remote server"
                    sshagent (credentials: [params.SSH_KEY_CREDENTIALS_ID]) {
                        sh """
                        ssh -o StrictHostKeyChecking=no ${params.REMOTE_USER}@${params.REMOTE_HOST} '
                        cd ${env.REMOTE_DIR} &&
                        yarn add tsconfig-paths ts-node &&
                        yarn
                        '
                        """
                    }
                }
            }
        }

        stage('Restart PM2 Process') {
            when {
                expression { return env.IS_PROD.toBoolean() }
            }
            steps {
                script {
                    echo "Restarting PM2 process: ${env.PM2_APP_NAME}"
                    sshagent (credentials: [params.SSH_KEY_CREDENTIALS_ID]) {
                        sh """
                        ssh -o StrictHostKeyChecking=no ${params.REMOTE_USER}@${params.REMOTE_HOST} '
                        cd ${env.REMOTE_DIR} &&
                        pm2 reload pm2.config.js --only ${env.PM2_APP_NAME}
                        '
                        """
                    }
                }
            }
        }
    }

    post {
        success {
            echo 'Deployment succeeded!'
            mail (
                subject: "构建成功",
                body: """
构建成功:
Job: ${env.JOB_NAME} [${env.BUILD_NUMBER}]
项目名称: ${env.JOB_NAME}
构建编号: ${env.BUILD_NUMBER}
构建状态: 成功
触发原因: ${currentBuild.getBuildCauses()}
构建地址: ${env.BUILD_URL}
构建时间: ${new Date(currentBuild.startTimeInMillis).format("yyyy-MM-dd HH:mm:ss")}
持续时间: ${currentBuild.durationString}
                """,
                to: '<EMAIL>'
            )
        }
        failure {
            echo 'Deployment failed!'
            mail (
                subject: "构建失败",
                body: """
构建失败:
Job: ${env.JOB_NAME} [${env.BUILD_NUMBER}]
项目名称: ${env.JOB_NAME}
构建编号: ${env.BUILD_NUMBER}
构建状态: 失败
触发原因: ${currentBuild.getBuildCauses()}
构建地址: ${env.BUILD_URL}
构建时间: ${new Date(currentBuild.startTimeInMillis).format("yyyy-MM-dd HH:mm:ss")}
持续时间: ${currentBuild.durationString}
               """,
                to: '<EMAIL>'
            )
        }
    }
}
