#!/bin/bash

# MongoDB URI and Database name
MONGODB_URI="mongodb://localhost:27017"
DATABASE_NAME="junshi" # 替换为你的数据库名称

# JSON 文件路径
JSON_FILE_PATH="schema.json"

# 清空 _Schema 集合中的数据
# mongo $MONGODB_URI/$DATABASE_NAME --eval "db._Schema.deleteMany({})"

# 导入 JSON 数据到 _Schema 集合
mongoimport --uri $MONGODB_URI/$DATABASE_NAME --collection _SCHEMA --file $JSON_FILE_PATH --jsonArray

echo "Schema data imported successfully"
