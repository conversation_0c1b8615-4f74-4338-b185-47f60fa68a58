Model Input Output
gpt-4o US$5.00 / 1M tokens US$15.00 / 1M tokens
Model Input Output
gpt-4-turbo US$10.00 / 1M tokens US$30.00 / 1M tokens
Model Input Output
gpt-4 US$30.00 / 1M tokens US$60.00 / 1M tokens，
Model Input Output
gpt-3.5-turbo-0125 US$0.50 / 1M tokens US$1.50 / 1M tokens
Model Input Output
gpt-4-0125-preview US$10.00 / 1M tokens US$30.00 / 1M tokens
gpt-4-1106-preview US$10.00 / 1M tokens US$30.00 / 1M tokens
gpt-4-vision-preview US$10.00 / 1M tokens US$30.00 / 1M tokens
gpt-3.5-turbo-1106 US$1.00 / 1M tokens US$2.00 / 1M tokens
gpt-3.5-turbo-0613 US$1.50 / 1M tokens US$2.00 / 1M tokens
gpt-3.5-turbo-16k-0613 US$3.00 / 1M tokens US$4.00 / 1M tokens
gpt-3.5-turbo-0301 US$1.50 / 1M tokens US$2.00 / 1M tokens
请根据这些数据，整理以50%的利润，整理成const pricingSchema = new Schema({
    modelName: { type: String, required: true, unique: true},
    price: { type: Number, required: true },
    count: { type: Number, required: true }
});的数据，单位为人民币，每1000个tokens