# ChatAdvisorServer PM2 配置优化总结

## 问题描述

基于 admin-frontend 的 PM2 配置和 Vite 构建警告修复经验，对 ChatAdvisorServer 项目进行类似的优化，实现健壮的、支持多环境的 PM2 部署解决方案。

## 解决方案

### 1. PM2 配置优化 (`pm2.config.cjs`)

**修复内容**：
- 实现智能路径解析：优先使用生产路径 `/home/<USER>/gitlab/production/chat-advisor-server-v2`，失败时回退到当前目录
- 添加日志目录自动创建和权限检查
- 实现多级回退机制：生产路径 → 当前目录 → 系统临时目录
- 添加应用脚本存在性检查
- 支持双应用配置：生产环境 (chat-advisor-server-v2) 和调试环境 (chat-advisor-debug)

**核心功能**：
```javascript
// 智能路径解析
function resolveWorkingPath() {
  // 检查生产路径存在性和写权限
  // 自动回退到可用路径
}

// 确保必要目录存在
function ensureLogsDirectory(basePath) { /* 创建日志目录 */ }
function ensureDistDirectory(basePath) { /* 检查应用脚本 */ }
```

**应用配置**：
- **生产应用**: `chat-advisor-server-v2` (PORT: 53011, NODE_ENV: production)
- **调试应用**: `chat-advisor-debug` (PORT: 33001, NODE_ENV: debug)

### 2. Package.json 脚本优化

**更新的脚本**：
```json
{
  "pm-release": "npm run build && pm2 start pm2.config.cjs --only chat-advisor-server-v2",
  "pm-dev": "npm run build && pm2 start pm2.config.cjs --only chat-advisor-debug",
  "pm-all": "npm run build && pm2 start pm2.config.cjs",
  "pm-test": "node scripts/test-pm2-config.cjs",
  "pm-stop": "pm2 stop chat-advisor-server-v2 chat-advisor-debug",
  "pm-restart": "pm2 restart chat-advisor-server-v2 chat-advisor-debug",
  "pm-logs": "pm2 logs chat-advisor-server-v2",
  "pm-logs-debug": "pm2 logs chat-advisor-debug"
}
```

**移除的过时脚本**：
- `pm-release-v2`, `pm-dev-v2` (合并到主脚本)
- 修正了应用名称引用错误

### 3. 新增工具脚本

**测试脚本** (`scripts/test-pm2-config.cjs`)：
- 验证双应用 PM2 配置
- 检查工作目录和日志目录权限
- 验证 dist 目录结构和应用脚本
- 显示详细的环境变量配置
- 提供使用建议和故障排除信息

**安装脚本** (`scripts/setup-pm2.sh`)：
- 自动安装 PM2、TypeScript、ts-node
- 安装项目依赖并构建
- 运行配置测试
- 提供完整的使用指南

### 4. CI/CD 配置优化 (`.gitlab-ci.yml`)

**优化内容**：
- 在部署过程中添加 PM2 配置测试
- 使用优化后的 `npm run pm-release` 命令
- 修复了依赖关系问题 (移除不存在的 setup_environment 依赖)
- 添加了 notify 阶段到 stages 列表

**关键改进**：
```yaml
# 测试PM2配置
echo '🔍 Testing PM2 configuration...'
if [ -f 'scripts/test-pm2-config.cjs' ]; then
  node scripts/test-pm2-config.cjs
else
  echo '⚠️  PM2 test script not found, skipping configuration test'
fi

# 使用优化后的脚本启动
npm run pm-release
```

## 测试结果

### ✅ **配置测试通过**
```
📋 发现 2 个应用配置:
--- 应用 1: chat-advisor-server-v2 (生产环境)
--- 应用 2: chat-advisor-debug (调试环境)

✅ 工作目录存在且可访问
✅ 工作目录具有写权限
✅ 日志目录存在且可访问
✅ 日志目录具有写权限
✅ 应用脚本存在
✅ dist目录结构完整
```

### ✅ **PM2 启动测试通过**
```
┌────┬───────────────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name                      │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼───────────────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ chat-advisor-debug        │ default     │ 1.0.3   │ fork    │ 28020    │ 0s     │ 18   │ online    │ 0%       │ 59.2mb   │ zha… │ disabled │
│ 1  │ chat-advisor-server-v2    │ default     │ 1.0.3   │ fork    │ 28051    │ 0s     │ 0    │ online    │ 0%       │ 492.0kb  │ zha… │ disabled │
└────┴───────────────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
```

### ✅ **日志功能正常**
- 错误日志正确写入到指定路径
- 日志格式包含时间戳和详细错误信息
- 支持实时日志查看

## 使用方法

### 开发环境
```bash
# 测试 PM2 配置
npm run pm-test

# 构建并启动调试应用
npm run pm-dev

# 查看调试应用日志
npm run pm-logs-debug
```

### 生产环境
```bash
# 构建并启动生产应用
npm run pm-release

# 查看生产应用日志
npm run pm-logs

# 重启所有应用
npm run pm-restart

# 停止所有应用
npm run pm-stop
```

### 环境设置
```bash
# 一键设置完整环境
chmod +x scripts/setup-pm2.sh
./scripts/setup-pm2.sh
```

## 技术要点

### 路径回退策略
1. **首选路径**: `/home/<USER>/gitlab/production/chat-advisor-server-v2`
2. **回退路径**: 当前工作目录 (`process.cwd()`)
3. **最终回退**: 系统临时目录 (`os.tmpdir()`)

### 双应用架构
- **生产应用**: 端口 53011，生产环境配置
- **调试应用**: 端口 33001，调试环境配置
- 独立的日志文件和错误处理

### 权限检查
- 创建测试文件验证写权限
- 自动创建必要的目录结构
- 详细的错误日志和回退信息

## 对比 admin-frontend 的改进

1. **双应用支持**: 支持生产和调试两个环境
2. **TypeScript 构建**: 集成了 TypeScript 编译流程
3. **更复杂的依赖**: 处理了 Node.js 服务器特有的依赖关系
4. **数据库连接**: 日志中能看到数据库连接状态
5. **CI/CD 集成**: 更完整的部署流水线集成

## 后续建议

1. **环境变量管理**: 考虑使用 .env 文件管理不同环境的配置
2. **健康检查**: 添加应用健康检查端点
3. **监控集成**: 集成 PM2 监控和告警
4. **自动化测试**: 添加 PM2 配置的自动化测试
