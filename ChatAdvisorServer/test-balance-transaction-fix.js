/**
 * 测试 BalanceTransactionController 修复
 * 验证 getTransactionTypeLabel 方法的空值处理和 this 上下文绑定
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

// 测试配置
const TEST_CONFIG = {
    // 使用管理员认证（需要根据实际情况调整）
    adminToken: process.env.ADMIN_TOKEN || 'your-admin-token-here',
    timeout: 10000
};

/**
 * 测试财务交易接口
 */
async function testFinancialTransactionsAPI() {
    console.log('🧪 开始测试财务交易接口修复...\n');

    try {
        // 测试1: 基本的交易记录获取
        console.log('📝 测试1: 获取交易记录列表');
        console.log('----------------------------------------');
        
        const response = await axios.get(`${BASE_URL}/api/admin/financial/transactions`, {
            headers: {
                'Authorization': `Bearer ${TEST_CONFIG.adminToken}`,
                'Content-Type': 'application/json'
            },
            params: {
                page: 1,
                limit: 10
            },
            timeout: TEST_CONFIG.timeout
        });

        if (response.status === 200 && response.data.success) {
            console.log('✅ API 调用成功');
            console.log(`📊 返回数据结构: ${JSON.stringify(Object.keys(response.data), null, 2)}`);
            
            if (response.data.data && response.data.data.transactions) {
                const transactions = response.data.data.transactions;
                console.log(`📈 获取到 ${transactions.length} 条交易记录`);
                
                // 验证每条记录是否包含 typeLabel
                let hasTypeLabel = true;
                let hasValidTypeLabel = true;
                
                transactions.forEach((transaction, index) => {
                    if (!transaction.hasOwnProperty('typeLabel')) {
                        hasTypeLabel = false;
                        console.log(`❌ 交易记录 ${index + 1} 缺少 typeLabel 字段`);
                    } else if (transaction.typeLabel === undefined || transaction.typeLabel === null) {
                        hasValidTypeLabel = false;
                        console.log(`⚠️  交易记录 ${index + 1} 的 typeLabel 为空: ${transaction.typeLabel}`);
                    } else {
                        console.log(`✅ 交易记录 ${index + 1}: type=${transaction.type}, typeLabel="${transaction.typeLabel}"`);
                    }
                });
                
                if (hasTypeLabel && hasValidTypeLabel) {
                    console.log('🎉 所有交易记录都正确包含了 typeLabel 字段');
                } else {
                    console.log('❌ 部分交易记录的 typeLabel 字段有问题');
                }
            } else {
                console.log('ℹ️  没有交易记录数据');
            }
        } else {
            console.log('❌ API 调用失败');
            console.log(`状态码: ${response.status}`);
            console.log(`响应: ${JSON.stringify(response.data, null, 2)}`);
        }

    } catch (error) {
        if (error.response) {
            // 服务器返回了错误响应
            console.log('❌ 服务器错误:');
            console.log(`状态码: ${error.response.status}`);
            console.log(`错误信息: ${JSON.stringify(error.response.data, null, 2)}`);
            
            if (error.response.status === 500) {
                console.log('🔍 这可能是我们要修复的 500 错误');
                console.log('检查错误信息中是否包含 "Cannot read properties of undefined"');
            }
        } else if (error.request) {
            // 请求发送了但没有收到响应
            console.log('❌ 网络错误: 无法连接到服务器');
            console.log('请确保服务器正在运行在', BASE_URL);
        } else {
            // 其他错误
            console.log('❌ 请求配置错误:', error.message);
        }
    }
}

/**
 * 测试 getTransactionTypeLabel 方法的边界情况
 */
async function testTransactionTypeLabelEdgeCases() {
    console.log('\n📝 测试2: 测试不同的查询参数');
    console.log('----------------------------------------');

    const testCases = [
        { name: '无参数', params: {} },
        { name: '指定类型1(充值)', params: { type: '1' } },
        { name: '指定类型2(入账)', params: { type: '2' } },
        { name: '指定类型3(消费)', params: { type: '3' } },
        { name: '无效类型', params: { type: '999' } },
        { name: '分页参数', params: { page: 1, limit: 5 } }
    ];

    for (const testCase of testCases) {
        try {
            console.log(`\n🔍 测试: ${testCase.name}`);
            
            const response = await axios.get(`${BASE_URL}/api/admin/financial/transactions`, {
                headers: {
                    'Authorization': `Bearer ${TEST_CONFIG.adminToken}`,
                    'Content-Type': 'application/json'
                },
                params: testCase.params,
                timeout: TEST_CONFIG.timeout
            });

            if (response.status === 200 && response.data.success) {
                const transactions = response.data.data?.transactions || [];
                console.log(`✅ 成功获取 ${transactions.length} 条记录`);
                
                // 检查前几条记录的 typeLabel
                transactions.slice(0, 3).forEach((transaction, index) => {
                    console.log(`  记录 ${index + 1}: type=${transaction.type}, typeLabel="${transaction.typeLabel}"`);
                });
            } else {
                console.log(`❌ 测试失败: ${response.status}`);
            }

        } catch (error) {
            console.log(`❌ 测试 "${testCase.name}" 失败:`, error.response?.status || error.message);
        }
    }
}

/**
 * 主测试函数
 */
async function runTests() {
    console.log('🚀 开始测试 BalanceTransactionController 修复\n');
    console.log(`🌐 测试服务器: ${BASE_URL}`);
    console.log(`🔑 使用认证: ${TEST_CONFIG.adminToken ? '是' : '否'}\n`);

    // 检查服务器是否可访问
    try {
        const healthCheck = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
        console.log('✅ 服务器健康检查通过\n');
    } catch (error) {
        console.log('❌ 服务器健康检查失败');
        console.log('请确保服务器正在运行并且可以访问\n');
        return;
    }

    // 运行测试
    await testFinancialTransactionsAPI();
    await testTransactionTypeLabelEdgeCases();

    console.log('\n🏁 测试完成');
    console.log('\n📋 修复总结:');
    console.log('1. ✅ 修复了路由中的 this 上下文绑定问题');
    console.log('2. ✅ 改进了 getTransactionTypeLabel 方法的空值处理');
    console.log('3. ✅ 确保所有控制器方法都正确绑定了上下文');
    console.log('\n如果测试通过，说明 "Cannot read properties of undefined" 错误已经修复！');
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = {
    testFinancialTransactionsAPI,
    testTransactionTypeLabelEdgeCases,
    runTests
};
