import crypto from 'crypto';
import { REGEX_PATTERNS } from '../constants/index';

/**
 * 通用工具函数集合
 */

/**
 * 生成随机字符串
 */
export function generateRandomString(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
}

/**
 * 生成UUID
 */
export function generateUUID(): string {
    return crypto.randomUUID();
}

/**
 * 生成数字验证码
 */
export function generateNumericCode(length: number = 6): string {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(Math.random() * (max - min + 1) + min).toString();
}

/**
 * 验证邮箱格式
 */
export function isValidEmail(email: string): boolean {
    return REGEX_PATTERNS.EMAIL.test(email);
}

/**
 * 验证手机号格式
 */
export function isValidPhone(phone: string): boolean {
    return REGEX_PATTERNS.PHONE.test(phone);
}

/**
 * 验证MongoDB ObjectId格式
 */
export function isValidObjectId(id: string): boolean {
    return REGEX_PATTERNS.MONGODB_OBJECT_ID.test(id);
}

/**
 * 验证UUID格式
 */
export function isValidUUID(uuid: string): boolean {
    return REGEX_PATTERNS.UUID.test(uuid);
}

/**
 * 安全地解析JSON
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
    try {
        return JSON.parse(jsonString);
    } catch {
        return defaultValue;
    }
}

/**
 * 深度克隆对象
 */
export function deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime()) as unknown as T;
    }
    
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item)) as unknown as T;
    }
    
    if (typeof obj === 'object') {
        const clonedObj = {} as T;
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
    
    return obj;
}

/**
 * 移除对象中的空值
 */
export function removeEmptyValues<T extends Record<string, any>>(obj: T): Partial<T> {
    const result: Partial<T> = {};
    
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            if (value !== null && value !== undefined && value !== '') {
                if (typeof value === 'object' && !Array.isArray(value)) {
                    const cleaned = removeEmptyValues(value);
                    if (Object.keys(cleaned).length > 0) {
                        result[key] = cleaned as T[Extract<keyof T, string>];
                    }
                } else {
                    result[key] = value;
                }
            }
        }
    }
    
    return result;
}

/**
 * 延迟执行
 */
export function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试机制
 */
export async function retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    delayMs: number = 1000
): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error as Error;
            if (attempt < maxAttempts) {
                await delay(delayMs * attempt);
            }
        }
    }
    
    throw lastError!;
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;

    return function(this: any, ...args: Parameters<T>) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle: boolean;

    return function(this: any, ...args: Parameters<T>) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
}

/**
 * 生成哈希值
 */
export function generateHash(data: string, algorithm: string = 'sha256'): string {
    return crypto.createHash(algorithm).update(data).digest('hex');
}

/**
 * 比较哈希值
 */
export function compareHash(data: string, hash: string, algorithm: string = 'sha256'): boolean {
    const dataHash = generateHash(data, algorithm);
    return crypto.timingSafeEqual(Buffer.from(dataHash), Buffer.from(hash));
}

/**
 * 掩码敏感信息
 */
export function maskSensitiveData(data: string, visibleChars: number = 4): string {
    if (data.length <= visibleChars * 2) {
        return '*'.repeat(data.length);
    }
    
    const start = data.substring(0, visibleChars);
    const end = data.substring(data.length - visibleChars);
    const middle = '*'.repeat(data.length - visibleChars * 2);
    
    return start + middle + end;
}

/**
 * 分页计算
 */
export function calculatePagination(page: number, pageSize: number, total: number) {
    const totalPages = Math.ceil(total / pageSize);
    const offset = (page - 1) * pageSize;
    
    return {
        page,
        pageSize,
        total,
        totalPages,
        offset,
        hasNext: page < totalPages,
        hasPrev: page > 1
    };
}

/**
 * 数组分块
 */
export function chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}

/**
 * 获取随机数组元素
 */
export function getRandomArrayElement<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
}

/**
 * 打乱数组
 */
export function shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}
