import { Request, Response, NextFunction } from 'express';
import { secretKey, tokenExpiresIn } from '../config/env';
import jwt from 'jsonwebtoken';
import User from '../models/User';
import { logger } from '../business/logger';

/**
 * 第三方登录提供商类型
 */
export enum ThirdPartyProvider {
    APPLE = 'apple',
    GOOGLE = 'google',
    FACEBOOK = 'facebook',
    TIKTOK = 'tiktok',
    TWITTER = 'twitter'
}

/**
 * 第三方登录用户信息接口
 */
export interface ThirdPartyUserInfo {
    sub?: string;           // Apple/Google 用户ID
    id?: string;            // Facebook/其他平台用户ID
    email?: string;         // 用户邮箱
    name?: string;          // 用户姓名
    picture?: string;       // 用户头像
    [key: string]: any;     // 其他平台特定字段
}

/**
 * 登录结果接口
 */
export interface LoginResult {
    success: boolean;
    user?: any;
    token?: string;
    message?: string;
    code?: number;
}

/**
 * 格式化用户数据的通用函数
 * 确保返回数据的一致性和类型安全
 */
export const formatUserData = (user: any, token: string) => {
    const userData = user.toObject();
    return {
        ...userData,
        userId: userData._id.toString(),
        _id: userData._id.toString(),
        token,
        balance: userData.balance || 0,
        language: userData.language || 'zh_CN',
        isDelete: userData.isDelete || false
    };
};

/**
 * 通用第三方登录处理器
 * 抽象了所有第三方登录的通用逻辑，消除重复代码
 */
export class ThirdPartyAuthHandler {
    /**
     * 处理第三方登录的通用逻辑
     * @param provider 登录提供商
     * @param userInfo 第三方平台返回的用户信息
     * @param req Express请求对象
     * @param res Express响应对象
     * @param next Express下一步处理函数
     */
    static async handleThirdPartyLogin(
        provider: ThirdPartyProvider,
        userInfo: ThirdPartyUserInfo,
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<any> {
        try {
            // 输入验证
            const validationResult = this.validateUserInfo(provider, userInfo);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    message: validationResult.message,
                    code: 400
                });
            }

            // 处理邮箱（Facebook可能没有邮箱）
            const email = this.normalizeEmail(provider, userInfo);
            
            // 查找现有用户
            const existingUser = await User.findOne({ email });

            if (existingUser) {
                // 处理现有用户登录
                const result = await this.handleExistingUserLogin(
                    provider, 
                    userInfo, 
                    existingUser, 
                    req
                );
                
                if (result.success) {
                    res.status(200).json({
                        message: `${this.getProviderDisplayName(provider)} login successful`,
                        code: 200,
                        data: result.user
                    });
                } else {
                    res.status(200).json({
                        message: result.message,
                        code: result.code || 409
                    });
                }
            } else {
                // 创建新用户
                const result = await this.createNewUser(provider, userInfo, email);
                
                res.status(200).json({
                    message: `${this.getProviderDisplayName(provider)} login successful`,
                    code: 200,
                    data: result.user
                });
            }
        } catch (error: any) {
            logger.error(`${provider} login error:`, error);
            next({
                message: `${this.getProviderDisplayName(provider)} login failed: ${error.message}`,
                code: 500
            });
        }
    }

    /**
     * 验证第三方用户信息
     */
    private static validateUserInfo(provider: ThirdPartyProvider, userInfo: ThirdPartyUserInfo): {
        isValid: boolean;
        message?: string;
    } {
        if (!userInfo) {
            return {
                isValid: false,
                message: `Invalid ${provider} user info: missing user data`
            };
        }

        // 根据不同平台验证必要字段
        switch (provider) {
            case ThirdPartyProvider.APPLE:
            case ThirdPartyProvider.GOOGLE:
                if (!userInfo.sub || !userInfo.email) {
                    return {
                        isValid: false,
                        message: `Invalid ${provider} token: missing required fields`
                    };
                }
                break;
            case ThirdPartyProvider.FACEBOOK:
                if (!userInfo.id) {
                    return {
                        isValid: false,
                        message: `Invalid ${provider} token: missing user ID`
                    };
                }
                break;
            default:
                if (!userInfo.id && !userInfo.sub) {
                    return {
                        isValid: false,
                        message: `Invalid ${provider} token: missing user identifier`
                    };
                }
        }

        return { isValid: true };
    }

    /**
     * 标准化邮箱地址
     */
    private static normalizeEmail(provider: ThirdPartyProvider, userInfo: ThirdPartyUserInfo): string {
        if (userInfo.email) {
            return userInfo.email;
        }
        
        // Facebook等平台可能没有邮箱，生成虚拟邮箱
        const userId = userInfo.id || userInfo.sub;
        return `${userId}@${provider}.virtual`;
    }

    /**
     * 处理现有用户登录
     */
    private static async handleExistingUserLogin(
        provider: ThirdPartyProvider,
        userInfo: ThirdPartyUserInfo,
        existingUser: any,
        req: Request
    ): Promise<LoginResult> {
        const accountField = this.getAccountField(provider);
        
        // 检查是否已绑定此第三方账号
        const isAccountBound = existingUser.externalAccounts && 
                              existingUser.externalAccounts[accountField] && 
                              this.compareUserIdentifiers(
                                  provider, 
                                  existingUser.externalAccounts[accountField], 
                                  userInfo
                              );

        if (isAccountBound) {
            // 更新用户状态
            await User.updateOne(
                { email: existingUser.email }, 
                { isDelete: false }
            );
            
            // 生成JWT Token
            const token = jwt.sign(
                { 
                    userId: existingUser._id.toString(), 
                    email: existingUser.email 
                }, 
                secretKey, 
                { expiresIn: tokenExpiresIn }
            );

            return {
                success: true,
                user: formatUserData(existingUser, token),
                token
            };
        } else {
            // 邮箱冲突：存在邮箱但未绑定此第三方账号
            return {
                success: false,
                message: req.t('email_conflict'),
                code: 409
            };
        }
    }

    /**
     * 创建新用户
     */
    private static async createNewUser(
        provider: ThirdPartyProvider,
        userInfo: ThirdPartyUserInfo,
        email: string
    ): Promise<LoginResult> {
        const accountField = this.getAccountField(provider);
        
        const newUser = new User({
            email,
            externalAccounts: {
                [accountField]: userInfo
            }
        });
        
        await newUser.save();
        
        const token = jwt.sign(
            { 
                userId: newUser._id.toString(), 
                email: newUser.email 
            }, 
            secretKey, 
            { expiresIn: tokenExpiresIn }
        );

        return {
            success: true,
            user: formatUserData(newUser, token),
            token
        };
    }

    /**
     * 获取账号字段名
     */
    private static getAccountField(provider: ThirdPartyProvider): string {
        return `${provider}Info`;
    }

    /**
     * 获取用户标识符
     */
    private static getUserIdentifier(provider: ThirdPartyProvider, userInfo: ThirdPartyUserInfo): string {
        switch (provider) {
            case ThirdPartyProvider.APPLE:
            case ThirdPartyProvider.GOOGLE:
                return userInfo.sub || '';
            case ThirdPartyProvider.FACEBOOK:
            default:
                return userInfo.id || userInfo.sub || '';
        }
    }

    /**
     * 比较用户标识符
     */
    private static compareUserIdentifiers(
        provider: ThirdPartyProvider,
        storedInfo: any,
        currentInfo: ThirdPartyUserInfo
    ): boolean {
        switch (provider) {
            case ThirdPartyProvider.APPLE:
            case ThirdPartyProvider.GOOGLE:
                return storedInfo.sub === currentInfo.sub;
            case ThirdPartyProvider.FACEBOOK:
            default:
                return storedInfo.id === currentInfo.id;
        }
    }

    /**
     * 获取提供商显示名称
     */
    private static getProviderDisplayName(provider: ThirdPartyProvider): string {
        const displayNames = {
            [ThirdPartyProvider.APPLE]: 'Apple',
            [ThirdPartyProvider.GOOGLE]: 'Google',
            [ThirdPartyProvider.FACEBOOK]: 'Facebook',
            [ThirdPartyProvider.TIKTOK]: 'TikTok',
            [ThirdPartyProvider.TWITTER]: 'Twitter'
        };
        return displayNames[provider] || provider;
    }
}
