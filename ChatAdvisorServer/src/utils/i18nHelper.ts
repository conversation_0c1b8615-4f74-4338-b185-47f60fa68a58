import { Request } from 'express';
import { logger } from '../business/logger';

/**
 * 安全的国际化函数，当req.t不存在时提供默认消息
 * @param req Express请求对象
 * @param key 翻译键
 * @param defaultMessage 默认消息
 * @returns 翻译后的消息或默认消息
 */
export function safeTranslate(req: Request, key: string, defaultMessage: string): string {
    try {
        // @ts-ignore
        return req.t && typeof req.t === 'function' ? req.t(key) : defaultMessage;
    } catch (error) {
        logger.warn(`Translation function failed for key: ${key}, using default message`);
        return defaultMessage;
    }
}

/**
 * 常用错误消息的默认值映射
 */
export const DEFAULT_ERROR_MESSAGES = {
    token_required: 'Token is required',
    unauthorized: 'Unauthorized access',
    user_not_exist: 'User does not exist',
    data_not_found: 'Data not found',
    internal_server_error: 'Internal server error',
    signature_required: 'Signature is required',
    decryption_failed: 'Decryption failed',
    invalid_signature: 'Invalid signature',
    invalid_apple_purchase: 'Invalid Apple purchase',
    validation_failed: 'Validation failed',
    'un-suppored-langue': 'Unsupported language'
} as const;

/**
 * 使用预定义默认消息的安全翻译函数
 * @param req Express请求对象
 * @param key 翻译键（必须是DEFAULT_ERROR_MESSAGES中的键）
 * @returns 翻译后的消息或默认消息
 */
export function safeTranslateWithDefaults(req: Request, key: keyof typeof DEFAULT_ERROR_MESSAGES): string {
    return safeTranslate(req, key, DEFAULT_ERROR_MESSAGES[key]);
}
