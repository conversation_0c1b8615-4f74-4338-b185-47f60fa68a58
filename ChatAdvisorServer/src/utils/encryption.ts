import crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const TAG_LENGTH = 16;

// 从环境变量获取加密密钥，如果不存在则生成一个
const getEncryptionKey = (): Buffer => {
    const key = process.env.AI_CONFIG_ENCRYPTION_KEY;
    if (!key) {
        throw new Error('AI_CONFIG_ENCRYPTION_KEY环境变量未设置');
    }
    return Buffer.from(key, 'hex');
};

/**
 * 加密API密钥
 */
export const encryptApiKey = (apiKey: string): string => {
    try {
        const key = getEncryptionKey();
        const iv = crypto.randomBytes(IV_LENGTH);
        const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

        let encrypted = cipher.update(apiKey, 'utf8', 'hex');
        encrypted += cipher.final('hex');

        // 返回格式：iv:encrypted
        return `${iv.toString('hex')}:${encrypted}`;
    } catch (error) {
        throw new Error(`API密钥加密失败: ${error.message}`);
    }
};

/**
 * 解密API密钥
 */
export const decryptApiKey = (encryptedApiKey: string): string => {
    try {
        const key = getEncryptionKey();
        const parts = encryptedApiKey.split(':');

        if (parts.length !== 2) {
            throw new Error('加密数据格式无效');
        }

        const iv = Buffer.from(parts[0], 'hex');
        const encrypted = parts[1];

        const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);

        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');

        return decrypted;
    } catch (error) {
        throw new Error(`API密钥解密失败: ${error.message}`);
    }
};

/**
 * 生成新的加密密钥（用于初始化）
 */
export const generateEncryptionKey = (): string => {
    return crypto.randomBytes(32).toString('hex');
};

/**
 * 验证加密密钥格式
 */
export const validateEncryptionKey = (key: string): boolean => {
    try {
        const buffer = Buffer.from(key, 'hex');
        return buffer.length === 32;
    } catch {
        return false;
    }
};

/**
 * 掩码显示API密钥（用于安全显示）
 */
export const maskApiKey = (apiKey: string): string => {
    if (!apiKey || apiKey.length < 8) {
        return '***';
    }
    return `${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`;
};
