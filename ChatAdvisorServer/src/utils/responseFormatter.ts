import { Response } from 'express';
import { HTTP_STATUS } from '../constants';

/**
 * 标准API响应格式
 */
export interface ApiResponse<T = any> {
    code: number;
    message: string;
    data: T | null;
    timestamp: string;
    requestId?: string;
}

/**
 * 分页响应格式
 */
export interface PaginatedResponse<T = any> {
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
}

/**
 * 响应格式化工具类
 */
export class ResponseFormatter {
    /**
     * 成功响应
     */
    static success<T>(
        res: Response,
        data: T = null as T,
        message: string = 'Success',
        statusCode: number = HTTP_STATUS.OK
    ): Response {
        const response: ApiResponse<T> = {
            code: statusCode,
            message,
            data,
            timestamp: new Date().toISOString()
        };

        return res.status(statusCode).json(response);
    }

    /**
     * 错误响应
     */
    static error(
        res: Response,
        message: string = 'Internal Server Error',
        statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,
        data: any = null
    ): Response {
        const response: ApiResponse = {
            code: statusCode,
            message,
            data,
            timestamp: new Date().toISOString()
        };

        return res.status(statusCode).json(response);
    }

    /**
     * 分页响应
     */
    static paginated<T>(
        res: Response,
        items: T[],
        total: number,
        page: number,
        pageSize: number,
        message: string = 'Success'
    ): Response {
        const totalPages = Math.ceil(total / pageSize);
        
        const paginatedData: PaginatedResponse<T> = {
            items,
            total,
            page,
            pageSize,
            totalPages
        };

        return ResponseFormatter.success(res, paginatedData, message);
    }

    /**
     * 创建响应（201状态码）
     */
    static created<T>(
        res: Response,
        data: T = null as T,
        message: string = 'Created successfully'
    ): Response {
        return ResponseFormatter.success(res, data, message, HTTP_STATUS.CREATED);
    }

    /**
     * 无内容响应（204状态码）
     */
    static noContent(res: Response): Response {
        return res.status(204).send();
    }

    /**
     * 验证错误响应
     */
    static validationError(
        res: Response,
        errors: string[] | string,
        message: string = 'Validation failed'
    ): Response {
        const errorData = Array.isArray(errors) ? { errors } : { error: errors };
        return ResponseFormatter.error(res, message, HTTP_STATUS.BAD_REQUEST, errorData);
    }

    /**
     * 未授权响应
     */
    static unauthorized(
        res: Response,
        message: string = 'Unauthorized access'
    ): Response {
        return ResponseFormatter.error(res, message, HTTP_STATUS.UNAUTHORIZED);
    }

    /**
     * 禁止访问响应
     */
    static forbidden(
        res: Response,
        message: string = 'Access forbidden'
    ): Response {
        return ResponseFormatter.error(res, message, HTTP_STATUS.FORBIDDEN);
    }

    /**
     * 资源未找到响应
     */
    static notFound(
        res: Response,
        message: string = 'Resource not found'
    ): Response {
        return ResponseFormatter.error(res, message, HTTP_STATUS.NOT_FOUND);
    }

    /**
     * 冲突响应
     */
    static conflict(
        res: Response,
        message: string = 'Resource conflict'
    ): Response {
        return ResponseFormatter.error(res, message, HTTP_STATUS.CONFLICT);
    }

    /**
     * 服务器内部错误响应
     */
    static internalError(
        res: Response,
        message: string = 'Internal server error'
    ): Response {
        return ResponseFormatter.error(res, message, HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
}

/**
 * 便捷的响应方法
 */
export const sendSuccess = ResponseFormatter.success;
export const sendError = ResponseFormatter.error;
export const sendPaginated = ResponseFormatter.paginated;
export const sendCreated = ResponseFormatter.created;
export const sendNoContent = ResponseFormatter.noContent;
export const sendValidationError = ResponseFormatter.validationError;
export const sendUnauthorized = ResponseFormatter.unauthorized;
export const sendForbidden = ResponseFormatter.forbidden;
export const sendNotFound = ResponseFormatter.notFound;
export const sendConflict = ResponseFormatter.conflict;
export const sendInternalError = ResponseFormatter.internalError;

export default ResponseFormatter;
