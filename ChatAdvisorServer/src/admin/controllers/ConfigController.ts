/**
 * 系统配置管理控制器
 * 提供配置项的增删改查、配置的导入导出等功能
 */

import { Request, Response, NextFunction } from 'express';
import { Config } from '../../models/Config';
import { logger } from '../../business/logger';
import { validationResult } from 'express-validator';

export class ConfigController {
    /**
     * 获取系统配置
     */
    public async getConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            // 通常系统配置只有一条记录
            const config = await Config.findOne().lean();

            if (!config) {
                res.status(404).json({
                    success: false,
                    message: 'System configuration not found'
                });
                return;
            }

            res.json({
                success: true,
                data: config
            });

        } catch (error) {
            logger.error('Get config failed:', error);
            next(error);
        }
    }

    /**
     * 更新系统配置
     */
    public async updateConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const updateData = req.body;

            // 查找现有配置或创建新配置
            let config = await Config.findOne();
            
            if (config) {
                // 更新现有配置
                Object.assign(config, updateData);
                await config.save();
            } else {
                // 创建新配置
                config = new Config(updateData);
                await config.save();
            }

            logger.info('System configuration updated');

            res.json({
                success: true,
                data: config,
                message: 'Configuration updated successfully'
            });

        } catch (error) {
            logger.error('Update config failed:', error);
            next(error);
        }
    }

    /**
     * 获取特定配置项
     */
    public async getConfigItem(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { key } = req.params;

            const config = await Config.findOne().lean();
            if (!config) {
                res.status(404).json({
                    success: false,
                    message: 'Configuration not found'
                });
                return;
            }

            const value = config[key as keyof typeof config];
            if (value === undefined) {
                res.status(404).json({
                    success: false,
                    message: `Configuration key '${key}' not found`
                });
                return;
            }

            res.json({
                success: true,
                data: {
                    key,
                    value
                }
            });

        } catch (error) {
            logger.error('Get config item failed:', error);
            next(error);
        }
    }

    /**
     * 更新特定配置项
     */
    public async updateConfigItem(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { key } = req.params;
            const { value } = req.body;

            let config = await Config.findOne();
            if (!config) {
                res.status(404).json({
                    success: false,
                    message: 'Configuration not found'
                });
                return;
            }

            // 验证配置项是否存在
            if (!(key in config.toObject())) {
                res.status(400).json({
                    success: false,
                    message: `Invalid configuration key: ${key}`
                });
                return;
            }

            // 更新配置项
            (config as any)[key] = value;
            await config.save();

            logger.info(`Configuration item updated: ${key}`);

            res.json({
                success: true,
                data: {
                    key,
                    value: (config as any)[key]
                },
                message: `Configuration item '${key}' updated successfully`
            });

        } catch (error) {
            logger.error('Update config item failed:', error);
            next(error);
        }
    }

    /**
     * 批量更新配置项
     */
    public async batchUpdateConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { updates } = req.body;

            if (!updates || typeof updates !== 'object') {
                res.status(400).json({
                    success: false,
                    message: 'Updates object is required'
                });
                return;
            }

            let config = await Config.findOne();
            if (!config) {
                res.status(404).json({
                    success: false,
                    message: 'Configuration not found'
                });
                return;
            }

            const configObject = config.toObject();
            const validUpdates: any = {};
            const invalidKeys: string[] = [];

            // 验证所有配置项
            for (const [key, value] of Object.entries(updates)) {
                if (key in configObject) {
                    validUpdates[key] = value;
                } else {
                    invalidKeys.push(key);
                }
            }

            if (invalidKeys.length > 0) {
                res.status(400).json({
                    success: false,
                    message: `Invalid configuration keys: ${invalidKeys.join(', ')}`
                });
                return;
            }

            // 应用更新
            Object.assign(config, validUpdates);
            await config.save();

            logger.info(`Batch configuration update: ${Object.keys(validUpdates).join(', ')}`);

            res.json({
                success: true,
                data: {
                    updatedKeys: Object.keys(validUpdates),
                    updates: validUpdates
                },
                message: `Successfully updated ${Object.keys(validUpdates).length} configuration items`
            });

        } catch (error) {
            logger.error('Batch update config failed:', error);
            next(error);
        }
    }

    /**
     * 导出配置
     */
    public async exportConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { format = 'json', includeSecrets = 'false' } = req.query;

            const config = await Config.findOne().lean();
            if (!config) {
                res.status(404).json({
                    success: false,
                    message: 'Configuration not found'
                });
                return;
            }

            // 创建配置副本
            const exportConfig = { ...config };
            delete (exportConfig as any)._id;
            delete (exportConfig as any).__v;

            // 如果不包含敏感信息，移除敏感字段
            if (includeSecrets !== 'true') {
                // 这里可以定义哪些字段是敏感的
                const sensitiveFields = ['supportEmail']; // 根据实际需要调整
                sensitiveFields.forEach(field => {
                    if (field in exportConfig) {
                        delete (exportConfig as any)[field];
                    }
                });
            }

            if (format === 'json') {
                res.json({
                    success: true,
                    data: exportConfig
                });
            } else if (format === 'file') {
                const filename = `config_export_${new Date().toISOString().split('T')[0]}.json`;
                
                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                res.send(JSON.stringify(exportConfig, null, 2));
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Unsupported format. Use json or file.'
                });
            }

            logger.info(`Configuration exported in ${format} format`);

        } catch (error) {
            logger.error('Export config failed:', error);
            next(error);
        }
    }

    /**
     * 导入配置
     */
    public async importConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { configData, mode = 'merge' } = req.body; // merge 或 replace

            if (!configData || typeof configData !== 'object') {
                res.status(400).json({
                    success: false,
                    message: 'Configuration data is required'
                });
                return;
            }

            let config = await Config.findOne();

            if (mode === 'replace') {
                // 替换模式：删除现有配置，创建新配置
                if (config) {
                    await Config.deleteOne({ _id: config._id });
                }
                config = new Config(configData);
            } else {
                // 合并模式：更新现有配置或创建新配置
                if (config) {
                    Object.assign(config, configData);
                } else {
                    config = new Config(configData);
                }
            }

            await config.save();

            logger.info(`Configuration imported in ${mode} mode`);

            res.json({
                success: true,
                data: config,
                message: `Configuration imported successfully in ${mode} mode`
            });

        } catch (error) {
            logger.error('Import config failed:', error);
            next(error);
        }
    }

    /**
     * 重置配置到默认值
     */
    public async resetConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { keys } = req.body; // 可选：指定要重置的配置项

            const defaultConfig = this.getDefaultConfig();

            let config = await Config.findOne();
            if (!config) {
                // 如果没有配置，创建默认配置
                config = new Config(defaultConfig);
                await config.save();
            } else {
                if (keys && Array.isArray(keys)) {
                    // 重置指定的配置项
                    for (const key of keys) {
                        if (key in defaultConfig) {
                            (config as any)[key] = (defaultConfig as any)[key];
                        }
                    }
                } else {
                    // 重置所有配置项
                    Object.assign(config, defaultConfig);
                }
                await config.save();
            }

            const resetKeys = keys && Array.isArray(keys) ? keys : Object.keys(defaultConfig);
            logger.info(`Configuration reset: ${resetKeys.join(', ')}`);

            res.json({
                success: true,
                data: config,
                message: `Configuration reset successfully`
            });

        } catch (error) {
            logger.error('Reset config failed:', error);
            next(error);
        }
    }

    /**
     * 验证配置
     */
    public async validateConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { configData } = req.body;

            if (!configData) {
                res.status(400).json({
                    success: false,
                    message: 'Configuration data is required'
                });
                return;
            }

            const validationResult = this.validateConfigData(configData);

            res.json({
                success: true,
                data: {
                    isValid: validationResult.isValid,
                    errors: validationResult.errors,
                    warnings: validationResult.warnings
                }
            });

        } catch (error) {
            logger.error('Validate config failed:', error);
            next(error);
        }
    }

    /**
     * 获取配置历史（如果有版本控制的话）
     */
    public async getConfigHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20
            } = req.query;

            // 这里可以实现配置历史记录功能
            // 目前返回空数组，表示没有历史记录
            const history: any[] = [];

            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));

            res.json({
                success: true,
                data: {
                    history,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: 0,
                        pages: 0
                    }
                },
                message: 'Configuration history feature not implemented yet'
            });

        } catch (error) {
            logger.error('Get config history failed:', error);
            next(error);
        }
    }

    /**
     * 获取配置模板
     */
    public async getConfigTemplate(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const template = this.getConfigTemplateData();

            res.json({
                success: true,
                data: template
            });

        } catch (error) {
            logger.error('Get config template failed:', error);
            next(error);
        }
    }

    /**
     * 获取版本控制配置
     */
    public async getVersionConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const config = await Config.findOne().lean();

            if (!config) {
                res.status(404).json({
                    success: false,
                    message: 'Configuration not found'
                });
                return;
            }

            const versionConfig = {
                latestVersion: config.latestVersion || '1.0.0',
                minimumVersion: config.minimumVersion || '1.0.0',
                forceUpdate: config.forceUpdate || false,
                updateMessage: config.updateMessage || new Map(),
                appStoreUrls: config.appStoreUrls || {
                    ios: 'APP_STORE_URL_PLACEHOLDER',
                    android: 'GOOGLE_PLAY_URL_PLACEHOLDER'
                },
                updateType: config.updateType || 'optional',
                versionCheckEnabled: config.versionCheckEnabled !== false
            };

            res.json({
                success: true,
                data: versionConfig
            });

        } catch (error) {
            logger.error('Get version config failed:', error);
            next(error);
        }
    }

    /**
     * 更新版本控制配置
     */
    public async updateVersionConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const {
                latestVersion,
                minimumVersion,
                forceUpdate,
                updateMessage,
                appStoreUrls,
                updateType,
                versionCheckEnabled
            } = req.body;

            let config = await Config.findOne();
            if (!config) {
                res.status(404).json({
                    success: false,
                    message: 'Configuration not found'
                });
                return;
            }

            // 更新版本控制相关字段
            if (latestVersion !== undefined) config.latestVersion = latestVersion;
            if (minimumVersion !== undefined) config.minimumVersion = minimumVersion;
            if (forceUpdate !== undefined) config.forceUpdate = forceUpdate;
            if (updateMessage !== undefined) (config.updateMessage as any) = new Map(Object.entries(updateMessage));
            if (appStoreUrls !== undefined) config.appStoreUrls = appStoreUrls;
            if (updateType !== undefined) config.updateType = updateType;
            if (versionCheckEnabled !== undefined) config.versionCheckEnabled = versionCheckEnabled;

            await config.save();

            logger.info('Version control configuration updated');

            res.json({
                success: true,
                data: {
                    latestVersion: config.latestVersion,
                    minimumVersion: config.minimumVersion,
                    forceUpdate: config.forceUpdate,
                    updateMessage: Object.fromEntries((config.updateMessage as any) || new Map()),
                    appStoreUrls: config.appStoreUrls,
                    updateType: config.updateType,
                    versionCheckEnabled: config.versionCheckEnabled
                },
                message: 'Version control configuration updated successfully'
            });

        } catch (error) {
            logger.error('Update version config failed:', error);
            next(error);
        }
    }

    /**
     * 获取默认配置
     */
    private getDefaultConfig() {
        return {
            privacyPolicy: 'Default privacy policy content',
            termsOfService: 'Default terms of service content',
            appVersion: '1.0.0',
            supportEmail: '<EMAIL>',
            featureFlags: new Map([
                ['enableRegistration', true],
                ['enableChat', true],
                ['enablePayment', true]
            ]),
            mainSolgan: new Map([
                ['zh_CN', ['欢迎使用ChatAdvisor', '智能对话助手']],
                ['en', ['Welcome to ChatAdvisor', 'Intelligent Chat Assistant']]
            ]),
            registerSolgan: new Map([
                ['zh_CN', ['立即注册', '开始您的智能对话之旅']],
                ['en', ['Register Now', 'Start Your Intelligent Chat Journey']]
            ]),
            emailLoginSolgan: new Map([
                ['zh_CN', ['邮箱登录', '安全便捷']],
                ['en', ['Email Login', 'Secure and Convenient']]
            ]),
            rechargeMessages: new Map([
                ['zh_CN', ['充值成功', '余额已更新']],
                ['en', ['Recharge Successful', 'Balance Updated']]
            ]),
            hideMessage: new Map([
                ['zh_CN', ['消息已隐藏']],
                ['en', ['Message Hidden']]
            ]),
            rechargeDescription: new Map([
                ['zh_CN', '充值说明：请选择合适的充值金额'],
                ['en', 'Recharge Description: Please select an appropriate amount']
            ]),
            promotLocal: new Map([
                ['zh_CN', '本地推广内容'],
                ['en', 'Local promotion content']
            ]),
            promotCloud: new Map([
                ['zh_CN', '云端推广内容'],
                ['en', 'Cloud promotion content']
            ]),
            compressRate: 0.8,
            // 版本控制相关的默认配置
            latestVersion: '1.0.0',
            minimumVersion: '1.0.0',
            forceUpdate: false,
            updateMessage: new Map([
                ['zh_CN', '发现新版本，建议立即更新以获得更好的体验'],
                ['en', 'New version available, please update for better experience']
            ]),
            appStoreUrls: {
                ios: 'APP_STORE_URL_PLACEHOLDER',
                android: 'GOOGLE_PLAY_URL_PLACEHOLDER'
            },
            updateType: 'optional',
            versionCheckEnabled: true
        };
    }

    /**
     * 获取配置模板数据
     */
    private getConfigTemplateData() {
        return {
            privacyPolicy: {
                type: 'string',
                required: true,
                description: '隐私政策内容'
            },
            termsOfService: {
                type: 'string',
                required: true,
                description: '服务条款内容'
            },
            appVersion: {
                type: 'string',
                required: true,
                pattern: '^\\d+\\.\\d+\\.\\d+$',
                description: '应用版本号（格式：x.y.z）'
            },
            supportEmail: {
                type: 'string',
                required: true,
                pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
                description: '支持邮箱地址'
            },
            featureFlags: {
                type: 'Map<string, boolean>',
                required: true,
                description: '功能开关配置'
            },
            mainSolgan: {
                type: 'Map<string, string[]>',
                required: true,
                description: '主标语（多语言）'
            },
            compressRate: {
                type: 'number',
                required: true,
                min: 0,
                max: 1,
                description: '压缩率（0-1之间）'
            },
            // 版本控制相关的配置模板
            latestVersion: {
                type: 'string',
                required: true,
                pattern: '^\\d+\\.\\d+\\.\\d+$',
                description: '最新版本号（格式：x.y.z）'
            },
            minimumVersion: {
                type: 'string',
                required: true,
                pattern: '^\\d+\\.\\d+\\.\\d+$',
                description: '最低支持版本号（格式：x.y.z）'
            },
            forceUpdate: {
                type: 'boolean',
                required: true,
                description: '是否强制更新'
            },
            updateMessage: {
                type: 'Map<string, string>',
                required: true,
                description: '更新提示消息（多语言）'
            },
            appStoreUrls: {
                type: 'object',
                required: true,
                properties: {
                    ios: { type: 'string', description: 'iOS应用商店链接' },
                    android: { type: 'string', description: 'Android应用商店链接' }
                },
                description: '应用商店下载链接'
            },
            updateType: {
                type: 'string',
                required: true,
                enum: ['force', 'optional'],
                description: '更新类型（强制/可选）'
            },
            versionCheckEnabled: {
                type: 'boolean',
                required: true,
                description: '是否启用版本检测'
            }
        };
    }

    /**
     * 验证配置数据
     */
    private validateConfigData(configData: any) {
        const errors: string[] = [];
        const warnings: string[] = [];

        // 验证必需字段
        const requiredFields = ['privacyPolicy', 'termsOfService', 'appVersion', 'supportEmail'];
        for (const field of requiredFields) {
            if (!configData[field]) {
                errors.push(`Missing required field: ${field}`);
            }
        }

        // 验证邮箱格式
        if (configData.supportEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(configData.supportEmail)) {
            errors.push('Invalid email format for supportEmail');
        }

        // 验证版本号格式
        if (configData.appVersion && !/^\d+\.\d+\.\d+$/.test(configData.appVersion)) {
            errors.push('Invalid version format for appVersion (expected: x.y.z)');
        }

        // 验证压缩率
        if (configData.compressRate !== undefined) {
            if (typeof configData.compressRate !== 'number' ||
                configData.compressRate < 0 ||
                configData.compressRate > 1) {
                errors.push('compressRate must be a number between 0 and 1');
            }
        }

        // 验证版本控制相关字段
        if (configData.latestVersion && !/^\d+\.\d+\.\d+$/.test(configData.latestVersion)) {
            errors.push('Invalid version format for latestVersion (expected: x.y.z)');
        }

        if (configData.minimumVersion && !/^\d+\.\d+\.\d+$/.test(configData.minimumVersion)) {
            errors.push('Invalid version format for minimumVersion (expected: x.y.z)');
        }

        if (configData.updateType && !['force', 'optional'].includes(configData.updateType)) {
            errors.push('updateType must be either "force" or "optional"');
        }

        if (configData.appStoreUrls) {
            if (typeof configData.appStoreUrls !== 'object') {
                errors.push('appStoreUrls must be an object');
            } else {
                if (!configData.appStoreUrls.ios || typeof configData.appStoreUrls.ios !== 'string') {
                    errors.push('appStoreUrls.ios must be a string');
                }
                if (!configData.appStoreUrls.android || typeof configData.appStoreUrls.android !== 'string') {
                    errors.push('appStoreUrls.android must be a string');
                }
            }
        }

        // 检查是否有未知字段
        const knownFields = [
            'privacyPolicy', 'termsOfService', 'appVersion', 'supportEmail',
            'featureFlags', 'mainSolgan', 'registerSolgan', 'emailLoginSolgan',
            'rechargeMessages', 'hideMessage', 'rechargeDescription',
            'promotLocal', 'promotCloud', 'compressRate',
            // 版本控制相关字段
            'latestVersion', 'minimumVersion', 'forceUpdate', 'updateMessage',
            'appStoreUrls', 'updateType', 'versionCheckEnabled'
        ];

        for (const field in configData) {
            if (!knownFields.includes(field)) {
                warnings.push(`Unknown configuration field: ${field}`);
            }
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}

export default new ConfigController();
