/**
 * 日志管理控制器
 * 统一管理RequestLog、ResponseLog、ErrorLog等日志数据
 */

import { Request, Response, NextFunction } from 'express';
import RequestLog from '../../models/RequestLog';
import ResponseLog from '../../models/ResponseLog';
import ErrorLog from '../../models/ErrorLog';
import { logger } from '../../business/logger';
import { validationResult } from 'express-validator';

export class LogController {
    /**
     * 获取请求日志列表
     */
    public async getRequestLogs(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                url,
                method,
                dateFrom,
                dateTo,
                sortBy = 'requestTime',
                sortOrder = 'desc'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            
            if (url) {
                query.url = { $regex: url, $options: 'i' };
            }

            if (method) {
                query.method = method.toString().toUpperCase();
            }

            if (dateFrom || dateTo) {
                query.requestTime = {};
                if (dateFrom) query.requestTime.$gte = new Date(dateFrom as string);
                if (dateTo) query.requestTime.$lte = new Date(dateTo as string);
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 排序
            const sort: any = {};
            sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

            // 查询请求日志
            const [logs, total] = await Promise.all([
                RequestLog.find(query)
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                RequestLog.countDocuments(query)
            ]);

            res.json({
                success: true,
                data: {
                    logs,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get request logs failed:', error);
            next(error);
        }
    }

    /**
     * 获取响应日志列表
     */
    public async getResponseLogs(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                status,
                dateFrom,
                dateTo,
                sortBy = 'responseTime',
                sortOrder = 'desc'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            
            if (status) {
                query.responseStatus = parseInt(status as string);
            }

            if (dateFrom || dateTo) {
                query.responseTime = {};
                if (dateFrom) query.responseTime.$gte = new Date(dateFrom as string);
                if (dateTo) query.responseTime.$lte = new Date(dateTo as string);
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 排序
            const sort: any = {};
            sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

            // 查询响应日志并关联请求日志
            const [logs, total] = await Promise.all([
                ResponseLog.find(query)
                    .populate('requestLogId', 'url method requestTime')
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                ResponseLog.countDocuments(query)
            ]);

            res.json({
                success: true,
                data: {
                    logs,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get response logs failed:', error);
            next(error);
        }
    }

    /**
     * 获取错误日志列表
     */
    public async getErrorLogs(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                errorCode,
                error,
                dateFrom,
                dateTo,
                sortBy = 'timestamp',
                sortOrder = 'desc'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            
            if (errorCode) {
                query.errorCode = parseInt(errorCode as string);
            }

            if (error) {
                query.error = { $regex: error, $options: 'i' };
            }

            if (dateFrom || dateTo) {
                query.timestamp = {};
                if (dateFrom) query.timestamp.$gte = new Date(dateFrom as string);
                if (dateTo) query.timestamp.$lte = new Date(dateTo as string);
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 排序
            const sort: any = {};
            sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

            // 查询错误日志并关联请求日志
            const [logs, total] = await Promise.all([
                ErrorLog.find(query)
                    .populate('requestId', 'url method requestTime')
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                ErrorLog.countDocuments(query)
            ]);

            res.json({
                success: true,
                data: {
                    logs,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get error logs failed:', error);
            next(error);
        }
    }

    /**
     * 获取日志详情（包含关联的请求、响应、错误信息）
     */
    public async getLogDetails(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { requestId } = req.params;

            // 查询请求日志
            const requestLog = await RequestLog.findById(requestId).lean();
            if (!requestLog) {
                res.status(404).json({
                    success: false,
                    message: 'Request log not found'
                });
                return;
            }

            // 查询关联的响应日志和错误日志
            const [responseLog, errorLogs] = await Promise.all([
                ResponseLog.findOne({ requestLogId: requestId }).lean(),
                ErrorLog.find({ requestId }).lean()
            ]);

            const logDetails = {
                request: requestLog,
                response: responseLog,
                errors: errorLogs,
                hasResponse: !!responseLog,
                hasErrors: errorLogs.length > 0,
                errorCount: errorLogs.length
            };

            res.json({
                success: true,
                data: logDetails
            });

        } catch (error) {
            logger.error('Get log details failed:', error);
            next(error);
        }
    }

    /**
     * 获取日志统计信息
     */
    public async getLogStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                dateFrom,
                dateTo,
                groupBy = 'day' // day, week, month
            } = req.query;

            const matchStage: any = {};
            if (dateFrom || dateTo) {
                matchStage.requestTime = {};
                if (dateFrom) matchStage.requestTime.$gte = new Date(dateFrom as string);
                if (dateTo) matchStage.requestTime.$lte = new Date(dateTo as string);
            }

            // 根据groupBy参数设置分组格式
            let dateFormat: string;
            switch (groupBy) {
                case 'week':
                    dateFormat = '%Y-%U';
                    break;
                case 'month':
                    dateFormat = '%Y-%m';
                    break;
                default:
                    dateFormat = '%Y-%m-%d';
            }

            const [
                requestStats,
                methodDistribution,
                statusDistribution,
                errorStats,
                timeSeriesData,
                topEndpoints
            ] = await Promise.all([
                // 请求统计
                RequestLog.aggregate([
                    { $match: matchStage },
                    {
                        $group: {
                            _id: null,
                            totalRequests: { $sum: 1 },
                            uniqueUrls: { $addToSet: '$url' }
                        }
                    },
                    {
                        $project: {
                            totalRequests: 1,
                            uniqueUrlCount: { $size: '$uniqueUrls' }
                        }
                    }
                ]),

                // 请求方法分布
                RequestLog.aggregate([
                    { $match: matchStage },
                    {
                        $group: {
                            _id: '$method',
                            count: { $sum: 1 }
                        }
                    },
                    { $sort: { count: -1 } }
                ]),

                // 响应状态码分布
                ResponseLog.aggregate([
                    {
                        $lookup: {
                            from: 'requestlogs',
                            localField: 'requestLogId',
                            foreignField: '_id',
                            as: 'request'
                        }
                    },
                    { $unwind: '$request' },
                    { $match: { 'request.requestTime': matchStage.requestTime || { $exists: true } } },
                    {
                        $group: {
                            _id: '$responseStatus',
                            count: { $sum: 1 }
                        }
                    },
                    { $sort: { _id: 1 } }
                ]),

                // 错误统计
                ErrorLog.aggregate([
                    {
                        $lookup: {
                            from: 'requestlogs',
                            localField: 'requestId',
                            foreignField: '_id',
                            as: 'request'
                        }
                    },
                    { $unwind: '$request' },
                    { $match: { 'request.requestTime': matchStage.requestTime || { $exists: true } } },
                    {
                        $group: {
                            _id: '$errorCode',
                            count: { $sum: 1 },
                            errors: { $addToSet: '$error' }
                        }
                    },
                    { $sort: { count: -1 } }
                ]),

                // 时间序列数据
                RequestLog.aggregate([
                    { $match: matchStage },
                    {
                        $group: {
                            _id: { $dateToString: { format: dateFormat, date: '$requestTime' } },
                            requestCount: { $sum: 1 }
                        }
                    },
                    { $sort: { _id: 1 } }
                ]),

                // 热门端点
                RequestLog.aggregate([
                    { $match: matchStage },
                    {
                        $group: {
                            _id: { url: '$url', method: '$method' },
                            count: { $sum: 1 }
                        }
                    },
                    { $sort: { count: -1 } },
                    { $limit: 10 }
                ])
            ]);

            res.json({
                success: true,
                data: {
                    overview: requestStats[0] || { totalRequests: 0, uniqueUrlCount: 0 },
                    methodDistribution,
                    statusDistribution,
                    errorStats,
                    timeSeriesData,
                    topEndpoints,
                    groupBy,
                    dateRange: {
                        from: dateFrom,
                        to: dateTo
                    }
                }
            });

        } catch (error) {
            logger.error('Get log stats failed:', error);
            next(error);
        }
    }

    /**
     * 清理日志数据
     */
    public async cleanupLogs(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const {
                type, // request, response, error, all
                daysToKeep = 30,
                batchSize = 1000
            } = req.body;

            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            let results: any = {};

            if (type === 'all' || type === 'request') {
                const requestResult = await this.cleanupLogsByType(
                    RequestLog,
                    'requestTime',
                    cutoffDate,
                    batchSize
                );
                results.requests = requestResult;
            }

            if (type === 'all' || type === 'response') {
                const responseResult = await this.cleanupLogsByType(
                    ResponseLog,
                    'responseTime',
                    cutoffDate,
                    batchSize
                );
                results.responses = responseResult;
            }

            if (type === 'all' || type === 'error') {
                const errorResult = await this.cleanupLogsByType(
                    ErrorLog,
                    'timestamp',
                    cutoffDate,
                    batchSize
                );
                results.errors = errorResult;
            }

            logger.info(`Log cleanup completed: ${JSON.stringify(results)}`);

            res.json({
                success: true,
                data: {
                    results,
                    cutoffDate,
                    daysToKeep
                },
                message: 'Log cleanup completed successfully'
            });

        } catch (error) {
            logger.error('Cleanup logs failed:', error);
            next(error);
        }
    }

    /**
     * 导出日志数据
     */
    public async exportLogs(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                type = 'request', // request, response, error
                format = 'csv',
                dateFrom,
                dateTo,
                limit = 10000
            } = req.query;

            // 构建查询条件
            const query: any = {};
            const timeField = type === 'request' ? 'requestTime' :
                             type === 'response' ? 'responseTime' : 'timestamp';

            if (dateFrom || dateTo) {
                query[timeField] = {};
                if (dateFrom) query[timeField].$gte = new Date(dateFrom as string);
                if (dateTo) query[timeField].$lte = new Date(dateTo as string);
            }

            let logs: any[] = [];
            const limitNum = Math.min(50000, parseInt(limit as string));

            // 根据类型查询不同的日志
            switch (type) {
                case 'request':
                    logs = await RequestLog.find(query)
                        .sort({ requestTime: -1 })
                        .limit(limitNum)
                        .lean();
                    break;
                case 'response':
                    logs = await ResponseLog.find(query)
                        .populate('requestLogId', 'url method')
                        .sort({ responseTime: -1 })
                        .limit(limitNum)
                        .lean();
                    break;
                case 'error':
                    logs = await ErrorLog.find(query)
                        .populate('requestId', 'url method')
                        .sort({ timestamp: -1 })
                        .limit(limitNum)
                        .lean();
                    break;
                default:
                    res.status(400).json({
                        success: false,
                        message: 'Invalid log type. Use request, response, or error.'
                    });
                    return;
            }

            // 格式化数据
            const formattedData = this.formatLogsForExport(logs, type);

            if (format === 'json') {
                res.json({
                    success: true,
                    data: formattedData,
                    total: formattedData.length,
                    type
                });
            } else if (format === 'csv') {
                const csv = this.generateCSV(formattedData);
                const filename = `${type}_logs_export_${new Date().toISOString().split('T')[0]}.csv`;

                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                res.send(csv);
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Unsupported format. Use json or csv.'
                });
            }

            logger.info(`Exported ${formattedData.length} ${type} logs in ${format} format`);

        } catch (error) {
            logger.error('Export logs failed:', error);
            next(error);
        }
    }

    /**
     * 搜索日志
     */
    public async searchLogs(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                keyword,
                type = 'all', // all, request, response, error
                page = 1,
                limit = 20,
                dateFrom,
                dateTo
            } = req.query;

            if (!keyword) {
                res.status(400).json({
                    success: false,
                    message: 'Keyword is required'
                });
                return;
            }

            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            let results: any = {};
            let totalCount = 0;

            // 构建时间查询条件
            const buildTimeQuery = (timeField: string) => {
                const query: any = {};
                if (dateFrom || dateTo) {
                    query[timeField] = {};
                    if (dateFrom) query[timeField].$gte = new Date(dateFrom as string);
                    if (dateTo) query[timeField].$lte = new Date(dateTo as string);
                }
                return query;
            };

            if (type === 'all' || type === 'request') {
                const query = {
                    ...buildTimeQuery('requestTime'),
                    $or: [
                        { url: { $regex: keyword, $options: 'i' } },
                        { method: { $regex: keyword, $options: 'i' } }
                    ]
                };

                const [requestLogs, requestCount] = await Promise.all([
                    RequestLog.find(query).sort({ requestTime: -1 }).limit(limitNum).lean(),
                    RequestLog.countDocuments(query)
                ]);

                results.requests = requestLogs;
                totalCount += requestCount;
            }

            if (type === 'all' || type === 'error') {
                const query = {
                    ...buildTimeQuery('timestamp'),
                    error: { $regex: keyword, $options: 'i' }
                };

                const [errorLogs, errorCount] = await Promise.all([
                    ErrorLog.find(query)
                        .populate('requestId', 'url method')
                        .sort({ timestamp: -1 })
                        .limit(limitNum)
                        .lean(),
                    ErrorLog.countDocuments(query)
                ]);

                results.errors = errorLogs;
                totalCount += errorCount;
            }

            res.json({
                success: true,
                data: {
                    results,
                    keyword,
                    type,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: totalCount,
                        pages: Math.ceil(totalCount / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Search logs failed:', error);
            next(error);
        }
    }

    /**
     * 按类型清理日志的辅助方法
     */
    private async cleanupLogsByType(Model: any, timeField: string, cutoffDate: Date, batchSize: number) {
        let totalDeleted = 0;
        let hasMore = true;

        while (hasMore) {
            const result = await Model.deleteMany({
                [timeField]: { $lt: cutoffDate }
            }).limit(batchSize);

            totalDeleted += result.deletedCount;
            hasMore = result.deletedCount === batchSize;

            // 避免阻塞事件循环
            if (hasMore) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }

        return { deletedCount: totalDeleted };
    }

    /**
     * 格式化日志数据用于导出
     */
    private formatLogsForExport(logs: any[], type: string): any[] {
        return logs.map(log => {
            switch (type) {
                case 'request':
                    return {
                        id: log._id.toString(),
                        url: log.url,
                        method: log.method,
                        requestTime: new Date(log.requestTime).toISOString(),
                        hasRequestBody: !!log.requestBody
                    };
                case 'response':
                    return {
                        id: log._id.toString(),
                        requestId: log.requestLogId?._id?.toString() || log.requestLogId,
                        url: log.requestLogId?.url || '',
                        method: log.requestLogId?.method || '',
                        responseStatus: log.responseStatus,
                        responseTime: new Date(log.responseTime).toISOString(),
                        hasResponseBody: !!log.responseBody
                    };
                case 'error':
                    return {
                        id: log._id.toString(),
                        requestId: log.requestId?._id?.toString() || log.requestId,
                        url: log.requestId?.url || '',
                        method: log.requestId?.method || '',
                        error: log.error,
                        errorCode: log.errorCode,
                        timestamp: new Date(log.timestamp).toISOString(),
                        hasStack: !!log.stack
                    };
                default:
                    return log;
            }
        });
    }

    /**
     * 生成CSV格式数据
     */
    private generateCSV(data: any[]): string {
        if (data.length === 0) return '';

        const fields = Object.keys(data[0]);

        // 生成表头
        const headers = fields.map(field => `"${field}"`).join(',');

        // 生成数据行
        const rows = data.map(row => {
            return fields.map(field => {
                const value = row[field];
                if (value === null || value === undefined) return '""';
                if (typeof value === 'string') {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return `"${value}"`;
            }).join(',');
        });

        return [headers, ...rows].join('\n');
    }
}

export default new LogController();
