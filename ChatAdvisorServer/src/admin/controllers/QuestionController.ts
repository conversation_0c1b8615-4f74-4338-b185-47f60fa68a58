/**
 * 问题信息管理控制器
 * 提供问题库的管理、问题分类、问题统计等功能
 */

import { Request, Response, NextFunction } from 'express';
import { Question } from '../../models/Question';
import { logger } from '../../business/logger';
import { validationResult } from 'express-validator';

export class QuestionController {
    /**
     * 获取问题列表
     */
    public async getQuestions(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                search,
                language,
                sortBy = 'createdAt',
                sortOrder = 'desc'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            
            if (search) {
                const searchRegex = { $regex: search, $options: 'i' };
                query.$or = [
                    { 'sketch.zh_CN': searchRegex },
                    { 'sketch.en': searchRegex },
                    { 'content.zh_CN': searchRegex },
                    { 'content.en': searchRegex },
                    { 'question.zh_CN': searchRegex },
                    { 'question.en': searchRegex }
                ];
            }

            if (language) {
                // 确保指定语言的内容存在
                query[`sketch.${language}`] = { $exists: true, $ne: '' };
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 排序
            const sort: any = {};
            sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

            // 查询问题
            const [questions, total] = await Promise.all([
                Question.find(query)
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                Question.countDocuments(query)
            ]);

            res.json({
                success: true,
                data: {
                    questions,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get questions failed:', error);
            next(error);
        }
    }

    /**
     * 获取问题详情
     */
    public async getQuestionById(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const question = await Question.findById(id).lean();

            if (!question) {
                res.status(404).json({
                    success: false,
                    message: 'Question not found'
                });
                return;
            }

            res.json({
                success: true,
                data: question
            });

        } catch (error) {
            logger.error('Get question by ID failed:', error);
            next(error);
        }
    }

    /**
     * 创建问题
     */
    public async createQuestion(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { sketch, content, question } = req.body;

            const newQuestion = new Question({
                sketch: new Map(Object.entries(sketch || {})),
                content: new Map(Object.entries(content || {})),
                question: new Map(Object.entries(question || {}))
            });

            await newQuestion.save();

            logger.info('Question created');

            res.status(201).json({
                success: true,
                data: newQuestion,
                message: 'Question created successfully'
            });

        } catch (error) {
            logger.error('Create question failed:', error);
            next(error);
        }
    }

    /**
     * 更新问题
     */
    public async updateQuestion(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { id } = req.params;
            const updateData = req.body;

            // 处理Map类型字段
            if (updateData.sketch) {
                updateData.sketch = new Map(Object.entries(updateData.sketch));
            }
            if (updateData.content) {
                updateData.content = new Map(Object.entries(updateData.content));
            }
            if (updateData.question) {
                updateData.question = new Map(Object.entries(updateData.question));
            }

            const question = await Question.findByIdAndUpdate(
                id,
                updateData,
                { new: true, runValidators: true }
            );

            if (!question) {
                res.status(404).json({
                    success: false,
                    message: 'Question not found'
                });
                return;
            }

            logger.info(`Question updated: ${id}`);

            res.json({
                success: true,
                data: question,
                message: 'Question updated successfully'
            });

        } catch (error) {
            logger.error('Update question failed:', error);
            next(error);
        }
    }

    /**
     * 删除问题
     */
    public async deleteQuestion(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const question = await Question.findByIdAndDelete(id);

            if (!question) {
                res.status(404).json({
                    success: false,
                    message: 'Question not found'
                });
                return;
            }

            logger.info(`Question deleted: ${id}`);

            res.json({
                success: true,
                message: 'Question deleted successfully'
            });

        } catch (error) {
            logger.error('Delete question failed:', error);
            next(error);
        }
    }

    /**
     * 批量删除问题
     */
    public async batchDeleteQuestions(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { questionIds } = req.body;

            if (!Array.isArray(questionIds) || questionIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'Question IDs array is required'
                });
                return;
            }

            const result = await Question.deleteMany({ _id: { $in: questionIds } });

            logger.info(`Batch deleted ${result.deletedCount} questions`);

            res.json({
                success: true,
                data: {
                    deletedCount: result.deletedCount,
                    total: questionIds.length
                },
                message: `Successfully deleted ${result.deletedCount} questions`
            });

        } catch (error) {
            logger.error('Batch delete questions failed:', error);
            next(error);
        }
    }

    /**
     * 获取问题统计信息
     */
    public async getQuestionStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const [
                totalQuestions,
                languageDistribution,
                contentLengthStats
            ] = await Promise.all([
                // 总问题数
                Question.countDocuments({}),
                
                // 语言分布统计
                Question.aggregate([
                    {
                        $project: {
                            languages: {
                                $filter: {
                                    input: [
                                        { lang: 'zh_CN', hasContent: { $ne: [{ $ifNull: ['$sketch.zh_CN', ''] }, ''] } },
                                        { lang: 'en', hasContent: { $ne: [{ $ifNull: ['$sketch.en', ''] }, ''] } }
                                    ],
                                    cond: '$$this.hasContent'
                                }
                            }
                        }
                    },
                    { $unwind: '$languages' },
                    {
                        $group: {
                            _id: '$languages.lang',
                            count: { $sum: 1 }
                        }
                    }
                ]),
                
                // 内容长度统计
                Question.aggregate([
                    {
                        $project: {
                            sketchLength: {
                                $add: [
                                    { $strLenCP: { $ifNull: ['$sketch.zh_CN', ''] } },
                                    { $strLenCP: { $ifNull: ['$sketch.en', ''] } }
                                ]
                            },
                            contentLength: {
                                $add: [
                                    { $strLenCP: { $ifNull: ['$content.zh_CN', ''] } },
                                    { $strLenCP: { $ifNull: ['$content.en', ''] } }
                                ]
                            }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            avgSketchLength: { $avg: '$sketchLength' },
                            avgContentLength: { $avg: '$contentLength' },
                            maxSketchLength: { $max: '$sketchLength' },
                            maxContentLength: { $max: '$contentLength' }
                        }
                    }
                ])
            ]);

            const stats = {
                overview: {
                    totalQuestions
                },
                languageDistribution,
                contentStats: contentLengthStats[0] || {
                    avgSketchLength: 0,
                    avgContentLength: 0,
                    maxSketchLength: 0,
                    maxContentLength: 0
                }
            };

            res.json({
                success: true,
                data: stats
            });

        } catch (error) {
            logger.error('Get question stats failed:', error);
            next(error);
        }
    }

    /**
     * 导出问题数据
     */
    public async exportQuestions(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                format = 'csv',
                language,
                includeAllLanguages = 'true'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            if (language) {
                query[`sketch.${language}`] = { $exists: true, $ne: '' };
            }

            // 查询问题数据
            const questions = await Question.find(query)
                .sort({ createdAt: -1 })
                .lean();

            // 格式化数据
            const formattedData = questions.map((question: any) => {
                const baseData = {
                    id: question._id.toString()
                };

                if (includeAllLanguages === 'true') {
                    return {
                        ...baseData,
                        sketchZhCN: question.sketch?.get('zh_CN') || '',
                        sketchEn: question.sketch?.get('en') || '',
                        contentZhCN: question.content?.get('zh_CN') || '',
                        contentEn: question.content?.get('en') || '',
                        questionZhCN: question.question?.get('zh_CN') || '',
                        questionEn: question.question?.get('en') || ''
                    };
                } else if (language) {
                    return {
                        ...baseData,
                        sketch: question.sketch?.get(language) || '',
                        content: question.content?.get(language) || '',
                        question: question.question?.get(language) || ''
                    };
                } else {
                    // 默认返回中文
                    return {
                        ...baseData,
                        sketch: question.sketch?.get('zh_CN') || '',
                        content: question.content?.get('zh_CN') || '',
                        question: question.question?.get('zh_CN') || ''
                    };
                }
            });

            if (format === 'json') {
                res.json({
                    success: true,
                    data: formattedData,
                    total: formattedData.length
                });
            } else if (format === 'csv') {
                const csv = this.generateCSV(formattedData);
                const filename = `questions_export_${new Date().toISOString().split('T')[0]}.csv`;

                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                res.send(csv);
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Unsupported format. Use json or csv.'
                });
            }

            logger.info(`Exported ${formattedData.length} questions in ${format} format`);

        } catch (error) {
            logger.error('Export questions failed:', error);
            next(error);
        }
    }

    /**
     * 导入问题数据
     */
    public async importQuestions(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { questions, mode = 'add' } = req.body; // add 或 replace

            if (!Array.isArray(questions) || questions.length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'Questions array is required'
                });
                return;
            }

            let results = {
                created: 0,
                updated: 0,
                failed: 0,
                errors: [] as string[]
            };

            if (mode === 'replace') {
                // 替换模式：先删除所有现有问题
                await Question.deleteMany({});
            }

            // 处理每个问题
            for (let i = 0; i < questions.length; i++) {
                try {
                    const questionData = questions[i];

                    const question = new Question({
                        sketch: new Map(Object.entries(questionData.sketch || {})),
                        content: new Map(Object.entries(questionData.content || {})),
                        question: new Map(Object.entries(questionData.question || {}))
                    });

                    await question.save();
                    results.created++;

                } catch (error) {
                    results.failed++;
                    results.errors.push(`Question ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }

            logger.info(`Question import completed: ${results.created} created, ${results.failed} failed`);

            res.json({
                success: true,
                data: results,
                message: `Import completed: ${results.created} created, ${results.failed} failed`
            });

        } catch (error) {
            logger.error('Import questions failed:', error);
            next(error);
        }
    }

    /**
     * 搜索问题
     */
    public async searchQuestions(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                keyword,
                language = 'zh_CN',
                page = 1,
                limit = 20
            } = req.query;

            if (!keyword) {
                res.status(400).json({
                    success: false,
                    message: 'Keyword is required'
                });
                return;
            }

            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 构建搜索查询
            const searchRegex = { $regex: keyword, $options: 'i' };
            const query = {
                $or: [
                    { [`sketch.${language}`]: searchRegex },
                    { [`content.${language}`]: searchRegex },
                    { [`question.${language}`]: searchRegex }
                ]
            };

            const [questions, total] = await Promise.all([
                Question.find(query)
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                Question.countDocuments(query)
            ]);

            // 高亮关键词
            const highlightedQuestions = questions.map((question: any) => ({
                ...question,
                highlightedSketch: this.highlightKeyword(
                    question.sketch?.get(language) || '',
                    keyword as string
                ),
                highlightedContent: this.highlightKeyword(
                    question.content?.get(language) || '',
                    keyword as string
                ),
                highlightedQuestion: this.highlightKeyword(
                    question.question?.get(language) || '',
                    keyword as string
                )
            }));

            res.json({
                success: true,
                data: {
                    questions: highlightedQuestions,
                    keyword,
                    language,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Search questions failed:', error);
            next(error);
        }
    }

    /**
     * 复制问题
     */
    public async duplicateQuestion(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            // 检查原问题是否存在
            const originalQuestion = await Question.findById(id);
            if (!originalQuestion) {
                res.status(404).json({
                    success: false,
                    message: 'Original question not found'
                });
                return;
            }

            // 创建新问题（复制）
            const newQuestion = new Question({
                sketch: originalQuestion.sketch,
                content: originalQuestion.content,
                question: originalQuestion.question
            });

            await newQuestion.save();

            logger.info(`Question duplicated: ${id} -> ${newQuestion._id}`);

            res.status(201).json({
                success: true,
                data: newQuestion,
                message: 'Question duplicated successfully'
            });

        } catch (error) {
            logger.error('Duplicate question failed:', error);
            next(error);
        }
    }

    /**
     * 高亮关键词
     */
    private highlightKeyword(content: string, keyword: string): string {
        if (!content || !keyword) return content;
        const regex = new RegExp(`(${keyword})`, 'gi');
        return content.replace(regex, '<mark>$1</mark>');
    }

    /**
     * 生成CSV格式数据
     */
    private generateCSV(data: any[]): string {
        if (data.length === 0) return '';

        const fields = Object.keys(data[0]);

        // 生成表头
        const headers = fields.map(field => `"${field}"`).join(',');

        // 生成数据行
        const rows = data.map(row => {
            return fields.map(field => {
                const value = row[field];
                if (value === null || value === undefined) return '""';
                if (typeof value === 'string') {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return `"${value}"`;
            }).join(',');
        });

        return [headers, ...rows].join('\n');
    }
}

export default new QuestionController();
