/**
 * 产品管理控制器
 * 提供产品的增删改查、价格管理、启用禁用等功能
 */

import { Request, Response, NextFunction } from 'express';
import { Product } from '../../models/Product';
import { logger } from '../../business/logger';
import { validationResult } from 'express-validator';

export class ProductController {
    /**
     * 获取产品列表
     */
    public async getProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                search,
                isEnable,
                nation,
                sortBy = 'createdAt',
                sortOrder = 'desc'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            
            if (search) {
                query.productIdentifier = { $regex: search, $options: 'i' };
            }

            if (isEnable !== undefined) {
                query.isEnable = isEnable === 'true';
            }

            if (nation) {
                query.nation = nation;
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 排序
            const sort: any = {};
            sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

            // 查询产品
            const [products, total] = await Promise.all([
                Product.find(query)
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                Product.countDocuments(query)
            ]);

            res.json({
                success: true,
                data: {
                    products,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get products failed:', error);
            next(error);
        }
    }

    /**
     * 获取产品详情
     */
    public async getProductById(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const product = await Product.findById(id).lean();

            if (!product) {
                res.status(404).json({
                    success: false,
                    message: 'Product not found'
                });
                return;
            }

            res.json({
                success: true,
                data: product
            });

        } catch (error) {
            logger.error('Get product by ID failed:', error);
            next(error);
        }
    }

    /**
     * 创建产品
     */
    public async createProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { productIdentifier, amount, isEnable = true, nation = 'en' } = req.body;

            // 检查产品标识符是否已存在
            const existingProduct = await Product.findOne({ productIdentifier });
            if (existingProduct) {
                res.status(409).json({
                    success: false,
                    message: 'Product identifier already exists'
                });
                return;
            }

            const product = new Product({
                productIdentifier,
                amount,
                isEnable,
                nation
            });

            await product.save();

            logger.info(`Product created: ${productIdentifier}`);

            res.status(201).json({
                success: true,
                data: product,
                message: 'Product created successfully'
            });

        } catch (error) {
            logger.error('Create product failed:', error);
            next(error);
        }
    }

    /**
     * 更新产品
     */
    public async updateProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { id } = req.params;
            const updateData = req.body;

            // 如果更新产品标识符，检查是否已存在
            if (updateData.productIdentifier) {
                const existingProduct = await Product.findOne({
                    productIdentifier: updateData.productIdentifier,
                    _id: { $ne: id }
                });
                
                if (existingProduct) {
                    res.status(409).json({
                        success: false,
                        message: 'Product identifier already exists'
                    });
                    return;
                }
            }

            const product = await Product.findByIdAndUpdate(
                id,
                updateData,
                { new: true, runValidators: true }
            );

            if (!product) {
                res.status(404).json({
                    success: false,
                    message: 'Product not found'
                });
                return;
            }

            logger.info(`Product updated: ${product.productIdentifier}`);

            res.json({
                success: true,
                data: product,
                message: 'Product updated successfully'
            });

        } catch (error) {
            logger.error('Update product failed:', error);
            next(error);
        }
    }

    /**
     * 删除产品
     */
    public async deleteProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const product = await Product.findByIdAndDelete(id);

            if (!product) {
                res.status(404).json({
                    success: false,
                    message: 'Product not found'
                });
                return;
            }

            logger.info(`Product deleted: ${product.productIdentifier}`);

            res.json({
                success: true,
                message: 'Product deleted successfully'
            });

        } catch (error) {
            logger.error('Delete product failed:', error);
            next(error);
        }
    }

    /**
     * 批量更新产品状态
     */
    public async batchUpdateProductStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { productIds, isEnable } = req.body;

            if (!Array.isArray(productIds) || productIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'Product IDs array is required'
                });
                return;
            }

            if (typeof isEnable !== 'boolean') {
                res.status(400).json({
                    success: false,
                    message: 'isEnable must be a boolean value'
                });
                return;
            }

            const result = await Product.updateMany(
                { _id: { $in: productIds } },
                { isEnable }
            );

            logger.info(`Batch updated ${result.modifiedCount} products status to ${isEnable}`);

            res.json({
                success: true,
                data: {
                    affected: result.modifiedCount,
                    total: productIds.length,
                    isEnable
                },
                message: `Successfully updated ${result.modifiedCount} products`
            });

        } catch (error) {
            logger.error('Batch update product status failed:', error);
            next(error);
        }
    }

    /**
     * 批量删除产品
     */
    public async batchDeleteProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { productIds } = req.body;

            if (!Array.isArray(productIds) || productIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'Product IDs array is required'
                });
                return;
            }

            const result = await Product.deleteMany({ _id: { $in: productIds } });

            logger.info(`Batch deleted ${result.deletedCount} products`);

            res.json({
                success: true,
                data: {
                    deletedCount: result.deletedCount,
                    total: productIds.length
                },
                message: `Successfully deleted ${result.deletedCount} products`
            });

        } catch (error) {
            logger.error('Batch delete products failed:', error);
            next(error);
        }
    }

    /**
     * 获取产品统计信息
     */
    public async getProductStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const [
                totalProducts,
                enabledProducts,
                disabledProducts,
                nationDistribution,
                priceRanges
            ] = await Promise.all([
                Product.countDocuments({}),
                Product.countDocuments({ isEnable: true }),
                Product.countDocuments({ isEnable: false }),

                // 国家分布
                Product.aggregate([
                    {
                        $group: {
                            _id: '$nation',
                            count: { $sum: 1 },
                            avgAmount: { $avg: '$amount' },
                            totalAmount: { $sum: '$amount' }
                        }
                    },
                    { $sort: { count: -1 } }
                ]),

                // 价格区间分布
                Product.aggregate([
                    {
                        $bucket: {
                            groupBy: '$amount',
                            boundaries: [0, 10, 50, 100, 500, 1000, Infinity],
                            default: 'Other',
                            output: {
                                count: { $sum: 1 },
                                products: { $push: '$productIdentifier' }
                            }
                        }
                    }
                ])
            ]);

            const stats = {
                overview: {
                    totalProducts,
                    enabledProducts,
                    disabledProducts,
                    enabledRate: totalProducts > 0 ? Math.round((enabledProducts / totalProducts) * 100) : 0
                },
                nationDistribution,
                priceRanges: priceRanges.map(range => ({
                    ...range,
                    range: this.getPriceRangeLabel(range._id)
                }))
            };

            res.json({
                success: true,
                data: stats
            });

        } catch (error) {
            logger.error('Get product stats failed:', error);
            next(error);
        }
    }

    /**
     * 导出产品数据
     */
    public async exportProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                format = 'csv',
                isEnable,
                nation
            } = req.query;

            // 构建查询条件
            const query: any = {};
            if (isEnable !== undefined) query.isEnable = isEnable === 'true';
            if (nation) query.nation = nation;

            // 查询产品数据
            const products = await Product.find(query)
                .sort({ createdAt: -1 })
                .lean();

            // 格式化数据
            const formattedData = products.map((product: any) => ({
                id: product._id.toString(),
                productIdentifier: product.productIdentifier,
                amount: product.amount,
                isEnable: product.isEnable,
                nation: product.nation,
                status: product.isEnable ? '启用' : '禁用'
            }));

            if (format === 'json') {
                res.json({
                    success: true,
                    data: formattedData,
                    total: formattedData.length
                });
            } else if (format === 'csv') {
                const csv = this.generateCSV(formattedData);
                const filename = `products_export_${new Date().toISOString().split('T')[0]}.csv`;

                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                res.send(csv);
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Unsupported format. Use json or csv.'
                });
            }

            logger.info(`Exported ${formattedData.length} products in ${format} format`);

        } catch (error) {
            logger.error('Export products failed:', error);
            next(error);
        }
    }

    /**
     * 复制产品
     */
    public async duplicateProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;
            const { newProductIdentifier } = req.body;

            if (!newProductIdentifier) {
                res.status(400).json({
                    success: false,
                    message: 'New product identifier is required'
                });
                return;
            }

            // 检查原产品是否存在
            const originalProduct = await Product.findById(id);
            if (!originalProduct) {
                res.status(404).json({
                    success: false,
                    message: 'Original product not found'
                });
                return;
            }

            // 检查新产品标识符是否已存在
            const existingProduct = await Product.findOne({ productIdentifier: newProductIdentifier });
            if (existingProduct) {
                res.status(409).json({
                    success: false,
                    message: 'New product identifier already exists'
                });
                return;
            }

            // 创建新产品
            const newProduct = new Product({
                productIdentifier: newProductIdentifier,
                amount: originalProduct.amount,
                isEnable: false, // 默认禁用新复制的产品
                nation: originalProduct.nation
            });

            await newProduct.save();

            logger.info(`Product duplicated: ${originalProduct.productIdentifier} -> ${newProductIdentifier}`);

            res.status(201).json({
                success: true,
                data: newProduct,
                message: 'Product duplicated successfully'
            });

        } catch (error) {
            logger.error('Duplicate product failed:', error);
            next(error);
        }
    }

    /**
     * 获取价格区间标签
     */
    private getPriceRangeLabel(boundary: any): string {
        if (boundary === 'Other') return '其他';
        if (boundary === 0) return '0-10';
        if (boundary === 10) return '10-50';
        if (boundary === 50) return '50-100';
        if (boundary === 100) return '100-500';
        if (boundary === 500) return '500-1000';
        if (boundary === 1000) return '1000+';
        return '未知';
    }

    /**
     * 生成CSV格式数据
     */
    private generateCSV(data: any[]): string {
        if (data.length === 0) return '';

        const fields = ['id', 'productIdentifier', 'amount', 'isEnable', 'nation', 'status'];

        // 生成表头
        const headers = fields.map(field => `"${field}"`).join(',');

        // 生成数据行
        const rows = data.map(row => {
            return fields.map(field => {
                const value = row[field];
                if (value === null || value === undefined) return '""';
                if (typeof value === 'string') {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return `"${value}"`;
            }).join(',');
        });

        return [headers, ...rows].join('\n');
    }
}

export default new ProductController();
