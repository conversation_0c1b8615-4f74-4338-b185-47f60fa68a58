/**
 * 统计报表控制器
 * 提供用户统计、聊天数据统计、财务数据统计等API接口
 */

import { Request, Response, NextFunction } from 'express';
import User from '../../models/User';
import { ChatMessageModel } from '../../models/ChatMessage';
import BalanceTransaction from '../../models/BalanceTransaction';
import RequestLog from '../../models/RequestLog';
import ResponseLog from '../../models/ResponseLog';
import ErrorLog from '../../models/ErrorLog';
import Pricing from '../../models/Pricing';
import { Product } from '../../models/Product';
import { logger } from '../../business/logger';

export class ReportController {
    /**
     * 获取仪表板总览数据
     */
    public async getDashboardOverview(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                dateFrom,
                dateTo
            } = req.query;

            // 构建时间查询条件
            const timeQuery = this.buildTimeQuery(dateFrom as string, dateTo as string);

            const [
                userStats,
                chatStats,
                financialStats,
                systemStats
            ] = await Promise.all([
                this.getUserOverviewStats(timeQuery),
                this.getChatOverviewStats(timeQuery),
                this.getFinancialOverviewStats(timeQuery),
                this.getSystemOverviewStats(timeQuery)
            ]);

            res.json({
                success: true,
                data: {
                    users: userStats,
                    chat: chatStats,
                    financial: financialStats,
                    system: systemStats,
                    dateRange: {
                        from: dateFrom,
                        to: dateTo
                    }
                }
            });

        } catch (error) {
            logger.error('Get dashboard overview failed:', error);
            next(error);
        }
    }

    /**
     * 获取用户统计报表
     */
    public async getUserReport(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                dateFrom,
                dateTo,
                groupBy = 'day' // day, week, month
            } = req.query;

            const timeQuery = this.buildTimeQuery(dateFrom as string, dateTo as string, 'createdAt');
            const dateFormat = this.getDateFormat(groupBy as string);

            const [
                registrationTrend,
                userDistribution,
                activityStats,
                balanceStats
            ] = await Promise.all([
                // 注册趋势
                User.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: { $dateToString: { format: dateFormat, date: '$createdAt' } },
                            newUsers: { $sum: 1 },
                            vipUsers: {
                                $sum: { $cond: ['$hasPurchase', 1, 0] }
                            }
                        }
                    },
                    { $sort: { _id: 1 } }
                ]),

                // 用户分布统计
                User.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: {
                                gender: '$gender',
                                language: '$language',
                                hasPurchase: '$hasPurchase'
                            },
                            count: { $sum: 1 }
                        }
                    }
                ]),

                // 活跃度统计
                User.aggregate([
                    { $match: timeQuery },
                    {
                        $project: {
                            daysSinceLastUpdate: {
                                $divide: [
                                    { $subtract: [new Date(), '$updatedAt'] },
                                    1000 * 60 * 60 * 24
                                ]
                            },
                            hasPurchase: 1
                        }
                    },
                    {
                        $group: {
                            _id: {
                                $switch: {
                                    branches: [
                                        { case: { $lte: ['$daysSinceLastUpdate', 1] }, then: 'today' },
                                        { case: { $lte: ['$daysSinceLastUpdate', 7] }, then: 'week' },
                                        { case: { $lte: ['$daysSinceLastUpdate', 30] }, then: 'month' }
                                    ],
                                    default: 'inactive'
                                }
                            },
                            count: { $sum: 1 }
                        }
                    }
                ]),

                // 余额统计
                User.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: null,
                            totalBalance: { $sum: '$balance' },
                            avgBalance: { $avg: '$balance' },
                            maxBalance: { $max: '$balance' },
                            minBalance: { $min: '$balance' },
                            usersWithBalance: {
                                $sum: { $cond: [{ $gt: ['$balance', 0] }, 1, 0] }
                            }
                        }
                    }
                ])
            ]);

            res.json({
                success: true,
                data: {
                    registrationTrend,
                    userDistribution,
                    activityStats,
                    balanceStats: balanceStats[0] || {},
                    groupBy,
                    dateRange: {
                        from: dateFrom,
                        to: dateTo
                    }
                }
            });

        } catch (error) {
            logger.error('Get user report failed:', error);
            next(error);
        }
    }

    /**
     * 获取聊天数据统计报表
     */
    public async getChatReport(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                dateFrom,
                dateTo,
                groupBy = 'day'
            } = req.query;

            const timeQuery = this.buildTimeQuery(dateFrom as string, dateTo as string, 'createdTime');
            const dateFormat = this.getDateFormat(groupBy as string);

            const [
                messageTrend,
                roleDistribution,
                sessionStats,
                completionStats
            ] = await Promise.all([
                // 消息趋势
                ChatMessageModel.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: {
                                date: { $dateToString: { format: dateFormat, date: '$createdTime' } },
                                role: '$role'
                            },
                            count: { $sum: 1 }
                        }
                    },
                    {
                        $group: {
                            _id: '$_id.date',
                            totalMessages: { $sum: '$count' },
                            userMessages: {
                                $sum: { $cond: [{ $eq: ['$_id.role', 'user'] }, '$count', 0] }
                            },
                            assistantMessages: {
                                $sum: { $cond: [{ $eq: ['$_id.role', 'assistant'] }, '$count', 0] }
                            }
                        }
                    },
                    { $sort: { _id: 1 } }
                ]),

                // 角色分布
                ChatMessageModel.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: '$role',
                            count: { $sum: 1 },
                            avgLength: { $avg: { $strLenCP: '$content' } }
                        }
                    }
                ]),

                // 会话统计
                ChatMessageModel.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: '$chatId',
                            messageCount: { $sum: 1 },
                            maxTime: { $max: '$createdTime' },
                            minTime: { $min: '$createdTime' }
                        }
                    },
                    {
                        $addFields: {
                            duration: {
                                $subtract: ['$maxTime', '$minTime']
                            }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            totalSessions: { $sum: 1 },
                            avgMessagesPerSession: { $avg: '$messageCount' },
                            avgSessionDuration: { $avg: '$duration' }
                        }
                    }
                ]),

                // 完成状态统计
                ChatMessageModel.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: '$isComplete',
                            count: { $sum: 1 }
                        }
                    }
                ])
            ]);

            res.json({
                success: true,
                data: {
                    messageTrend,
                    roleDistribution,
                    sessionStats: sessionStats[0] || {},
                    completionStats,
                    groupBy,
                    dateRange: {
                        from: dateFrom,
                        to: dateTo
                    }
                }
            });

        } catch (error) {
            logger.error('Get chat report failed:', error);
            next(error);
        }
    }

    /**
     * 获取财务数据统计报表
     */
    public async getFinancialReport(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                dateFrom,
                dateTo,
                groupBy = 'day'
            } = req.query;

            const timeQuery = this.buildTimeQuery(dateFrom as string, dateTo as string, 'timestamp');
            const dateFormat = this.getDateFormat(groupBy as string);

            const [
                revenueTrend,
                transactionStats,
                modelRevenue,
                userSpending
            ] = await Promise.all([
                // 收入趋势
                BalanceTransaction.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: {
                                date: { $dateToString: { format: dateFormat, date: '$timestamp' } },
                                type: '$type'
                            },
                            amount: { $sum: '$amount' },
                            count: { $sum: 1 }
                        }
                    },
                    {
                        $group: {
                            _id: '$_id.date',
                            totalAmount: { $sum: '$amount' },
                            deposits: {
                                $sum: { $cond: [{ $eq: ['$_id.type', 1] }, '$amount', 0] }
                            },
                            consumptions: {
                                $sum: { $cond: [{ $eq: ['$_id.type', 3] }, '$amount', 0] }
                            },
                            transactionCount: { $sum: '$count' }
                        }
                    },
                    { $sort: { _id: 1 } }
                ]),

                // 交易统计
                BalanceTransaction.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: '$type',
                            totalAmount: { $sum: '$amount' },
                            count: { $sum: 1 },
                            avgAmount: { $avg: '$amount' }
                        }
                    }
                ]),

                // 模型收入统计
                BalanceTransaction.aggregate([
                    { 
                        $match: { 
                            ...timeQuery, 
                            modelId: { $exists: true, $ne: null } 
                        } 
                    },
                    {
                        $lookup: {
                            from: 'pricings',
                            localField: 'modelId',
                            foreignField: '_id',
                            as: 'pricing'
                        }
                    },
                    { $unwind: '$pricing' },
                    {
                        $group: {
                            _id: '$modelId',
                            modelName: { $first: '$pricing.modelName' },
                            revenue: { $sum: '$amount' },
                            usageCount: { $sum: 1 }
                        }
                    },
                    { $sort: { revenue: -1 } },
                    { $limit: 10 }
                ]),

                // 用户消费排行
                BalanceTransaction.aggregate([
                    { 
                        $match: { 
                            ...timeQuery, 
                            type: 3 // 消费类型
                        } 
                    },
                    {
                        $group: {
                            _id: '$userId',
                            totalSpent: { $sum: '$amount' },
                            transactionCount: { $sum: 1 }
                        }
                    },
                    { $sort: { totalSpent: -1 } },
                    { $limit: 10 }
                ])
            ]);

            res.json({
                success: true,
                data: {
                    revenueTrend,
                    transactionStats,
                    modelRevenue,
                    userSpending,
                    groupBy,
                    dateRange: {
                        from: dateFrom,
                        to: dateTo
                    }
                }
            });

        } catch (error) {
            logger.error('Get financial report failed:', error);
            next(error);
        }
    }

    /**
     * 获取系统性能监控报表
     */
    public async getSystemReport(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                dateFrom,
                dateTo,
                groupBy = 'day'
            } = req.query;

            const timeQuery = this.buildTimeQuery(dateFrom as string, dateTo as string, 'requestTime');
            const dateFormat = this.getDateFormat(groupBy as string);

            const [
                apiUsageTrend,
                statusCodeDistribution,
                errorStats,
                performanceStats
            ] = await Promise.all([
                // API使用趋势
                RequestLog.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: {
                                date: { $dateToString: { format: dateFormat, date: '$requestTime' } },
                                method: '$method'
                            },
                            count: { $sum: 1 }
                        }
                    },
                    {
                        $group: {
                            _id: '$_id.date',
                            totalRequests: { $sum: '$count' },
                            getRequests: {
                                $sum: { $cond: [{ $eq: ['$_id.method', 'GET'] }, '$count', 0] }
                            },
                            postRequests: {
                                $sum: { $cond: [{ $eq: ['$_id.method', 'POST'] }, '$count', 0] }
                            },
                            putRequests: {
                                $sum: { $cond: [{ $eq: ['$_id.method', 'PUT'] }, '$count', 0] }
                            },
                            deleteRequests: {
                                $sum: { $cond: [{ $eq: ['$_id.method', 'DELETE'] }, '$count', 0] }
                            }
                        }
                    },
                    { $sort: { _id: 1 } }
                ]),

                // 状态码分布
                ResponseLog.aggregate([
                    {
                        $lookup: {
                            from: 'requestlogs',
                            localField: 'requestLogId',
                            foreignField: '_id',
                            as: 'request'
                        }
                    },
                    { $unwind: '$request' },
                    { $match: { 'request.requestTime': timeQuery.requestTime || { $exists: true } } },
                    {
                        $group: {
                            _id: {
                                $switch: {
                                    branches: [
                                        { case: { $lt: ['$responseStatus', 300] }, then: '2xx' },
                                        { case: { $lt: ['$responseStatus', 400] }, then: '3xx' },
                                        { case: { $lt: ['$responseStatus', 500] }, then: '4xx' },
                                        { case: { $gte: ['$responseStatus', 500] }, then: '5xx' }
                                    ],
                                    default: 'other'
                                }
                            },
                            count: { $sum: 1 }
                        }
                    }
                ]),

                // 错误统计
                ErrorLog.aggregate([
                    {
                        $lookup: {
                            from: 'requestlogs',
                            localField: 'requestId',
                            foreignField: '_id',
                            as: 'request'
                        }
                    },
                    { $unwind: '$request' },
                    { $match: { 'request.requestTime': timeQuery.requestTime || { $exists: true } } },
                    {
                        $group: {
                            _id: '$errorCode',
                            count: { $sum: 1 },
                            errors: { $addToSet: '$error' }
                        }
                    },
                    { $sort: { count: -1 } }
                ]),

                // 性能统计（热门端点）
                RequestLog.aggregate([
                    { $match: timeQuery },
                    {
                        $group: {
                            _id: { url: '$url', method: '$method' },
                            count: { $sum: 1 }
                        }
                    },
                    { $sort: { count: -1 } },
                    { $limit: 10 }
                ])
            ]);

            res.json({
                success: true,
                data: {
                    apiUsageTrend,
                    statusCodeDistribution,
                    errorStats,
                    topEndpoints: performanceStats,
                    groupBy,
                    dateRange: {
                        from: dateFrom,
                        to: dateTo
                    }
                }
            });

        } catch (error) {
            logger.error('Get system report failed:', error);
            next(error);
        }
    }

    /**
     * 获取实时统计数据
     */
    public async getRealTimeStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
            const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

            const [
                recentUsers,
                recentMessages,
                recentTransactions,
                recentErrors,
                activeUsers
            ] = await Promise.all([
                // 最近1小时新用户
                User.countDocuments({ createdAt: { $gte: oneHourAgo } }),

                // 最近1小时消息数
                ChatMessageModel.countDocuments({ createdTime: { $gte: oneHourAgo } }),

                // 最近1小时交易数
                BalanceTransaction.countDocuments({ timestamp: { $gte: oneHourAgo } }),

                // 最近1小时错误数
                ErrorLog.countDocuments({ timestamp: { $gte: oneHourAgo } }),

                // 最近24小时活跃用户
                User.countDocuments({ updatedAt: { $gte: oneDayAgo } })
            ]);

            res.json({
                success: true,
                data: {
                    recentUsers,
                    recentMessages,
                    recentTransactions,
                    recentErrors,
                    activeUsers,
                    timestamp: now.toISOString()
                }
            });

        } catch (error) {
            logger.error('Get real-time stats failed:', error);
            next(error);
        }
    }

    /**
     * 导出报表数据
     */
    public async exportReport(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                reportType, // dashboard, user, chat, financial, system
                format = 'csv',
                dateFrom,
                dateTo
            } = req.query;

            let reportData: any;

            // 根据报表类型获取数据
            switch (reportType) {
                case 'user':
                    reportData = await this.getUserReportData(dateFrom as string, dateTo as string);
                    break;
                case 'chat':
                    reportData = await this.getChatReportData(dateFrom as string, dateTo as string);
                    break;
                case 'financial':
                    reportData = await this.getFinancialReportData(dateFrom as string, dateTo as string);
                    break;
                case 'system':
                    reportData = await this.getSystemReportData(dateFrom as string, dateTo as string);
                    break;
                default:
                    res.status(400).json({
                        success: false,
                        message: 'Invalid report type'
                    });
                    return;
            }

            if (format === 'json') {
                res.json({
                    success: true,
                    data: reportData
                });
            } else if (format === 'csv') {
                const csv = this.generateCSV(reportData);
                const filename = `${reportType}_report_${new Date().toISOString().split('T')[0]}.csv`;

                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                res.send(csv);
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Unsupported format. Use json or csv.'
                });
            }

            logger.info(`Exported ${reportType} report in ${format} format`);

        } catch (error) {
            logger.error('Export report failed:', error);
            next(error);
        }
    }

    /**
     * 构建时间查询条件
     */
    private buildTimeQuery(dateFrom?: string, dateTo?: string, timeField: string = 'createdAt'): any {
        const query: any = {};

        if (dateFrom || dateTo) {
            query[timeField] = {};
            if (dateFrom) query[timeField].$gte = new Date(dateFrom);
            if (dateTo) query[timeField].$lte = new Date(dateTo);
        }

        return query;
    }

    /**
     * 获取日期格式
     */
    private getDateFormat(groupBy: string): string {
        switch (groupBy) {
            case 'week':
                return '%Y-%U';
            case 'month':
                return '%Y-%m';
            case 'year':
                return '%Y';
            default:
                return '%Y-%m-%d';
        }
    }

    /**
     * 获取用户总览统计
     */
    private async getUserOverviewStats(timeQuery: any) {
        const [totalUsers, newUsers, activeUsers, vipUsers] = await Promise.all([
            User.countDocuments({}),
            User.countDocuments(timeQuery),
            User.countDocuments({
                updatedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
            }),
            User.countDocuments({ hasPurchase: true })
        ]);

        return {
            totalUsers,
            newUsers,
            activeUsers,
            vipUsers,
            growthRate: totalUsers > 0 ? Math.round((newUsers / totalUsers) * 100) : 0
        };
    }

    /**
     * 获取聊天总览统计
     */
    private async getChatOverviewStats(timeQuery: any) {
        const chatTimeQuery = this.buildTimeQuery(
            timeQuery.createdAt?.$gte?.toISOString(),
            timeQuery.createdAt?.$lte?.toISOString(),
            'createdTime'
        );

        const [totalMessages, newMessages, totalSessions] = await Promise.all([
            ChatMessageModel.countDocuments({}),
            ChatMessageModel.countDocuments(chatTimeQuery),
            ChatMessageModel.distinct('chatId').then(sessions => sessions.length)
        ]);

        return {
            totalMessages,
            newMessages,
            totalSessions,
            avgMessagesPerSession: totalSessions > 0 ? Math.round(totalMessages / totalSessions) : 0
        };
    }

    /**
     * 获取财务总览统计
     */
    private async getFinancialOverviewStats(timeQuery: any) {
        const transactionTimeQuery = this.buildTimeQuery(
            timeQuery.createdAt?.$gte?.toISOString(),
            timeQuery.createdAt?.$lte?.toISOString(),
            'timestamp'
        );

        const [totalRevenue, newRevenue, totalTransactions] = await Promise.all([
            BalanceTransaction.aggregate([
                { $match: { type: 3 } },
                { $group: { _id: null, total: { $sum: '$amount' } } }
            ]).then(result => result[0]?.total || 0),

            BalanceTransaction.aggregate([
                { $match: { ...transactionTimeQuery, type: 3 } },
                { $group: { _id: null, total: { $sum: '$amount' } } }
            ]).then(result => result[0]?.total || 0),

            BalanceTransaction.countDocuments({})
        ]);

        return {
            totalRevenue,
            newRevenue,
            totalTransactions,
            avgTransactionAmount: totalTransactions > 0 ? Math.round(totalRevenue / totalTransactions) : 0
        };
    }

    /**
     * 获取系统总览统计
     */
    private async getSystemOverviewStats(timeQuery: any) {
        const logTimeQuery = this.buildTimeQuery(
            timeQuery.createdAt?.$gte?.toISOString(),
            timeQuery.createdAt?.$lte?.toISOString(),
            'requestTime'
        );

        const [totalRequests, newRequests, totalErrors] = await Promise.all([
            RequestLog.countDocuments({}),
            RequestLog.countDocuments(logTimeQuery),
            ErrorLog.countDocuments({})
        ]);

        return {
            totalRequests,
            newRequests,
            totalErrors,
            errorRate: totalRequests > 0 ? Math.round((totalErrors / totalRequests) * 100) : 0
        };
    }

    /**
     * 获取用户报表数据（用于导出）
     */
    private async getUserReportData(dateFrom?: string, dateTo?: string) {
        const timeQuery = this.buildTimeQuery(dateFrom, dateTo, 'createdAt');

        return await User.find(timeQuery)
            .select('email fullName gender language balance hasPurchase createdAt')
            .lean()
            .then(users => users.map((user: any) => ({
                email: user.email,
                fullName: user.fullName || '',
                gender: user.gender || '',
                language: user.language || '',
                balance: user.balance || 0,
                isVip: user.hasPurchase || false,
                registrationDate: new Date(user.createdAt).toISOString().split('T')[0]
            })));
    }

    /**
     * 获取聊天报表数据（用于导出）
     */
    private async getChatReportData(dateFrom?: string, dateTo?: string) {
        const timeQuery = this.buildTimeQuery(dateFrom, dateTo, 'createdTime');

        return await ChatMessageModel.find(timeQuery)
            .select('chatId role content isComplete createdTime')
            .lean()
            .then(messages => messages.map(message => ({
                chatId: message.chatId,
                role: message.role,
                contentLength: message.content?.length || 0,
                isComplete: message.isComplete,
                createdTime: new Date(message.createdTime).toISOString()
            })));
    }

    /**
     * 获取财务报表数据（用于导出）
     */
    private async getFinancialReportData(dateFrom?: string, dateTo?: string) {
        const timeQuery = this.buildTimeQuery(dateFrom, dateTo, 'timestamp');

        return await BalanceTransaction.find(timeQuery)
            .populate('modelId', 'modelName')
            .select('userId amount reason timestamp type')
            .lean()
            .then(transactions => transactions.map(transaction => ({
                userId: transaction.userId,
                amount: transaction.amount,
                reason: transaction.reason,
                type: this.getTransactionTypeLabel(transaction.type),
                modelName: (transaction.modelId as any)?.modelName || '',
                timestamp: new Date(transaction.timestamp).toISOString()
            })));
    }

    /**
     * 获取系统报表数据（用于导出）
     */
    private async getSystemReportData(dateFrom?: string, dateTo?: string) {
        const timeQuery = this.buildTimeQuery(dateFrom, dateTo, 'requestTime');

        return await RequestLog.find(timeQuery)
            .select('url method requestTime')
            .lean()
            .then(logs => logs.map(log => ({
                url: log.url,
                method: log.method,
                requestTime: new Date(log.requestTime).toISOString()
            })));
    }

    /**
     * 获取交易类型标签
     */
    private getTransactionTypeLabel(type: number): string {
        switch (type) {
            case 1: return '充值';
            case 2: return '入账';
            case 3: return '消费';
            default: return '未知';
        }
    }

    /**
     * 生成CSV格式数据
     */
    private generateCSV(data: any[]): string {
        if (data.length === 0) return '';

        const fields = Object.keys(data[0]);

        // 生成表头
        const headers = fields.map(field => `"${field}"`).join(',');

        // 生成数据行
        const rows = data.map(row => {
            return fields.map(field => {
                const value = row[field];
                if (value === null || value === undefined) return '""';
                if (typeof value === 'string') {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return `"${value}"`;
            }).join(',');
        });

        return [headers, ...rows].join('\n');
    }
}

export default new ReportController();
