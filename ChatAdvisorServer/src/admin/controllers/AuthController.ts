import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { validationResult } from 'express-validator';
import * as OTPAuth from 'otpauth';
import User from '../../models/User';
import logger from '../../utils/logger';
import { secretKey } from '../../config/env';

// CORS辅助函数 - 确保401响应包含正确的CORS头
function setCorsHeaders(req: Request, res: Response): void {
    const origin = req.headers.origin;
    const referer = req.headers.referer;
    const allowedOrigins = [
        'http://localhost:3000', 
        'http://localhost:54001',
        'https://advisor.sanva.tk', 
        'https://advisor.sanva.top', 
        'https://admin.sanva.top',
        'https://admin.sanva.tk'
    ];
    
    // 如果有origin头，使用origin
    if (origin && allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin);
        res.header('Access-Control-Allow-Credentials', 'true');
    }
    // 如果没有origin但有referer，从referer推断origin
    else if (!origin && referer) {
        try {
            const refererUrl = new URL(referer);
            const inferredOrigin = `${refererUrl.protocol}//${refererUrl.host}`;
            if (allowedOrigins.includes(inferredOrigin)) {
                res.header('Access-Control-Allow-Origin', inferredOrigin);
                res.header('Access-Control-Allow-Credentials', 'true');
            }
        } catch (e) {
            // 如果referer解析失败，尝试localhost默认
            if (referer.includes('localhost:54001')) {
                res.header('Access-Control-Allow-Origin', 'http://localhost:54001');
                res.header('Access-Control-Allow-Credentials', 'true');
            }
        }
    }
    // 如果都没有，但请求来自localhost，默认允许
    else if (!origin && req.get('host')?.includes('localhost')) {
        res.header('Access-Control-Allow-Origin', 'http://localhost:54001');
        res.header('Access-Control-Allow-Credentials', 'true');
    }
}

/**
 * 管理员认证控制器
 */
export class AuthController {
    /**
     * 管理员登录
     */
    public async login(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            // 验证请求参数
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { email, password, twoFactorToken } = req.body;

            logger.info(`Admin login attempt for email: ${email}`);

            // 查找管理员用户（包含2FA信息和密码）
            const user = await User.findOne({
                email: email.toLowerCase(),
                role: { $in: ['admin', 'super_admin'] },
                isDelete: { $ne: true }
            }).select('+password +twoFactorSecret +twoFactorBackupCodes');

            logger.info(`User query result: ${user ? 'found' : 'not found'}`);
            if (user) {
                logger.info(`User details: id=${user._id}, email=${user.email}, role=${user.role}, status=${user.status}, hasPassword=${!!user.password}, twoFactorEnabled=${user.twoFactorEnabled}`);
            }

            if (!user) {
                logger.warn(`Admin login failed: User not found or not admin - ${email}`);
                setCorsHeaders(req, res);
                res.status(401).json({
                    success: false,
                    message: 'Invalid credentials'
                });
                return;
            }

            // 验证密码
            logger.info(`Attempting password verification for user: ${email}`);
            const isPasswordValid = await bcrypt.compare(password, user.password);
            logger.info(`Password verification result: ${isPasswordValid}`);

            if (!isPasswordValid) {
                logger.warn(`Admin login failed: Invalid password - ${email}`);
                setCorsHeaders(req, res);
                res.status(401).json({
                    success: false,
                    message: 'Invalid credentials'
                });
                return;
            }

            // 检查用户状态
            if (user.status !== 'active') {
                logger.warn(`Admin login failed: Account not active - ${email}`);
                setCorsHeaders(req, res);
                res.status(401).json({
                    success: false,
                    message: 'Account is not active'
                });
                return;
            }

            // 检查是否启用了 2FA
            if (user.twoFactorEnabled && user.twoFactorSecret) {
                if (!twoFactorToken) {
                    // 需要 2FA 验证，返回特殊状态
                    res.status(200).json({
                        success: false,
                        requiresTwoFactor: true,
                        message: 'Two-factor authentication required'
                    });
                    return;
                }

                // 验证 2FA token
                let isValidToken = false;

                // 首先尝试验证 TOTP token
                try {
                    const totpValid = OTPAuth.TOTP.validate({
                        token: twoFactorToken,
                        secret: OTPAuth.Secret.fromBase32(user.twoFactorSecret),
                        window: 1 // 允许前后1个时间窗口的误差
                    });
                    isValidToken = totpValid !== null;
                } catch (error) {
                    logger.warn(`2FA TOTP validation error for ${email}:`, error);
                }

                // 如果 TOTP 验证失败，尝试备用恢复码
                if (!isValidToken && user.twoFactorBackupCodes && user.twoFactorBackupCodes.length > 0) {
                    const backupCodeIndex = user.twoFactorBackupCodes.indexOf(twoFactorToken.toUpperCase());
                    if (backupCodeIndex !== -1) {
                        isValidToken = true;
                        // 使用后删除备用码
                        user.twoFactorBackupCodes.splice(backupCodeIndex, 1);
                        await user.save();
                        logger.info(`Backup code used for admin login: ${email}`);
                    }
                }

                if (!isValidToken) {
                    logger.warn(`Admin login failed: Invalid 2FA token - ${email}`);
                    setCorsHeaders(req, res);
                    res.status(401).json({
                        success: false,
                        message: 'Invalid two-factor authentication code'
                    });
                    return;
                }

                logger.info(`2FA verification successful for admin: ${email}`);
            }

            // 生成JWT token
            const token = jwt.sign(
                {
                    userId: user._id,
                    email: user.email,
                    role: user.role,
                    type: 'admin'
                },
                secretKey,
                { expiresIn: '24h' }
            );

            // 更新最后登录时间
            await User.findByIdAndUpdate(user._id, {
                lastLoginAt: new Date(),
                lastLoginIP: req.ip
            });

            logger.info(`Admin login successful: ${email}`);

            res.json({
                success: true,
                data: {
                    token,
                    user: {
                        id: user._id,
                        email: user.email,
                        fullName: user.fullName,
                        role: user.role,
                        avatar: user.avatar
                    }
                },
                message: 'Login successful'
            });

        } catch (error) {
            logger.error('Admin login error:', error);
            next(error);
        }
    }

    /**
     * 管理员登出
     */
    public async logout(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            // 这里可以实现token黑名单机制
            // 目前简单返回成功
            
            logger.info(`Admin logout: ${(req as any).user?.email}`);

            res.json({
                success: true,
                message: 'Logout successful'
            });

        } catch (error) {
            logger.error('Admin logout error:', error);
            next(error);
        }
    }

    /**
     * 获取当前管理员信息
     */
    public async getCurrentUser(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            // 修复：使用req.admin而不是req.user，与adminAuth中间件保持一致
            const admin = (req as any).admin;

            if (!admin) {
                setCorsHeaders(req, res);
                res.status(401).json({
                    success: false,
                    message: 'Unauthorized'
                });
                return;
            }

            // 如果admin.id是数据库用户ID，从数据库获取完整信息
            if (admin.id && admin.id.toString().length === 24) { // MongoDB ObjectId长度
                const user = await User.findById(admin.id)
                    .select('-password')
                    .lean();

                if (!user) {
                    res.status(404).json({
                        success: false,
                        message: 'User not found'
                    });
                    return;
                }

                res.json({
                    success: true,
                    data: {
                        id: user._id,
                        email: user.email,
                        fullName: user.fullName,
                        role: user.role,
                        avatar: user.avatar,
                        lastLoginAt: user.lastLoginAt
                    }
                });
            } else {
                // 硬编码管理员用户，直接返回admin信息
                res.json({
                    success: true,
                    data: {
                        id: admin.id,
                        email: admin.username, // 硬编码用户使用username作为email
                        fullName: admin.username,
                        role: admin.role,
                        avatar: null,
                        lastLoginAt: new Date()
                    }
                });
            }

        } catch (error) {
            logger.error('Get current admin user error:', error);
            next(error);
        }
    }

    /**
     * 刷新token
     */
    public async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const userId = (req as any).user?.userId;
            const userEmail = (req as any).user?.email;
            const userRole = (req as any).user?.role;

            if (!userId) {
                setCorsHeaders(req, res);
                res.status(401).json({
                    success: false,
                    message: 'Unauthorized'
                });
                return;
            }

            // 生成新的token
            const newToken = jwt.sign(
                {
                    userId,
                    email: userEmail,
                    role: userRole,
                    type: 'admin'
                },
                secretKey,
                { expiresIn: '24h' }
            );

            res.json({
                success: true,
                data: {
                    token: newToken
                },
                message: 'Token refreshed successfully'
            });

        } catch (error) {
            logger.error('Refresh token error:', error);
            next(error);
        }
    }

    /**
     * 修改密码
     */
    public async changePassword(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const userId = (req as any).user?.userId;
            const { currentPassword, newPassword } = req.body;

            const user = await User.findById(userId);
            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            // 验证当前密码
            const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
            if (!isCurrentPasswordValid) {
                res.status(400).json({
                    success: false,
                    message: 'Current password is incorrect'
                });
                return;
            }

            // 加密新密码
            const hashedNewPassword = await bcrypt.hash(newPassword, 12);

            // 更新密码
            await User.findByIdAndUpdate(userId, {
                password: hashedNewPassword,
                updatedAt: new Date()
            });

            logger.info(`Admin password changed: ${user.email}`);

            res.json({
                success: true,
                message: 'Password changed successfully'
            });

        } catch (error) {
            logger.error('Change password error:', error);
            next(error);
        }
    }
}

export default new AuthController();
