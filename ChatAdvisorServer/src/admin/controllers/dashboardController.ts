/**
 * 仪表板控制器
 * 提供管理后台仪表板数据API
 */

import { Request, Response } from 'express';
import User from '../../models/User';
import { ChatMessageModel } from '../../models/ChatMessage';
import BalanceTransaction, { TransactionType } from '../../models/BalanceTransaction';
import ErrorLog from '../../models/ErrorLog';
import { logger } from '../../utils/logger';

/**
 * 获取仪表板概览数据
 */
export const getOverview = async (req: Request, res: Response) => {
    try {
        const { timeRange = '7d' } = req.query;
        
        // 计算时间范围
        const now = new Date();
        let startDate: Date;
        
        switch (timeRange) {
            case '24h':
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '30d':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case '7d':
            default:
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
        }

        // 获取用户统计
        const totalUsers = await User.countDocuments({ isDelete: false });
        const activeUsers = await User.countDocuments({ 
            isDelete: false, 
            lastLoginAt: { $gte: startDate } 
        });
        const newUsersToday = await User.countDocuments({
            isDelete: false,
            createdAt: { $gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()) }
        });

        // 计算用户增长率（与上一个周期对比）
        const previousStartDate = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()));
        const previousUsers = await User.countDocuments({
            isDelete: false,
            createdAt: { $gte: previousStartDate, $lt: startDate }
        });
        const currentUsers = await User.countDocuments({
            isDelete: false,
            createdAt: { $gte: startDate }
        });
        const userGrowth = previousUsers > 0 ? ((currentUsers - previousUsers) / previousUsers) * 100 : 0;

        // 获取聊天统计
        const totalMessages = await ChatMessageModel.countDocuments({
            createdTime: { $gte: startDate }
        });
        const totalSessions = await ChatMessageModel.distinct('chatId', {
            createdTime: { $gte: startDate }
        }).then(sessions => sessions.length);
        const todayMessages = await ChatMessageModel.countDocuments({
            createdTime: { $gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()) }
        });

        // 计算聊天增长率
        const previousMessages = await ChatMessageModel.countDocuments({
            createdTime: { $gte: previousStartDate, $lt: startDate }
        });
        const chatGrowth = previousMessages > 0 ? ((totalMessages - previousMessages) / previousMessages) * 100 : 0;

        // 获取财务统计
        const revenueTransactions = await BalanceTransaction.aggregate([
            {
                $match: {
                    type: TransactionType.Deposit,
                    timestamp: { $gte: startDate }
                }
            },
            {
                $group: {
                    _id: null,
                    totalRevenue: { $sum: '$amount' },
                    totalTransactions: { $sum: 1 }
                }
            }
        ]);

        const todayRevenue = await BalanceTransaction.aggregate([
            {
                $match: {
                    type: TransactionType.Deposit,
                    timestamp: { $gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()) }
                }
            },
            {
                $group: {
                    _id: null,
                    todayRevenue: { $sum: '$amount' }
                }
            }
        ]);

        // 计算财务增长率
        const previousRevenue = await BalanceTransaction.aggregate([
            {
                $match: {
                    type: TransactionType.Deposit,
                    timestamp: { $gte: previousStartDate, $lt: startDate }
                }
            },
            {
                $group: {
                    _id: null,
                    revenue: { $sum: '$amount' }
                }
            }
        ]);

        const currentRevenue = revenueTransactions[0]?.totalRevenue || 0;
        const prevRevenue = previousRevenue[0]?.revenue || 0;
        const financialGrowth = prevRevenue > 0 ? ((currentRevenue - prevRevenue) / prevRevenue) * 100 : 0;

        // 获取系统统计
        const errorCount = await ErrorLog.countDocuments({
            timestamp: { $gte: startDate }
        });
        const totalRequests = Math.max(totalMessages * 2, 1000); // 估算请求数
        const errorRate = (errorCount / totalRequests) * 100;

        // 模拟平均响应时间（实际项目中应该从性能监控系统获取）
        const avgResponseTime = 150 + Math.random() * 100;

        const overview = {
            users: {
                total: totalUsers,
                active: activeUsers,
                newToday: newUsersToday,
                growth: userGrowth
            },
            chat: {
                totalMessages,
                totalSessions,
                todayMessages,
                growth: chatGrowth
            },
            financial: {
                totalRevenue: currentRevenue,
                totalTransactions: revenueTransactions[0]?.totalTransactions || 0,
                todayRevenue: todayRevenue[0]?.todayRevenue || 0,
                growth: financialGrowth
            },
            system: {
                errorRate,
                avgResponseTime,
                uptime: process.uptime() / 3600 // 运行时间（小时）
            }
        };

        res.json({
            success: true,
            data: overview,
            message: '获取概览数据成功'
        });

    } catch (error) {
        logger.error('获取仪表板概览数据失败:', error);
        res.status(500).json({
            success: false,
            message: '获取概览数据失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};

/**
 * 获取趋势数据
 */
export const getTrends = async (req: Request, res: Response) => {
    try {
        const { timeRange = '7d' } = req.query;

        // 计算时间范围和间隔
        const now = new Date();
        let startDate: Date;
        let interval: number; // 毫秒
        let points: number;

        switch (timeRange) {
            case '24h':
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                interval = 60 * 60 * 1000; // 1小时间隔
                points = 24;
                break;
            case '30d':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                interval = 24 * 60 * 60 * 1000; // 1天间隔
                points = 30;
                break;
            case '7d':
            default:
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                interval = 24 * 60 * 60 * 1000; // 1天间隔
                points = 7;
                break;
        }

        const trends = [];

        for (let i = 0; i < points; i++) {
            const pointStart = new Date(startDate.getTime() + i * interval);
            const pointEnd = new Date(pointStart.getTime() + interval);

            // 获取该时间点的用户数据
            const users = await User.countDocuments({
                isDelete: false,
                createdAt: { $gte: pointStart, $lt: pointEnd }
            });

            // 获取该时间点的消息数据
            const messages = await ChatMessageModel.countDocuments({
                createdTime: { $gte: pointStart, $lt: pointEnd }
            });

            // 获取该时间点的收入数据
            const revenue = await BalanceTransaction.aggregate([
                {
                    $match: {
                        type: TransactionType.Deposit,
                        timestamp: { $gte: pointStart, $lt: pointEnd }
                    }
                },
                {
                    $group: {
                        _id: null,
                        total: { $sum: '$amount' }
                    }
                }
            ]);

            trends.push({
                timestamp: pointStart.toISOString(),
                users,
                messages,
                revenue: revenue[0]?.total || 0,
                date: pointStart.toISOString().split('T')[0]
            });
        }

        res.json({
            success: true,
            data: trends,
            message: '获取趋势数据成功'
        });

    } catch (error) {
        logger.error('获取趋势数据失败:', error);
        res.status(500).json({
            success: false,
            message: '获取趋势数据失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};

/**
 * 获取实时数据
 */
export const getRealTimeData = async (req: Request, res: Response) => {
    try {
        const now = new Date();
        const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
        const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

        // 获取在线用户数（最近5分钟有活动的用户）
        const onlineUsers = await User.countDocuments({
            isDelete: false,
            lastLoginAt: { $gte: fiveMinutesAgo }
        });

        // 获取活跃聊天数（最近5分钟有消息的聊天）
        const activeChats = await ChatMessageModel.distinct('chatId', {
            createdTime: { $gte: fiveMinutesAgo }
        }).then(chats => chats.length);

        // 计算每秒请求数（最近1分钟的消息数 / 60）
        const recentMessages = await ChatMessageModel.countDocuments({
            createdTime: { $gte: oneMinuteAgo }
        });
        const requestsPerSecond = recentMessages / 60;

        // 计算每秒错误数（最近1分钟的错误数 / 60）
        const recentErrors = await ErrorLog.countDocuments({
            timestamp: { $gte: oneMinuteAgo }
        });
        const errorsPerSecond = recentErrors / 60;

        // 模拟平均响应时间（实际项目中应该从性能监控系统获取）
        const avgResponseTime = 120 + Math.random() * 80;

        const realTimeData = {
            timestamp: now.toISOString(),
            onlineUsers,
            activeChats,
            requestsPerSecond,
            errorsPerSecond,
            avgResponseTime,
            serverUptime: process.uptime()
        };

        res.json({
            success: true,
            data: realTimeData,
            message: '获取实时数据成功'
        });

    } catch (error) {
        logger.error('获取实时数据失败:', error);
        res.status(500).json({
            success: false,
            message: '获取实时数据失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};

/**
 * 获取告警数据
 */
export const getAlerts = async (req: Request, res: Response) => {
    try {
        const { resolved = false, limit = 5 } = req.query;

        // 获取最近的错误日志作为告警数据
        const recentErrors = await ErrorLog.find({})
            .sort({ timestamp: -1 })
            .limit(parseInt(limit as string))
            .populate('requestId');

        // 模拟告警数据（实际项目中应该有专门的告警系统）
        const alerts = recentErrors.map((error, index) => {
            const errorCode = Number(error.errorCode);
            return {
                id: error._id.toString(),
                type: errorCode >= 500 ? 'critical' : errorCode >= 400 ? 'error' : 'warning',
                title: `系统错误 ${errorCode}`,
                message: error.error.substring(0, 100) + (error.error.length > 100 ? '...' : ''),
                timestamp: error.timestamp.toISOString(),
                source: 'system',
                resolved: false,
                severity: errorCode >= 500 ? 3 : errorCode >= 400 ? 2 : 1
            };
        });

        // 如果没有真实错误，添加一些示例告警（仅用于演示）
        if (alerts.length === 0) {
            const now = new Date();
            const sampleAlerts = [
                {
                    id: 'sample-1',
                    type: 'info',
                    title: '系统运行正常',
                    message: '所有服务运行正常，无异常告警',
                    timestamp: now.toISOString(),
                    source: 'monitor',
                    resolved: false,
                    severity: 1
                }
            ];

            res.json({
                success: true,
                data: sampleAlerts,
                message: '获取告警数据成功'
            });
            return;
        }

        res.json({
            success: true,
            data: alerts,
            message: '获取告警数据成功'
        });

    } catch (error) {
        logger.error('获取告警数据失败:', error);
        res.status(500).json({
            success: false,
            message: '获取告警数据失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
};
