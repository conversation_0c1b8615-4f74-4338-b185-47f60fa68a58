import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import * as OTPAuth from 'otpauth';
import * as QRCode from 'qrcode';
import crypto from 'crypto';
import User from '../../models/User';
import { logger } from '../../business/logger';

/**
 * 两步验证控制器
 */
export class TwoFactorController {
    /**
     * 生成 2FA 设置信息（密钥和二维码）
     */
    public async setup(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const userId = (req as any).admin?.id;
            const userEmail = (req as any).admin?.email;

            if (!userId) {
                res.status(401).json({
                    success: false,
                    message: 'Unauthorized'
                });
                return;
            }

            // 查找用户
            const user = await User.findById(userId);
            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            // 检查是否已启用 2FA
            if (user.twoFactorEnabled) {
                res.status(400).json({
                    success: false,
                    message: 'Two-factor authentication is already enabled'
                });
                return;
            }

            // 生成新的密钥
            const secret = new OTPAuth.Secret({ size: 20 });
            const secretBase32 = secret.base32;

            // 创建 TOTP 实例
            const totp = new OTPAuth.TOTP({
                issuer: 'ChatAdvisor Admin',
                label: userEmail,
                algorithm: 'SHA1',
                digits: 6,
                period: 30,
                secret: secretBase32
            });

            // 生成二维码 URI
            const uri = totp.toString();

            // 生成二维码图片
            const qrCodeDataURL = await QRCode.toDataURL(uri);

            // 临时保存密钥（未启用状态）
            await User.findByIdAndUpdate(userId, {
                twoFactorSecret: secretBase32
            });

            logger.info(`2FA setup initiated for admin: ${userEmail}`);

            res.json({
                success: true,
                data: {
                    secret: secretBase32,
                    qrCode: qrCodeDataURL,
                    manualEntryKey: secretBase32
                },
                message: '2FA setup information generated'
            });

        } catch (error) {
            logger.error('2FA setup error:', error);
            next(error);
        }
    }

    /**
     * 验证并启用 2FA
     */
    public async verify(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            // 验证请求参数
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const userId = (req as any).admin?.id;
            const userEmail = (req as any).admin?.email;
            const { token } = req.body;

            if (!userId) {
                res.status(401).json({
                    success: false,
                    message: 'Unauthorized'
                });
                return;
            }

            // 查找用户（包含密钥）
            const user = await User.findById(userId).select('+twoFactorSecret');
            if (!user || !user.twoFactorSecret) {
                res.status(400).json({
                    success: false,
                    message: 'No 2FA setup found. Please setup 2FA first.'
                });
                return;
            }

            // 验证 TOTP token
            const isValid = OTPAuth.TOTP.validate({
                token,
                secret: OTPAuth.Secret.fromBase32(user.twoFactorSecret),
                window: 1 // 允许前后1个时间窗口的误差
            });

            if (isValid === null) {
                res.status(400).json({
                    success: false,
                    message: 'Invalid verification code'
                });
                return;
            }

            // 生成备用恢复码
            const backupCodes = this.generateBackupCodes();

            // 启用 2FA
            await User.findByIdAndUpdate(userId, {
                twoFactorEnabled: true,
                twoFactorBackupCodes: backupCodes
            });

            logger.info(`2FA enabled for admin: ${userEmail}`);

            res.json({
                success: true,
                data: {
                    backupCodes
                },
                message: '2FA enabled successfully'
            });

        } catch (error) {
            logger.error('2FA verification error:', error);
            next(error);
        }
    }

    /**
     * 禁用 2FA
     */
    public async disable(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            // 验证请求参数
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const userId = (req as any).admin?.id;
            const userEmail = (req as any).admin?.email;
            const { password, token } = req.body;

            if (!userId) {
                res.status(401).json({
                    success: false,
                    message: 'Unauthorized'
                });
                return;
            }

            // 查找用户（包含密码和2FA信息）
            const user = await User.findById(userId).select('+password +twoFactorSecret +twoFactorBackupCodes');
            if (!user) {
                res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
                return;
            }

            // 验证密码
            const bcrypt = require('bcrypt');
            const isPasswordValid = await bcrypt.compare(password, user.password);
            if (!isPasswordValid) {
                res.status(400).json({
                    success: false,
                    message: 'Invalid password'
                });
                return;
            }

            // 如果提供了 token，验证 2FA token
            if (token && user.twoFactorSecret) {
                const isTokenValid = OTPAuth.TOTP.validate({
                    token,
                    secret: OTPAuth.Secret.fromBase32(user.twoFactorSecret),
                    window: 1
                });

                if (isTokenValid === null) {
                    res.status(400).json({
                        success: false,
                        message: 'Invalid verification code'
                    });
                    return;
                }
            }

            // 禁用 2FA
            await User.findByIdAndUpdate(userId, {
                twoFactorEnabled: false,
                twoFactorSecret: undefined,
                twoFactorBackupCodes: undefined
            });

            logger.info(`2FA disabled for admin: ${userEmail}`);

            res.json({
                success: true,
                message: '2FA disabled successfully'
            });

        } catch (error) {
            logger.error('2FA disable error:', error);
            next(error);
        }
    }

    /**
     * 获取新的备用恢复码
     */
    public async getBackupCodes(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const userId = (req as any).admin?.id;
            const userEmail = (req as any).user?.email;

            if (!userId) {
                res.status(401).json({
                    success: false,
                    message: 'Unauthorized'
                });
                return;
            }

            // 查找用户
            const user = await User.findById(userId);
            if (!user || !user.twoFactorEnabled) {
                res.status(400).json({
                    success: false,
                    message: '2FA is not enabled'
                });
                return;
            }

            // 生成新的备用码
            const backupCodes = this.generateBackupCodes();

            // 更新备用码
            await User.findByIdAndUpdate(userId, {
                twoFactorBackupCodes: backupCodes
            });

            logger.info(`New backup codes generated for admin: ${userEmail}`);

            res.json({
                success: true,
                data: {
                    backupCodes
                },
                message: 'New backup codes generated'
            });

        } catch (error) {
            logger.error('Generate backup codes error:', error);
            next(error);
        }
    }

    /**
     * 生成备用恢复码
     */
    private generateBackupCodes(): string[] {
        const codes: string[] = [];
        for (let i = 0; i < 10; i++) {
            // 生成8位随机码
            const code = crypto.randomBytes(4).toString('hex').toUpperCase();
            codes.push(code);
        }
        return codes;
    }
}
