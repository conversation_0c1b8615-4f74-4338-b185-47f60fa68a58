/**
 * 仪表板路由
 * 提供管理后台仪表板相关的API路由
 */

import { Router } from 'express';
import { getOverview, getTrends, getRealTimeData, getAlerts } from '../controllers/dashboardController';
import { adminAuth } from '../middleware/adminAuth';

const router = Router();

// 应用管理员认证中间件
router.use(adminAuth);

// 获取仪表板概览数据
router.get('/overview', getOverview);

// 获取趋势数据
router.get('/trends', getTrends);

// 获取实时数据
router.get('/realtime', getRealTimeData);

// 获取告警数据
router.get('/alerts', getAlerts);

export default router;
