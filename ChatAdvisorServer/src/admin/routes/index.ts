/**
 * 后台管理路由入口
 */

import { Router } from 'express';
import authRoutes from './authRoutes';
import userRoutes from './userRoutes';
import systemRoutes from './systemRoutes';
import chatRoutes from './chatRoutes';
import financialRoutes from './financialRoutes';
import logRoutes from './logRoutes';
import reportRoutes from './reportRoutes';
import dashboardRoutes from './dashboardRoutes';
import aiConfigRoutes from './aiConfigRoutes';
import modelPricingRoutes from './modelPricingRoutes';

const router = Router();

// 认证相关路由
router.use('/auth', authRoutes);

// 仪表板路由
router.use('/dashboard', dashboardRoutes);

// 用户管理路由
router.use('/users', userRoutes);

// 聊天管理路由
router.use('/chat', chatRoutes);

// 财务管理路由
router.use('/financial', financialRoutes);

// 系统管理路由
router.use('/system', systemRoutes);

// 日志管理路由
router.use('/logs', logRoutes);

// 统计报表路由
router.use('/reports', reportRoutes);

// AI配置管理路由
router.use('/ai', aiConfigRoutes);

// 模型价格管理路由（基于AI配置系统）
router.use('/model-pricing', modelPricingRoutes);

export default router;
