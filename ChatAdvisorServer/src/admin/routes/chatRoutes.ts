/**
 * 聊天管理路由
 */

import { Router } from 'express';
import { body, param, query } from 'express-validator';
import ChatMessageController from '../controllers/ChatMessageController';
import { adminAuth } from '../middleware/adminAuth';
import { validateRequest } from '../middleware/validateRequest';

const router = Router();

// 应用管理员认证中间件
router.use(adminAuth);

// 获取聊天消息列表
router.get('/messages',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('search').optional().isString().trim(),
        query('chatId').optional().isString().trim(),
        query('role').optional().isIn(['user', 'assistant', 'other']),
        query('isComplete').optional().isBoolean(),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('sortBy').optional().isIn(['createdTime', 'role', 'isComplete']),
        query('sortOrder').optional().isIn(['asc', 'desc'])
    ],
    validateRequest,
    ChatMessageController.getMessages
);

// 获取消息详情
router.get('/messages/:id',
    [
        param('id').isMongoId().withMessage('Invalid message ID')
    ],
    validateRequest,
    ChatMessageController.getMessageById
);

// 更新消息
router.put('/messages/:id',
    [
        param('id').isMongoId().withMessage('Invalid message ID'),
        body('content').optional().isString().trim(),
        body('isComplete').optional().isBoolean()
    ],
    validateRequest,
    ChatMessageController.updateMessage
);

// 删除消息
router.delete('/messages/:id',
    [
        param('id').isMongoId().withMessage('Invalid message ID')
    ],
    validateRequest,
    ChatMessageController.deleteMessage
);

// 批量删除消息
router.post('/messages/batch-delete',
    [
        body('messageIds').optional().isArray().withMessage('Message IDs must be an array'),
        body('messageIds.*').optional().isMongoId().withMessage('Invalid message ID'),
        body('chatId').optional().isString().trim(),
        body('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        body('dateTo').optional().isISO8601().withMessage('Invalid date format')
    ],
    validateRequest,
    ChatMessageController.batchDeleteMessages
);

// 获取聊天会话列表
router.get('/sessions',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format')
    ],
    validateRequest,
    ChatMessageController.getChatSessions
);

// 获取聊天统计信息
router.get('/stats',
    [
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('groupBy').optional().isIn(['day', 'week', 'month'])
    ],
    validateRequest,
    ChatMessageController.getChatStats
);

// 搜索消息
router.get('/search',
    [
        query('keyword').notEmpty().withMessage('Keyword is required'),
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('chatId').optional().isString().trim(),
        query('role').optional().isIn(['user', 'assistant', 'other']),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format')
    ],
    validateRequest,
    ChatMessageController.searchMessages
);

// 导出聊天数据
router.get('/export',
    [
        query('format').optional().isIn(['json', 'csv']),
        query('chatId').optional().isString().trim(),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('role').optional().isIn(['user', 'assistant', 'other'])
    ],
    validateRequest,
    ChatMessageController.exportChatData
);

export default router;
