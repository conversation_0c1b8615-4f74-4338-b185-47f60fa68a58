import express from 'express';
import { body, param, query } from 'express-validator';
import ModelPricingController from '../controllers/ModelPricingController';
import { validateRequest } from '../middleware/validateRequest';

const router = express.Router();

// 获取模型价格列表
router.get('/',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('search').optional().isString().withMessage('Search must be a string'),
        query('configId').optional().isMongoId().withMessage('Config ID must be a valid MongoDB ID'),
        query('sortBy').optional().isString().withMessage('Sort by must be a string'),
        query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc')
    ],
    validateRequest,
    ModelPricingController.getModelPricings.bind(ModelPricingController)
);

// 更新模型价格
router.put('/:id',
    [
        param('id').isMongoId().withMessage('Valid model ID is required'),
        body('pricing').optional().isObject().withMessage('Pricing must be an object'),
        body('pricing.inputPrice').optional().isFloat({ min: 0 }).withMessage('Input price must be a non-negative number'),
        body('pricing.outputPrice').optional().isFloat({ min: 0 }).withMessage('Output price must be a non-negative number'),
        body('pricing.currency').optional().isString().isLength({ max: 3 }).withMessage('Currency must be a valid 3-character code'),
        body('displayName').optional().isString().isLength({ min: 1, max: 200 }).withMessage('Display name must be between 1 and 200 characters'),
        body('description').optional().isString().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
        body('maxTokens').optional().isInt({ min: 1, max: 1000000 }).withMessage('Max tokens must be between 1 and 1,000,000')
    ],
    validateRequest,
    ModelPricingController.updateModelPricing.bind(ModelPricingController)
);

// 批量修复零价格模型
router.post('/fix-zero-pricing',
    validateRequest,
    ModelPricingController.fixZeroModelPricing.bind(ModelPricingController)
);

// 同步模型并设置默认价格
router.post('/sync-models',
    [
        body('configId').isMongoId().withMessage('Valid config ID is required')
    ],
    validateRequest,
    ModelPricingController.syncModelsWithPricing.bind(ModelPricingController)
);

// 获取当前启用模型的价格信息
router.get('/current',
    validateRequest,
    ModelPricingController.getCurrentModelPricing.bind(ModelPricingController)
);

// 批量禁用所有模型
router.post('/disable-all/:configId',
    [
        param('configId').isMongoId().withMessage('Valid config ID is required')
    ],
    validateRequest,
    ModelPricingController.disableAllModels.bind(ModelPricingController)
);

// 切换模型启用状态
router.post('/toggle-status/:modelId',
    [
        param('modelId').isMongoId().withMessage('Valid model ID is required')
    ],
    validateRequest,
    ModelPricingController.toggleModelStatus.bind(ModelPricingController)
);

export default router;
