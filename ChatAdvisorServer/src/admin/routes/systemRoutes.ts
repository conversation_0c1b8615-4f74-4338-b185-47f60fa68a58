/**
 * 系统管理路由
 */

import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { adminAuth } from '../middleware/adminAuth';
import { validateRequest } from '../middleware/validateRequest';
import SystemController from '../controllers/SystemController';
import ConfigController from '../controllers/ConfigController';
import QuestionController from '../controllers/QuestionController';

const router = Router();

// 应用管理员认证中间件
router.use(adminAuth);

// ==================== 系统状态 ====================

// 获取系统状态
router.get('/status', SystemController.getSystemStatus);

// 获取数据库状态
router.get('/database', SystemController.getDatabaseStatus);

// 获取系统监控数据
router.get('/monitor', SystemController.getSystemMonitorData);

// 获取迁移状态
router.get('/migrations', SystemController.getMigrationStatus);

// 运行迁移
router.post('/migrations/run', SystemController.runMigrations);

// ==================== 系统配置管理 ====================

// 获取系统配置
router.get('/config', ConfigController.getConfig);

// 更新系统配置
router.put('/config',
    [
        body('privacyPolicy').optional().isString().trim(),
        body('termsOfService').optional().isString().trim(),
        body('appVersion').optional().matches(/^\d+\.\d+\.\d+$/).withMessage('Invalid version format'),
        body('supportEmail').optional().isEmail().withMessage('Invalid email format'),
        body('featureFlags').optional().isObject(),
        body('compressRate').optional().isFloat({ min: 0, max: 1 }).withMessage('Compress rate must be between 0 and 1'),
        // 版本控制相关字段验证
        body('latestVersion').optional().matches(/^\d+\.\d+\.\d+$/).withMessage('Invalid latest version format'),
        body('minimumVersion').optional().matches(/^\d+\.\d+\.\d+$/).withMessage('Invalid minimum version format'),
        body('forceUpdate').optional().isBoolean().withMessage('Force update must be boolean'),
        body('updateMessage').optional().isObject().withMessage('Update message must be an object'),
        body('appStoreUrls').optional().isObject().withMessage('App store URLs must be an object'),
        body('appStoreUrls.ios').optional().isString().withMessage('iOS app store URL must be string'),
        body('appStoreUrls.android').optional().isString().withMessage('Android app store URL must be string'),
        body('updateType').optional().isIn(['force', 'optional']).withMessage('Update type must be force or optional'),
        body('versionCheckEnabled').optional().isBoolean().withMessage('Version check enabled must be boolean')
    ],
    validateRequest,
    ConfigController.updateConfig
);

// 获取特定配置项
router.get('/config/:key',
    [
        param('key').notEmpty().withMessage('Config key is required')
    ],
    validateRequest,
    ConfigController.getConfigItem
);

// 更新特定配置项
router.put('/config/:key',
    [
        param('key').notEmpty().withMessage('Config key is required'),
        body('value').exists().withMessage('Value is required')
    ],
    validateRequest,
    ConfigController.updateConfigItem
);

// 批量更新配置项
router.post('/config/batch-update',
    [
        body('updates').isObject().withMessage('Updates must be an object')
    ],
    validateRequest,
    ConfigController.batchUpdateConfig
);

// 导出配置
router.get('/config/export',
    [
        query('format').optional().isIn(['json', 'file']),
        query('includeSecrets').optional().isBoolean()
    ],
    validateRequest,
    ConfigController.exportConfig
);

// 导入配置
router.post('/config/import',
    [
        body('configData').isObject().withMessage('Config data must be an object'),
        body('mode').optional().isIn(['merge', 'replace']).withMessage('Mode must be merge or replace')
    ],
    validateRequest,
    ConfigController.importConfig
);

// 重置配置到默认值
router.post('/config/reset',
    [
        body('keys').optional().isArray().withMessage('Keys must be an array')
    ],
    validateRequest,
    ConfigController.resetConfig
);

// 验证配置
router.post('/config/validate',
    [
        body('configData').isObject().withMessage('Config data must be an object')
    ],
    validateRequest,
    ConfigController.validateConfig
);

// 版本控制专用路由
// 获取版本控制配置
router.get('/version-config', ConfigController.getVersionConfig);

// 更新版本控制配置
router.put('/version-config',
    [
        body('latestVersion').optional().matches(/^\d+\.\d+\.\d+$/).withMessage('Invalid latest version format'),
        body('minimumVersion').optional().matches(/^\d+\.\d+\.\d+$/).withMessage('Invalid minimum version format'),
        body('forceUpdate').optional().isBoolean().withMessage('Force update must be boolean'),
        body('updateMessage').optional().isObject().withMessage('Update message must be an object'),
        body('appStoreUrls').optional().isObject().withMessage('App store URLs must be an object'),
        body('appStoreUrls.ios').optional().isString().withMessage('iOS app store URL must be string'),
        body('appStoreUrls.android').optional().isString().withMessage('Android app store URL must be string'),
        body('updateType').optional().isIn(['force', 'optional']).withMessage('Update type must be force or optional'),
        body('versionCheckEnabled').optional().isBoolean().withMessage('Version check enabled must be boolean')
    ],
    validateRequest,
    ConfigController.updateVersionConfig
);

// 管理后台版本检查测试
router.get('/check-version',
    [
        query('platform').optional().isIn(['ios', 'android']).withMessage('Platform must be ios or android')
    ],
    validateRequest,
    async (req, res, next) => {
        // 导入版本检查业务逻辑
        const { checkVersion } = await import('../../business/versionCheck');
        return checkVersion(req, res, next);
    }
);

// 获取版本信息
router.get('/version-info',
    async (req, res, next) => {
        const { getVersionInfo } = await import('../../business/versionCheck');
        return getVersionInfo(req, res, next);
    }
);

// 获取配置历史
router.get('/config/history',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
    ],
    validateRequest,
    ConfigController.getConfigHistory
);

// 获取配置模板
router.get('/config/template', ConfigController.getConfigTemplate);

// ==================== 问题库管理 ====================

// 获取问题列表
router.get('/questions',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('search').optional().isString().trim(),
        query('language').optional().isIn(['zh_CN', 'en']),
        query('sortBy').optional().isIn(['createdAt']),
        query('sortOrder').optional().isIn(['asc', 'desc'])
    ],
    validateRequest,
    QuestionController.getQuestions
);

// 获取问题详情
router.get('/questions/:id',
    [
        param('id').isMongoId().withMessage('Invalid question ID')
    ],
    validateRequest,
    QuestionController.getQuestionById
);

// 创建问题
router.post('/questions',
    [
        body('sketch').isObject().withMessage('Sketch must be an object'),
        body('content').isObject().withMessage('Content must be an object'),
        body('question').isObject().withMessage('Question must be an object')
    ],
    validateRequest,
    QuestionController.createQuestion
);

// 更新问题
router.put('/questions/:id',
    [
        param('id').isMongoId().withMessage('Invalid question ID'),
        body('sketch').optional().isObject().withMessage('Sketch must be an object'),
        body('content').optional().isObject().withMessage('Content must be an object'),
        body('question').optional().isObject().withMessage('Question must be an object')
    ],
    validateRequest,
    QuestionController.updateQuestion
);

// 删除问题
router.delete('/questions/:id',
    [
        param('id').isMongoId().withMessage('Invalid question ID')
    ],
    validateRequest,
    QuestionController.deleteQuestion
);

// 批量删除问题
router.post('/questions/batch-delete',
    [
        body('questionIds').isArray().withMessage('Question IDs must be an array'),
        body('questionIds.*').isMongoId().withMessage('Invalid question ID')
    ],
    validateRequest,
    QuestionController.batchDeleteQuestions
);

// 获取问题统计信息
router.get('/questions/stats', QuestionController.getQuestionStats);

// 导出问题数据
router.get('/questions/export',
    [
        query('format').optional().isIn(['json', 'csv']),
        query('language').optional().isIn(['zh_CN', 'en']),
        query('includeAllLanguages').optional().isBoolean()
    ],
    validateRequest,
    QuestionController.exportQuestions
);

// 导入问题数据
router.post('/questions/import',
    [
        body('questions').isArray().withMessage('Questions must be an array'),
        body('mode').optional().isIn(['add', 'replace']).withMessage('Mode must be add or replace')
    ],
    validateRequest,
    QuestionController.importQuestions
);

// 搜索问题
router.get('/questions/search',
    [
        query('keyword').notEmpty().withMessage('Keyword is required'),
        query('language').optional().isIn(['zh_CN', 'en']),
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
    ],
    validateRequest,
    QuestionController.searchQuestions
);

// 复制问题
router.post('/questions/:id/duplicate',
    [
        param('id').isMongoId().withMessage('Invalid question ID')
    ],
    validateRequest,
    QuestionController.duplicateQuestion
);

export default router;
