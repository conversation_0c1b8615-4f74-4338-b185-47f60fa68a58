import { Router } from 'express';
import { body } from 'express-validator';
import rateLimit from 'express-rate-limit';
import { TwoFactorController } from '../controllers/TwoFactorController';
import { adminAuth } from '../middleware/adminAuth';

const router = Router();
const twoFactorController = new TwoFactorController();

// 2FA 验证速率限制 - 更严格的限制防止暴力破解
const twoFactorRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 5, // 每15分钟最多5次尝试
    message: {
        success: false,
        message: 'Too many 2FA attempts, please try again later'
    },
    standardHeaders: true,
    legacyHeaders: false
});

// 设置相关操作的速率限制
const setupRateLimit = rateLimit({
    windowMs: 5 * 60 * 1000, // 5分钟
    max: 50, // 每5分钟最多50次设置尝试（开发环境）
    message: {
        success: false,
        message: 'Too many setup attempts, please try again later'
    }
});

// 验证规则
const verifyTokenValidation = [
    body('token')
        .isLength({ min: 6, max: 6 })
        .isNumeric()
        .withMessage('Token must be a 6-digit number')
];

const disableValidation = [
    body('password')
        .notEmpty()
        .withMessage('Password is required'),
    body('token')
        .optional()
        .isLength({ min: 6, max: 6 })
        .isNumeric()
        .withMessage('Token must be a 6-digit number')
];

/**
 * @route GET /admin/auth/2fa/setup
 * @desc 生成 2FA 设置信息（密钥和二维码）
 * @access Private (Admin)
 */
router.get('/setup', adminAuth, setupRateLimit, twoFactorController.setup.bind(twoFactorController));

/**
 * @route POST /admin/auth/2fa/verify
 * @desc 验证并启用 2FA
 * @access Private (Admin)
 */
router.post('/verify', adminAuth, twoFactorRateLimit, verifyTokenValidation, twoFactorController.verify.bind(twoFactorController));

/**
 * @route POST /admin/auth/2fa/disable
 * @desc 禁用 2FA
 * @access Private (Admin)
 */
router.post('/disable', adminAuth, twoFactorRateLimit, disableValidation, twoFactorController.disable.bind(twoFactorController));

/**
 * @route GET /admin/auth/2fa/backup-codes
 * @desc 获取新的备用恢复码
 * @access Private (Admin)
 */
router.get('/backup-codes', adminAuth, twoFactorController.getBackupCodes.bind(twoFactorController));

export default router;
