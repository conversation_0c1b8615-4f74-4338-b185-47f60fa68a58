/**
 * 日志管理路由
 */

import { Router } from 'express';
import { body, param, query } from 'express-validator';
import LogController from '../controllers/LogController';
import { adminAuth } from '../middleware/adminAuth';
import { validateRequest } from '../middleware/validateRequest';

const router = Router();

// 应用管理员认证中间件
router.use(adminAuth);

// ==================== 请求日志 ====================

// 获取请求日志列表
router.get('/requests',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('url').optional().isString().trim(),
        query('method').optional().isIn(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('sortBy').optional().isIn(['requestTime', 'url', 'method']),
        query('sortOrder').optional().isIn(['asc', 'desc'])
    ],
    validateRequest,
    LogController.getRequestLogs
);

// ==================== 响应日志 ====================

// 获取响应日志列表
router.get('/responses',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('status').optional().isInt({ min: 100, max: 599 }).withMessage('Status must be a valid HTTP status code'),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('sortBy').optional().isIn(['responseTime', 'responseStatus']),
        query('sortOrder').optional().isIn(['asc', 'desc'])
    ],
    validateRequest,
    LogController.getResponseLogs
);

// ==================== 错误日志 ====================

// 获取错误日志列表
router.get('/errors',
    [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('errorCode').optional().isInt().withMessage('Error code must be an integer'),
        query('error').optional().isString().trim(),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('sortBy').optional().isIn(['timestamp', 'errorCode', 'error']),
        query('sortOrder').optional().isIn(['asc', 'desc'])
    ],
    validateRequest,
    LogController.getErrorLogs
);

// ==================== 日志详情 ====================

// 获取日志详情（包含关联的请求、响应、错误信息）
router.get('/details/:requestId',
    [
        param('requestId').isMongoId().withMessage('Invalid request ID')
    ],
    validateRequest,
    LogController.getLogDetails
);

// ==================== 日志统计 ====================

// 获取日志统计信息
router.get('/stats',
    [
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('groupBy').optional().isIn(['day', 'week', 'month'])
    ],
    validateRequest,
    LogController.getLogStats
);

// ==================== 日志清理 ====================

// 清理日志数据
router.post('/cleanup',
    [
        body('type').isIn(['request', 'response', 'error', 'all']).withMessage('Type must be request, response, error, or all'),
        body('daysToKeep').optional().isInt({ min: 1, max: 365 }).withMessage('Days to keep must be between 1 and 365'),
        body('batchSize').optional().isInt({ min: 100, max: 10000 }).withMessage('Batch size must be between 100 and 10000')
    ],
    validateRequest,
    LogController.cleanupLogs
);

// ==================== 日志导出 ====================

// 导出日志数据
router.get('/export',
    [
        query('type').isIn(['request', 'response', 'error']).withMessage('Type must be request, response, or error'),
        query('format').optional().isIn(['json', 'csv']),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('limit').optional().isInt({ min: 1, max: 50000 }).withMessage('Limit must be between 1 and 50000')
    ],
    validateRequest,
    LogController.exportLogs
);

// ==================== 日志搜索 ====================

// 搜索日志
router.get('/search',
    [
        query('keyword').notEmpty().withMessage('Keyword is required'),
        query('type').optional().isIn(['all', 'request', 'response', 'error']),
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format')
    ],
    validateRequest,
    LogController.searchLogs
);

export default router;
