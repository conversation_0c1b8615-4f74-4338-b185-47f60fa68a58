/**
 * 统计报表路由
 */

import { Router } from 'express';
import { query } from 'express-validator';
import { ReportController } from '../controllers/ReportController';
import { adminAuth } from '../middleware/adminAuth';
import { validateRequest } from '../middleware/validateRequest';

const router = Router();
const reportController = new ReportController();

// 应用管理员认证中间件
router.use(adminAuth);

// ==================== 仪表板总览 ====================

// 获取仪表板总览数据
router.get('/dashboard',
    [
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format')
    ],
    validateRequest,
    reportController.getDashboardOverview.bind(reportController)
);

// ==================== 用户统计报表 ====================

// 获取用户统计报表
router.get('/users',
    [
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('groupBy').optional().isIn(['day', 'week', 'month']).withMessage('GroupBy must be day, week, or month')
    ],
    validateRequest,
    reportController.getUserReport.bind(reportController)
);

// ==================== 聊天数据统计报表 ====================

// 获取聊天数据统计报表
router.get('/chat',
    [
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('groupBy').optional().isIn(['day', 'week', 'month']).withMessage('GroupBy must be day, week, or month')
    ],
    validateRequest,
    reportController.getChatReport.bind(reportController)
);

// ==================== 财务数据统计报表 ====================

// 获取财务数据统计报表
router.get('/financial',
    [
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('groupBy').optional().isIn(['day', 'week', 'month']).withMessage('GroupBy must be day, week, or month')
    ],
    validateRequest,
    reportController.getFinancialReport.bind(reportController)
);

// ==================== 系统性能监控报表 ====================

// 获取系统性能监控报表
router.get('/system',
    [
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format'),
        query('groupBy').optional().isIn(['day', 'week', 'month']).withMessage('GroupBy must be day, week, or month')
    ],
    validateRequest,
    reportController.getSystemReport.bind(reportController)
);

// ==================== 实时统计数据 ====================

// 获取实时统计数据
router.get('/realtime', reportController.getRealTimeStats.bind(reportController));

// ==================== 报表导出 ====================

// 导出报表数据
router.get('/export',
    [
        query('reportType').isIn(['user', 'chat', 'financial', 'system']).withMessage('Report type must be user, chat, financial, or system'),
        query('format').optional().isIn(['json', 'csv']).withMessage('Format must be json or csv'),
        query('dateFrom').optional().isISO8601().withMessage('Invalid date format'),
        query('dateTo').optional().isISO8601().withMessage('Invalid date format')
    ],
    validateRequest,
    reportController.exportReport.bind(reportController)
);

export default router;
