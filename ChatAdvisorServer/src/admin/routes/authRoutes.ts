import { Router } from 'express';
import { body } from 'express-validator';
import AuthController from '../controllers/AuthController';
import { adminAuth } from '../middleware/adminAuth';
import twoFactorRoutes from './twoFactorRoutes';

const router = Router();

/**
 * 管理员登录
 * POST /api/admin/auth/login
 */
router.post('/login', [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('password')
        .isLength({ min: 6 })
        .withMessage('Password must be at least 6 characters long'),
    body('twoFactorToken')
        .optional()
        .isLength({ min: 6, max: 8 })
        .withMessage('Two-factor token must be 6-8 characters long')
], AuthController.login);

/**
 * 管理员登出
 * POST /api/admin/auth/logout
 */
router.post('/logout', adminAuth, AuthController.logout);

/**
 * 获取当前管理员信息
 * GET /api/admin/auth/me
 */
router.get('/me', adminAuth, AuthController.getCurrentUser);

/**
 * 刷新token
 * POST /api/admin/auth/refresh
 */
router.post('/refresh', adminAuth, AuthController.refreshToken);

/**
 * 修改密码
 * PUT /api/admin/auth/password
 */
router.put('/password', [
    adminAuth,
    body('currentPassword')
        .isLength({ min: 6 })
        .withMessage('Current password is required'),
    body('newPassword')
        .isLength({ min: 6 })
        .withMessage('New password must be at least 6 characters long'),
    body('confirmPassword')
        .custom((value, { req }) => {
            if (value !== req.body.newPassword) {
                throw new Error('Password confirmation does not match password');
            }
            return true;
        })
], AuthController.changePassword);

/**
 * 两步验证相关路由
 * /api/admin/auth/2fa/*
 */
router.use('/2fa', twoFactorRoutes);

export default router;
