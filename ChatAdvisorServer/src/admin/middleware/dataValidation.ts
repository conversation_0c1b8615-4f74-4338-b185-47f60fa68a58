/**
 * 数据验证中间件
 * 提供高级数据验证、清理和转换功能
 */

import { Request, Response, NextFunction } from 'express';
import { body, query, param, ValidationChain } from 'express-validator';
import { logger } from '../../business/logger';

/**
 * 通用验证规则
 */
export const ValidationRules = {
    // MongoDB ObjectId验证
    mongoId: (field: string) => 
        param(field).isMongoId().withMessage(`${field} must be a valid MongoDB ObjectId`),
    
    // 分页参数验证
    pagination: () => [
        query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
        query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
    ],
    
    // 排序参数验证
    sorting: (allowedFields: string[]) => [
        query('sortBy').optional().isIn(allowedFields).withMessage(`SortBy must be one of: ${allowedFields.join(', ')}`),
        query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('SortOrder must be asc or desc')
    ],
    
    // 日期范围验证
    dateRange: () => [
        query('dateFrom').optional().isISO8601().withMessage('DateFrom must be a valid ISO 8601 date'),
        query('dateTo').optional().isISO8601().withMessage('DateTo must be a valid ISO 8601 date')
    ],
    
    // 搜索关键词验证
    search: () => 
        query('search').optional().isString().trim().isLength({ min: 1, max: 100 })
            .withMessage('Search keyword must be between 1 and 100 characters'),
    
    // 邮箱验证
    email: (field: string, required: boolean = true) => {
        const validator = required ? body(field) : body(field).optional();
        return validator.isEmail().normalizeEmail().withMessage(`${field} must be a valid email address`);
    },
    
    // 密码验证
    password: (field: string) => 
        body(field).isLength({ min: 6, max: 50 }).withMessage(`${field} must be between 6 and 50 characters`),
    
    // 用户名验证
    username: (field: string) => 
        body(field).isAlphanumeric().isLength({ min: 3, max: 20 })
            .withMessage(`${field} must be alphanumeric and between 3 and 20 characters`),
    
    // 金额验证
    amount: (field: string, min: number = 0) => 
        body(field).isFloat({ min }).withMessage(`${field} must be a number greater than or equal to ${min}`),
    
    // 数组验证
    array: (field: string, itemValidator?: (value: any) => boolean | Promise<boolean>) => {
        const validators = [body(field).isArray().withMessage(`${field} must be an array`)];
        if (itemValidator) {
            validators.push(body(`${field}.*`).custom(itemValidator));
        }
        return validators;
    },
    
    // 枚举值验证
    enum: (field: string, values: string[], required: boolean = true) => {
        const validator = required ? body(field) : body(field).optional();
        return validator.isIn(values).withMessage(`${field} must be one of: ${values.join(', ')}`);
    },
    
    // 布尔值验证
    boolean: (field: string, required: boolean = true) => {
        const validator = required ? body(field) : body(field).optional();
        return validator.isBoolean().withMessage(`${field} must be a boolean value`);
    },
    
    // 字符串长度验证
    stringLength: (field: string, min: number, max: number, required: boolean = true) => {
        const validator = required ? body(field) : body(field).optional();
        return validator.isString().trim().isLength({ min, max })
            .withMessage(`${field} must be between ${min} and ${max} characters`);
    },
    
    // 整数验证
    integer: (field: string, min?: number, max?: number, required: boolean = true) => {
        const validator = required ? body(field) : body(field).optional();
        const options: any = {};
        if (min !== undefined) options.min = min;
        if (max !== undefined) options.max = max;
        return validator.isInt(options).withMessage(
            `${field} must be an integer${min !== undefined ? ` >= ${min}` : ''}${max !== undefined ? ` <= ${max}` : ''}`
        );
    }
};

/**
 * 数据清理中间件
 */
export function sanitizeData(req: Request, res: Response, next: NextFunction): void {
    try {
        // 清理查询参数
        if (req.query) {
            for (const key in req.query) {
                if (typeof req.query[key] === 'string') {
                    req.query[key] = (req.query[key] as string).trim();
                }
            }
        }
        
        // 清理请求体
        if (req.body && typeof req.body === 'object') {
            sanitizeObject(req.body);
        }
        
        next();
    } catch (error) {
        logger.error('Data sanitization error:', error);
        next(error);
    }
}

/**
 * 递归清理对象
 */
function sanitizeObject(obj: any): void {
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            
            if (typeof value === 'string') {
                // 清理字符串：去除首尾空格，防止XSS
                obj[key] = value.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                // 递归清理嵌套对象
                sanitizeObject(value);
            } else if (Array.isArray(value)) {
                // 清理数组中的每个元素
                value.forEach((item, index) => {
                    if (typeof item === 'string') {
                        value[index] = item.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
                    } else if (typeof item === 'object' && item !== null) {
                        sanitizeObject(item);
                    }
                });
            }
        }
    }
}

/**
 * 请求大小限制中间件
 */
export function limitRequestSize(maxSizeKB: number = 1024) {
    return (req: Request, res: Response, next: NextFunction): void => {
        const contentLength = parseInt(req.headers['content-length'] || '0');
        const maxSizeBytes = maxSizeKB * 1024;
        
        if (contentLength > maxSizeBytes) {
            res.status(413).json({
                success: false,
                message: `Request too large. Maximum size is ${maxSizeKB}KB`
            });
            return;
        }
        
        next();
    };
}

/**
 * 频率限制中间件
 */
export function rateLimit(windowMs: number = 60000, maxRequests: number = 100) {
    const requests = new Map<string, { count: number; resetTime: number }>();
    
    return (req: Request, res: Response, next: NextFunction): void => {
        const clientId = req.ip || 'unknown';
        const now = Date.now();
        
        // 清理过期记录
        for (const [key, value] of requests.entries()) {
            if (now > value.resetTime) {
                requests.delete(key);
            }
        }
        
        // 检查当前客户端的请求次数
        const clientRequests = requests.get(clientId);
        
        if (!clientRequests) {
            requests.set(clientId, { count: 1, resetTime: now + windowMs });
            next();
            return;
        }
        
        if (clientRequests.count >= maxRequests) {
            res.status(429).json({
                success: false,
                message: 'Too many requests. Please try again later.',
                retryAfter: Math.ceil((clientRequests.resetTime - now) / 1000)
            });
            return;
        }
        
        clientRequests.count++;
        next();
    };
}

/**
 * 自定义验证器
 */
export const CustomValidators = {
    // 验证日期范围
    dateRange: (dateFrom?: string, dateTo?: string) => {
        if (dateFrom && dateTo) {
            const from = new Date(dateFrom);
            const to = new Date(dateTo);
            if (from >= to) {
                throw new Error('DateFrom must be before DateTo');
            }
        }
        return true;
    },
    
    // 验证MongoDB ObjectId数组
    mongoIdArray: (value: any) => {
        if (!Array.isArray(value)) {
            throw new Error('Must be an array');
        }
        
        const mongoIdRegex = /^[0-9a-fA-F]{24}$/;
        for (const id of value) {
            if (!mongoIdRegex.test(id)) {
                throw new Error('All items must be valid MongoDB ObjectIds');
            }
        }
        return true;
    },
    
    // 验证文件类型
    fileType: (allowedTypes: string[]) => (value: any) => {
        if (value && value.mimetype && !allowedTypes.includes(value.mimetype)) {
            throw new Error(`File type must be one of: ${allowedTypes.join(', ')}`);
        }
        return true;
    },
    
    // 验证密码强度
    strongPassword: (value: string) => {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(value);
        const hasLowerCase = /[a-z]/.test(value);
        const hasNumbers = /\d/.test(value);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
        
        if (value.length < minLength) {
            throw new Error(`Password must be at least ${minLength} characters long`);
        }
        
        if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
            throw new Error('Password must contain uppercase, lowercase, numbers, and special characters');
        }
        
        return true;
    }
};

/**
 * 条件验证中间件
 */
export function conditionalValidation(condition: (req: Request) => boolean, validators: ValidationChain[]) {
    return (req: Request, res: Response, next: NextFunction): void => {
        if (condition(req)) {
            // 如果条件满足，应用验证器
            Promise.all(validators.map(validator => validator.run(req)))
                .then(() => next())
                .catch(next);
        } else {
            // 如果条件不满足，跳过验证
            next();
        }
    };
}

/**
 * 批量操作验证
 */
export const BatchValidation = {
    // 验证批量操作的ID数组
    ids: (field: string, maxCount: number = 1000) => [
        body(field).isArray().withMessage(`${field} must be an array`),
        body(field).isLength({ min: 1, max: maxCount }).withMessage(`${field} must contain 1 to ${maxCount} items`),
        body(`${field}.*`).isMongoId().withMessage('All IDs must be valid MongoDB ObjectIds')
    ],
    
    // 验证批量更新的数据
    updates: (allowedFields: string[]) => 
        body('updates').custom((value) => {
            if (typeof value !== 'object' || value === null) {
                throw new Error('Updates must be an object');
            }
            
            const invalidFields = Object.keys(value).filter(key => !allowedFields.includes(key));
            if (invalidFields.length > 0) {
                throw new Error(`Invalid fields: ${invalidFields.join(', ')}. Allowed fields: ${allowedFields.join(', ')}`);
            }
            
            return true;
        })
};

export default {
    ValidationRules,
    sanitizeData,
    limitRequestSize,
    rateLimit,
    CustomValidators,
    conditionalValidation,
    BatchValidation
};
