/**
 * 管理员认证中间件
 * 验证管理员身份和权限
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { secretKey } from '../../config/env';
import { logger } from '../../business/logger';

// CORS辅助函数 - 确保401响应包含正确的CORS头
function setCorsHeaders(req: Request, res: Response): void {
    const origin = req.headers.origin;
    const referer = req.headers.referer;
    const allowedOrigins = [
        'http://localhost:3000', 
        'http://localhost:54001',
        'https://advisor.sanva.tk', 
        'https://advisor.sanva.top', 
        'https://admin.sanva.top',
        'https://admin.sanva.tk'
    ];
    
    // 如果有origin头，使用origin
    if (origin && allowedOrigins.includes(origin)) {
        res.header('Access-Control-Allow-Origin', origin);
        res.header('Access-Control-Allow-Credentials', 'true');
    }
    // 如果没有origin但有referer，从referer推断origin
    else if (!origin && referer) {
        try {
            const refererUrl = new URL(referer);
            const inferredOrigin = `${refererUrl.protocol}//${refererUrl.host}`;
            if (allowedOrigins.includes(inferredOrigin)) {
                res.header('Access-Control-Allow-Origin', inferredOrigin);
                res.header('Access-Control-Allow-Credentials', 'true');
            }
        } catch (e) {
            // 如果referer解析失败，尝试localhost默认
            if (referer.includes('localhost:54001')) {
                res.header('Access-Control-Allow-Origin', 'http://localhost:54001');
                res.header('Access-Control-Allow-Credentials', 'true');
            }
        }
    }
    // 如果都没有，但请求来自localhost，默认允许
    else if (!origin && req.get('host')?.includes('localhost')) {
        res.header('Access-Control-Allow-Origin', 'http://localhost:54001');
        res.header('Access-Control-Allow-Credentials', 'true');
    }
}

// 扩展Request接口
declare global {
    namespace Express {
        interface Request {
            admin?: {
                id: string;
                username: string;
                email?: string;
                role: string;
                permissions: string[];
            };
        }
    }
}

// 管理员角色和权限配置
const ADMIN_ROLES = {
    SUPER_ADMIN: 'super_admin',
    ADMIN: 'admin',
    MODERATOR: 'moderator'
};

const PERMISSIONS = {
    // 用户管理权限
    USER_READ: 'user:read',
    USER_WRITE: 'user:write',
    USER_DELETE: 'user:delete',
    USER_BATCH: 'user:batch',
    USER_EXPORT: 'user:export',

    // 聊天管理权限
    CHAT_READ: 'chat:read',
    CHAT_WRITE: 'chat:write',
    CHAT_DELETE: 'chat:delete',
    CHAT_EXPORT: 'chat:export',

    // 财务管理权限
    BALANCE_READ: 'balance:read',
    BALANCE_WRITE: 'balance:write',
    TRANSACTION_READ: 'transaction:read',
    TRANSACTION_EXPORT: 'transaction:export',
    PRODUCT_READ: 'product:read',
    PRODUCT_WRITE: 'product:write',
    PRODUCT_DELETE: 'product:delete',
    PRICING_READ: 'pricing:read',
    PRICING_WRITE: 'pricing:write',
    PRICING_DELETE: 'pricing:delete',

    // 系统管理权限
    SYSTEM_READ: 'system:read',
    SYSTEM_WRITE: 'system:write',
    CONFIG_READ: 'config:read',
    CONFIG_WRITE: 'config:write',
    QUESTION_READ: 'question:read',
    QUESTION_WRITE: 'question:write',
    QUESTION_DELETE: 'question:delete',

    // 日志管理权限
    LOG_READ: 'log:read',
    LOG_DELETE: 'log:delete',
    LOG_EXPORT: 'log:export',

    // 报表权限
    REPORT_READ: 'report:read',
    REPORT_EXPORT: 'report:export'
};

const ROLE_PERMISSIONS = {
    [ADMIN_ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),
    [ADMIN_ROLES.ADMIN]: [
        // 用户管理权限
        PERMISSIONS.USER_READ,
        PERMISSIONS.USER_WRITE,
        PERMISSIONS.USER_DELETE,
        PERMISSIONS.USER_BATCH,
        PERMISSIONS.USER_EXPORT,

        // 聊天管理权限
        PERMISSIONS.CHAT_READ,
        PERMISSIONS.CHAT_WRITE,
        PERMISSIONS.CHAT_DELETE,
        PERMISSIONS.CHAT_EXPORT,

        // 财务管理权限
        PERMISSIONS.BALANCE_READ,
        PERMISSIONS.BALANCE_WRITE,
        PERMISSIONS.TRANSACTION_READ,
        PERMISSIONS.TRANSACTION_EXPORT,
        PERMISSIONS.PRODUCT_READ,
        PERMISSIONS.PRODUCT_WRITE,
        PERMISSIONS.PRICING_READ,
        PERMISSIONS.PRICING_WRITE,

        // 系统管理权限（只读）
        PERMISSIONS.SYSTEM_READ,
        PERMISSIONS.CONFIG_READ,
        PERMISSIONS.QUESTION_READ,
        PERMISSIONS.QUESTION_WRITE,

        // 日志管理权限
        PERMISSIONS.LOG_READ,
        PERMISSIONS.LOG_EXPORT,

        // 报表权限
        PERMISSIONS.REPORT_READ,
        PERMISSIONS.REPORT_EXPORT
    ],
    [ADMIN_ROLES.MODERATOR]: [
        // 用户管理权限（只读）
        PERMISSIONS.USER_READ,

        // 聊天管理权限（只读）
        PERMISSIONS.CHAT_READ,

        // 财务管理权限（只读）
        PERMISSIONS.BALANCE_READ,
        PERMISSIONS.TRANSACTION_READ,
        PERMISSIONS.PRODUCT_READ,
        PERMISSIONS.PRICING_READ,

        // 系统管理权限（只读）
        PERMISSIONS.SYSTEM_READ,
        PERMISSIONS.CONFIG_READ,
        PERMISSIONS.QUESTION_READ,

        // 日志管理权限（只读）
        PERMISSIONS.LOG_READ,

        // 报表权限（只读）
        PERMISSIONS.REPORT_READ
    ]
};

// 临时管理员配置（生产环境应该使用数据库）
const ADMIN_USERS = [
    {
        id: 'admin_001',
        username: 'admin',
        password: process.env.ADMIN_PASSWORD || 'admin123456',
        role: ADMIN_ROLES.SUPER_ADMIN
    },
    {
        id: 'admin_002',
        username: 'moderator',
        password: process.env.MODERATOR_PASSWORD || 'mod123456',
        role: ADMIN_ROLES.MODERATOR
    }
];

/**
 * 管理员登录
 */
export async function adminLogin(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            res.status(400).json({
                success: false,
                message: 'Username and password are required'
            });
            return;
        }

        // 查找管理员用户
        const admin = ADMIN_USERS.find(u => u.username === username && u.password === password);

        if (!admin) {
            setCorsHeaders(req, res);
            res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
            return;
        }

        // 生成JWT token
        const token = jwt.sign(
            {
                id: admin.id,
                username: admin.username,
                role: admin.role,
                type: 'admin'
            },
            secretKey,
            { expiresIn: '24h' }
        );

        logger.info(`Admin login successful: ${username}`);

        res.json({
            success: true,
            data: {
                token,
                admin: {
                    id: admin.id,
                    username: admin.username,
                    role: admin.role,
                    permissions: ROLE_PERMISSIONS[admin.role] || []
                }
            },
            message: 'Login successful'
        });

    } catch (error) {
        logger.error('Admin login failed:', error);
        next(error);
    }
}

/**
 * 管理员认证中间件
 */
export async function adminAuth(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
        const authHeader = req.headers.authorization;
        logger.debug('Auth header received:', { authHeader: authHeader ? 'Bearer ***' : 'none' });

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            logger.debug('No valid auth header found');
            // 修复CORS问题：确保401响应包含正确的CORS头
            setCorsHeaders(req, res);
            res.status(401).json({
                success: false,
                message: 'Access token required'
            });
            return;
        }

        const token = authHeader.substring(7);

        try {
            const decoded = jwt.verify(token, secretKey) as any;
            logger.debug('Token decoded successfully:', {
                userId: decoded.userId || decoded.id,
                email: decoded.email,
                type: decoded.type,
                role: decoded.role,
                fullDecoded: decoded
            });

            if (decoded.type !== 'admin') {
                logger.debug('Access denied - not admin type:', { type: decoded.type });
                res.status(403).json({
                    success: false,
                    message: 'Admin access required'
                });
                return;
            }

            // 验证管理员是否存在（支持数据库用户和硬编码用户）
            let admin = ADMIN_USERS.find(u => u.id === decoded.id);

            // 如果不是硬编码用户，检查是否是数据库中的管理员用户
            if (!admin && (decoded.userId || decoded.id) && decoded.type === 'admin') {
                // 从数据库验证的用户，构造admin对象
                const adminId = decoded.userId || decoded.id;
                admin = {
                    id: adminId,
                    username: decoded.email || decoded.username || 'admin',
                    password: '', // 不需要密码
                    role: decoded.role || ADMIN_ROLES.ADMIN
                };
            }

            if (!admin) {
                setCorsHeaders(req, res);
                res.status(401).json({
                    success: false,
                    message: 'Invalid admin token'
                });
                return;
            }

            // 设置请求上下文
            req.admin = {
                id: admin.id,
                username: admin.username,
                email: decoded.email || admin.username + '@admin.local',
                role: admin.role,
                permissions: ROLE_PERMISSIONS[admin.role] || []
            };

            next();

        } catch (jwtError) {
            setCorsHeaders(req, res);
            res.status(401).json({
                success: false,
                message: 'Invalid or expired token'
            });
            return;
        }

    } catch (error) {
        logger.error('Admin auth middleware error:', error);
        next(error);
    }
}

/**
 * 权限检查中间件
 */
export function requirePermission(permission: string) {
    return (req: Request, res: Response, next: NextFunction): void => {
        if (!req.admin) {
            setCorsHeaders(req, res);
            res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
            return;
        }

        if (!req.admin.permissions.includes(permission)) {
            res.status(403).json({
                success: false,
                message: 'Insufficient permissions'
            });
            return;
        }

        next();
    };
}

/**
 * 角色检查中间件
 */
export function requireRole(role: string) {
    return (req: Request, res: Response, next: NextFunction): void => {
        if (!req.admin) {
            setCorsHeaders(req, res);
            res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
            return;
        }

        if (req.admin.role !== role) {
            res.status(403).json({
                success: false,
                message: 'Insufficient role'
            });
            return;
        }

        next();
    };
}

/**
 * 获取管理员信息
 */
export async function getAdminInfo(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
        if (!req.admin) {
            setCorsHeaders(req, res);
            res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
            return;
        }

        res.json({
            success: true,
            data: req.admin
        });

    } catch (error) {
        logger.error('Get admin info failed:', error);
        next(error);
    }
}

export { ADMIN_ROLES, PERMISSIONS, ROLE_PERMISSIONS };
