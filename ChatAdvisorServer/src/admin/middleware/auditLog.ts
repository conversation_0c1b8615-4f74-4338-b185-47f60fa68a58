/**
 * 审计日志中间件
 * 记录管理员的所有操作行为
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../../business/logger';

/**
 * 审计日志接口
 */
interface AuditLogEntry {
    timestamp: Date;
    adminId: string;
    adminEmail: string;
    adminRole: string;
    action: string;
    resource: string;
    resourceId?: string;
    method: string;
    path: string;
    ip: string;
    userAgent: string;
    requestBody?: any;
    responseStatus?: number;
    duration?: number;
    success: boolean;
    error?: string;
}

/**
 * 审计日志存储（内存存储，生产环境应使用数据库）
 */
class AuditLogStore {
    private logs: AuditLogEntry[] = [];
    private maxLogs = 10000; // 最大日志条数

    add(entry: AuditLogEntry): void {
        this.logs.push(entry);
        
        // 保持日志数量在限制内
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }
    }

    getLogs(limit: number = 100, offset: number = 0): AuditLogEntry[] {
        return this.logs
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
            .slice(offset, offset + limit);
    }

    getLogsByAdmin(adminId: string, limit: number = 100): AuditLogEntry[] {
        return this.logs
            .filter(log => log.adminId === adminId)
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
            .slice(0, limit);
    }

    getLogsByAction(action: string, limit: number = 100): AuditLogEntry[] {
        return this.logs
            .filter(log => log.action === action)
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
            .slice(0, limit);
    }

    getLogsByDateRange(startDate: Date, endDate: Date): AuditLogEntry[] {
        return this.logs
            .filter(log => log.timestamp >= startDate && log.timestamp <= endDate)
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    }

    getTotalCount(): number {
        return this.logs.length;
    }

    clear(): void {
        this.logs = [];
    }
}

// 全局审计日志存储实例
const auditLogStore = new AuditLogStore();

/**
 * 操作类型映射
 */
const ACTION_MAPPING: { [key: string]: string } = {
    'GET': 'READ',
    'POST': 'create',
    'PUT': 'update',
    'PATCH': 'update',
    'DELETE': 'delete'
};

/**
 * 资源类型映射
 */
const RESOURCE_MAPPING: { [key: string]: string } = {
    '/api/admin/users': 'user',
    '/api/admin/chat': 'chat',
    '/api/admin/financial/transactions': 'transaction',
    '/api/admin/financial/products': 'product',
    '/api/admin/financial/pricing': 'pricing',
    '/api/admin/system/config': 'config',
    '/api/admin/system/questions': 'question',
    '/api/admin/logs': 'log',
    '/api/admin/reports': 'report'
};

/**
 * 敏感操作列表
 */
const SENSITIVE_ACTIONS = [
    'delete',
    'batch-delete',
    'batch-update',
    'config-update',
    'user-balance-adjust',
    'system-migration',
    'log-cleanup'
];

/**
 * 审计日志中间件
 */
export function auditLogger() {
    return (req: Request, res: Response, next: NextFunction): void => {
        // 跳过不需要审计的路由
        const skipRoutes = [
            '/api/admin/login',
            '/api/admin/me',
            '/api/admin/reports/realtime'
        ];

        if (skipRoutes.some(route => req.path.startsWith(route))) {
            next();
            return;
        }

        const startTime = Date.now();
        
        // 保存原始的 res.json 方法
        const originalJson = res.json;
        let responseBody: any;
        let responseStatus = res.statusCode;

        // 重写 res.json 方法以捕获响应
        res.json = function(body: any) {
            responseBody = body;
            responseStatus = res.statusCode;
            return originalJson.call(this, body);
        };

        // 监听响应完成事件
        res.on('finish', () => {
            try {
                const endTime = Date.now();
                const duration = endTime - startTime;

                // 创建审计日志条目
                const auditEntry: AuditLogEntry = {
                    timestamp: new Date(startTime),
                    adminId: req.admin?.id || 'anonymous',
                    adminEmail: req.admin?.email || 'anonymous',
                    adminRole: req.admin?.role || 'unknown',
                    action: getActionFromRequest(req),
                    resource: getResourceFromRequest(req),
                    resourceId: getResourceIdFromRequest(req),
                    method: req.method,
                    path: req.path,
                    ip: getClientIP(req),
                    userAgent: req.headers['user-agent'] || 'unknown',
                    requestBody: sanitizeRequestBody(req.body),
                    responseStatus,
                    duration,
                    success: responseStatus < 400,
                    error: responseStatus >= 400 ? responseBody?.message : undefined
                };

                // 存储审计日志
                auditLogStore.add(auditEntry);

                // 记录到系统日志
                logAuditEntry(auditEntry);

                // 如果是敏感操作，发送警报
                if (isSensitiveAction(auditEntry.action)) {
                    sendSecurityAlert(auditEntry);
                }

            } catch (error) {
                logger.error('Audit logging error:', error);
            }
        });

        next();
    };
}

/**
 * 从请求中获取操作类型
 */
function getActionFromRequest(req: Request): string {
    const method = req.method;
    const path = req.path;

    // 特殊操作映射
    if (path.includes('/batch-delete')) return 'batch-delete';
    if (path.includes('/batch-update') || path.includes('/batch-status') || path.includes('/batch-balance')) return 'batch-update';
    if (path.includes('/export')) return 'export';
    if (path.includes('/import')) return 'import';
    if (path.includes('/duplicate')) return 'duplicate';
    if (path.includes('/restore')) return 'restore';
    if (path.includes('/cleanup')) return 'cleanup';
    if (path.includes('/reset')) return 'reset';
    if (path.includes('/search')) return 'search';
    if (path.includes('/stats')) return 'stats';

    return ACTION_MAPPING[method] || method.toLowerCase();
}

/**
 * 从请求中获取资源类型
 */
function getResourceFromRequest(req: Request): string {
    const path = req.path;

    for (const [routePrefix, resource] of Object.entries(RESOURCE_MAPPING)) {
        if (path.startsWith(routePrefix)) {
            return resource;
        }
    }

    // 从路径中提取资源类型
    const pathParts = path.split('/').filter(part => part);
    if (pathParts.length >= 3) {
        return pathParts[2]; // /api/admin/[resource]
    }

    return 'unknown';
}

/**
 * 从请求中获取资源ID
 */
function getResourceIdFromRequest(req: Request): string | undefined {
    // 从路径参数中获取ID
    if (req.params && req.params.id) {
        return req.params.id;
    }

    // 从查询参数中获取ID
    if (req.query && req.query.id) {
        return req.query.id as string;
    }

    // 从请求体中获取ID（批量操作）
    if (req.body) {
        if (req.body.userIds && Array.isArray(req.body.userIds)) {
            return `batch:${req.body.userIds.length}`;
        }
        if (req.body.productIds && Array.isArray(req.body.productIds)) {
            return `batch:${req.body.productIds.length}`;
        }
        if (req.body.questionIds && Array.isArray(req.body.questionIds)) {
            return `batch:${req.body.questionIds.length}`;
        }
    }

    return undefined;
}

/**
 * 获取客户端IP地址
 */
function getClientIP(req: Request): string {
    return (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
           req.headers['x-real-ip'] as string ||
           req.connection.remoteAddress ||
           req.socket.remoteAddress ||
           'unknown';
}

/**
 * 清理请求体中的敏感信息
 */
function sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') {
        return body;
    }

    const sanitized = { ...body };
    const sensitiveFields = ['password', 'token', 'secret', 'key'];

    for (const field of sensitiveFields) {
        if (sanitized[field]) {
            sanitized[field] = '[REDACTED]';
        }
    }

    return sanitized;
}

/**
 * 记录审计日志到系统日志
 */
function logAuditEntry(entry: AuditLogEntry): void {
    const logLevel = entry.success ? 'info' : 'warn';
    const message = `Admin Action: ${entry.adminEmail} (${entry.adminRole}) ${entry.action} ${entry.resource}${entry.resourceId ? ` (${entry.resourceId})` : ''} - ${entry.success ? 'SUCCESS' : 'FAILED'}`;

    logger[logLevel](message, {
        auditLog: true,
        adminId: entry.adminId,
        action: entry.action,
        resource: entry.resource,
        ip: entry.ip,
        duration: entry.duration,
        status: entry.responseStatus
    });
}

/**
 * 检查是否为敏感操作
 */
function isSensitiveAction(action: string): boolean {
    return SENSITIVE_ACTIONS.includes(action);
}

/**
 * 发送安全警报
 */
function sendSecurityAlert(entry: AuditLogEntry): void {
    // 这里可以实现发送邮件、短信或其他通知方式
    logger.warn(`SECURITY ALERT: Sensitive action performed`, {
        adminEmail: entry.adminEmail,
        action: entry.action,
        resource: entry.resource,
        ip: entry.ip,
        timestamp: entry.timestamp
    });
}

/**
 * 审计日志查询API
 */
export const AuditLogAPI = {
    // 获取审计日志列表
    getLogs: (limit?: number, offset?: number) => {
        return auditLogStore.getLogs(limit, offset);
    },

    // 根据管理员ID获取日志
    getLogsByAdmin: (adminId: string, limit?: number) => {
        return auditLogStore.getLogsByAdmin(adminId, limit);
    },

    // 根据操作类型获取日志
    getLogsByAction: (action: string, limit?: number) => {
        return auditLogStore.getLogsByAction(action, limit);
    },

    // 根据日期范围获取日志
    getLogsByDateRange: (startDate: Date, endDate: Date) => {
        return auditLogStore.getLogsByDateRange(startDate, endDate);
    },

    // 获取日志总数
    getTotalCount: () => {
        return auditLogStore.getTotalCount();
    },

    // 清空日志
    clearLogs: () => {
        auditLogStore.clear();
    },

    // 获取统计信息
    getStats: () => {
        const logs = auditLogStore.getLogs(1000);
        const stats = {
            totalLogs: logs.length,
            successRate: logs.filter(log => log.success).length / logs.length * 100,
            topActions: getTopActions(logs),
            topAdmins: getTopAdmins(logs),
            recentFailures: logs.filter(log => !log.success).slice(0, 10)
        };
        return stats;
    }
};

/**
 * 获取最常见的操作
 */
function getTopActions(logs: AuditLogEntry[]): { action: string; count: number }[] {
    const actionCounts: { [key: string]: number } = {};
    
    logs.forEach(log => {
        actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;
    });

    return Object.entries(actionCounts)
        .map(([action, count]) => ({ action, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);
}

/**
 * 获取最活跃的管理员
 */
function getTopAdmins(logs: AuditLogEntry[]): { adminEmail: string; count: number }[] {
    const adminCounts: { [key: string]: number } = {};
    
    logs.forEach(log => {
        adminCounts[log.adminEmail] = (adminCounts[log.adminEmail] || 0) + 1;
    });

    return Object.entries(adminCounts)
        .map(([adminEmail, count]) => ({ adminEmail, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);
}

export default {
    auditLogger,
    AuditLogAPI
};
