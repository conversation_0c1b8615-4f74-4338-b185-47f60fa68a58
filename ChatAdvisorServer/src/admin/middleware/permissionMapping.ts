/**
 * 权限映射中间件
 * 根据路由自动应用权限检查
 */

import { Request, Response, NextFunction } from 'express';
import { PERMISSIONS } from './adminAuth';

/**
 * 路由权限映射配置
 */
const ROUTE_PERMISSIONS: { [key: string]: string } = {
    // 用户管理路由权限
    'GET /api/admin/users': PERMISSIONS.USER_READ,
    'GET /api/admin/users/:id': PERMISSIONS.USER_READ,
    'POST /api/admin/users': PERMISSIONS.USER_WRITE,
    'PUT /api/admin/users/:id': PERMISSIONS.USER_WRITE,
    'DELETE /api/admin/users/:id': PERMISSIONS.USER_DELETE,
    'POST /api/admin/users/batch-delete': PERMISSIONS.USER_BATCH,
    'POST /api/admin/users/batch-status': PERMISSIONS.USER_BATCH,
    'POST /api/admin/users/batch-balance': PERMISSIONS.USER_BATCH,
    'POST /api/admin/users/batch-restore': PERMISSIONS.USER_BATCH,
    'GET /api/admin/users/export': PERMISSIONS.USER_EXPORT,
    'GET /api/admin/users/stats': PERMISSIONS.USER_READ,
    'GET /api/admin/users/detailed-stats': PERMISSIONS.USER_READ,
    
    // 聊天管理路由权限
    'GET /api/admin/chat/messages': PERMISSIONS.CHAT_READ,
    'GET /api/admin/chat/messages/:id': PERMISSIONS.CHAT_READ,
    'PUT /api/admin/chat/messages/:id': PERMISSIONS.CHAT_WRITE,
    'DELETE /api/admin/chat/messages/:id': PERMISSIONS.CHAT_DELETE,
    'POST /api/admin/chat/messages/batch-delete': PERMISSIONS.CHAT_DELETE,
    'GET /api/admin/chat/sessions': PERMISSIONS.CHAT_READ,
    'GET /api/admin/chat/stats': PERMISSIONS.CHAT_READ,
    'GET /api/admin/chat/search': PERMISSIONS.CHAT_READ,
    'GET /api/admin/chat/export': PERMISSIONS.CHAT_EXPORT,
    
    // 财务管理路由权限
    'GET /api/admin/financial/transactions': PERMISSIONS.TRANSACTION_READ,
    'GET /api/admin/financial/transactions/:id': PERMISSIONS.TRANSACTION_READ,
    'GET /api/admin/financial/transactions/user/:userId': PERMISSIONS.TRANSACTION_READ,
    'GET /api/admin/financial/transactions/stats': PERMISSIONS.TRANSACTION_READ,
    'GET /api/admin/financial/transactions/export': PERMISSIONS.TRANSACTION_EXPORT,
    
    'GET /api/admin/financial/products': PERMISSIONS.PRODUCT_READ,
    'GET /api/admin/financial/products/:id': PERMISSIONS.PRODUCT_READ,
    'POST /api/admin/financial/products': PERMISSIONS.PRODUCT_WRITE,
    'PUT /api/admin/financial/products/:id': PERMISSIONS.PRODUCT_WRITE,
    'DELETE /api/admin/financial/products/:id': PERMISSIONS.PRODUCT_DELETE,
    'POST /api/admin/financial/products/batch-status': PERMISSIONS.PRODUCT_WRITE,
    'POST /api/admin/financial/products/batch-delete': PERMISSIONS.PRODUCT_DELETE,
    'GET /api/admin/financial/products/stats': PERMISSIONS.PRODUCT_READ,
    'GET /api/admin/financial/products/export': PERMISSIONS.PRODUCT_READ,
    'POST /api/admin/financial/products/:id/duplicate': PERMISSIONS.PRODUCT_WRITE,
    
    'GET /api/admin/financial/pricing': PERMISSIONS.PRICING_READ,
    'GET /api/admin/financial/pricing/:id': PERMISSIONS.PRICING_READ,
    'POST /api/admin/financial/pricing': PERMISSIONS.PRICING_WRITE,
    'PUT /api/admin/financial/pricing/:id': PERMISSIONS.PRICING_WRITE,
    'DELETE /api/admin/financial/pricing/:id': PERMISSIONS.PRICING_DELETE,
    'POST /api/admin/financial/pricing/batch-update': PERMISSIONS.PRICING_WRITE,
    'GET /api/admin/financial/pricing/stats': PERMISSIONS.PRICING_READ,
    'GET /api/admin/financial/pricing/export': PERMISSIONS.PRICING_READ,
    'POST /api/admin/financial/pricing/:id/duplicate': PERMISSIONS.PRICING_WRITE,
    
    // 系统管理路由权限
    'GET /api/admin/system/status': PERMISSIONS.SYSTEM_READ,
    'GET /api/admin/system/database': PERMISSIONS.SYSTEM_READ,
    'GET /api/admin/system/migrations': PERMISSIONS.SYSTEM_READ,
    'POST /api/admin/system/migrations/run': PERMISSIONS.SYSTEM_WRITE,
    
    'GET /api/admin/system/config': PERMISSIONS.CONFIG_READ,
    'PUT /api/admin/system/config': PERMISSIONS.CONFIG_WRITE,
    'GET /api/admin/system/config/:key': PERMISSIONS.CONFIG_READ,
    'PUT /api/admin/system/config/:key': PERMISSIONS.CONFIG_WRITE,
    'POST /api/admin/system/config/batch-update': PERMISSIONS.CONFIG_WRITE,
    'GET /api/admin/system/config/export': PERMISSIONS.CONFIG_READ,
    'POST /api/admin/system/config/import': PERMISSIONS.CONFIG_WRITE,
    'POST /api/admin/system/config/reset': PERMISSIONS.CONFIG_WRITE,
    'POST /api/admin/system/config/validate': PERMISSIONS.CONFIG_READ,
    'GET /api/admin/system/config/history': PERMISSIONS.CONFIG_READ,
    'GET /api/admin/system/config/template': PERMISSIONS.CONFIG_READ,
    
    'GET /api/admin/system/questions': PERMISSIONS.QUESTION_READ,
    'GET /api/admin/system/questions/:id': PERMISSIONS.QUESTION_READ,
    'POST /api/admin/system/questions': PERMISSIONS.QUESTION_WRITE,
    'PUT /api/admin/system/questions/:id': PERMISSIONS.QUESTION_WRITE,
    'DELETE /api/admin/system/questions/:id': PERMISSIONS.QUESTION_DELETE,
    'POST /api/admin/system/questions/batch-delete': PERMISSIONS.QUESTION_DELETE,
    'GET /api/admin/system/questions/stats': PERMISSIONS.QUESTION_READ,
    'GET /api/admin/system/questions/export': PERMISSIONS.QUESTION_READ,
    'POST /api/admin/system/questions/import': PERMISSIONS.QUESTION_WRITE,
    'GET /api/admin/system/questions/search': PERMISSIONS.QUESTION_READ,
    'POST /api/admin/system/questions/:id/duplicate': PERMISSIONS.QUESTION_WRITE,
    
    // 日志管理路由权限
    'GET /api/admin/logs/requests': PERMISSIONS.LOG_READ,
    'GET /api/admin/logs/responses': PERMISSIONS.LOG_READ,
    'GET /api/admin/logs/errors': PERMISSIONS.LOG_READ,
    'GET /api/admin/logs/details/:requestId': PERMISSIONS.LOG_READ,
    'GET /api/admin/logs/stats': PERMISSIONS.LOG_READ,
    'POST /api/admin/logs/cleanup': PERMISSIONS.LOG_DELETE,
    'GET /api/admin/logs/export': PERMISSIONS.LOG_EXPORT,
    'GET /api/admin/logs/search': PERMISSIONS.LOG_READ,
    
    // 报表路由权限
    'GET /api/admin/reports/dashboard': PERMISSIONS.REPORT_READ,
    'GET /api/admin/reports/users': PERMISSIONS.REPORT_READ,
    'GET /api/admin/reports/chat': PERMISSIONS.REPORT_READ,
    'GET /api/admin/reports/financial': PERMISSIONS.REPORT_READ,
    'GET /api/admin/reports/system': PERMISSIONS.REPORT_READ,
    'GET /api/admin/reports/realtime': PERMISSIONS.REPORT_READ,
    'GET /api/admin/reports/export': PERMISSIONS.REPORT_EXPORT
};

/**
 * 权限检查中间件工厂
 */
export function createPermissionMiddleware() {
    return (req: Request, res: Response, next: NextFunction): void => {
        // 跳过登录和获取管理员信息的路由
        const skipRoutes = [
            'POST /api/admin/login',
            'GET /api/admin/me'
        ];
        
        const routeKey = `${req.method} ${req.route?.path ? req.baseUrl + req.route.path : req.path}`;
        
        if (skipRoutes.includes(routeKey)) {
            next();
            return;
        }
        
        // 检查是否需要认证
        if (!req.admin) {
            res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
            return;
        }
        
        // 获取路由所需权限
        const requiredPermission = getRequiredPermission(req.method, req.path);
        
        if (!requiredPermission) {
            // 如果没有配置权限要求，默认允许（但记录警告）
            console.warn(`No permission configured for route: ${req.method} ${req.path}`);
            next();
            return;
        }
        
        // 检查用户是否有所需权限
        if (!req.admin.permissions.includes(requiredPermission)) {
            res.status(403).json({
                success: false,
                message: 'Insufficient permissions',
                required: requiredPermission,
                current: req.admin.permissions
            });
            return;
        }
        
        next();
    };
}

/**
 * 获取路由所需权限
 */
function getRequiredPermission(method: string, path: string): string | null {
    // 首先尝试精确匹配
    const exactKey = `${method} ${path}`;
    if (ROUTE_PERMISSIONS[exactKey]) {
        return ROUTE_PERMISSIONS[exactKey];
    }
    
    // 然后尝试参数化路由匹配
    for (const routePattern in ROUTE_PERMISSIONS) {
        if (matchRoute(routePattern, `${method} ${path}`)) {
            return ROUTE_PERMISSIONS[routePattern];
        }
    }
    
    return null;
}

/**
 * 路由模式匹配
 */
function matchRoute(pattern: string, actual: string): boolean {
    // 将路由参数 :id 转换为正则表达式
    const regexPattern = pattern
        .replace(/:[^/]+/g, '[^/]+')  // :id -> [^/]+
        .replace(/\//g, '\\/');       // / -> \/
    
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(actual);
}

/**
 * 权限检查装饰器（用于控制器方法）
 */
export function RequirePermission(permission: string) {
    return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        
        descriptor.value = function(req: Request, res: Response, next: NextFunction) {
            if (!req.admin) {
                res.status(401).json({
                    success: false,
                    message: 'Authentication required'
                });
                return;
            }
            
            if (!req.admin.permissions.includes(permission)) {
                res.status(403).json({
                    success: false,
                    message: 'Insufficient permissions',
                    required: permission
                });
                return;
            }
            
            return originalMethod.call(this, req, res, next);
        };
        
        return descriptor;
    };
}

/**
 * 角色检查装饰器
 */
export function RequireRole(role: string) {
    return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        
        descriptor.value = function(req: Request, res: Response, next: NextFunction) {
            if (!req.admin) {
                res.status(401).json({
                    success: false,
                    message: 'Authentication required'
                });
                return;
            }
            
            if (req.admin.role !== role) {
                res.status(403).json({
                    success: false,
                    message: 'Insufficient role',
                    required: role,
                    current: req.admin.role
                });
                return;
            }
            
            return originalMethod.call(this, req, res, next);
        };
        
        return descriptor;
    };
}

/**
 * 权限工具函数
 */
export const PermissionUtils = {
    // 检查用户是否有指定权限
    hasPermission: (req: Request, permission: string): boolean => {
        return req.admin?.permissions.includes(permission) || false;
    },
    
    // 检查用户是否有任一权限
    hasAnyPermission: (req: Request, permissions: string[]): boolean => {
        return permissions.some(permission => req.admin?.permissions.includes(permission)) || false;
    },
    
    // 检查用户是否有所有权限
    hasAllPermissions: (req: Request, permissions: string[]): boolean => {
        return permissions.every(permission => req.admin?.permissions.includes(permission)) || false;
    },
    
    // 获取用户权限列表
    getUserPermissions: (req: Request): string[] => {
        return req.admin?.permissions || [];
    },
    
    // 检查用户角色
    hasRole: (req: Request, role: string): boolean => {
        return req.admin?.role === role;
    }
};

export default {
    createPermissionMiddleware,
    RequirePermission,
    RequireRole,
    PermissionUtils,
    ROUTE_PERMISSIONS
};
