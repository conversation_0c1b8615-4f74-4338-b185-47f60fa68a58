/**
 * 初始化版本控制配置脚本
 * 确保数据库中有正确的版本控制配置
 */

import mongoose from 'mongoose';
import { Config } from '../models/Config';
import { logger } from '../business/logger';
// import env from '../config/env'; // 不再需要

async function initVersionControl() {
    try {
        // 连接数据库
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor_test';
        await mongoose.connect(mongoUri);
        logger.info('Connected to MongoDB');

        // 查找现有配置
        let config = await Config.findOne();

        if (!config) {
            // 创建新配置
            config = new Config({
                privacyPolicy: 'https://advisor.sanva.tk/privacy.html',
                termsOfService: 'https://advisortest.sanva.tk/userTerms.html',
                appVersion: '1.0.0',
                supportEmail: '<EMAIL>',
                featureFlags: new Map([
                    ['enableRegistration', true],
                    ['enableChat', true],
                    ['enablePayment', true]
                ]),
                mainSolgan: new Map([
                    ['zh_CN', ['欢迎使用ChatAdvisor', '智能对话助手']],
                    ['en', ['Welcome to ChatAdvisor', 'Intelligent Chat Assistant']]
                ]),
                registerSolgan: new Map([
                    ['zh_CN', ['立即注册', '开始您的智能对话之旅']],
                    ['en', ['Register Now', 'Start Your Intelligent Chat Journey']]
                ]),
                emailLoginSolgan: new Map([
                    ['zh_CN', ['邮箱登录', '安全便捷']],
                    ['en', ['Email Login', 'Secure and Convenient']]
                ]),
                rechargeMessages: new Map([
                    ['zh_CN', ['充值成功', '余额已更新']],
                    ['en', ['Recharge Successful', 'Balance Updated']]
                ]),
                hideMessage: new Map([
                    ['zh_CN', ['消息已隐藏']],
                    ['en', ['Message Hidden']]
                ]),
                rechargeDescription: new Map([
                    ['zh_CN', '充值说明'],
                    ['en', 'Recharge Description']
                ]),
                promotLocal: new Map([
                    ['zh_CN', '本地推广'],
                    ['en', 'Local Promotion']
                ]),
                promotCloud: new Map([
                    ['zh_CN', '云端推广'],
                    ['en', 'Cloud Promotion']
                ]),
                compressRate: 0.3,
                // 版本控制配置
                latestVersion: '1.2.4',
                minimumVersion: '1.2.0',
                forceUpdate: false,
                updateMessage: new Map([
                    ['zh_CN', '发现新版本，建议立即更新以获得更好的体验'],
                    ['zh_TW', '發現新版本，建議立即更新以獲得更好的體驗'],
                    ['en', 'New version available, please update for better experience'],
                    ['ja', '新しいバージョンが利用可能です。より良い体験のためにアップデートしてください'],
                    ['ko', '새 버전을 사용할 수 있습니다. 더 나은 경험을 위해 업데이트하세요'],
                    ['es', 'Nueva versión disponible, actualice para una mejor experiencia'],
                    ['fr', 'Nouvelle version disponible, veuillez mettre à jour pour une meilleure expérience'],
                    ['de', 'Neue Version verfügbar, bitte aktualisieren Sie für eine bessere Erfahrung'],
                    ['ru', 'Доступна новая версия, обновите для лучшего опыта'],
                    ['ar', 'إصدار جديد متاح، يرجى التحديث للحصول على تجربة أفضل']
                ]),
                appStoreUrls: {
                    ios: 'https://apps.apple.com/us/app/chat-advisor/id6526465428',
                    android: 'https://play.google.com/store/apps/details?id=com.sanva.chatadvisor'
                },
                updateType: 'optional',
                versionCheckEnabled: true
            });

            await config.save();
            logger.info('Created new config with version control settings');
        } else {
            // 更新现有配置的版本控制字段
            const updateData: any = {};

            // 确保版本控制字段存在
            if (!config.latestVersion) {
                updateData.latestVersion = '1.2.4';
            }
            if (!config.minimumVersion) {
                updateData.minimumVersion = '1.2.0';
            }
            if (config.forceUpdate === undefined) {
                updateData.forceUpdate = false;
            }
            if (!config.updateMessage || (config.updateMessage instanceof Map && config.updateMessage.size === 0) || (typeof config.updateMessage === 'object' && Object.keys(config.updateMessage).length === 0)) {
                updateData.updateMessage = {
                    'zh_CN': '发现新版本，建议立即更新以获得更好的体验',
                    'zh_TW': '發現新版本，建議立即更新以獲得更好的體驗',
                    'en': 'New version available, please update for better experience',
                    'ja': '新しいバージョンが利用可能です。より良い体験のためにアップデートしてください',
                    'ko': '새 버전을 사용할 수 있습니다. 더 나은 경험을 위해 업데이트하세요',
                    'es': 'Nueva versión disponible, actualice para una mejor experiencia',
                    'fr': 'Nouvelle version disponible, veuillez mettre à jour pour une meilleure expérience',
                    'de': 'Neue Version verfügbar, bitte aktualisieren Sie für eine bessere Erfahrung',
                    'ru': 'Доступна новая версия, обновите для лучшего опыта',
                    'ar': 'إصدار جديد متاح، يرجى التحديث للحصول على تجربة أفضل'
                };
            }
            if (!config.appStoreUrls) {
                updateData.appStoreUrls = {
                    ios: 'https://apps.apple.com/us/app/chat-advisor/id6526465428',
                    android: 'https://play.google.com/store/apps/details?id=com.sanva.chatadvisor'
                };
            }
            if (!config.updateType) {
                updateData.updateType = 'optional';
            }
            if (config.versionCheckEnabled === undefined) {
                updateData.versionCheckEnabled = true;
            }

            if (Object.keys(updateData).length > 0) {
                await Config.updateOne({ _id: config._id }, { $set: updateData });
                logger.info('Updated existing config with version control settings');
            } else {
                logger.info('Config already has version control settings');
            }
        }

        // 验证配置
        const finalConfig = await Config.findOne();
        logger.info('Final version control config:', {
            latestVersion: finalConfig?.latestVersion,
            minimumVersion: finalConfig?.minimumVersion,
            forceUpdate: finalConfig?.forceUpdate,
            updateType: finalConfig?.updateType,
            versionCheckEnabled: finalConfig?.versionCheckEnabled,
            appStoreUrls: finalConfig?.appStoreUrls
        });

        logger.info('Version control initialization completed successfully');

    } catch (error) {
        logger.error('Version control initialization failed:', error);
        throw error;
    } finally {
        await mongoose.disconnect();
        logger.info('Disconnected from MongoDB');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    initVersionControl()
        .then(() => {
            logger.info('Script completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            logger.error('Script failed:', error);
            process.exit(1);
        });
}

export default initVersionControl;
