<!DOCTYPE html>
<html lang="{{lang}}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <!-- 引入 Bootstrap CSS -->
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入 FontAwesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .price-container {
            display: flex;
            align-items: center;
        }

        .price-icon {
            /* 调整图标和文字之间的间距 */
            width: 24px;
            /* 根据需要调整宽度 */
            height: 24px;
            /* 根据需要调整高度 */
            vertical-align: middle;
            margin-right: 5px;
            /* 调整图标与文字之间的间距 */
        }

        table {
            width: 100%;
            margin: 20px 0;
            border-collapse: collapse;
            table-layout: auto;
            /* 根据内容自动调整列宽 */
        }

        th,
        td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
            white-space: nowrap;
            /* 保持内容不换行 */
        }

        th {
            background-color: #f4f4f4;
        }

        /* 自定义媒体查询 */
        @media (max-width: 576px) {

            th,
            td {
                padding: 5px;
            }

            h1,
            h2 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 768px) {

            th,
            td {
                padding: 8px;
            }

            h1,
            h2 {
                font-size: 1.75rem;
            }
        }
    </style>
</head>

<body>
    <div class="container">
    
        <!-- 常见问题解答 -->
        <h2 class="my-4">{{#if pricingContent}}{{lookup pricingContent 'faqTitle'}}{{else}}Frequently Asked Questions
            (FAQ){{/if}}</h2>
        <div id="faq">
            {{#each faqContent}}
            <div class="faq-item mb-3">
                <h5 class="faq-question">{{this.question}}</h5>
                <p class="faq-answer">{{{this.answer}}}</p>
            </div>
            {{/each}}
        </div>

        <!-- 使用指南 -->
        {{!-- <h2 class="my-4">{{#if pricingContent}}{{lookup pricingContent 'userGuideTitle'}}{{else}}User Guide{{/if}}</h2>
        <div>
            {{{userGuideContent}}}
        </div> --}}


    <!-- 价格信息展示 -->
    <h2 class="my-4">{{#if pricingContent}}{{lookup pricingContent 'pricingTitle'}}{{else}}Pricing{{/if}}</h2>
    <div class="table-responsive">
        <div class="price-container">
            {{exchangeNote}} <img src="/images/money.png" alt="Price" class="price-icon">
        </div>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>{{#if pricingContent}}{{lookup pricingContent 'alias'}}{{else}}Alias{{/if}}</th>
                    <th>{{#if pricingContent}}{{lookup pricingContent 'inPrice'}}{{else}}In Price{{/if}}</th>
                    <th>{{#if pricingContent}}{{lookup pricingContent 'outPrice'}}{{else}}Out Price{{/if}}</th>
                    <th>{{#if pricingContent}}{{lookup pricingContent 'count'}}{{else}}Count{{/if}}</th>
                </tr>
            </thead>
            <tbody>
                {{#each pricingData}}
                <tr>
                    <td>{{lookup alias ../lang}}</td>
                    <td>
                        <div class="price-container">
                            <img src="/images/money.png" alt="Price" class="price-icon"> {{inPrice}}
                        </div>
                    </td>
                    <td>
                        <div class="price-container">
                            <img src="/images/money.png" alt="Price" class="price-icon"> {{outPrice}}
                        </div>
                    </td>
                    <td>{{count}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>
    </div>

        <!-- 联系我们 -->
        <h2 class="my-4">{{#if pricingContent}}{{lookup pricingContent 'contactUsTitle'}}{{else}}Contact Us{{/if}}</h2>
        <div>
            {{{contactUsContent}}}
        </div>
    </div>


    <!-- 引入 jQuery 和 Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>

</html>