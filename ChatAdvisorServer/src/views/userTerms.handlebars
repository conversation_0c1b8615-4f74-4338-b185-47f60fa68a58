<!DOCTYPE html>
<html lang="{{lang}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{{title}}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        h1, h2 {
            color: #444;
        }
        p, ul, li {
            margin: 10px 0;
        }
        ul {
            padding-left: 20px;
        }
        a {
            color: #1a73e8;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                padding: 10px;
            }
            h1 {
                font-size: 1.5em;
            }
            h2 {
                font-size: 1.2em;
            }
        }
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #121212;
                color: #e0e0e0;
            }
            .container {
                background-color: #1e1e1e;
                box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
            }
            h1, h2 {
                color: #e0e0e0;
            }
            a {
                color: #8ab4f8;
            }
            a:hover {
                text-decoration: underline;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        {{{userTermsContent}}}
        <p>{{lastUpdated}}</p>
    </div>
</body>
</html>
