/**
 * 应用常量定义
 */

// HTTP状态码常量
export const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500
} as const;

// 文件上传常量
export const UPLOAD_CONSTANTS = {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_AUDIO_TYPES: ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/m4a'],
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    UPLOAD_DIR: 'uploads'
} as const;

// 数据库常量
export const DB_CONSTANTS = {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    MONG<PERSON>B_REPLICA_SET: 'rs0'
} as const;

// 认证常量
export const AUTH_CONSTANTS = {
    TOKEN_HEADER: 'authorization',
    TOKEN_PREFIX: 'Bearer ',
    DEFAULT_TOKEN_EXPIRY: 7 * 24 * 60 * 60, // 7 days in seconds
    PASSWORD_MIN_LENGTH: 6,
    PASSWORD_MAX_LENGTH: 128,
    EMAIL_CODE_LENGTH: 6,
    EMAIL_CODE_EXPIRY: 10 * 60 * 1000 // 10 minutes in milliseconds
} as const;

// 聊天常量
export const CHAT_CONSTANTS = {
    MAX_MESSAGE_LENGTH: 10000,
    MAX_MESSAGES_PER_REQUEST: 50,
    DEFAULT_MODEL: 'gpt-3.5-turbo',
    SUPPORTED_MODELS: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'] as const
} as const;

// 错误消息常量
export const ERROR_MESSAGES = {
    VALIDATION_FAILED: 'Validation failed',
    UNAUTHORIZED: 'Unauthorized access',
    TOKEN_REQUIRED: 'Token is required',
    TOKEN_INVALID: 'Invalid token',
    TOKEN_EXPIRED: 'Token has expired',
    USER_NOT_FOUND: 'User not found',
    EMAIL_ALREADY_EXISTS: 'Email already exists',
    INVALID_CREDENTIALS: 'Invalid email or password',
    INSUFFICIENT_BALANCE: 'Insufficient balance',
    FILE_TOO_LARGE: 'File size exceeds limit',
    UNSUPPORTED_FILE_TYPE: 'Unsupported file type',
    INTERNAL_ERROR: 'Internal server error'
} as const;

// 正则表达式常量
export const REGEX_PATTERNS = {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE: /^\+?[\d\s\-\(\)]+$/,
    MONGODB_OBJECT_ID: /^[a-fA-F0-9]{24}$/,
    UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    NUMERIC_CODE: /^\d{6}$/,
    ALPHANUMERIC: /^[a-zA-Z0-9]+$/
} as const;

// 缓存常量
export const CACHE_CONSTANTS = {
    DEFAULT_TTL: 60 * 60, // 1 hour in seconds
    SHORT_TTL: 5 * 60, // 5 minutes
    LONG_TTL: 24 * 60 * 60, // 24 hours
    USER_SESSION_TTL: 30 * 60, // 30 minutes
    CONFIG_CACHE_TTL: 60 * 60 // 1 hour
} as const;

// 日志级别常量
export const LOG_LEVELS = {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug',
    SILLY: 'silly'
} as const;

// 支持的语言常量
export const SUPPORTED_LANGUAGES = [
    'en', 'zh', 'zh-CN', 'zh-TW', 'ja', 'ko', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ar', 'hi'
] as const;

// 第三方服务常量
export const THIRD_PARTY_SERVICES = {
    OPENAI: {
        DEFAULT_BASE_URL: 'https://api.openai.com/v1',
        DEFAULT_MODEL: 'gpt-3.5-turbo',
        MAX_TOKENS: 4096
    },
    GOOGLE: {
        OAUTH_SCOPE: ['email', 'profile']
    },
    APPLE: {
        TEAM_ID_LENGTH: 10,
        KEY_ID_LENGTH: 10
    }
} as const;

// 业务规则常量
export const BUSINESS_RULES = {
    DEFAULT_USER_BALANCE: 50,
    MIN_RECHARGE_AMOUNT: 1,
    MAX_RECHARGE_AMOUNT: 10000,
    PROFIT_RATE_MIN: 0,
    PROFIT_RATE_MAX: 1,
    EXCHANGE_RATE_MIN: 1,
    EXCHANGE_RATE_MAX: 1000
} as const;

// 环境常量
export const ENVIRONMENTS = {
    DEVELOPMENT: 'development',
    PRODUCTION: 'production',
    TEST: 'test',
    DEBUG: 'debug'
} as const;

// 导出所有常量的类型
export type HttpStatus = typeof HTTP_STATUS[keyof typeof HTTP_STATUS];
export type SupportedModel = typeof CHAT_CONSTANTS.SUPPORTED_MODELS[number];
export type SupportedLanguage = typeof SUPPORTED_LANGUAGES[number];
export type Environment = typeof ENVIRONMENTS[keyof typeof ENVIRONMENTS];

export default {
    HTTP_STATUS,
    UPLOAD_CONSTANTS,
    DB_CONSTANTS,
    AUTH_CONSTANTS,
    CHAT_CONSTANTS,
    ERROR_MESSAGES,
    REGEX_PATTERNS,
    CACHE_CONSTANTS,
    LOG_LEVELS,
    SUPPORTED_LANGUAGES,
    THIRD_PARTY_SERVICES,
    BUSINESS_RULES,
    ENVIRONMENTS
};
