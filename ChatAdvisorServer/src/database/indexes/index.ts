/**
 * 数据库索引管理
 */

import { User } from '../models/User';
import { logger } from '../../utils/logger';

/**
 * 创建所有数据库索引
 */
export async function createIndexes(): Promise<void> {
    try {
        logger.info('Creating database indexes...');

        // 用户索引
        await createUserIndexes();

        logger.info('Database indexes created successfully');
    } catch (error) {
        logger.error('Failed to create database indexes:', error);
        throw error;
    }
}

/**
 * 创建用户相关索引
 */
async function createUserIndexes(): Promise<void> {
    try {
        // 邮箱唯一索引
        await User.collection.createIndex(
            { email: 1 },
            { unique: true, background: true }
        );

        // 外部账户索引
        await User.collection.createIndex(
            { 'externalAccounts.appleInfo.sub': 1 },
            { sparse: true, background: true }
        );

        await User.collection.createIndex(
            { 'externalAccounts.googleInfo.sub': 1 },
            { sparse: true, background: true }
        );

        await User.collection.createIndex(
            { 'externalAccounts.facebookInfo.id': 1 },
            { sparse: true, background: true }
        );

        await User.collection.createIndex(
            { 'externalAccounts.twitterInfo.id': 1 },
            { sparse: true, background: true }
        );

        await User.collection.createIndex(
            { 'externalAccounts.tikTokInfo.open_id': 1 },
            { sparse: true, background: true }
        );

        // 业务查询索引
        await User.collection.createIndex(
            { isVip: 1, vipExpiredAt: 1 },
            { background: true }
        );

        await User.collection.createIndex(
            { balance: 1 },
            { background: true }
        );

        await User.collection.createIndex(
            { lastLoginAt: -1 },
            { background: true }
        );

        await User.collection.createIndex(
            { createdAt: -1 },
            { background: true }
        );

        await User.collection.createIndex(
            { emailVerified: 1 },
            { background: true }
        );

        await User.collection.createIndex(
            { isDeleted: 1 },
            { background: true }
        );

        // 复合索引
        await User.collection.createIndex(
            { isDeleted: 1, createdAt: -1 },
            { background: true }
        );

        await User.collection.createIndex(
            { isDeleted: 1, isVip: 1 },
            { background: true }
        );

        // 文本搜索索引 - 使用 'none' 作为默认语言以支持中文内容
        try {
            await User.collection.createIndex(
                {
                    email: 'text',
                    fullName: 'text',
                    username: 'text'
                },
                {
                    background: true,
                    default_language: 'none', // 避免语言特定处理，支持中文等非支持语言
                    weights: {
                        email: 10,
                        fullName: 5,
                        username: 3
                    },
                    name: 'user_text_search_index' // 指定索引名称以避免冲突
                }
            );
        } catch (error: any) {
            // 如果索引已存在，忽略错误
            if (error.code === 85 || error.codeName === 'IndexOptionsConflict') {
                logger.info('Text search index already exists, skipping creation');
            } else {
                throw error;
            }
        }

        logger.info('User indexes created successfully');
    } catch (error) {
        logger.error('Failed to create user indexes:', error);
        throw error;
    }
}

/**
 * 删除所有索引（仅用于开发环境）
 */
export async function dropIndexes(): Promise<void> {
    if (process.env.NODE_ENV === 'production') {
        throw new Error('Cannot drop indexes in production environment');
    }

    try {
        logger.warn('Dropping all database indexes...');

        await User.collection.dropIndexes();

        logger.info('Database indexes dropped successfully');
    } catch (error) {
        logger.error('Failed to drop database indexes:', error);
        throw error;
    }
}

/**
 * 获取索引信息
 */
export async function getIndexInfo(): Promise<any> {
    try {
        const userIndexes = await User.collection.listIndexes().toArray();

        return {
            users: userIndexes
        };
    } catch (error) {
        logger.error('Failed to get index info:', error);
        throw error;
    }
}

export default createIndexes;
