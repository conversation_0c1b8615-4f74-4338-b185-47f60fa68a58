/**
 * 数据库迁移管理器
 * 负责管理数据库版本和执行迁移脚本
 */

import { Schema, model, Document } from 'mongoose';
import { logger } from '../../utils/logger';
import fs from 'fs';
import path from 'path';

// 迁移记录接口
interface IMigration extends Document {
    name: string;
    version: number;
    executedAt: Date;
    checksum: string;
}

// 迁移记录Schema
const migrationSchema = new Schema<IMigration>({
    name: { type: String, required: true, unique: true },
    version: { type: Number, required: true },
    executedAt: { type: Date, default: Date.now },
    checksum: { type: String, required: true }
}, {
    collection: 'migrations',
    versionKey: false
});

const Migration = model<IMigration>('Migration', migrationSchema);

// 迁移脚本接口
export interface IMigrationScript {
    name: string;
    version: number;
    description: string;
    up(): Promise<void>;
    down(): Promise<void>;
}

export class MigrationManager {
    private migrationsPath: string;

    constructor(migrationsPath?: string) {
        this.migrationsPath = migrationsPath || path.join(__dirname, 'scripts');
    }

    /**
     * 运行待执行的迁移
     */
    public async runPendingMigrations(): Promise<void> {
        try {
            logger.info('Checking for pending migrations...');

            const pendingMigrations = await this.getPendingMigrations();
            
            if (pendingMigrations.length === 0) {
                logger.info('No pending migrations found');
                return;
            }

            logger.info(`Found ${pendingMigrations.length} pending migrations`);

            for (const migration of pendingMigrations) {
                await this.executeMigration(migration);
            }

            logger.info('All pending migrations executed successfully');

        } catch (error) {
            logger.error('Migration execution failed:', error);
            throw error;
        }
    }

    /**
     * 获取待执行的迁移
     */
    private async getPendingMigrations(): Promise<IMigrationScript[]> {
        const allMigrations = await this.loadMigrationScripts();
        const executedMigrations = await Migration.find().sort({ version: 1 });
        
        const executedVersions = new Set(executedMigrations.map(m => m.version));
        
        return allMigrations.filter(migration => !executedVersions.has(migration.version));
    }

    /**
     * 加载迁移脚本
     */
    private async loadMigrationScripts(): Promise<IMigrationScript[]> {
        const migrations: IMigrationScript[] = [];

        if (!fs.existsSync(this.migrationsPath)) {
            logger.warn(`Migrations directory not found: ${this.migrationsPath}`);
            return migrations;
        }

        const files = fs.readdirSync(this.migrationsPath)
            .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
            .sort();

        for (const file of files) {
            try {
                const filePath = path.join(this.migrationsPath, file);
                const migrationModule = await import(filePath);
                
                if (migrationModule.default && typeof migrationModule.default === 'object') {
                    migrations.push(migrationModule.default);
                }
            } catch (error) {
                logger.error(`Failed to load migration file ${file}:`, error);
                throw error;
            }
        }

        return migrations.sort((a, b) => a.version - b.version);
    }

    /**
     * 执行单个迁移
     */
    private async executeMigration(migration: IMigrationScript): Promise<void> {
        logger.info(`Executing migration: ${migration.name} (v${migration.version})`);

        try {
            // 执行迁移
            await migration.up();

            // 记录迁移执行
            await this.recordMigration(migration);

            logger.info(`Migration ${migration.name} executed successfully`);

        } catch (error) {
            logger.error(`Migration ${migration.name} failed:`, error);
            throw error;
        }
    }

    /**
     * 记录迁移执行
     */
    private async recordMigration(migration: IMigrationScript): Promise<void> {
        const checksum = this.calculateChecksum(migration);
        
        await Migration.create({
            name: migration.name,
            version: migration.version,
            checksum
        });
    }

    /**
     * 计算迁移校验和
     */
    private calculateChecksum(migration: IMigrationScript): string {
        const crypto = require('crypto');
        const content = migration.up.toString() + migration.down.toString();
        return crypto.createHash('md5').update(content).digest('hex');
    }

    /**
     * 回滚迁移
     */
    public async rollbackMigration(version: number): Promise<void> {
        logger.info(`Rolling back migration version: ${version}`);

        try {
            const migrationRecord = await Migration.findOne({ version });
            
            if (!migrationRecord) {
                throw new Error(`Migration version ${version} not found`);
            }

            const migrations = await this.loadMigrationScripts();
            const migration = migrations.find(m => m.version === version);

            if (!migration) {
                throw new Error(`Migration script for version ${version} not found`);
            }

            // 执行回滚
            await migration.down();

            // 删除迁移记录
            await Migration.deleteOne({ version });

            logger.info(`Migration version ${version} rolled back successfully`);

        } catch (error) {
            logger.error(`Rollback failed for version ${version}:`, error);
            throw error;
        }
    }

    /**
     * 获取迁移状态
     */
    public async getMigrationStatus(): Promise<any> {
        const allMigrations = await this.loadMigrationScripts();
        const executedMigrations = await Migration.find().sort({ version: 1 });
        
        const executedVersions = new Set(executedMigrations.map(m => m.version));
        
        return {
            total: allMigrations.length,
            executed: executedMigrations.length,
            pending: allMigrations.length - executedMigrations.length,
            migrations: allMigrations.map(migration => ({
                name: migration.name,
                version: migration.version,
                description: migration.description,
                executed: executedVersions.has(migration.version),
                executedAt: executedMigrations.find(m => m.version === migration.version)?.executedAt
            }))
        };
    }

    /**
     * 创建新的迁移文件
     */
    public async createMigration(name: string, description: string): Promise<string> {
        const version = Date.now();
        const fileName = `${version}_${name.replace(/\s+/g, '_').toLowerCase()}.ts`;
        const filePath = path.join(this.migrationsPath, fileName);

        const template = `/**
 * Migration: ${name}
 * Version: ${version}
 * Description: ${description}
 */

import { IMigrationScript } from '../MigrationManager';

const migration: IMigrationScript = {
    name: '${name}',
    version: ${version},
    description: '${description}',

    async up(): Promise<void> {
        // TODO: Implement migration logic
        console.log('Executing migration: ${name}');
    },

    async down(): Promise<void> {
        // TODO: Implement rollback logic
        console.log('Rolling back migration: ${name}');
    }
};

export default migration;
`;

        // 确保目录存在
        if (!fs.existsSync(this.migrationsPath)) {
            fs.mkdirSync(this.migrationsPath, { recursive: true });
        }

        fs.writeFileSync(filePath, template);
        
        logger.info(`Migration file created: ${fileName}`);
        return filePath;
    }

    /**
     * 验证迁移完整性
     */
    public async validateMigrations(): Promise<boolean> {
        try {
            const migrations = await this.loadMigrationScripts();
            const executedMigrations = await Migration.find();

            // 检查版本号唯一性
            const versions = migrations.map(m => m.version);
            const uniqueVersions = new Set(versions);
            
            if (versions.length !== uniqueVersions.size) {
                logger.error('Duplicate migration versions found');
                return false;
            }

            // 检查已执行迁移的校验和
            for (const executed of executedMigrations) {
                const migration = migrations.find(m => m.version === executed.version);
                
                if (!migration) {
                    logger.error(`Migration script not found for executed version: ${executed.version}`);
                    return false;
                }

                const currentChecksum = this.calculateChecksum(migration);
                if (currentChecksum !== executed.checksum) {
                    logger.error(`Checksum mismatch for migration: ${migration.name}`);
                    return false;
                }
            }

            logger.info('Migration validation passed');
            return true;

        } catch (error) {
            logger.error('Migration validation failed:', error);
            return false;
        }
    }
}

export default MigrationManager;
