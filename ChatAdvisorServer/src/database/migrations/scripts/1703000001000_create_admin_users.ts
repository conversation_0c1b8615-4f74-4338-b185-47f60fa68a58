/**
 * Migration: Create Admin Users
 * Version: 1703000001000
 * Description: Create default admin and moderator users for the system
 */

import { IMigrationScript } from '../MigrationManager';
import { createDefaultAdminUser } from '../createAdminUser';
import { logger } from '../../../utils/logger';

const migration: IMigrationScript = {
    name: 'Create Admin Users',
    version: 1703000001000,
    description: 'Create default admin and moderator users for the system',

    async up(): Promise<void> {
        try {
            logger.info('Creating default admin users...');
            await createDefaultAdminUser();
            logger.info('Default admin users created successfully');
        } catch (error) {
            logger.error('Failed to create admin users:', error);
            throw error;
        }
    },

    async down(): Promise<void> {
        try {
            logger.info('Removing default admin users...');
            
            // 动态导入User模型以避免循环依赖
            const { default: User } = await import('../../../models/User');
            
            // 删除默认管理员用户
            await User.deleteMany({
                email: { $in: ['<EMAIL>', '<EMAIL>'] },
                role: { $in: ['admin', 'super_admin'] }
            });
            
            logger.info('Default admin users removed successfully');
        } catch (error) {
            logger.error('Failed to remove admin users:', error);
            throw error;
        }
    }
};

export default migration;
