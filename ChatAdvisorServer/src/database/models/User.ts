/**
 * 用户模型
 * 重构后的用户数据模型，包含完整的类型定义和业务逻辑
 */

import { Schema, model, Types } from 'mongoose';
import { createBaseSchema, IBaseDocument, IBaseModel, commonFields } from './base/BaseModel';
import { defaultBalance } from '../../config/env';

// 外部账户信息接口
export interface IAppleInfo {
    iss: string;
    aud: string;
    exp: number;
    iat: number;
    sub: string;
    c_hash: string;
    email: string;
    email_verified: boolean;
    is_private_email?: boolean;
    auth_time: number;
    nonce_supported: boolean;
}

export interface IGoogleInfo {
    iss: string;
    azp: string;
    aud: string;
    sub: string;
    email: string;
    email_verified: boolean;
    at_hash: string;
    nonce: string;
    name: string;
    picture: string;
    given_name: string;
    family_name: string;
    iat: number;
    exp: number;
}

export interface IFacebookInfo {
    id: string;
    name: string;
    email: string;
    picture?: {
        data: {
            url: string;
        };
    };
}

export interface ITwitterInfo {
    id: string;
    name: string;
    screen_name: string;
    location: string;
    description: string;
    url: string;
    followers_count: number;
    friends_count: number;
    listed_count: number;
    created_at: string;
    favourites_count: number;
    verified: boolean;
    statuses_count: number;
    profile_image_url: string;
    email: string;
}

export interface ITikTokInfo {
    open_id: string;
    union_id: string;
    avatar_url: string;
    display_name: string;
    email?: string;
}

// 用户地址信息
export interface IAddress {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
}

// 外部账户绑定
export interface IExternalAccounts {
    weChatId?: string;
    qqId?: string;
    appleInfo?: IAppleInfo;
    googleInfo?: IGoogleInfo;
    facebookInfo?: IFacebookInfo;
    twitterInfo?: ITwitterInfo;
    tikTokInfo?: ITikTokInfo;
}

// 用户文档接口
export interface IUser extends IBaseDocument {
    // 基本信息
    username?: string;
    password?: string;
    email: string;
    fullName?: string;
    avatar?: string;
    
    // 个人信息
    birthDate?: Date;
    gender?: 'Male' | 'Female' | 'Other';
    phone?: string;
    address?: IAddress;
    
    // 系统设置
    language: string;
    timeZone?: string;
    currency: string;
    
    // 职业信息
    occupation?: string;
    company?: string;
    
    // 健康信息
    allergies?: string[];
    medicalConditions?: string[];
    
    // 账户信息
    balance: number;
    totalSpent: number;
    lastLoginAt?: Date;
    emailVerified: boolean;
    phoneVerified: boolean;
    
    // 外部账户
    externalAccounts?: IExternalAccounts;
    
    // 业务标记
    hasPurchase: boolean;
    isVip: boolean;
    vipExpiredAt?: Date;
    
    // 统计信息
    loginCount: number;
    chatCount: number;

    // 两步验证相关
    twoFactorSecret?: string;
    twoFactorEnabled: boolean;
    twoFactorBackupCodes?: string[];

    // 方法
    addBalance(amount: number): Promise<IUser>;
    deductBalance(amount: number): Promise<IUser>;
    updateLastLogin(): Promise<IUser>;
    verifyEmail(): Promise<IUser>;
    verifyPhone(): Promise<IUser>;
}

// 用户模型接口
export interface IUserModel extends IBaseModel<IUser> {
    findByEmail(email: string): Promise<IUser | null>;
    findByExternalAccount(provider: string, id: string): Promise<IUser | null>;
    findVipUsers(): Promise<IUser[]>;
    findByBalanceRange(min: number, max: number): Promise<IUser[]>;
}

// Schema定义
const userSchema = createBaseSchema<IUser>({
    // 基本信息
    username: {
        type: String,
        trim: true,
        minlength: 2,
        maxlength: 50
    },
    
    password: {
        type: String,
        minlength: 6,
        select: false // 默认不返回密码
    },
    
    email: {
        ...commonFields.email,
        unique: true
    },
    
    fullName: {
        type: String,
        trim: true,
        maxlength: 100
    },
    
    avatar: {
        type: String,
        validate: {
            validator: function(url: string) {
                return !url || /^https?:\/\/.+/.test(url);
            },
            message: 'Avatar must be a valid URL'
        }
    },
    
    // 个人信息
    birthDate: {
        type: Date,
        validate: {
            validator: function(date: Date) {
                return !date || date < new Date();
            },
            message: 'Birth date cannot be in the future'
        }
    },
    
    gender: {
        type: String,
        enum: ['Male', 'Female', 'Other']
    },
    
    phone: commonFields.phone,
    
    address: {
        street: String,
        city: String,
        state: String,
        country: String,
        postalCode: String
    },
    
    // 系统设置
    language: {
        ...commonFields.language,
        required: true
    },
    
    timeZone: String,
    currency: commonFields.currency,
    
    // 职业信息
    occupation: String,
    company: String,
    
    // 健康信息
    allergies: [String],
    medicalConditions: [String],
    
    // 账户信息
    balance: {
        type: Number,
        default: defaultBalance,
        min: 0
    },
    
    totalSpent: {
        type: Number,
        default: 0,
        min: 0
    },
    
    lastLoginAt: Date,
    emailVerified: { type: Boolean, default: false },
    phoneVerified: { type: Boolean, default: false },
    
    // 外部账户
    externalAccounts: {
        weChatId: { type: String, unique: true, sparse: true },
        qqId: { type: String, unique: true, sparse: true },
        appleInfo: {
            iss: String,
            aud: String,
            exp: Number,
            iat: Number,
            sub: String,
            c_hash: String,
            email: String,
            email_verified: Boolean,
            is_private_email: Boolean,
            auth_time: Number,
            nonce_supported: Boolean
        },
        googleInfo: {
            iss: String,
            azp: String,
            aud: String,
            sub: String,
            email: String,
            email_verified: Boolean,
            at_hash: String,
            nonce: String,
            name: String,
            picture: String,
            given_name: String,
            family_name: String,
            iat: Number,
            exp: Number
        },
        facebookInfo: {
            id: String,
            name: String,
            email: String,
            picture: {
                data: {
                    url: String
                }
            }
        },
        twitterInfo: {
            id: String,
            name: String,
            screen_name: String,
            location: String,
            description: String,
            url: String,
            followers_count: Number,
            friends_count: Number,
            listed_count: Number,
            created_at: String,
            favourites_count: Number,
            verified: Boolean,
            statuses_count: Number,
            profile_image_url: String,
            email: String
        },
        tikTokInfo: {
            open_id: String,
            union_id: String,
            avatar_url: String,
            display_name: String,
            email: String
        }
    },
    
    // 业务标记
    hasPurchase: { type: Boolean, default: false },
    isVip: { type: Boolean, default: false },
    vipExpiredAt: Date,
    
    // 统计信息
    loginCount: { type: Number, default: 0 },
    chatCount: { type: Number, default: 0 },

    // 两步验证相关
    twoFactorSecret: {
        type: String,
        select: false // 默认不返回密钥，确保安全
    },
    twoFactorEnabled: { type: Boolean, default: false },
    twoFactorBackupCodes: {
        type: [String],
        select: false // 默认不返回备用码，确保安全
    }
});

// 添加索引
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ 'externalAccounts.appleInfo.sub': 1 }, { sparse: true });
userSchema.index({ 'externalAccounts.googleInfo.sub': 1 }, { sparse: true });
userSchema.index({ isVip: 1, vipExpiredAt: 1 });
userSchema.index({ balance: 1 });
userSchema.index({ lastLoginAt: -1 });

// 静态方法
userSchema.statics.findByEmail = function(email: string) {
    return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findByExternalAccount = function(provider: string, id: string) {
    const query: any = {};
    query[`externalAccounts.${provider}.sub`] = id;
    return this.findOne(query);
};

userSchema.statics.findVipUsers = function() {
    return this.find({
        isVip: true,
        $or: [
            { vipExpiredAt: { $exists: false } },
            { vipExpiredAt: { $gt: new Date() } }
        ]
    });
};

userSchema.statics.findByBalanceRange = function(min: number, max: number) {
    return this.find({ balance: { $gte: min, $lte: max } });
};

// 实例方法
userSchema.methods.addBalance = function(amount: number) {
    this.balance += amount;
    return this.save();
};

userSchema.methods.deductBalance = function(amount: number) {
    if (this.balance < amount) {
        throw new Error('Insufficient balance');
    }
    this.balance -= amount;
    this.totalSpent += amount;
    return this.save();
};

userSchema.methods.updateLastLogin = function() {
    this.lastLoginAt = new Date();
    this.loginCount += 1;
    return this.save();
};

userSchema.methods.verifyEmail = function() {
    this.emailVerified = true;
    return this.save();
};

userSchema.methods.verifyPhone = function() {
    this.phoneVerified = true;
    return this.save();
};

// 中间件
userSchema.pre('save', function(next) {
    if (this.isModified('email')) {
        this.email = this.email.toLowerCase();
    }
    next();
});

// 创建模型
export const User = model<IUser, IUserModel>('User', userSchema);

export default User;
