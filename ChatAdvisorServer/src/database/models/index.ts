/**
 * 数据库模型注册
 */

import { logger } from '../../utils/logger';

// 导入所有模型
import { User } from './User';
// 可以在这里导入其他模型
// import { Config } from './Config';
// import { Product } from './Product';

/**
 * 注册所有数据库模型
 */
export async function registerModels(): Promise<void> {
    try {
        logger.info('Registering database models...');

        // 模型会在导入时自动注册到mongoose
        // 这里可以添加额外的模型初始化逻辑

        // 验证模型注册
        const registeredModels = [
            'User'
            // 'Config',
            // 'Product'
        ];

        for (const modelName of registeredModels) {
            const mongoose = require('mongoose');
            if (!mongoose.models[modelName]) {
                throw new Error(`Model ${modelName} not registered`);
            }
        }

        logger.info(`Successfully registered ${registeredModels.length} models`);
    } catch (error) {
        logger.error('Failed to register models:', error);
        throw error;
    }
}

// 导出所有模型
export {
    User
};

export default registerModels;
