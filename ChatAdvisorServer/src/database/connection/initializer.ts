/**
 * 数据库初始化器
 * 负责数据库连接、模型注册、索引创建和数据迁移
 */

import { DatabaseManager } from './DatabaseManager';
import { getDatabaseConfig, validateDatabaseConfig } from './config';
import { logger } from '../../utils/logger';
import { MigrationManager } from '../migrations/MigrationManager';
import { registerModels } from '../models';
import { createIndexes } from '../indexes';

export class DatabaseInitializer {
    private databaseManager: DatabaseManager;
    private migrationManager: MigrationManager;
    private isInitialized = false;

    constructor() {
        const config = getDatabaseConfig();
        validateDatabaseConfig(config);
        
        this.databaseManager = DatabaseManager.getInstance(config);
        this.migrationManager = new MigrationManager();
    }

    /**
     * 初始化数据库
     */
    public async initialize(): Promise<void> {
        if (this.isInitialized) {
            logger.info('Database already initialized');
            return;
        }

        try {
            logger.info('Starting database initialization...');

            // 1. 连接数据库
            await this.connectDatabase();

            // 2. 注册模型
            await this.registerModels();

            // 3. 创建索引
            await this.createIndexes();

            // 4. 运行迁移
            await this.runMigrations();

            // 5. 验证连接
            await this.validateConnection();

            this.isInitialized = true;
            logger.info('Database initialization completed successfully');

        } catch (error) {
            logger.error('Database initialization failed:', error);
            throw error;
        }
    }

    /**
     * 连接数据库
     */
    private async connectDatabase(): Promise<void> {
        logger.info('Connecting to database...');
        await this.databaseManager.connect();
    }

    /**
     * 注册模型
     */
    private async registerModels(): Promise<void> {
        logger.info('Registering database models...');
        await registerModels();
        logger.info('Database models registered successfully');
    }

    /**
     * 创建索引
     */
    private async createIndexes(): Promise<void> {
        logger.info('Creating database indexes...');
        await createIndexes();
        logger.info('Database indexes created successfully');
    }

    /**
     * 运行迁移
     */
    private async runMigrations(): Promise<void> {
        logger.info('Running database migrations...');
        await this.migrationManager.runPendingMigrations();
        logger.info('Database migrations completed');
    }

    /**
     * 验证连接
     */
    private async validateConnection(): Promise<void> {
        logger.info('Validating database connection...');
        const isHealthy = await this.databaseManager.healthCheck();
        
        if (!isHealthy) {
            throw new Error('Database health check failed');
        }
        
        logger.info('Database connection validated successfully');
    }

    /**
     * 关闭数据库连接
     */
    public async shutdown(): Promise<void> {
        logger.info('Shutting down database...');
        await this.databaseManager.disconnect();
        this.isInitialized = false;
        logger.info('Database shutdown completed');
    }

    /**
     * 获取数据库管理器
     */
    public getDatabaseManager(): DatabaseManager {
        return this.databaseManager;
    }

    /**
     * 获取迁移管理器
     */
    public getMigrationManager(): MigrationManager {
        return this.migrationManager;
    }

    /**
     * 检查是否已初始化
     */
    public isReady(): boolean {
        return this.isInitialized && this.databaseManager.getConnectionState() === 'connected';
    }

    /**
     * 重置数据库（仅用于测试环境）
     */
    public async reset(): Promise<void> {
        const env = process.env.NODE_ENV;
        
        if (env === 'production') {
            throw new Error('Database reset is not allowed in production environment');
        }

        logger.warn('Resetting database...');
        
        const db = this.databaseManager.getDatabase();
        if (db) {
            await db.dropDatabase();
            logger.info('Database reset completed');
        }
    }

    /**
     * 获取数据库统计信息
     */
    public async getStats(): Promise<any> {
        try {
            const db = this.databaseManager.getDatabase();
            if (!db) {
                throw new Error('Database not connected');
            }

            const stats = await db.stats();
            const connectionState = this.databaseManager.getConnectionState();
            
            return {
                connectionState,
                collections: stats.collections,
                dataSize: stats.dataSize,
                storageSize: stats.storageSize,
                indexes: stats.indexes,
                indexSize: stats.indexSize,
                objects: stats.objects
            };
        } catch (error) {
            logger.error('Failed to get database stats:', error);
            throw error;
        }
    }
}

// 单例实例
let databaseInitializer: DatabaseInitializer | null = null;

/**
 * 获取数据库初始化器实例
 */
export function getDatabaseInitializer(): DatabaseInitializer {
    if (!databaseInitializer) {
        databaseInitializer = new DatabaseInitializer();
    }
    return databaseInitializer;
}

/**
 * 初始化数据库（便捷方法）
 */
export async function initializeDatabase(): Promise<DatabaseInitializer> {
    const initializer = getDatabaseInitializer();
    await initializer.initialize();
    return initializer;
}

export default DatabaseInitializer;
