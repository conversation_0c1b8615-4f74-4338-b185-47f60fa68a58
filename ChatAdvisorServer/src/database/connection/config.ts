/**
 * 数据库配置管理
 * 根据环境提供不同的数据库配置
 */

import { DatabaseConfig } from './DatabaseManager';

export interface EnvironmentConfig {
    development: DatabaseConfig;
    test: DatabaseConfig;
    production: DatabaseConfig;
}

/**
 * 获取当前环境
 */
export function getCurrentEnvironment(): string {
    return process.env.NODE_ENV || 'development';
}

/**
 * 获取数据库URI - 简化配置，只使用MONGODB_URI
 */
function getDatabaseUri(): string {
    // 如果环境变量中有MONGODB_URI，直接使用
    if (process.env.MONGODB_URI) {
        return process.env.MONGODB_URI;
    }

    // 否则根据环境提供默认值
    const env = getCurrentEnvironment();
    if (env === 'production') {
        return 'mongodb://localhost:27017/ChatAdvisor?replicaSet=rs0';
    } else {
        return 'mongodb://localhost:27017/ChatAdvisor_test';
    }
}

/**
 * 数据库环境配置
 */
export const databaseConfigs: EnvironmentConfig = {
    development: {
        uri: getDatabaseUri(),
        options: {
            maxPoolSize: 5,
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
            bufferCommands: false,
        },
        retryAttempts: 3,
        retryDelay: 3000
    },

    test: {
        uri: getDatabaseUri(),
        options: {
            maxPoolSize: 2,
            serverSelectionTimeoutMS: 3000,
            socketTimeoutMS: 30000,
            bufferCommands: false,
        },
        retryAttempts: 1,
        retryDelay: 1000
    },

    production: {
        uri: getDatabaseUri(),
        options: {
            maxPoolSize: 20,
            serverSelectionTimeoutMS: 10000,
            socketTimeoutMS: 60000,
            bufferCommands: false,
            // 生产环境额外配置
            compressors: ['zlib'],
        },
        retryAttempts: 10,
        retryDelay: 5000
    }
};

/**
 * 获取当前环境的数据库配置
 */
export function getDatabaseConfig(): DatabaseConfig {
    const env = getCurrentEnvironment() as keyof EnvironmentConfig;
    const config = databaseConfigs[env];
    
    if (!config) {
        throw new Error(`No database configuration found for environment: ${env}`);
    }

    if (!config.uri) {
        throw new Error(`Database URI not configured for environment: ${env}`);
    }

    return config;
}

/**
 * 验证数据库配置
 */
export function validateDatabaseConfig(config: DatabaseConfig): void {
    if (!config.uri) {
        throw new Error('Database URI is required');
    }

    try {
        new URL(config.uri);
    } catch (error) {
        throw new Error('Invalid database URI format');
    }

    if (config.retryAttempts && config.retryAttempts < 0) {
        throw new Error('Retry attempts must be non-negative');
    }

    if (config.retryDelay && config.retryDelay < 0) {
        throw new Error('Retry delay must be non-negative');
    }
}

/**
 * 数据库连接字符串构建器
 */
export class DatabaseUriBuilder {
    private host: string = 'localhost';
    private port: number = 27017;
    private database: string = 'ChatAdvisor';
    private username?: string;
    private password?: string;
    private options: Record<string, string> = {};

    setHost(host: string): this {
        this.host = host;
        return this;
    }

    setPort(port: number): this {
        this.port = port;
        return this;
    }

    setDatabase(database: string): this {
        this.database = database;
        return this;
    }

    setCredentials(username: string, password: string): this {
        this.username = username;
        this.password = password;
        return this;
    }

    addOption(key: string, value: string): this {
        this.options[key] = value;
        return this;
    }

    setReplicaSet(replicaSet: string): this {
        this.options.replicaSet = replicaSet;
        return this;
    }

    build(): string {
        let uri = 'mongodb://';
        
        if (this.username && this.password) {
            uri += `${encodeURIComponent(this.username)}:${encodeURIComponent(this.password)}@`;
        }
        
        uri += `${this.host}:${this.port}/${this.database}`;
        
        const optionString = Object.entries(this.options)
            .map(([key, value]) => `${key}=${value}`)
            .join('&');
            
        if (optionString) {
            uri += `?${optionString}`;
        }
        
        return uri;
    }
}

export default getDatabaseConfig;
