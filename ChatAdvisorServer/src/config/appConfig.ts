import 'dotenv/config';

/**
 * 应用配置接口
 */
interface AppConfig {
    // 服务器配置
    server: {
        port: number;
        nodeEnv: string;
        isProduction: boolean;
        isDevelopment: boolean;
        isTest: boolean;
    };
    
    // 数据库配置
    database: {
        mongoUri: string;
    };
    
    // JWT配置
    jwt: {
        secret: string;
        expiresIn: number;
    };
    
    // OpenAI配置
    openai: {
        apiKey: string;
        baseUrl: string;
    };
    
    // 第三方登录配置
    oauth: {
        google: {
            clientId: string;
        };
        twitter: {
            apiKey: string;
            apiSecret: string;
        };
        tiktok: {
            clientId: string;
            clientSecret: string;
        };
        facebook: {
            appId: string;
            appSecret: string;
        };
    };
    
    // 应用业务配置
    app: {
        baseUrl: string;
        appVersion: string;
        supportEmail: string;
        defaultBalance: number;
        profitRate: number;
        exchangeRate: number;
        needToEncrypt: boolean;
    };
    
    // CORS配置
    cors: {
        allowedOrigins: string[];
    };
    
    // 邮件配置
    email?: {
        smtpHost: string;
        smtpPort: number;
        smtpUser: string;
        smtpPass: string;
    };
}

/**
 * 验证必需的环境变量
 */
function validateRequiredEnvVars(): void {
    const requiredVars = [
        'JWT_SECRET',
        'OPENAI_API_KEY'
    ];
    
    const missingVars = requiredVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
        console.error(`Missing required environment variables: ${missingVars.join(', ')}`);
        console.error('Please check your .env file and ensure all required variables are set.');
        process.exit(1);
    }
}

/**
 * 解析环境变量为布尔值
 */
function parseBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
    if (!value) return defaultValue;
    return value.toLowerCase() === 'true' || value === '1';
}

/**
 * 解析环境变量为数字
 */
function parseNumber(value: string | undefined, defaultValue: number): number {
    if (!value) return defaultValue;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 解析环境变量为浮点数
 */
function parseFloat(value: string | undefined, defaultValue: number): number {
    if (!value) return defaultValue;
    const parsed = Number.parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 解析逗号分隔的字符串为数组
 */
function parseArray(value: string | undefined, defaultValue: string[] = []): string[] {
    if (!value) return defaultValue;
    return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
}

/**
 * 创建应用配置
 */
function createAppConfig(): AppConfig {
    // 验证必需的环境变量
    validateRequiredEnvVars();
    
    const nodeEnv = process.env.NODE_ENV || 'development';
    
    const config: AppConfig = {
        server: {
            port: parseNumber(process.env.PORT, nodeEnv === 'production' ? 53011 : 33001),
            nodeEnv,
            isProduction: nodeEnv === 'production',
            isDevelopment: nodeEnv === 'development',
            isTest: nodeEnv === 'test'
        },
        
        database: {
            mongoUri: (() => {
                // 如果环境变量中有MONGODB_URI，直接使用
                if (process.env.MONGODB_URI) {
                    return process.env.MONGODB_URI;
                }

                // 否则根据环境提供默认值
                if (nodeEnv === 'production') {
                    return 'mongodb://localhost:27017/ChatAdvisor?replicaSet=rs0';
                } else {
                    return 'mongodb://localhost:27017/ChatAdvisor_test';
                }
            })()
        },
        
        jwt: {
            secret: process.env.JWT_SECRET!,
            expiresIn: parseNumber(process.env.JWT_EXPIRES_IN, 604800) // 7 days
        },
        
        openai: {
            apiKey: process.env.OPENAI_API_KEY!,
            baseUrl: process.env.OPENAI_BASE_URL || 'https://api.x.ai/v1/'
        },
        
        oauth: {
            google: {
                clientId: process.env.GOOGLE_CLIENT_ID || ''
            },
            twitter: {
                apiKey: process.env.TWITTER_API_KEY || '',
                apiSecret: process.env.TWITTER_API_SECRET || ''
            },
            tiktok: {
                clientId: process.env.TIKTOK_CLIENT_ID || '',
                clientSecret: process.env.TIKTOK_CLIENT_SECRET || ''
            },
            facebook: {
                appId: process.env.FACEBOOK_APP_ID || '',
                appSecret: process.env.FACEBOOK_APP_SECRET || ''
            }
        },
        
        app: {
            baseUrl: process.env.BASE_URL || 'https://advisor.sanva.tk',
            appVersion: process.env.APP_VERSION || '1.0',
            supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
            defaultBalance: parseNumber(process.env.DEFAULT_BALANCE, 50),
            profitRate: parseFloat(process.env.PROFIT_RATE, 0.045),
            exchangeRate: parseNumber(process.env.EXCHANGE_RATE, 100),
            needToEncrypt: parseBoolean(process.env.NEED_TO_ENCRYPT, false)
        },
        
        cors: {
            allowedOrigins: parseArray(
                process.env.ALLOWED_ORIGINS, 
                ['http://localhost:3000', 'https://advisor.sanva.tk', 'https://advisor.sanva.top', 'https://admin.sanva.top']
            )
        }
    };
    
    // 可选的邮件配置
    if (process.env.SMTP_HOST) {
        config.email = {
            smtpHost: process.env.SMTP_HOST,
            smtpPort: parseNumber(process.env.SMTP_PORT, 587),
            smtpUser: process.env.SMTP_USER || '',
            smtpPass: process.env.SMTP_PASS || ''
        };
    }
    
    return config;
}

// 创建并导出配置实例
export const appConfig = createAppConfig();

// 导出配置类型
export type { AppConfig };

// 记录配置加载信息（延迟到logger可用时）
console.log('Application configuration loaded successfully', {
    nodeEnv: appConfig.server.nodeEnv,
    port: appConfig.server.port,
    hasEmailConfig: !!appConfig.email
});

export default appConfig;
