import path from 'path'; // 导入 path 模块
import i18next from 'i18next';
import Backend from 'i18next-fs-backend';
import { LanguageDetector } from 'i18next-http-middleware';

const langDir = path.join(process.cwd(), 'locales/api'); // 构建多语言文件目录的绝对路径

i18next
    .use(Backend)
    .use(LanguageDetector)
    .init({
        backend: {
            loadPath: `${langDir}/{{lng}}.json` // 使用构建的多语言文件目录路径
        },
        fallbackLng: 'en',
        preload: ['en', 'de', 'zh']
    });

export default i18next;
