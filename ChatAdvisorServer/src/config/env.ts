import crypto from 'crypto';
import dotenv from 'dotenv';
import path from 'path';

// 根据 NODE_ENV 加载对应的环境文件
const nodeEnv = process.env.NODE_ENV || 'development';
const envFile = `.env.${nodeEnv}`;

// 先加载特定环境的配置
dotenv.config({ path: path.resolve(process.cwd(), envFile) });
// 再加载通用配置作为后备
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

// 使用已经声明的 nodeEnv 变量

// 兼容性导出 - 逐步迁移到新的配置系统
const env = {
    isProduction: nodeEnv === 'production',
    isDevelopment: nodeEnv === 'development',
    isTest: nodeEnv === 'test',
    isRelease: nodeEnv === 'production'
};

// 加密
export const needToEncrypt = parseInt(process.env.NEED_TO_ENCRYPT || '0');
// 端口
export const port = process.env.PORT || '3000';

// 秘钥 - 使用相同的编码格式和哈希算法保持兼容性
const originalSecretKey = process.env.JWT_SECRET || 'default-secret-key-change-in-production';
const hash = crypto.createHash('sha256');
hash.update(originalSecretKey);
export const secretKey = hash.digest('hex');

// token有效期
export const tokenExpiresIn = parseInt(process.env.JWT_EXPIRES_IN || '604800');

// 创建用户时给的默认余额
export const defaultBalance = parseInt(process.env.DEFAULT_BALANCE || '50');

// 根据环境自动选择数据库URI - 简化配置，只使用MONGODB_URI
export const ENV_MONGODB_URI = (() => {
    // 如果环境变量中有MONGODB_URI，直接使用
    if (process.env.MONGODB_URI) {
        return process.env.MONGODB_URI;
    }

    // 否则根据环境提供默认值
    if (env.isProduction) {
        // 生产环境默认使用副本集
        return 'mongodb://localhost:27017/ChatAdvisor?replicaSet=rs0';
    } else {
        // 开发/测试环境使用测试数据库
        return 'mongodb://localhost:27017/ChatAdvisor_test';
    }
})();

// 利润
export const profitRate = parseFloat(process.env.PROFIT_RATE || '0.045');
// 汇率
export const exchangeRate = parseInt(process.env.EXCHANGE_RATE || '100');

export const googleClientId = process.env.GOOGLE_CLIENT_ID || '';

export const twitterApiKey = process.env.TWITTER_API_KEY || '';
export const twitterApiSecret = process.env.TWITTER_API_SECRET || '';

export const tiktokClientId = process.env.TIKTOK_CLIENT_ID || '';
export const tiktokClientSecret = process.env.TIKTOK_CLIENT_SECRET || '';

export default env;

