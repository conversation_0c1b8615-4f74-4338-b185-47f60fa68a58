import { ErrorRequestH<PERSON><PERSON>, Request, Response, NextFunction } from 'express';
import ErrorLog from '../models/ErrorLog';
import RequestLog from '../models/RequestLog';
import env from '../config/env';
import { logger } from '../business/logger';
import { HttpStatusCode } from 'axios';
import mongoose from 'mongoose';

// 定义标准错误响应格式
interface ErrorResponse {
    code: number;
    message: string;
    data: null;
    timestamp: string;
    path: string;
    requestId?: string;
    stack?: string;
}

// 自定义错误类
export class AppError extends Error {
    public statusCode: number;
    public isOperational: boolean;

    constructor(message: string, statusCode: number = HttpStatusCode.InternalServerError) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;

        Error.captureStackTrace(this, this.constructor);
    }
}

// 处理不同类型的错误
function handleError(err: any): { statusCode: number; message: string } {
    let statusCode = HttpStatusCode.InternalServerError;
    let message = 'Internal Server Error';

    // 自定义应用错误
    if (err instanceof AppError) {
        statusCode = err.statusCode;
        message = err.message;
    }
    // MongoDB错误
    else if (err.name === 'ValidationError') {
        statusCode = HttpStatusCode.BadRequest;
        message = Object.values(err.errors).map((val: any) => val.message).join(', ');
    }
    else if (err.name === 'CastError') {
        statusCode = HttpStatusCode.BadRequest;
        message = 'Invalid data format';
    }
    else if (err.code === 11000) {
        statusCode = HttpStatusCode.Conflict;
        message = 'Duplicate field value';
    }
    // JWT错误
    else if (err.name === 'JsonWebTokenError') {
        statusCode = HttpStatusCode.Unauthorized;
        message = 'Invalid token';
    }
    else if (err.name === 'TokenExpiredError') {
        statusCode = HttpStatusCode.Unauthorized;
        message = 'Token expired';
    }
    // 其他HTTP错误
    else if (err.code || err.status || err.statusCode) {
        statusCode = parseInt(err.code) || err.status || err.statusCode || HttpStatusCode.InternalServerError;
        message = err.message || 'Unknown error';
    }
    // 默认错误
    else if (err.message) {
        message = err.message;
    }

    return { statusCode, message };
}

const errorHandler: ErrorRequestHandler = async (err, req: Request, res: Response, next: NextFunction) => {
    const { statusCode, message } = handleError(err);

    // 生成请求ID（如果不存在）
    const requestId = (req as any).logId || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 记录错误日志
    logger.error(`Error occurred:`, {
        requestId,
        path: req.path,
        method: req.method,
        statusCode,
        message,
        stack: err.stack,
        userAgent: req.get('User-Agent'),
        ip: req.ip
    });

    // 在生产环境中保存错误日志到数据库
    if (env.isRelease) {
        try {
            const errorLogEntry = new ErrorLog({
                requestId,
                error: message,
                errorCode: statusCode,
                stack: err.stack,
                path: req.path,
                method: req.method,
                userAgent: req.get('User-Agent'),
                ip: req.ip
            });
            await errorLogEntry.save();
        } catch (logError) {
            logger.error('Failed to save error log to database:', logError);
        }
    }

    // 构建错误响应
    const errorResponse: ErrorResponse = {
        code: statusCode,
        message: message,
        data: null,
        timestamp: new Date().toISOString(),
        path: req.path,
        requestId
    };

    // 在开发环境中包含堆栈信息
    if (!env.isRelease) {
        errorResponse.stack = err.stack;
    }

    // 发送错误响应
    // 注意：某些特殊端点可能需要不同的状态码处理
    const httpStatusCode = req.url === '/refund' ? statusCode : HttpStatusCode.Ok;

    res.status(httpStatusCode).json(errorResponse);
};

export default errorHandler;
