/**
 * 统一验证规则管理
 * 消除重复的验证规则定义，提供一致的验证标准
 */

import { ValidationRule } from './validationHandler';

/**
 * 通用验证规则
 * 这些规则可以在多个路由中复用
 */
export const commonValidationRules = {
    // 邮箱验证规则
    email: {
        field: 'email',
        required: true,
        type: 'string' as const,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        maxLength: 255,
        sanitize: true,
        errorMessage: '请输入有效的邮箱地址'
    },

    // 密码验证规则
    password: {
        field: 'password',
        required: true,
        type: 'string' as const,
        minLength: 6,
        maxLength: 128,
        sanitize: false,
        errorMessage: '密码长度必须在6-128个字符之间'
    },

    // 确认密码验证规则
    confirmPassword: {
        field: 'confirmPassword',
        required: true,
        type: 'string' as const,
        minLength: 6,
        maxLength: 128,
        sanitize: false,
        errorMessage: '确认密码长度必须在6-128个字符之间'
    },

    // 6位数字验证码规则
    verificationCode: {
        field: 'code',
        required: true,
        type: 'string' as const,
        pattern: /^\d{6}$/,
        sanitize: true,
        errorMessage: '验证码必须是6位数字'
    },

    // 用户ID验证规则
    userId: {
        field: 'userId',
        required: true,
        type: 'string' as const,
        pattern: /^[a-fA-F0-9]{24}$/,
        sanitize: true,
        errorMessage: '用户ID格式无效'
    },

    // 聊天消息验证规则
    messages: {
        field: 'messages',
        required: true,
        type: 'array' as const,
        sanitize: true,
        errorMessage: '消息内容不能为空'
    },

    // 模型名称验证规则
    model: {
        field: 'model',
        required: false,
        type: 'string' as const,
        maxLength: 100,
        sanitize: true,
        errorMessage: '模型名称长度不能超过100个字符'
    },

    // 流式传输标志验证规则
    stream: {
        field: 'stream',
        required: false,
        type: 'boolean' as const,
        errorMessage: 'stream参数必须是布尔值'
    },

    // 第三方登录token验证规则
    idToken: {
        field: 'idToken',
        required: true,
        type: 'string' as const,
        minLength: 10,
        sanitize: true,
        errorMessage: 'ID Token不能为空且长度必须大于10个字符'
    },

    // 应用ID验证规则
    appId: {
        field: 'appId',
        required: true,
        type: 'string' as const,
        minLength: 5,
        sanitize: true,
        errorMessage: '应用ID不能为空且长度必须大于5个字符'
    },

    // Facebook访问令牌验证规则
    accessToken: {
        field: 'accessToken',
        required: true,
        type: 'string' as const,
        minLength: 10,
        sanitize: true,
        errorMessage: 'Access Token不能为空且长度必须大于10个字符'
    }
};

/**
 * 组合验证规则
 * 为常见的业务场景提供预定义的验证规则组合
 */
export const validationRuleGroups = {
    // 登录验证规则组合
    login: [
        commonValidationRules.email,
        commonValidationRules.password
    ],

    // 注册验证规则组合
    register: [
        commonValidationRules.email,
        commonValidationRules.password,
        commonValidationRules.confirmPassword
    ],

    // 邮箱验证码验证规则组合
    emailCodeVerification: [
        commonValidationRules.email,
        commonValidationRules.verificationCode
    ],

    // 发送邮箱验证码规则组合
    sendEmailCode: [
        commonValidationRules.email
    ],

    // 聊天消息验证规则组合
    chat: [
        commonValidationRules.messages,
        commonValidationRules.model,
        commonValidationRules.stream
    ],

    // Apple登录验证规则组合
    appleLogin: [
        commonValidationRules.idToken,
        commonValidationRules.appId
    ],

    // Google登录验证规则组合
    googleLogin: [
        commonValidationRules.idToken
    ],

    // Facebook登录验证规则组合
    facebookLogin: [
        commonValidationRules.accessToken
    ]
};

/**
 * 验证规则工厂函数
 * 根据业务场景动态创建验证规则
 */
export class ValidationRuleFactory {
    /**
     * 创建自定义验证规则
     * @param field 字段名
     * @param options 验证选项
     */
    static createRule(field: string, options: Partial<ValidationRule>): ValidationRule {
        return {
            field,
            required: options.required ?? true,
            type: options.type ?? 'string',
            minLength: options.minLength,
            maxLength: options.maxLength,
            pattern: options.pattern,
            sanitize: options.sanitize ?? true,
            errorMessage: options.errorMessage ?? `${field}字段验证失败`
        };
    }

    /**
     * 创建数组验证规则
     * @param field 字段名
     * @param minItems 最小项目数
     * @param maxItems 最大项目数
     */
    static createArrayRule(field: string, minItems?: number, maxItems?: number): ValidationRule {
        return {
            field,
            required: true,
            type: 'array',
            minLength: minItems,
            maxLength: maxItems,
            sanitize: true,
            errorMessage: `${field}必须是有效的数组`
        };
    }

    /**
     * 创建数字验证规则
     * @param field 字段名
     * @param min 最小值
     * @param max 最大值
     */
    static createNumberRule(field: string, min?: number, max?: number): ValidationRule {
        return {
            field,
            required: true,
            type: 'number',
            minLength: min,
            maxLength: max,
            sanitize: true,
            errorMessage: `${field}必须是有效的数字`
        };
    }

    /**
     * 创建可选字段验证规则
     * @param baseRule 基础规则
     */
    static createOptionalRule(baseRule: ValidationRule): ValidationRule {
        return {
            ...baseRule,
            required: false
        };
    }
}

/**
 * 验证规则助手函数
 */
export const validationHelpers = {
    /**
     * 合并多个验证规则组
     * @param groups 验证规则组数组
     */
    mergeRuleGroups: (...groups: ValidationRule[][]): ValidationRule[] => {
        return groups.flat();
    },

    /**
     * 过滤验证规则
     * @param rules 原始规则数组
     * @param predicate 过滤条件
     */
    filterRules: (rules: ValidationRule[], predicate: (rule: ValidationRule) => boolean): ValidationRule[] => {
        return rules.filter(predicate);
    },

    /**
     * 根据字段名查找验证规则
     * @param rules 规则数组
     * @param fieldName 字段名
     */
    findRuleByField: (rules: ValidationRule[], fieldName: string): ValidationRule | undefined => {
        return rules.find(rule => rule.field === fieldName);
    }
};
