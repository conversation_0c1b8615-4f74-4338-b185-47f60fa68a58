import { Request, Response, NextFunction } from 'express';
import { HttpStatusCode } from 'axios';
import { logger } from '../business/logger';

// 通用输入验证和清理中间件
export interface ValidationRule {
    field: string;
    required?: boolean;
    type?: 'string' | 'number' | 'email' | 'boolean' | 'array' | 'object';
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    sanitize?: boolean;
    allowedValues?: any[];
    errorMessage?: string;
}

// HTML标签清理函数
function sanitizeHtml(input: string): string {
    if (typeof input !== 'string') return input;
    
    // 移除HTML标签
    return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<[^>]*>/g, '')
        .trim();
}

// SQL注入防护
function sanitizeSql(input: string): string {
    if (typeof input !== 'string') return input;

    // 移除常见的SQL注入关键词
    const sqlKeywords = [
        'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
        'EXEC', 'EXECUTE', 'UNION', 'SCRIPT', '--', ';', '/*', '*/'
    ];

    let sanitized = input;
    sqlKeywords.forEach(keyword => {
        // 转义正则表达式中的特殊字符
        const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(escapedKeyword, 'gi');
        sanitized = sanitized.replace(regex, '');
    });

    return sanitized.trim();
}

// 通用数据清理函数
function sanitizeInput(value: any, rule: ValidationRule): any {
    if (!rule.sanitize) return value;
    
    if (typeof value === 'string') {
        let sanitized = value;
        
        // HTML清理
        sanitized = sanitizeHtml(sanitized);
        
        // SQL注入防护
        sanitized = sanitizeSql(sanitized);
        
        // XSS防护 - 转义特殊字符
        sanitized = sanitized
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
        
        return sanitized;
    }
    
    return value;
}

// 验证单个字段
function validateField(value: any, rule: ValidationRule): { isValid: boolean; error?: string } {
    // 检查必填字段
    if (rule.required && (value === undefined || value === null || value === '')) {
        return { isValid: false, error: `${rule.field} is required` };
    }
    
    // 如果字段不是必填且为空，跳过其他验证
    if (!rule.required && (value === undefined || value === null || value === '')) {
        return { isValid: true };
    }
    
    // 类型验证
    if (rule.type) {
        switch (rule.type) {
            case 'string':
                if (typeof value !== 'string') {
                    return { isValid: false, error: `${rule.field} must be a string` };
                }
                break;
            case 'number':
                if (typeof value !== 'number' && isNaN(Number(value))) {
                    return { isValid: false, error: `${rule.field} must be a number` };
                }
                value = Number(value);
                break;
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    return { isValid: false, error: `${rule.field} must be a valid email` };
                }
                break;
            case 'boolean':
                if (typeof value !== 'boolean' && value !== 'true' && value !== 'false') {
                    return { isValid: false, error: `${rule.field} must be a boolean` };
                }
                break;
            case 'array':
                if (!Array.isArray(value)) {
                    return { isValid: false, error: `${rule.field} must be an array` };
                }
                break;
            case 'object':
                if (typeof value !== 'object' || Array.isArray(value)) {
                    return { isValid: false, error: `${rule.field} must be an object` };
                }
                break;
        }
    }
    
    // 字符串长度验证
    if (typeof value === 'string') {
        if (rule.minLength && value.length < rule.minLength) {
            return { isValid: false, error: `${rule.field} must be at least ${rule.minLength} characters` };
        }
        if (rule.maxLength && value.length > rule.maxLength) {
            return { isValid: false, error: `${rule.field} must be no more than ${rule.maxLength} characters` };
        }
    }
    
    // 数值范围验证
    if (typeof value === 'number') {
        if (rule.min !== undefined && value < rule.min) {
            return { isValid: false, error: `${rule.field} must be at least ${rule.min}` };
        }
        if (rule.max !== undefined && value > rule.max) {
            return { isValid: false, error: `${rule.field} must be no more than ${rule.max}` };
        }
    }
    
    // 正则表达式验证
    if (rule.pattern && typeof value === 'string') {
        if (!rule.pattern.test(value)) {
            return { isValid: false, error: `${rule.field} format is invalid` };
        }
    }
    
    // 允许值验证
    if (rule.allowedValues && !rule.allowedValues.includes(value)) {
        return { isValid: false, error: `${rule.field} must be one of: ${rule.allowedValues.join(', ')}` };
    }
    
    return { isValid: true };
}

// 创建验证中间件
export function createValidationMiddleware(rules: ValidationRule[]) {
    return (req: Request, res: Response, next: NextFunction) => {
        const errors: string[] = [];
        const data = { ...req.body, ...req.query, ...req.params };
        
        // 验证每个字段
        for (const rule of rules) {
            const value = data[rule.field];
            const validation = validateField(value, rule);
            
            if (!validation.isValid) {
                errors.push(validation.error!);
            } else {
                // 如果验证通过，进行数据清理
                const sanitizedValue = sanitizeInput(value, rule);
                
                // 更新请求数据
                if (req.body.hasOwnProperty(rule.field)) {
                    req.body[rule.field] = sanitizedValue;
                }
                if (req.query.hasOwnProperty(rule.field)) {
                    req.query[rule.field] = sanitizedValue;
                }
                if (req.params.hasOwnProperty(rule.field)) {
                    req.params[rule.field] = sanitizedValue;
                }
            }
        }
        
        if (errors.length > 0) {
            logger.warn('Validation errors:', errors);
            return res.status(HttpStatusCode.BadRequest).json({
                message: 'Validation failed',
                errors: errors,
                code: HttpStatusCode.BadRequest
            });
        }
        
        next();
    };
}

// 常用验证规则预设
export const commonValidationRules = {
    email: {
        field: 'email',
        required: true,
        type: 'email' as const,
        maxLength: 255,
        sanitize: true
    },
    password: {
        field: 'password',
        required: true,
        type: 'string' as const,
        minLength: 6,
        maxLength: 128,
        sanitize: false // 密码不需要HTML清理
    },
    chatMessage: {
        field: 'message',
        required: true,
        type: 'string' as const,
        maxLength: 10000,
        sanitize: true
    },
    userId: {
        field: 'userId',
        required: true,
        type: 'string' as const,
        pattern: /^[a-fA-F0-9]{24}$/, // MongoDB ObjectId 格式
        sanitize: true
    }
};

export default createValidationMiddleware;
