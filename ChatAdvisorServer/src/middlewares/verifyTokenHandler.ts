import { Request, Response, NextFunction } from 'express';
import env, { secretKey } from '../config/env';
import jwt from 'jsonwebtoken';
import User from '../models/User';
import { logger } from '../business/logger';
import { HttpStatusCode } from 'axios';
import { routeInfo } from '../routers';
import { safeTranslateWithDefaults } from '../utils/i18nHelper';

// 检查路径是否需要认证
function requiresAuthentication(path: string): boolean {
    // 检查是否在公开路由列表中
    const isPublicRoute = routeInfo.publicRoutes.some(route => {
        // 精确匹配
        if (route === path) return true;

        // 通配符匹配（如果路由以/*结尾）
        if (route.endsWith('/*')) {
            const basePath = route.slice(0, -2);
            return path.startsWith(basePath);
        }

        return false;
    });

    // 如果已经匹配到公开路由，直接返回
    if (isPublicRoute) {
        return false;
    }

    // 额外检查：admin相关路径（包括API）
    if (path.startsWith('/admin') || path.startsWith('/api/admin')) {
        return false;
    }

    // 额外检查：.well-known相关路径
    if (path.startsWith('/.well-known')) {
        return false;
    }

    // 检查是否是静态文件
    if (path.includes('.') && (
        path.endsWith('.html') ||
        path.endsWith('.css') ||
        path.endsWith('.js') ||
        path.endsWith('.ico') ||
        path.endsWith('.txt') ||
        path.endsWith('.png') ||
        path.endsWith('.jpg') ||
        path.endsWith('.jpeg') ||
        path.endsWith('.gif') ||
        path.endsWith('.svg') ||
        path.endsWith('.woff') ||
        path.endsWith('.woff2') ||
        path.endsWith('.ttf') ||
        path.endsWith('.eot') ||
        path.endsWith('.json') ||
        path.endsWith('.map')
    )) {
        return false;
    }

    return true;
}

// Token校验中间件
const verifyTokenHandler = async (req: Request, res: Response, next: NextFunction) => {
    try {
        // 检查路径是否需要认证
        if (!requiresAuthentication(req.path)) {
            next();
            return;
        }

        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // 提取token

        if (token == null) {
            // console.log('Token is required!');
            next({ code: HttpStatusCode.Forbidden, message: safeTranslateWithDefaults(req, 'token_required') });
            return;
        }

        const decoded = jwt.verify(token, secretKey);
        // decoded to json
        logger.silly('decoded: ', decoded);
        // @ts-ignore
        // 判断token中的userId在数据库是否存在
        if (decoded && decoded.userId) {
            // @ts-ignore
            let user = await User.findById(decoded.userId).lean();
            // logger.silly('userEmail : ', user?.email);
            if (!user) {
                next({ code: HttpStatusCode.Unauthorized, message: safeTranslateWithDefaults(req, 'unauthorized') });
                return;
            }
        } else {
            next({ code: HttpStatusCode.Unauthorized, message: safeTranslateWithDefaults(req, 'unauthorized') });
        }
        // @ts-ignore
        req.token = decoded;
        // @ts-ignore
        logger.debug('req.token: ', req.token);
        next();
    } catch (error) {
        next({ code: HttpStatusCode.Unauthorized, message: safeTranslateWithDefaults(req, 'unauthorized') });
    }
};

export default verifyTokenHandler;
