import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { needToEncrypt, secretKey } from '../config/env';
import { logger } from '../business/logger';
import { HttpStatusCode } from 'axios';
import { safeTranslateWithDefaults } from '../utils/i18nHelper';

// 用于解密数据的函数（假设使用 AES）
const decryptData = (encryptedData: string, secretKey: string, iv: Buffer): Buffer => {
    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(secretKey, 'hex'), iv);
    let decrypted = Buffer.concat([decipher.update(Buffer.from(encryptedData, 'hex')), decipher.final()]);
    return decrypted;
};

// 用于校验数据的函数（使用 SHA-256）
const verifyData = (data: string, signature: string, secretKey: string): boolean => {
    console.debug('data', data);
    console.debug('signature', signature);
    console.debug('secretKey', secretKey);
    // 确保data是字符串格式，如果是对象或数组，转为JSON字符串
    const dataString = typeof data === 'string' ? data : JSON.stringify(data);
    // 如果secretKey是Hex字符串，需要转换格式
    const secretKeyBuffer = Buffer.from(secretKey, 'hex');
    const hash = crypto.createHmac('sha256', secretKeyBuffer).update(dataString).digest('hex');
    return hash === signature;
};

const decryptionHandler = (req: Request, res: Response, next: NextFunction) => {
    try {
        // 没有http  body数据，直接通过 或者是 {} 空对象
        if (!req.body || JSON.stringify(req.body) === '{}') {
            next();
            return;
        }
        // console.log('req.body: ${JSON.stringify(req.body)}, req.url', req.url);
        // 如果请求的是verifyPurchase不需要解密
        if (req.url == '/verifyPurchase'|| req.url == '/refund') {
            next();
            return;
        }
        const signature = req.headers['signature'] as string;
        var httpBodyData: string = needToEncrypt ? req.body : JSON.stringify(req.body);
        logger.warn('request.url', req.url)
        if (!signature || !!!signature) {
            next({ message: safeTranslateWithDefaults(req, 'signature_required'), code: HttpStatusCode.BadRequest });
            return;
        }

        if (needToEncrypt) {
            const decryptedDataBuffer = decryptData(httpBodyData, secretKey, Buffer.from(signature.slice(0, 16)));
            // console.log('iv:', Buffer.from(signature.slice(0, 16)));
            if (decryptedDataBuffer === null) {
                // console.log('Decryption failed');
                // @ts-ignore
                return next({ message: `${safeTranslateWithDefaults(req, 'decryption_failed')}:`, code: HttpStatusCode.InternalServerError });
            }
            httpBodyData = decryptedDataBuffer.toString('utf8');
            // console.log('decryptedDataBuffer:', decryptedDataBuffer);
            // 假设解密后的数据是 JSON 格式
            req.body = JSON.parse(httpBodyData);
            req.headers['Content-type'] = 'application/json';
        }
        if (!verifyData(httpBodyData, signature, secretKey)) {
            // console.log('verifyData failed');
            next({ message: safeTranslateWithDefaults(req, 'invalid_signature'), code: HttpStatusCode.BadRequest });
            return;
        }
        next();
    } catch (error) {
        // console.log('decode error:', error);
        // @ts-ignore
        next({ message: `${safeTranslateWithDefaults(req, 'decryption_failed')}:`, code: HttpStatusCode.InternalServerError });
    }
};

export default decryptionHandler;
