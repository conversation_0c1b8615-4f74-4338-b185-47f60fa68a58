<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TikTok Call Login</title>
  <style>
    /* 设置页面居中 */
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #f0f0f0;
      font-family: Arial, sans-serif;
      box-sizing: border-box;
    }

    .loading-container {
      text-align: center;
      padding: 20px;
    }

    /* 创建加载的圆圈动画 */
    .loader {
      border: 8px solid #f3f3f3;
      border-top: 8px solid #3498db;
      border-radius: 50%;
      width: 60px; /* 适配小屏幕 */
      height: 60px; /* 适配小屏幕 */
      animation: spin 2s linear infinite;
      margin-bottom: 20px;
    }

    /* 动画效果 */
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 提示用户的备用链接 */
    .backup-link {
      margin-top: 20px;
      font-size: 16px;
    }
    
    .backup-link a {
      color: #3498db;
      text-decoration: none;
    }

    /* 为更小屏幕做适配 */
    @media (max-width: 480px) {
      .loader {
        width: 50px;
        height: 50px;
      }

      .backup-link {
        font-size: 14px;
      }
    }
  </style>
</head>
<body>
  <div class="loading-container">
    <div class="loader"></div>
    <!-- 备用链接 -->
    <p class="backup-link">If the redirect doesn't work, <a id="backup-link" href="#">click here</a>.</p>
  </div>

  <script>
    // 将window.location.href包装成参数
    // 获取备用链接元素并设置其 href 属性
    const backupLink = document.getElementById('backup-link');
    backupLink.href = window.location.href;
  </script>
</body>
</html>
