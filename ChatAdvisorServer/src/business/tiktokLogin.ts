import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/User';
import { secretKey, tokenExpiresIn, tiktokClientId, tiktokClientSecret } from '../config/env';
import axios from 'axios';
const crypto = require('crypto');
import { logger } from './logger';

// 验证 TikTok authorization_code 并获取 access_token 和用户信息
async function getTikTokAccessToken(code: string, redirectUri: string, codeVerifier: string): Promise<any> {
    try {
        const query = new URLSearchParams({
            client_key: tiktokClientId,
            client_secret: tiktokClientSecret,
            code,
            grant_type: 'authorization_code',
            redirect_uri: redirectUri,
            code_verifier: codeVerifier
        }).toString();
        const response = await axios.post('https://open.tiktokapis.com/v2/oauth/token/', query, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        return response.data;
    } catch (error) {
        throw new Error(`TikTok API error: ${error}`);
    }
}

// TikTok 登录校验逻辑
const tiktokLogin = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { code, grantType, redirectUri, codeVerifier } = req.body;

        // 获取 TikTok access_token 和用户信息
        const tikTokInfo = await getTikTokAccessToken(code, redirectUri, codeVerifier);
        if (!tikTokInfo || !tikTokInfo.open_id) {
            throw new Error('Invalid TikTok token');
        }

        // 查找用户是否已注册
        const existingUser = await User.findOne({ 'externalAccounts.tikTokInfo.open_id': tikTokInfo.open_id });
        if (existingUser) {
            // 检查用户是否已绑定此 TikTok 账号
            await User.updateOne({ 'externalAccounts.tikTokInfo.open_id': tikTokInfo.open_id }, { isDelete: false });
            const token = jwt.sign({ userId: existingUser._id, email: existingUser.email }, secretKey, { expiresIn: tokenExpiresIn });

            res.status(200).json({
                message: 'TikTok login successful',
                code: 200,
                data: { ...existingUser.toObject(), token }
            });
        } else {
            const generateMail = `TikTok_${tikTokInfo.open_id.slice(tikTokInfo.open_id.length - 6, tikTokInfo.open_id.length)}`;
            const newUser = new User({
                email: tikTokInfo.email ?? generateMail, // TikTok的用户数据可能没有邮箱, 根据需求处理
                externalAccounts: {
                    tikTokInfo
                }
            });
            newUser.save().then(() => {
            }).catch((err) => {
            });
            const token = jwt.sign({ userId: newUser._id, email: newUser.email }, secretKey, { expiresIn: tokenExpiresIn });

            res.status(200).json({
                message: 'TikTok login successful',
                code: 200,
                data: { ...newUser.toObject(), token }
            });
        }
    } catch (error: any) {
        console.log(error);
        next(error);
    }
};

export default tiktokLogin;
