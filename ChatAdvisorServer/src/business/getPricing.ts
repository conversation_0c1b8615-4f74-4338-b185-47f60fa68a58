import { Request, Response, NextFunction } from 'express';
import { HttpStatusCode } from 'axios';
import { logger } from './logger';
import AIServiceModel from '../models/AIServiceModel';

export const getPricing = async (req: Request, res: Response, next: NextFunction) => {
    try {
        // 获取当前启用的配置信息
        const { default: activeAIConfigManager } = await import('../services/ActiveAIConfigManager');
        const activeConfig = await activeAIConfigManager.getActiveConfig();

        let currentModelId = '';
        let currentModelName = '';
        let configId = '';

        if (activeConfig && activeConfig.configId && activeConfig.modelId) {
            currentModelId = (activeConfig.modelId as any)._id.toString();
            currentModelName = (activeConfig.modelId as any).modelName;
            configId = (activeConfig.configId as any)._id.toString();
            logger.info(`当前启用的模型: ${currentModelName} (ID: ${currentModelId})`);
        } else {
            logger.warn('未找到启用的AI配置，无法返回价格信息');
            res.status(404).json({
                code: 404,
                message: '未找到启用的AI配置',
                data: []
            });
            return;
        }

        // 只获取当前启用的模型
        const models = await AIServiceModel.find({
            _id: currentModelId,
            isActive: true
        }).populate('configId', 'name provider').lean();

        if (models.length === 0) {
            logger.warn(`当前启用的模型 ${currentModelName} 不存在或已禁用`);
            res.status(404).json({
                code: 404,
                message: '当前启用的模型不存在或已禁用',
                data: []
            });
            return;
        }

        const DEFAULT_PRICE = 5 / 3000; // 5每3000字符
        const DEFAULT_COUNT = 3000; // 3000字符

        const formattedPricings = models.map((model: any) => {
            // 如果价格为0，使用默认价格
            const inputPrice = model.pricing.inputPrice === 0 ? DEFAULT_PRICE : model.pricing.inputPrice;
            const outputPrice = model.pricing.outputPrice === 0 ? DEFAULT_PRICE : model.pricing.outputPrice;

            return {
                id: model._id,
                modelName: model.modelName,
                inPrice: inputPrice,
                outPrice: outputPrice,
                count: DEFAULT_COUNT, // 统一使用3000字符作为计费单位
                alias: model.displayName || model.modelName,
                intro: model.description || `${model.modelName} - 来源: ${model.configId?.name || '未知配置'}`,
                // 额外信息
                configId: model.configId?._id,
                configName: model.configId?.name,
                provider: model.configId?.provider,
                maxTokens: model.maxTokens,
                supportedFeatures: model.supportedFeatures,
                isCurrentModel: model._id.toString() === currentModelId
            };
        });

        // 返回当前启用模型的价格信息
        logger.info(`返回当前启用模型的价格信息: ${currentModelName}`);
        res.json({
            data: formattedPricings,
            code: HttpStatusCode.Ok,
            currentModel: currentModelName,
            currentModelId: currentModelId,
            configId: configId,
            totalModels: formattedPricings.length
        });
    } catch (error) {
        logger.error(`获取价格信息失败: ${error}`);
        res.status(HttpStatusCode.InternalServerError).json({
            code: HttpStatusCode.InternalServerError,
            message: 'Error accessing the database',
            error: error.message
        });
    }
};
