import { createLogger, transports, format } from 'winston';
import { MongoDB } from 'winston-mongodb';
import moment from 'moment-timezone';
import util from 'util';
import env, { ENV_MONGODB_URI } from '../config/env';

const customFormat = format.printf(({ level, message, timestamp, ...meta }) => {
    // 将时区调整为东八区
    const adjustedTimestamp = moment(timestamp).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

    let msg = message;
    if (typeof msg === 'object') {
        msg = util.inspect(msg, { depth: null });
    }

    let formattedMeta = '';
    if (meta[Symbol.for('splat')]) {
        // 格式化 splat 参数
        msg = util.format(msg, ...meta[Symbol.for('splat')]);
    }

    if (Object.keys(meta).length > 0) {
        // 移除 splat 后的 meta
        const { [Symbol.for('splat')]: splat, ...restMeta } = meta;
        formattedMeta = util.inspect(restMeta, { depth: null });
    }

    return `${adjustedTimestamp} ${level}: ${msg ?? formattedMeta}`;
});

export const logger = createLogger({
    level: env.isRelease ? "error" : "silly",
    format: format.combine(
        format.timestamp(),
        format.colorize({
            all: true,
            colors: {
                error: 'red',
                warn: 'yellow',
                info: 'green',
                http: 'magenta',
                verbose: 'cyan',
                debug: 'white',
                silly: 'grey'
            }
        }),
        customFormat,
    ),
    transports: [
        new transports.Console(),
        new MongoDB({
            level: 'error',
            db: ENV_MONGODB_URI,
            collection: 'logger_logs',
            options: {
                useNewUrlParser: true,
                useUnifiedTopology: true
            }
        })
    ]
});
