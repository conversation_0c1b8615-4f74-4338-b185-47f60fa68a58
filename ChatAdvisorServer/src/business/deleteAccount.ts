import { Request, Response, NextFunction } from 'express';
import User from '../models/User';
import { DateTime } from 'luxon';
import { logger } from './logger';
import { HttpStatusCode } from 'axios';

export default async function deleteAccount(req: Request, res: Response, next: NextFunction) {
    try {
        // @ts-ignore
        const { userId, email } = req.token;

        // 查找用户
        const user = await User.findOne({ email, _id: userId});
        if (!user) {
            return res.status(HttpStatusCode.Ok).json({
                message: req.t('user_not_exist'),
                code: HttpStatusCode.NotFound,
            });
        }

        // 拼接邮箱并设置删除标志
        // const deletionDate = DateTime.now().toISODate();
        // user.email = `${user.email}_delete_${deletionDate}`;
        // // 邮箱账号需要校验密码
        // if (user.externalAccounts?.appleInfo?.email == null) {
        //     const password = req.body.password
        //     if (password !== user.password) {
        //         return res.status(HttpStatusCode.Ok).json({
        //             message: req.t('password_incorrect'),
        //             code: HttpStatusCode.BadRequest,
        //         });
        //     }
        // } else {
        //     user.externalAccounts.appleInfo.email = `${user?.externalAccounts?.appleInfo?.email}_delete_${deletionDate}`;
        // }

        logger.warn(`Deleting account: ${user.email}`);
        user.isDelete = true;
       
        // 保存更改
        await user.save();

        res.status(HttpStatusCode.Ok).json({
            message: 'success',
            code: HttpStatusCode.Ok,
        });
    } catch (error: any) {
        logger.error(`Error deleting account: ${error}`);
        error.statusCode = HttpStatusCode.InternalServerError;
        next(error);
    }
};
