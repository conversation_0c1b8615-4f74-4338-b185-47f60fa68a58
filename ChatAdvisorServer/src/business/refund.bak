import { Request, Response, NextFunction } from 'express';
import UserBalance from 'src/models/Balance';
import UserVerification from 'src/modelsVerification';
import VerificationResult from 'src/models/VerificationResult';
import { HttpStatusCode } from 'axios';
import { logger } from 'src/business/logger';

interface RefundNotification {
    notification_type: string;
    password: string;
    environment: string;
    unified_receipt: {
        status: number;
        environment: string;
        latest_receipt: string;
        latest_receipt_info: Array<{
            quantity: string;
            product_id: string;
            transaction_id: string;
            original_transaction_id: string;
            purchase_date: string;
            purchase_date_ms: string;
            purchase_date_pst: string;
            original_purchase_date: string;
            original_purchase_date_ms: string;
            original_purchase_date_pst: string;
            expires_date: string;
            expires_date_ms: string;
            expires_date_pst: string;
            web_order_line_item_id: string;
            is_trial_period: string;
            is_in_intro_offer_period: string;
            in_app_ownership_type: string;
            subscription_group_identifier: string;
        }>;
        pending_renewal_info: Array<{
            auto_renew_product_id: string;
            original_transaction_id: string;
            product_id: string;
            auto_renew_status: string;
        }>;
    };
}

async function handleRefund(req: Request, res: Response, next: NextFunction) {
    try {
        const refundNotification: RefundNotification = req.body;
        
        if (refundNotification.notification_type !== 'REFUND') {
            return next({ message: 'Invalid notification type', code: HttpStatusCode.BadRequest });
        }

        const transactionId = refundNotification.unified_receipt.latest_receipt_info[0].transaction_id;

        const userVerification = await UserVerification.findOne({ 'verificationResult.receipt.in_app.transaction_id': transactionId }).populate('verificationResult');

        if (!userVerification) {
            return next({ message: 'Transaction not found', code: HttpStatusCode.NotFound });
        }

        const userId = userVerification.userId;
        const amount = userVerification.amount;

        const userBalance = await UserBalance.findOneAndUpdate(
            { userId },
            { $inc: { balance: -amount } },
            { new: true }
        );

        if (!userBalance) {
            return next({ message: 'User balance not found', code: HttpStatusCode.NotFound });
        }

        return res.status(HttpStatusCode.Ok).json({
            message: 'Refund processed and balance updated successfully',
            code: HttpStatusCode.Ok,
            data: userBalance.balance
        });
    } catch (error) {
        console.error(error);
        next({ message: 'Failed to handle refund', code: HttpStatusCode.InternalServerError });
    }
}

export default handleRefund;
