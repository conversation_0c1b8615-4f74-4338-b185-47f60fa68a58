import { Request, Response, NextFunction } from 'express';
import { ResponseFormatter } from '../utils/responseFormatter';
import { logger } from '../utils/logger';

/**
 * Ping接口 - 用于客户端网络测速和连通性检测
 * 返回简单的状态信息和网络相关数据
 */
export const ping = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
    try {
        const startTime = Date.now();
        
        // 获取客户端信息
        const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
        const userAgent = req.headers['user-agent'] || 'unknown';
        const platform = req.headers['platform'] as string || 'unknown';
        const appVersion = req.headers['app-version'] as string || 'unknown';
        
        // 获取地理位置相关信息（从请求头）
        const cfCountry = req.headers['cf-ipcountry'] as string; // Cloudflare 提供的国家代码
        const cfRay = req.headers['cf-ray'] as string; // Cloudflare Ray ID
        const cfConnectingIP = req.headers['cf-connecting-ip'] as string; // 真实IP（通过Cloudflare）
        
        // 计算处理时间
        const processingTime = Date.now() - startTime;
        
        // 构建响应数据
        const responseData = {
            status: 'ok',
            timestamp: new Date().toISOString(),
            serverTime: Date.now(),
            processingTime,
            network: {
                clientIP: cfConnectingIP || clientIP,
                country: cfCountry || 'unknown',
                cfRay: cfRay || null,
                userAgent,
                platform,
                appVersion
            },
            server: {
                region: process.env.SERVER_REGION || 'unknown',
                node: process.env.NODE_ENV || 'production',
                uptime: process.uptime(),
                memory: {
                    used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024), // MB
                    total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024) // MB
                }
            }
        };
        
        // 记录ping请求（用于监控和分析）
        logger.info(`Ping请求 - IP: ${responseData.network.clientIP}, 国家: ${responseData.network.country}, 平台: ${platform}, 版本: ${appVersion}, 处理时间: ${processingTime}ms`);
        
        return ResponseFormatter.success(res, responseData, 'Ping successful');
        
    } catch (error) {
        logger.error(`Ping接口错误: ${error}`);
        return ResponseFormatter.error(res, 'Ping failed', 500);
    }
};

/**
 * 健康检查接口 - 更简单的版本，用于负载均衡器等
 */
export const health = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
    try {
        const responseData = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        };
        
        return ResponseFormatter.success(res, responseData, 'Health check passed');
        
    } catch (error) {
        logger.error(`健康检查错误: ${error}`);
        return ResponseFormatter.error(res, 'Health check failed', 500);
    }
};
