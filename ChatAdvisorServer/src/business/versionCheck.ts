import { Request, Response, NextFunction } from 'express';
import { Config } from '../models/Config';
import { logger } from '../utils/logger';
import { HTTP_STATUS } from '../constants';
import { ResponseFormatter } from '../utils/responseFormatter';

/**
 * 版本比较工具函数
 * 比较两个语义化版本号
 * @param version1 版本号1
 * @param version2 版本号2
 * @returns 1: version1 > version2, 0: 相等, -1: version1 < version2
 */
function compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    
    // 确保版本号都有三个部分（major.minor.patch）
    while (v1Parts.length < 3) v1Parts.push(0);
    while (v2Parts.length < 3) v2Parts.push(0);
    
    for (let i = 0; i < 3; i++) {
        if (v1Parts[i] > v2Parts[i]) return 1;
        if (v1Parts[i] < v2Parts[i]) return -1;
    }
    
    return 0;
}

/**
 * 版本检测接口
 * 检查客户端版本并返回更新信息
 */
export const checkVersion = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
    try {
        // 从请求头获取客户端版本信息
        const clientVersion = req.headers['app-version'] as string || '1.0.0';
        const platform = req.headers['platform'] as string || 'ios'; // ios 或 android
        const lang = req.headers['local'] as string || 'en';
        
        logger.info(`版本检测请求 - 客户端版本: ${clientVersion}, 平台: ${platform}, 语言: ${lang}`);
        
        // 获取服务端配置
        const config = await Config.findOne().lean();
        if (!config) {
            return ResponseFormatter.error(res, '配置未找到', HTTP_STATUS.NOT_FOUND);
        }
        
        // 检查是否启用版本检测
        if (!config.versionCheckEnabled) {
            // 即使禁用也返回完整的数据结构，保持客户端兼容性
            const responseData = {
                needUpdate: false,
                updateType: 'none' as const,
                canUseApp: true,
                currentVersion: clientVersion,
                latestVersion: config.latestVersion || '1.0.0',
                minimumVersion: config.minimumVersion || '1.0.0',
                updateMessage: '版本检测已禁用',
                downloadUrl: platform === 'android' ?
                    (config.appStoreUrls?.android || 'https://play.google.com/store/apps/details?id=com.sanva.chatadvisor') :
                    (config.appStoreUrls?.ios || 'https://apps.apple.com/us/app/chat-advisor/id6526465428'),
                platform,
                versionInfo: {
                    isLatest: true,
                    isBelowMinimum: false,
                    hasNewVersion: false
                }
            };

            logger.info(`版本检测已禁用，返回默认数据: ${JSON.stringify(responseData)}`);
            return ResponseFormatter.success(res, responseData, '版本检测已禁用');
        }
        
        // 比较版本号
        const latestVersionComparison = compareVersions(config.latestVersion, clientVersion);
        const minimumVersionComparison = compareVersions(clientVersion, config.minimumVersion);
        
        // 判断是否需要更新
        let needUpdate = false;
        let updateType: 'force' | 'optional' | 'none' = 'none';
        let canUseApp = true;
        
        // 如果客户端版本低于最低支持版本，强制更新
        if (minimumVersionComparison < 0) {
            needUpdate = true;
            updateType = 'force';
            canUseApp = false;
            logger.warn(`客户端版本 ${clientVersion} 低于最低支持版本 ${config.minimumVersion}`);
        }
        // 如果有新版本可用
        else if (latestVersionComparison > 0) {
            needUpdate = true;
            updateType = config.forceUpdate ? 'force' : 'optional';
            canUseApp = !config.forceUpdate;
            logger.info(`发现新版本 ${config.latestVersion}，当前版本 ${clientVersion}`);
        }
        
        // 获取本地化的更新消息，支持语言回退机制
        const getLocalizedMessage = (messageMap: any, language: string): string => {
            // 直接匹配
            if (messageMap?.[language]) {
                return messageMap[language];
            }

            // 语言代码回退 (zh_CN -> zh)
            const langCode = language.split('_')[0];
            if (messageMap?.[langCode]) {
                return messageMap[langCode];
            }

            // 常见语言回退
            const fallbackMap: { [key: string]: string } = {
                'zh': 'zh_CN',
                'en': 'en',
                'ja': 'ja',
                'ko': 'ko'
            };

            if (fallbackMap[langCode] && messageMap?.[fallbackMap[langCode]]) {
                return messageMap[fallbackMap[langCode]];
            }

            // 最终回退到英语或中文
            return messageMap?.['en'] || messageMap?.['zh_CN'] || '发现新版本，建议立即更新以获得更好的体验';
        };

        const updateMessage = getLocalizedMessage(config.updateMessage, lang);
        
        // 获取应用商店链接
        const downloadUrl = platform === 'android' ?
                           (config.appStoreUrls?.android || 'https://play.google.com/store/apps/details?id=com.sanva.chatadvisor') :
                           (config.appStoreUrls?.ios || 'https://apps.apple.com/us/app/chat-advisor/id6526465428');
        
        // 构建响应数据
        const responseData = {
            needUpdate,
            updateType,
            canUseApp,
            currentVersion: clientVersion,
            latestVersion: config.latestVersion,
            minimumVersion: config.minimumVersion,
            updateMessage,
            downloadUrl,
            platform,
            versionInfo: {
                isLatest: latestVersionComparison === 0,
                isBelowMinimum: minimumVersionComparison < 0,
                hasNewVersion: latestVersionComparison > 0
            }
        };
        
        logger.info(`版本检测结果: ${JSON.stringify(responseData)}`);
        
        return ResponseFormatter.success(res, responseData, '版本检测完成');
        
    } catch (error) {
        logger.error('版本检测失败:', error);
        return ResponseFormatter.error(res, '版本检测失败', HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
};

/**
 * 获取版本信息接口
 * 返回当前服务端版本配置信息
 */
export const getVersionInfo = async (req: Request, res: Response, next: NextFunction): Promise<any> => {
    try {
        const config = await Config.findOne().lean();
        if (!config) {
            return ResponseFormatter.error(res, '配置未找到', HTTP_STATUS.NOT_FOUND);
        }
        
        const versionInfo = {
            latestVersion: config.latestVersion,
            minimumVersion: config.minimumVersion,
            versionCheckEnabled: config.versionCheckEnabled,
            updateType: config.updateType,
            appStoreUrls: config.appStoreUrls
        };
        
        return ResponseFormatter.success(res, versionInfo, '获取版本信息成功');
        
    } catch (error) {
        logger.error('获取版本信息失败:', error);
        return ResponseFormatter.error(res, '获取版本信息失败', HTTP_STATUS.INTERNAL_SERVER_ERROR);
    }
};

export default {
    checkVersion,
    getVersionInfo,
    compareVersions
};
