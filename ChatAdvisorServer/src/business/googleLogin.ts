import { Request, Response, NextFunction } from 'express';
import { googleClientId } from '../config/env';
import { OAuth2Client } from 'google-auth-library';
import { logger } from './logger';
import {
    ThirdPartyAuthHandler,
    ThirdPartyProvider,
    ThirdPartyUserInfo
} from '../utils/authHelper';

// 初始化 Google OAuth2 Client
const client = new OAuth2Client(googleClientId);

// 验证 Google 登录的 idToken
export async function verifyGoogleIdToken(idToken: string, audience: string): Promise<any> {
    const ticket = await client.verifyIdToken({
        idToken,
        audience: googleClientId
    });
    const payload = ticket.getPayload();
    if (!payload) {
        throw new Error('Invalid Google ID token');
    }
    return payload;
}

// Google 登录校验逻辑
const googleLogin = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { idToken } = req.body;

        // 验证 Google 登录的 idToken
        const googleInfo = await verifyGoogleIdToken(idToken, googleClientId);

        // 检查解码后的信息是否有效
        if (!googleInfo || !googleInfo.email) {
            throw new Error('Invalid Google token');
        }

        // 使用通用第三方登录处理器
        await ThirdPartyAuthHandler.handleThirdPartyLogin(
            ThirdPartyProvider.GOOGLE,
            googleInfo as ThirdPartyUserInfo,
            req,
            res,
            next
        );
    } catch (error: any) {
        logger.error('Google login error:', error);
        next(error);
    }
};

export default googleLogin;
