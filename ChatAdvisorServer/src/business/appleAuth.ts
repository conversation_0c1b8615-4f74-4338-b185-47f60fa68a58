import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import jwksClient, { SigningKey } from 'jwks-rsa';
import { logger } from './logger';
import {
    ThirdPartyAuthHandler,
    ThirdPartyProvider,
    ThirdPartyUserInfo
} from '../utils/authHelper';

// 初始化 jwksClient
const client = jwksClient({
    jwksUri: 'https://appleid.apple.com/auth/keys'
});

// 获取签名密钥的方法
function getKey(header: jwt.JwtHeader, callback: jwt.SigningKeyCallback): void {
    client.getSigningKey(header.kid, function (err, key) {
        const signingKey = key?.getPublicKey();
        callback(err, signingKey);
    });
}

// 验证苹果登录的 idToken
export function verifyAppleIdToken(idToken: string, audience: string): Promise<jwt.JwtPayload> {
    return new Promise((resolve, reject) => {
        // 输入验证
        if (!idToken || typeof idToken !== 'string') {
            return reject(new Error('Invalid idToken: must be a non-empty string'));
        }

        if (!audience || typeof audience !== 'string') {
            return reject(new Error('Invalid audience: must be a non-empty string'));
        }

        jwt.verify(
            idToken,
            getKey,
            {
                algorithms: ['RS256'],
                audience,
                issuer: 'https://appleid.apple.com'
            },
            (err, decoded) => {
                if (err) {
                    logger.error('Apple ID token verification failed:', err);
                    return reject(new Error(`Token verification failed: ${err.message}`));
                }

                // 验证解码后的数据结构
                const payload = decoded as jwt.JwtPayload;
                if (!payload.email || !payload.sub) {
                    return reject(new Error('Invalid token payload: missing required fields'));
                }

                resolve(payload);
            }
        );
    });
}

// 苹果登录校验逻辑
const appleLogin = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { idToken, appId } = req.body;

        // 输入验证
        if (!idToken || !appId) {
            return res.status(400).json({
                message: 'Missing required parameters: idToken and appId',
                code: 400
            });
        }

        // 验证苹果登录的 idToken
        const appleInfo = await verifyAppleIdToken(idToken, appId);

        // 检查解码后的信息是否有效
        if (!appleInfo || !appleInfo.email) {
            return res.status(400).json({
                message: 'Invalid Apple ID token: missing email',
                code: 400
            });
        }

        // 使用通用第三方登录处理器
        await ThirdPartyAuthHandler.handleThirdPartyLogin(
            ThirdPartyProvider.APPLE,
            appleInfo as ThirdPartyUserInfo,
            req,
            res,
            next
        );
    } catch (error: any) {
        logger.error('Apple login error:', error);
        next({
            message: `Apple login failed: ${error.message}`,
            code: 500
        });
    }
};

export  const appleAppSiteAssociation = async (req: Request, res: Response, next: NextFunction) => {
    try {
        res.json({
            "applinks": {
                "apps": [],
                "details": [
                    {
                        "appID": "2XBCJAM843.com.sanva.chatadvisor",
                        "paths": [
                            "/tiktoklogin*",
                            "/tiktokcallbak*"
                        ]
                    }
                ]
            }
        });
    } catch (error: any) {
        console.log(error);
        next(error);
    }
}

export default appleLogin;
