import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/User';
import { secretKey, tokenExpiresIn, twitterApiKey, twitterApiSecret } from '../config/env';
import { logger } from './logger';
import oauth from 'oauth';
import querystring from 'query-string';

// Twitter API 的 URL
const twitterApiUrl = 'https://api.twitter.com/1.1/account/verify_credentials.json';

// 创建 OAuth 客户端
const oauthClient = new oauth.OAuth(
    'https://api.twitter.com/oauth/request_token',
    'https://api.twitter.com/oauth/access_token',
    twitterApiKey,
    twitterApiSecret,
    '1.0A',
    null,
    'HMAC-SHA1'
);

// 验证 Twitter 登录的 token 和 secret
export async function verifyTwitterToken(token: string, tokenSecret: string): Promise<any> {
    return new Promise((resolve, reject) => {
        oauthClient.get(
            `${twitterApiUrl}?include_email=true`,
            token,
            tokenSecret,
            (error, data) => {
                if (error || !data) {
                    return reject(new Error('Invalid Twitter token or secret'));
                }
                const parsedData = JSON.parse(data.toString());
                if (!parsedData || !parsedData.id_str || !parsedData.email) {
                    return reject(new Error('Invalid Twitter token or secret'));
                }
                resolve(parsedData);
            }
        );
    });
}

const twitterLogin = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { token, secret } = req.body;

        // 验证 Twitter 登录的 token 和 secret
        const twitterInfo = await verifyTwitterToken(token, secret);
        logger.silly('Twitter info:', twitterInfo);

        // 检查解码后的信息是否有效
        if (!twitterInfo || !twitterInfo.id_str || !twitterInfo.email) {
            throw new Error('Invalid Twitter token or secret');
        }

        // 查找邮箱是否存在
        const existingUser = await User.findOne({ email: twitterInfo.email });

        if (existingUser) {
            // 检查用户是否已绑定此 Twitter 账号
            logger.silly('existingUser:', existingUser);
            if (existingUser.externalAccounts && existingUser.externalAccounts.twitterInfo && existingUser.externalAccounts.twitterInfo.id === twitterInfo.id_str) {
                // 更新 isDelete 状态
                await User.updateOne({ email: twitterInfo.email }, { isDelete: false });
                // 生成 JWT Token
                const token = jwt.sign({ userId: existingUser._id, email: existingUser.email }, secretKey, { expiresIn: tokenExpiresIn });
                res.status(200).json({
                    message: 'Twitter login successful',
                    code: 200,
                    data: { ...existingUser.toObject(), token }
                });
            } else {
                logger.warn('Twitter login conflict');
                // 存在邮箱但未绑定此 Twitter 账号，提示邮箱冲突
                res.status(409).json({
                    message: req.t('email_conflict'),
                    code: 409,
                });
            }
        } else {
            // 创建新用户
            const newUser = new User({
                email: twitterInfo.email,
                externalAccounts: {
                    twitterInfo: twitterInfo
                }
            });
            await newUser.save();
            const token = jwt.sign({ userId: newUser._id, email: newUser.email }, secretKey, { expiresIn: tokenExpiresIn });
            res.status(200).json({
                message: 'Twitter login successful',
                code: 200,
                data: { ...newUser.toObject(), token }
            });
        }
    } catch (error: any) {
        logger.error('Twitter login error', error);
        next(error);
    }
};

export default twitterLogin;
