import { Request, Response, NextFunction } from 'express';
import axios from 'axios';
import { logger } from './logger';
import {
    ThirdPartyAuthHandler,
    ThirdPartyProvider,
    ThirdPartyUserInfo
} from '../utils/authHelper';

// 验证 Facebook 登录的 accessToken
export async function verifyFacebookAccessToken(accessToken: string): Promise<any> {
    const response = await axios.get(`https://graph.facebook.com/me?access_token=${accessToken}&fields=id,email`);
    const data = response.data;
    return data;
}

// Facebook 登录校验逻辑
const facebookLogin = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const { accessToken } = req.body;

        // 验证 Facebook 登录的 accessToken
        let facebookInfo = await verifyFacebookAccessToken(accessToken);

        // 检查解码后的信息是否有效
        if (!facebookInfo || !facebookInfo.id) {
            throw new Error('Invalid Facebook access token');
        }

        // Facebook可能没有邮箱，生成虚拟邮箱
        if (!facebookInfo.email) {
            facebookInfo.email = `${facebookInfo.id}@facebook.virtual`;
        }

        // 使用通用第三方登录处理器
        await ThirdPartyAuthHandler.handleThirdPartyLogin(
            ThirdPartyProvider.FACEBOOK,
            facebookInfo as ThirdPartyUserInfo,
            req,
            res,
            next
        );
    } catch (error: any) {
        logger.error('Facebook login error:', error);
        next(error);
    }
};

export default facebookLogin;
