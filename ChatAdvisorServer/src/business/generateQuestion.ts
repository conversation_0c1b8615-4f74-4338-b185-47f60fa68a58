import OpenAI from 'openai';
import { Request, Response, NextFunction } from 'express';
import { ChatCompletionMessageParam } from 'openai/resources';
import { Question } from '../models/Question';
import env from '../config/env';
import { logger } from './logger';
import { HttpStatusCode } from 'axios';

const openai = new OpenAI();

export async function generateQuestion(req: Request, res: Response, next: NextFunction) {
    // 从请求头部获取语言
    const acceptLanguageHeader = req.headers['accept-language'];
    const language = acceptLanguageHeader ? acceptLanguageHeader.split(',')[0].split(';')[0] : 'en'; // 默认为英文

    if (0) {
        try {
            // 将用户的请求内容（例如某个问题生成提示）作为输入消息
            let messages: ChatCompletionMessageParam[] = [{ role: 'system', content: `${req.t('generate_help')}` }];
            logger.debug('messages', messages);
            // 请求 OpenAI API 生成聊天完成
            const completion = await openai.chat.completions.create({
                model: 'gpt-4o',
                messages
            });
            console.log('completion:', completion.choices[0].message.content);

            // 将提取的数据作为响应返回给客户端
            res.status(HttpStatusCode.Ok).json({data: completion.choices[0].message.content, code: HttpStatusCode.Ok, message: 'success'});
        } catch (e) {
            logger.error(`Error: ${e}`);
            // 转发错误到 Express 的错误处理中间件
            next(e);
        }
    } else {
        // 从请求头部获取语言
        const acceptLanguageHeader = req.headers['accept-language'];
        const lang = acceptLanguageHeader ? acceptLanguageHeader.split(',')[0].split('-')[0] : 'en'; // 默认为英文

        try {
            const questions = await Question.aggregate([{ $sample: { size: Math.floor(Math.random() * 4) + 4 } }]);

            // 对于每个问题，根据请求的语言获取相应的文本
            const jsonData = questions.map((question: any) => {
                return {
                    sketch: question.sketch[lang] || question.sketch['en'], // 如果请求的语言不存在，则使用默认的英文
                    content: question.content[lang] || question.content['en'],
                    question: question.question[lang] || question.question['en']
                };
            });

            // logger.debug('jsonData:', jsonData);

            res.json({
                code: HttpStatusCode.Ok,
                message: 'success',
                data: jsonData // 直接返回 jsonData 数组
            });
        } catch (error) {
            res.status(HttpStatusCode.Ok).json({ code: HttpStatusCode.Ok, message: 'Error accessing the database', error });
        }
    }
}
