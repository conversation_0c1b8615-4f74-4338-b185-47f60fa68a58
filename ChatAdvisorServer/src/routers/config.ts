import express from 'express';
import { getConfig, limit } from '../business/getConfig';
import { getProducts } from '../business/getProducts';
import { getPricing } from '../business/getPricing';
import { generateTitle } from '../business/generateTitle';
import { generateQuestion } from '../business/generateQuestion';
import { checkVersion, getVersionInfo } from '../business/versionCheck';
import errorHandler from '../middlewares/errorHandler';

const router = express.Router();

// 标准API路由（带/api前缀）
router.get('/getPricing', getPricing);
router.get('/getConfig', getConfig);
router.get('/getProducts', getProducts);
router.post('/generateTitle', generateTitle);
router.post('/generateQuestion', generateQuestion);
router.get('/limit', limit);

// 版本控制相关路由
router.get('/checkVersion', checkVersion);
router.get('/versionInfo', getVersionInfo);

router.use(errorHandler);

export default router;