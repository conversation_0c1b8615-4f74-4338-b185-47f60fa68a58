import express from 'express';
import appleLogin, {appleAppSiteAssociation} from '../business/appleAuth';
import verifyPurchase from '../business/verifyPurchase';
import handleRefund from '../business/refund';
import errorHandler from '../middlewares/errorHandler';


const router = express.Router();

router.get('/apple-app-site-association', appleAppSiteAssociation);
router.post('/appleLogin', appleLogin);
router.post('/verifyPurchase', verifyPurchase);
router.post('/refund', handleRefund);
router.use(errorHandler);

export default router;
