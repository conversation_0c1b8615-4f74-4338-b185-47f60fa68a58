import express from 'express';
import login  from '../business/auth';
import  refreshToken  from '../business/refreshToken';
import register from '../business/register';
import sendEmailCode from '../business/sendEmailCode';
import verifyEmailCode from '../business/verifyEmailCode';
import deleteAccount from '../business/deleteAccount';
import userBalance from '../business/userBalance';
import errorHandler from '../middlewares/errorHandler';
import { createValidationMiddleware } from '../middlewares/validationHandler';
import { validationRuleGroups } from '../middlewares/validationRules';

const router = express.Router();

// 使用统一的验证规则组合
router.post('/login', createValidationMiddleware(validationRuleGroups.login), login);
router.post('/register', createValidationMiddleware(validationRuleGroups.register), register);
router.post('/sendEmailCode', createValidationMiddleware(validationRuleGroups.sendEmailCode), sendEmailCode);
router.post('/verifyEmailCode', createValidationMiddleware(validationRuleGroups.emailCodeVerification), verifyEmailCode);
router.get('/userBalance', userBalance);
router.delete('/deleteAccount', deleteAccount);
router.post('/refreshToken', refreshToken);
router.use(errorHandler);
export default router;
