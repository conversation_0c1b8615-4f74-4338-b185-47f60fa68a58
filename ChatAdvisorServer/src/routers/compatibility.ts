import express from 'express';
import multer from 'multer';
import fs from 'fs';
import { getConfig } from '../business/getConfig';
import { getProducts } from '../business/getProducts';
import { getPricing } from '../business/getPricing';
import { checkVersion, getVersionInfo } from '../business/versionCheck';
import { generateTitle } from '../business/generateTitle';
import { generateQuestion } from '../business/generateQuestion';
import userBalance from '../business/userBalance';
import chatWithOpenai from '../business/chat';
import login from '../business/auth';
import refreshToken from '../business/refreshToken';
import register from '../business/register';
import sendEmailCode from '../business/sendEmailCode';
import verifyEmailCode from '../business/verifyEmailCode';
import deleteAccount from '../business/deleteAccount';
import { uploadAudioChat, uploadAudio } from '../business/upload';
import errorHandler from '../middlewares/errorHandler';
import { createValidationMiddleware } from '../middlewares/validationHandler';
import { validationRuleGroups } from '../middlewares/validationRules';

/**
 * 兼容性路由 - 支持旧版本客户端的API路径
 * 这些路由直接在根路径下提供服务，无需/api前缀
 */
const router = express.Router();

// 配置文件上传
const uploadDir = 'uploads';
const upload = multer({ dest: 'uploads/' });

if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir);
}

// 使用统一的验证规则组合，消除重复定义

// 配置相关的兼容性路由
router.get('/getConfig', getConfig);
router.get('/getPricing', getPricing);
router.get('/getProducts', getProducts);

// 版本控制相关的兼容性路由（支持旧版本客户端）
router.get('/checkVersion', checkVersion);
router.get('/versionInfo', getVersionInfo);

// 用户余额的兼容性路由（需要认证）
router.get('/userBalance', userBalance);

// 聊天相关的兼容性路由（需要认证）
router.post('/chat', createValidationMiddleware(validationRuleGroups.chat), chatWithOpenai);
router.post('/generateTitle', generateTitle);

// 问题生成的兼容性路由（不需要认证）
router.post('/generateQuestion', generateQuestion);

// 文件上传的兼容性路由（需要认证）
router.post('/uploadAudioChat', upload.single('audio'), uploadAudioChat);
router.post('/uploadAudio', upload.single('audio'), uploadAudio);

// 认证相关的兼容性路由（不需要预先认证）
router.post('/login', createValidationMiddleware(validationRuleGroups.login), login);
router.post('/register', createValidationMiddleware(validationRuleGroups.register), register);
router.post('/sendEmailCode', createValidationMiddleware(validationRuleGroups.sendEmailCode), sendEmailCode);
router.post('/verifyEmailCode', createValidationMiddleware(validationRuleGroups.emailCodeVerification), verifyEmailCode);
router.post('/refreshToken', refreshToken);
router.delete('/deleteAccount', deleteAccount);

router.use(errorHandler);

export default router;
