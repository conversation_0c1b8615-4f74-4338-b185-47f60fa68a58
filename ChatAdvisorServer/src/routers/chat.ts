import express from 'express';
import chatWithOpenai from '../business/chat';
import errorHandler from '../middlewares/errorHandler';
import { createValidationMiddleware } from '../middlewares/validationHandler';

const router = express.Router();

// 聊天消息验证规则
const chatValidationRules = [
    {
        field: 'messages',
        required: true,
        type: 'array' as const,
        sanitize: true
    },
    {
        field: 'model',
        required: false,
        type: 'string' as const,
        maxLength: 100,
        sanitize: true
    },
    {
        field: 'stream',
        required: false,
        type: 'boolean' as const
    }
];

router.use(errorHandler);
router.post('/chat', createValidationMiddleware(chatValidationRules), chatWithOpenai);

export default router;