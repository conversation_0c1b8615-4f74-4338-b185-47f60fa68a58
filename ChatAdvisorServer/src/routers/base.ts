import express from 'express';

import i18next from '../config/i18nNext';
import morgan from 'morgan';
import i18nextMiddleware from 'i18next-http-middleware';
import { ping, health } from '../business/ping';

const router = express.Router();

router.use(express.text());
router.use(express.json());
router.use(morgan('dev'));
router.use(i18nextMiddleware.handle(i18next));

// {{ AURA-X: Add - 添加ping和健康检查端点用于网络测速和连通性检测. Approval: mcp-feedback-enhanced(ID:***********). }}
// Ping端点 - 用于客户端网络测速
router.get('/ping', ping);
router.post('/ping', ping); // 支持POST请求

// 健康检查端点 - 用于负载均衡器等
router.get('/health', health);

export default router;
