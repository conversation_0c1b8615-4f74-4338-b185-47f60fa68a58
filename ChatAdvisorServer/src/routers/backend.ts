// app.use(bodyParser.urlencoded({ extended: true }));
// app.use(bodyParser.json());
// if (env.isRelease == false) {
//     // 后台数据操作相关
//     // 显示所有价格记录
//     app.get('/pricing', async (req, res) => {
//         try {
//             const pricings = await Pricing.find().lean();
//             const pricingsData = pricings.map(pricing => pricing.toObject());

//             res.render('pricing/pricing', { pricings: pricingsData });
//         } catch (err) {
//             console.error(err);
//             res.status(HttpStatusCode.InternalServerError).send('服务器错误');
//         }
//     });

//     app.delete('/pricing/:id', async (req, res) => {
//         try {
//             const { id } = req.params;
//             await Pricing.findByIdAndDelete(id).lean();
//             res.redirect('/pricing'); // 删除成功后重定向到价格页面
//         } catch (error) {
//             res.status(HttpStatusCode.InternalServerError).send('Internal Server Error');
//         }
//     });

//     // 新增价格记录页面
//     app.get('/pricing/new', (req, res) => {
//         res.render('pricing/new');
//     });

//     // 处理新增价格记录请求
//     app.post('/pricing', async (req, res) => {
//         try {
//             const { modelName, price, count } = req.body;

//             // 创建新的价格记录
//             const newPricing = new Pricing({
//                 modelName,
//                 price,
//                 count
//             });

//             // 保存到数据库
//             const savedPricing = await newPricing.save();

//             res.redirect('pricing'); // 重定向到价格记录页面
//         } catch (err) {
//             console.error(err);
//             res.status(HttpStatusCode.InternalServerError).send('服务器错误');
//         }
//     });
// }