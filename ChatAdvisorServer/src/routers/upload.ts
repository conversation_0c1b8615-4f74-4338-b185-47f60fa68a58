import express from 'express';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import {uploadAudioChat, uploadAudio} from '../business/upload';
import errorHandler from '../middlewares/errorHandler';

const router = express.Router();

const uploadDir = 'uploads';
const upload = multer({ dest: 'uploads/' }); // 上传目录

if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir);
}

router.post('/uploadAudioChat', upload.single('audio'), uploadAudioChat);

router.post('/uploadAudio', upload.single('audio'), uploadAudio);
router.use(errorHandler);

export default router;
