import express from 'express';
import facebookLogin from '../business/facebookLogin';
import facebookDataDeletionCallback from '../business/facebookDataDeletionCallback';
import verifyDeletionRecord from '../business/verifyDeletionRecord';
import path from 'path';
import errorHandler from '../middlewares/errorHandler';

const router = express.Router();

router.post('/facebookLogin', facebookLogin);
router.post('/facebook-data-deletion', facebookDataDeletionCallback);
router.get('/verify-deletion', verifyDeletionRecord);
router.use(errorHandler);

// 提供 HTML 页面
router.get('/deletion', (req, res) => {
    res.sendFile(path.join(__dirname, '../views/deletion.html'));
});

export default router;
