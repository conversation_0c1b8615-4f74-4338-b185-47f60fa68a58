import { IAIServiceConfig } from '../models/AIServiceConfig';
import { IAIServiceModel } from '../models/AIServiceModel';
import { logger } from '../business/logger';

interface CacheItem<T> {
    data: T;
    timestamp: number;
    ttl: number;
}

class AIConfigCache {
    private configCache = new Map<string, CacheItem<IAIServiceConfig>>();
    private modelCache = new Map<string, CacheItem<IAIServiceModel[]>>();
    private defaultConfigCache: CacheItem<IAIServiceConfig> | null = null;
    
    private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5分钟
    private readonly CLEANUP_INTERVAL = 10 * 60 * 1000; // 10分钟清理一次

    constructor() {
        // 定期清理过期缓存
        setInterval(() => {
            this.cleanupExpiredCache();
        }, this.CLEANUP_INTERVAL);
    }

    /**
     * 获取缓存的配置
     */
    async getCachedConfig(id: string): Promise<IAIServiceConfig | null> {
        const cached = this.configCache.get(id);
        if (!cached) {
            return null;
        }

        if (this.isExpired(cached)) {
            this.configCache.delete(id);
            return null;
        }

        logger.debug(`从缓存获取AI配置: ${id}`);
        return cached.data;
    }

    /**
     * 设置配置缓存
     */
    async setCachedConfig(config: IAIServiceConfig, ttl: number = this.DEFAULT_TTL): Promise<void> {
        const cacheItem: CacheItem<IAIServiceConfig> = {
            data: config,
            timestamp: Date.now(),
            ttl
        };

        this.configCache.set(config._id.toString(), cacheItem);
        
        // 如果是默认配置，也缓存到默认配置缓存
        if (config.isDefault) {
            this.defaultConfigCache = cacheItem;
        }

        logger.debug(`缓存AI配置: ${config._id} (${config.name})`);
    }

    /**
     * 获取默认配置缓存
     */
    async getCachedDefaultConfig(): Promise<IAIServiceConfig | null> {
        if (!this.defaultConfigCache) {
            return null;
        }

        if (this.isExpired(this.defaultConfigCache)) {
            this.defaultConfigCache = null;
            return null;
        }

        logger.debug('从缓存获取默认AI配置');
        return this.defaultConfigCache.data;
    }

    /**
     * 清除配置缓存
     */
    async clearConfigCache(id?: string): Promise<void> {
        if (id) {
            this.configCache.delete(id);
            // 如果清除的是默认配置，也清除默认配置缓存
            if (this.defaultConfigCache && this.defaultConfigCache.data._id.toString() === id) {
                this.defaultConfigCache = null;
            }
            logger.debug(`清除AI配置缓存: ${id}`);
        } else {
            this.configCache.clear();
            this.defaultConfigCache = null;
            logger.debug('清除所有AI配置缓存');
        }
    }

    /**
     * 获取缓存的模型列表
     */
    async getCachedModels(configId: string): Promise<IAIServiceModel[]> {
        const cached = this.modelCache.get(configId);
        if (!cached) {
            return [];
        }

        if (this.isExpired(cached)) {
            this.modelCache.delete(configId);
            return [];
        }

        logger.debug(`从缓存获取AI模型列表: ${configId}`);
        return cached.data;
    }

    /**
     * 设置模型缓存
     */
    async setCachedModels(configId: string, models: IAIServiceModel[], ttl: number = this.DEFAULT_TTL): Promise<void> {
        const cacheItem: CacheItem<IAIServiceModel[]> = {
            data: models,
            timestamp: Date.now(),
            ttl
        };

        this.modelCache.set(configId, cacheItem);
        logger.debug(`缓存AI模型列表: ${configId} (${models.length}个模型)`);
    }

    /**
     * 清除模型缓存
     */
    async clearModelCache(configId?: string): Promise<void> {
        if (configId) {
            this.modelCache.delete(configId);
            logger.debug(`清除AI模型缓存: ${configId}`);
        } else {
            this.modelCache.clear();
            logger.debug('清除所有AI模型缓存');
        }
    }

    /**
     * 清除所有缓存
     */
    async clearAllCache(): Promise<void> {
        this.configCache.clear();
        this.modelCache.clear();
        this.defaultConfigCache = null;
        logger.debug('清除所有AI配置缓存');
    }

    /**
     * 获取缓存统计信息
     */
    getCacheStats() {
        return {
            configCacheSize: this.configCache.size,
            modelCacheSize: this.modelCache.size,
            hasDefaultConfig: !!this.defaultConfigCache
        };
    }

    /**
     * 检查缓存项是否过期
     */
    private isExpired(item: CacheItem<any>): boolean {
        return Date.now() - item.timestamp > item.ttl;
    }

    /**
     * 清理过期缓存
     */
    private cleanupExpiredCache(): void {
        let cleanedCount = 0;

        // 清理配置缓存
        for (const [key, item] of this.configCache.entries()) {
            if (this.isExpired(item)) {
                this.configCache.delete(key);
                cleanedCount++;
            }
        }

        // 清理模型缓存
        for (const [key, item] of this.modelCache.entries()) {
            if (this.isExpired(item)) {
                this.modelCache.delete(key);
                cleanedCount++;
            }
        }

        // 清理默认配置缓存
        if (this.defaultConfigCache && this.isExpired(this.defaultConfigCache)) {
            this.defaultConfigCache = null;
            cleanedCount++;
        }

        if (cleanedCount > 0) {
            logger.debug(`清理过期AI配置缓存: ${cleanedCount}项`);
        }
    }
}

// 单例模式
export default new AIConfigCache();
