import mongoose, { Schema, Document } from 'mongoose';

interface Delta {
    role?: string | null;
    content?: string | null;
}

interface Choice {
    index?: number | null;
    delta?: Delta | null;
    logprobs?: string | null;
    finishReason?: string | null;
}

interface ChatCompletionChunk extends Document {
    id: string;
    object?: string | null;
    created?: number | null;
    [key: string]: any; // 使用索引签名以允许任意其他属性
    systemFingerprint?: string | null;
    choices: Choice[];
}

const DeltaSchema = new Schema<Delta>({
    role: { type: String },
    content: { type: String }
});

const ChoiceSchema = new Schema<Choice>({
    index: { type: Number },
    delta: DeltaSchema,
    logprobs: { type: String },
    finishReason: { type: String }
});

const ChatCompletionChunkSchema = new Schema<ChatCompletionChunk>({
    id: { type: String, required: true },
    object: { type: String },
    created: { type: Number },
    model: { type: String }, // 这里修正为正确的字符串类型定义
    systemFingerprint: { type: String },
    choices: [ChoiceSchema]
},{
    versionKey: false
});

const modelName = 'ChatCompletionChunk';


const ChatCompletionChunkModel = mongoose.models[modelName] || mongoose.model<ChatCompletionChunk>(modelName, ChatCompletionChunkSchema);

export { ChatCompletionChunkModel };
