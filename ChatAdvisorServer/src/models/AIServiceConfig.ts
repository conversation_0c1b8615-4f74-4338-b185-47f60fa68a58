import mongoose, { Document, Model, Schema } from 'mongoose';

export interface IAIServiceConfig extends Document {
    name: string;
    description?: string;
    baseURL: string;
    apiKey: string;
    provider: string;
    isActive: boolean;
    isDefault: boolean;
    maxRetries: number;
    timeout: number;
    proxyConfig?: {
        enabled: boolean;
        url?: string;
    };
    rateLimits?: {
        requestsPerMinute: number;
        tokensPerMinute: number;
    };
    createdAt: Date;
    updatedAt: Date;
    createdBy: mongoose.Types.ObjectId;
    lastUsedAt?: Date;
}

const aiServiceConfigSchema = new Schema({
    name: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        maxlength: 100
    },
    description: {
        type: String,
        trim: true,
        maxlength: 500
    },
    baseURL: {
        type: String,
        required: true,
        trim: true,
        validate: {
            validator: function(v: string) {
                return /^https?:\/\/.+/.test(v);
            },
            message: 'baseURL必须是有效的HTTP/HTTPS URL'
        }
    },
    apiKey: {
        type: String,
        required: true,
        select: false // 默认查询时不返回API密钥
    },
    provider: {
        type: String,
        required: true,
        enum: ['openai', 'anthropic', 'google', 'azure', 'custom'],
        default: 'openai'
    },
    isActive: {
        type: Boolean,
        required: true,
        default: true
    },
    isDefault: {
        type: Boolean,
        required: true,
        default: false
    },
    maxRetries: {
        type: Number,
        required: true,
        default: 3,
        min: 0,
        max: 10
    },
    timeout: {
        type: Number,
        required: true,
        default: 60000,
        min: 5000,
        max: 300000
    },
    proxyConfig: {
        enabled: {
            type: Boolean,
            default: false
        },
        url: {
            type: String,
            trim: true
        }
    },
    rateLimits: {
        requestsPerMinute: {
            type: Number,
            default: 60,
            min: 1,
            max: 1000
        },
        tokensPerMinute: {
            type: Number,
            default: 100000,
            min: 1000,
            max: 1000000
        }
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    lastUsedAt: {
        type: Date
    }
}, {
    timestamps: true,
    versionKey: false
});

// 索引
aiServiceConfigSchema.index({ isActive: 1, isDefault: 1 });
aiServiceConfigSchema.index({ provider: 1, isActive: 1 });
aiServiceConfigSchema.index({ createdBy: 1 });

// 中间件：确保只有一个默认配置
aiServiceConfigSchema.pre('save', async function(next) {
    if (this.isDefault && this.isModified('isDefault')) {
        await (this.constructor as Model<IAIServiceConfig>).updateMany(
            { _id: { $ne: this._id }, isDefault: true },
            { isDefault: false }
        );
    }
    next();
});

// 虚拟字段：隐藏敏感信息的安全版本
aiServiceConfigSchema.virtual('safeConfig').get(function() {
    const config = this.toObject();
    if (config.apiKey) {
        config.apiKey = `${config.apiKey.substring(0, 8)}...${config.apiKey.substring(config.apiKey.length - 4)}`;
    }
    return config;
});

const modelName = 'AIServiceConfig';

const AIServiceConfig: Model<IAIServiceConfig> = mongoose.models[modelName] || 
    mongoose.model<IAIServiceConfig>(modelName, aiServiceConfigSchema);

export default AIServiceConfig;
