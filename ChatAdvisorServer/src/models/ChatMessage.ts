import mongoose, { Schema, Document } from 'mongoose';

// 定义 Role 枚举
enum Role {
    ASSISTANT = 'assistant',
    USER = 'user',
    OTHER = 'other'
}

interface ChatMessage extends Document {
    id: string;
    chatId: string;
    userId?: string; // 添加用户ID字段
    role: Role;
    content: string;
    createdTime: Date;
    isComplete: boolean;
}

const ChatMessageSchema = new Schema<ChatMessage>({
    id: { type: String, required: true },
    chatId: { type: String, required: true },
    userId: { type: String, required: false }, // 添加用户ID字段
    role: { type: String, enum: Object.values(Role), required: true, default: Role.ASSISTANT},
    content: { type: String, required: true },
    createdTime: { type: Date, default: Date.now },
    isComplete: { type: Boolean, default: false }
},{
    versionKey: false
});

const modelName = 'ChatMessage';


const ChatMessageModel = mongoose.models[modelName] || mongoose.model<ChatMessage>(modelName, ChatMessageSchema);

export { ChatMessageModel, Role };
