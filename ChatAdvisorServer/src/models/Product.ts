import mongoose, { Schema, model } from 'mongoose';

interface IProduct {
    productIdentifier: string;
    amount: number;
    isEnable: boolean;
    // 国家
    nation: String;
}

const productSchema = new Schema<IProduct>({
    productIdentifier: { type: String, required: true, unique: true },
    amount: { type: Number, required: true },
    isEnable: { type: Boolean, required: true, default: true },
    nation: { type: String, required: true, default: 'en' },
},{
    versionKey: false
});

const modelName = 'Product';

const Product = mongoose.models[modelName] || model<IProduct>(modelName, productSchema);

export { Product };
