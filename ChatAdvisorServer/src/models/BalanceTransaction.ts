import mongoose, { Schema, Document, model } from 'mongoose';

// 定义一个交易类型枚举，1 表示充值，2 入消费，3、出消费
export enum TransactionType {
    Deposit = 1,
    In = 2,
    Out = 3
}

// 定义余额变动记录的接口
interface IBalanceTransaction extends Document {
    userId: string;
    amount: number;
    reason: string;
    timestamp: Date;
    modelId?: string;
    type?: TransactionType;
}

// 定义余额变动记录的模式
const BalanceTransactionSchema: Schema = new Schema({
    userId: { type: String, required: true },
    amount: { type: Number, required: true },
    reason: { type: String, required: true },
    timestamp: { type: Date, default: Date.now },
    modelId: { type: Schema.Types.ObjectId, ref: 'Pricing' },
    type: { type: Number, required: true }
},{
    versionKey: false
});

const modelName = 'BalanceTransaction';

// 创建余额变动记录的模型
const BalanceTransaction = mongoose.models[modelName] || mongoose.model<IBalanceTransaction>(modelName, BalanceTransactionSchema);

export default BalanceTransaction;
