import mongoose, { Document, Model, Schema } from 'mongoose';

export interface IAIConfigUsageLog extends Document {
    configId: mongoose.Types.ObjectId;
    modelId: mongoose.Types.ObjectId;
    userId: mongoose.Types.ObjectId;
    requestId: string;
    tokensUsed: number;
    cost: number;
    responseTime: number;
    success: boolean;
    errorMessage?: string;
    timestamp: Date;
}

const aiConfigUsageLogSchema = new Schema({
    configId: {
        type: Schema.Types.ObjectId,
        ref: 'AIServiceConfig',
        required: true
    },
    modelId: {
        type: Schema.Types.ObjectId,
        ref: 'AIServiceModel',
        required: true
    },
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    requestId: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    tokensUsed: {
        type: Number,
        required: true,
        min: 0
    },
    cost: {
        type: Number,
        required: true,
        min: 0
    },
    responseTime: {
        type: Number,
        required: true,
        min: 0
    },
    success: {
        type: Boolean,
        required: true,
        default: true
    },
    errorMessage: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    timestamp: {
        type: Date,
        required: true,
        default: Date.now
    }
}, {
    versionKey: false
});

// 索引
aiConfigUsageLogSchema.index({ configId: 1, timestamp: -1 });
aiConfigUsageLogSchema.index({ modelId: 1, timestamp: -1 });
aiConfigUsageLogSchema.index({ userId: 1, timestamp: -1 });
aiConfigUsageLogSchema.index({ success: 1, timestamp: -1 });
aiConfigUsageLogSchema.index({ timestamp: -1 });

// TTL索引：30天后自动删除日志
aiConfigUsageLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });

const modelName = 'AIConfigUsageLog';

const AIConfigUsageLog: Model<IAIConfigUsageLog> = mongoose.models[modelName] || 
    mongoose.model<IAIConfigUsageLog>(modelName, aiConfigUsageLogSchema);

export default AIConfigUsageLog;
