import mongoose, { Document, Schema } from 'mongoose';
import { defaultBalance } from '../config/env';

// 用户余额模型的TypeScript接口
export interface IUserBalance extends Document {
    userId: Schema.Types.ObjectId; // 关联用户ID
    balance: number;
}

const userBalanceSchema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User' }, // 关联用户ID
    balance: { type: Number, default: defaultBalance } 
},{
    versionKey: false
});

const modelName = 'UserBalance';

// 检查模型是否已经存在，如果存在则使用已存在的模型

const UserBalance = mongoose.models[modelName] || mongoose.model<IUserBalance>(modelName, userBalanceSchema);


export default UserBalance 
