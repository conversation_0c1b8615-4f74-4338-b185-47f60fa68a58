import mongoose, { Document, Model, Schema } from 'mongoose';

export interface IActiveAIConfig extends Document {
    configId: mongoose.Types.ObjectId;
    modelId: mongoose.Types.ObjectId;
    activatedAt: Date;
    activatedBy: mongoose.Types.ObjectId;
}

export interface IActiveAIConfigModel extends Model<IActiveAIConfig> {
    getActiveConfig(): Promise<IActiveAIConfig | null>;
    setActiveConfig(configId: string, modelId: string, activatedBy: string): Promise<IActiveAIConfig>;
    clearActiveConfig(): Promise<void>;
}

const activeAIConfigSchema = new Schema({
    configId: {
        type: Schema.Types.ObjectId,
        ref: 'AIServiceConfig',
        required: true,
        index: true
    },
    modelId: {
        type: Schema.Types.ObjectId,
        ref: 'AIServiceModel',
        required: true,
        index: true
    },
    activatedAt: {
        type: Date,
        required: true,
        default: Date.now
    },
    activatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    }
}, {
    timestamps: true,
    collection: 'activeaiconfigs'
});

// 确保只有一条记录的索引
activeAIConfigSchema.index({}, { unique: true });

// 实例方法
activeAIConfigSchema.methods.toJSON = function() {
    const obj = this.toObject();
    return obj;
};

// 静态方法
activeAIConfigSchema.statics.getActiveConfig = async function(): Promise<IActiveAIConfig | null> {
    return await this.findOne()
        .populate('configId', 'name baseURL provider isActive')
        .populate('modelId', 'modelName displayName maxTokens pricing');
};

activeAIConfigSchema.statics.setActiveConfig = async function(
    configId: string, 
    modelId: string, 
    activatedBy: string
): Promise<IActiveAIConfig> {
    // 删除现有的启用配置
    await this.deleteMany({});
    
    // 创建新的启用配置
    const activeConfig = new this({
        configId,
        modelId,
        activatedBy
    });
    
    return await activeConfig.save();
};

activeAIConfigSchema.statics.clearActiveConfig = async function(): Promise<void> {
    await this.deleteMany({});
};

const ActiveAIConfig = mongoose.model<IActiveAIConfig, IActiveAIConfigModel>('ActiveAIConfig', activeAIConfigSchema);

export default ActiveAIConfig;
