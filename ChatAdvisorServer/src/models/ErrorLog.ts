// models/ErrorLog.ts
import mongoose, { Document, Schema } from 'mongoose';

interface IErrorLog extends Document {
    requestId: mongoose.Schema.Types.ObjectId;
    timestamp: Date;
    error: string;
    errorCode: Number;
    stack?: string;
}

const ErrorLogSchema: Schema = new Schema({
    requestId: { type: Schema.Types.ObjectId, ref: 'RequestLog', required: true },
    timestamp: { type: Date, default: Date.now },
    error: { type: String, required: true },
    errorCode: { type: Number, required: true },
    stack: { type: String }
},{
    versionKey: false
});
const modelName = 'ErrorLog';
const ErrorLog = mongoose.model<IErrorLog>(modelName, ErrorLogSchema);
export default ErrorLog;
