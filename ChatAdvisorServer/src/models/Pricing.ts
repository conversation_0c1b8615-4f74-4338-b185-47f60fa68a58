import mongoose, { Document, Model, Schema } from 'mongoose';
//
// 定义模型的 TypeScript 接口
export interface IPricing extends Document {
    modelName: string;
    inPrice: number;
    outPrice: number;
    count: number;
    alias: {
        [language: string]: string;
    };
    intro: {
        [language: string]: string;
    };
    // 新增字段：模型信息
    modelInfo?: {
        [key: string]: any;
    };
    // 新增字段：模型完整JSON数据
    modelData?: any;
    // 新增字段：是否通过同步创建
    isSynced?: boolean;
    // 新增字段：同步时间
    syncedAt?: Date;
    // 新增字段：来源配置ID
    sourceConfigId?: string;
}

// 创建价格模式
const pricingSchema = new Schema({
    modelName: { type: String, required: true, unique: false },
    inPrice: { type: Number, required: true },
    outPrice: { type: Number, required: true },
    count: { type: Number, required: true },
    alias: {
        type: Map,
        of: String,
        required: true
    },
    intro: {
        type: Map,
        of: String,
        required: false
    },
    // 新增字段：模型信息（动态属性）
    modelInfo: {
        type: Map,
        of: Schema.Types.Mixed,
        required: false
    },
    // 新增字段：模型完整JSON数据
    modelData: {
        type: Schema.Types.Mixed,
        required: false
    },
    // 新增字段：是否通过同步创建
    isSynced: {
        type: Boolean,
        default: false
    },
    // 新增字段：同步时间
    syncedAt: {
        type: Date,
        required: false
    },
    // 新增字段：来源配置ID
    sourceConfigId: {
        type: Schema.Types.ObjectId,
        ref: 'AIServiceConfig',
        required: false
    }
},{
    versionKey: false,
    timestamps: true // 自动添加 createdAt 和 updatedAt
});

const modelName = 'Pricing';

// 检查模型是否已经存在，如果存在则使用已存在的模型
const Pricing: Model<IPricing> = mongoose.models[modelName] || mongoose.model<IPricing>(modelName, pricingSchema);
export default Pricing;