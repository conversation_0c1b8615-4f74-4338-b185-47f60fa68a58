// models/sho
import mongoose, { Document, Schema } from 'mongoose';

interface IRequestLog extends Document {
  url: string;
  method: string;
  requestBody: any;
  requestTime: Date;
}

const RequestLogSchema: Schema = new Schema({
  url: { type: String, required: true },
  method: { type: String, required: true },
  requestBody: { type: Schema.Types.Mixed },
  requestTime: { type: Date, default: Date.now }
},{
  versionKey: false
});
const modelName = 'RequestLog';

const RequestLog = mongoose.models[modelName] || mongoose.model<IRequestLog>(modelName, RequestLogSchema);
export default RequestLog;
