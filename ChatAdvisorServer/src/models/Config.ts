import mongoose,{ Schema, model, Model } from 'mongoose';

interface IConfig {
  privacyPolicy: string;
  termsOfService: string;
  appVersion: string;
  supportEmail: string;
  featureFlags: { [key: string]: boolean };
  mainSolgan: { [language: string]: string[] };
  registerSolgan: { [language: string]: string[] };
  emailLoginSolgan: { [language: string]: string[] };
  rechargeMessages: { [language: string]: string[] };
  hideMessage: { [language: string]: string[] };
  rechargeDescription: { [language: string]: string };
  promotLocal: { [language: string]: string };
  promotCloud: { [language: string]: string };
  compressRate: number;
  // 版本控制相关字段
  latestVersion: string;                                    // 最新版本号
  minimumVersion: string;                                   // 最低支持版本号
  forceUpdate: boolean;                                     // 是否强制更新
  updateMessage: { [language: string]: string };           // 更新提示消息（多语言）
  appStoreUrls: {                                          // 应用商店下载链接
    ios: string;
    android: string;
  };
  updateType: 'force' | 'optional';                        // 更新类型（强制/可选）
  versionCheckEnabled: boolean;                             // 是否启用版本检测
}

const configSchema = new Schema({
  privacyPolicy: { type: String, required: true },
  termsOfService: { type: String, required: true },
  appVersion: { type: String, required: true },
  supportEmail: { type: String, required: true },
  featureFlags: { type: Map, of: Boolean, required: true },
  mainSolgan: { type: Map, of: [String], required: true },
  registerSolgan: { type: Map, of: [String], required: true },
  emailLoginSolgan: { type: Map, of: [String], required: true },
  rechargeMessages: { type: Map, of: [String], required: true },
  hideMessage: { type: Map, of: [String], required: true },
  rechargeDescription: { type: Map, of: String, required: true },
  promotCloud: { type: Map, of: String, required: false },
  promotLocal: { type: Map, of: String, required: false },
  compressRate: { type: Number, required: true },
  // 版本控制相关字段的Schema定义
  latestVersion: { type: String, required: true, default: '1.0.0' },
  minimumVersion: { type: String, required: true, default: '1.0.0' },
  forceUpdate: { type: Boolean, required: true, default: false },
  updateMessage: {
    type: Map,
    of: String,
    required: true,
    default: new Map()
  },
  appStoreUrls: {
    ios: { type: String, required: true, default: 'APP_STORE_URL_PLACEHOLDER' },
    android: { type: String, required: true, default: 'GOOGLE_PLAY_URL_PLACEHOLDER' }
  },
  updateType: {
    type: String,
    enum: ['force', 'optional'],
    required: true,
    default: 'optional'
  },
  versionCheckEnabled: { type: Boolean, required: true, default: true }
},{
  versionKey: false
});

const modelName = 'Config';


const Config: Model<IConfig>  = mongoose.models[modelName] || model<IConfig>(modelName, configSchema);

export { Config };
