import mongoose, { Schema, Document } from 'mongoose';

interface IInAppReceipt {
    quantity: string;
    product_id: string;
    transaction_id: string;
    original_transaction_id: string;
    purchase_date: string;
    purchase_date_ms: string;
    purchase_date_pst: string;
    original_purchase_date: string;
    original_purchase_date_ms: string;
    original_purchase_date_pst: string;
    is_trial_period: string;
    in_app_ownership_type: string;
}

interface IReceipt {
    receipt_type: string;
    adam_id: number;
    app_item_id: number;
    bundle_id: string;
    application_version: string;
    download_id: number;
    version_external_identifier: number;
    receipt_creation_date: string;
    receipt_creation_date_ms: string;
    receipt_creation_date_pst: string;
    request_date: string;
    request_date_ms: string;
    request_date_pst: string;
    original_purchase_date: string;
    original_purchase_date_ms: string;
    original_purchase_date_pst: string;
    original_application_version: string;
    in_app: IInAppReceipt[];
}

interface IVerificationResult extends Document {
    receipt: IReceipt;
    environment: string;
    status: number;
    createdAt: Date;
}

const InAppReceiptSchema: Schema = new Schema({
    quantity: { type: String, required: true },
    product_id: { type: String, required: true },
    transaction_id: { type: String, required: true },
    original_transaction_id: { type: String, required: true },
    purchase_date: { type: String, required: true },
    purchase_date_ms: { type: String, required: true },
    purchase_date_pst: { type: String, required: true },
    original_purchase_date: { type: String, required: true },
    original_purchase_date_ms: { type: String, required: true },
    original_purchase_date_pst: { type: String, required: true },
    is_trial_period: { type: String, required: true },
    in_app_ownership_type: { type: String, required: true }
});

const ReceiptSchema: Schema = new Schema({
    receipt_type: { type: String, required: true },
    adam_id: { type: Number, required: true },
    app_item_id: { type: Number, required: true },
    bundle_id: { type: String, required: true },
    application_version: { type: String, required: true },
    download_id: { type: Number, required: true },
    version_external_identifier: { type: Number, required: true },
    receipt_creation_date: { type: String, required: true },
    receipt_creation_date_ms: { type: String, required: true },
    receipt_creation_date_pst: { type: String, required: true },
    request_date: { type: String, required: true },
    request_date_ms: { type: String, required: true },
    request_date_pst: { type: String, required: true },
    original_purchase_date: { type: String, required: true },
    original_purchase_date_ms: { type: String, required: true },
    original_purchase_date_pst: { type: String, required: true },
    original_application_version: { type: String, required: true },
    in_app: { type: [InAppReceiptSchema], required: true }
});

const VerificationResultSchema: Schema = new Schema({
    receipt: { type: ReceiptSchema, required: true },
    environment: { type: String, required: true },
    status: { type: Number, required: true },
    createdAt: { type: Date, default: Date.now }
},{
    versionKey: false
});

const VerificationResult = mongoose.model<IVerificationResult>('VerificationResult', VerificationResultSchema);

export default VerificationResult;
export { IVerificationResult };
