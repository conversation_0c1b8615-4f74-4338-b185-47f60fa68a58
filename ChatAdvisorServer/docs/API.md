# ChatAdvisor API 文档

## 基础信息

- **Base URL**: `https://advisor.sanva.tk` (生产环境)
- **Base URL**: `http://localhost:33001` (开发环境)
- **API Version**: v1
- **Content-Type**: `application/json`

## 认证

大部分 API 需要在请求头中包含 JWT token：

```http
Authorization: Bearer <your_jwt_token>
```

## 响应格式

所有 API 响应都遵循统一格式：

```json
{
  "code": 200,
  "message": "Success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 错误处理

错误响应格式：

```json
{
  "code": 400,
  "message": "Validation failed",
  "data": {
    "errors": ["Email is required", "Password must be at least 6 characters"]
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## API 端点

### 1. 认证相关

#### 1.1 用户注册

**POST** `/api/auth/register`

注册新用户账户。

**请求体：**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123"
}
```

**响应：**
```json
{
  "code": 201,
  "message": "User registered successfully",
  "data": {
    "userId": "64f1a2b3c4d5e6f7g8h9i0j1",
    "email": "<EMAIL>",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### 1.2 用户登录

**POST** `/api/auth/login`

用户登录获取访问令牌。

**请求体：**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应：**
```json
{
  "code": 200,
  "message": "Login successful",
  "data": {
    "userId": "64f1a2b3c4d5e6f7g8h9i0j1",
    "email": "<EMAIL>",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "balance": 50
  }
}
```

#### 1.3 发送邮箱验证码

**POST** `/api/auth/sendEmailCode`

发送邮箱验证码。

**请求体：**
```json
{
  "email": "<EMAIL>"
}
```

#### 1.4 验证邮箱验证码

**POST** `/api/auth/verifyEmailCode`

验证邮箱验证码。

**请求体：**
```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

#### 1.5 刷新令牌

**POST** `/api/auth/refreshToken`

刷新访问令牌。

**请求头：**
```http
Authorization: Bearer <refresh_token>
```

#### 1.6 删除账户

**DELETE** `/api/auth/deleteAccount`

删除用户账户。

**请求头：**
```http
Authorization: Bearer <access_token>
```

### 2. 第三方登录

#### 2.1 Apple 登录

**POST** `/api/oauth/appleLogin`

使用 Apple ID 登录。

**请求体：**
```json
{
  "identityToken": "apple_identity_token",
  "authorizationCode": "apple_authorization_code"
}
```

#### 2.2 Google 登录

**POST** `/api/oauth/googleLogin`

使用 Google 账户登录。

**请求体：**
```json
{
  "idToken": "google_id_token"
}
```

### 3. 聊天相关

#### 3.1 发送聊天消息

**POST** `/api/chat`

发送聊天消息并获取 AI 回复。

**请求头：**
```http
Authorization: Bearer <access_token>
```

**请求体：**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ],
  "model": "gpt-3.5-turbo",
  "stream": false
}
```

**响应：**
```json
{
  "code": 200,
  "message": "Chat completed",
  "data": {
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": 1677652288,
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "Hello! I'm doing well, thank you for asking. How can I help you today?"
        },
        "finish_reason": "stop"
      }
    ],
    "usage": {
      "prompt_tokens": 9,
      "completion_tokens": 12,
      "total_tokens": 21
    }
  }
}
```

#### 3.2 生成标题

**POST** `/api/generateTitle`

为聊天对话生成标题。

**请求头：**
```http
Authorization: Bearer <access_token>
```

**请求体：**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "What's the weather like today?"
    }
  ]
}
```

### 4. 用户相关

#### 4.1 获取用户余额

**GET** `/api/userBalance`

获取当前用户的余额信息。

**请求头：**
```http
Authorization: Bearer <access_token>
```

**响应：**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "balance": 50,
    "currency": "credits"
  }
}
```

### 5. 文件上传

#### 5.1 上传音频文件

**POST** `/api/uploadAudio`

上传音频文件进行处理。

**请求头：**
```http
Authorization: Bearer <access_token>
Content-Type: multipart/form-data
```

**请求体：**
```
audio: <audio_file>
```

### 6. 配置相关

#### 6.1 获取应用配置

**GET** `/api/getConfig`

获取应用配置信息。

**响应：**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "appVersion": "1.0.0",
    "supportEmail": "<EMAIL>",
    "privacyPolicy": "https://advisor.sanva.tk/privacy.html",
    "termsOfService": "https://advisor.sanva.tk/userTerms.html"
  }
}
```

#### 6.2 获取产品信息

**GET** `/api/getProducts`

获取可购买的产品列表。

#### 6.3 获取定价信息

**GET** `/api/getPricing`

获取定价信息。

### 7. 支付相关

#### 7.1 验证购买

**POST** `/api/verifyPurchase`

验证应用内购买。

**请求体：**
```json
{
  "receiptData": "base64_encoded_receipt",
  "productId": "com.example.product.id"
}
```

#### 7.2 退款

**POST** `/api/refund`

处理退款请求。

## 状态码说明

- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源未找到
- `409` - 资源冲突
- `500` - 服务器内部错误

## 限制说明

- API 请求频率限制：每分钟 100 次
- 文件上传大小限制：10MB
- 聊天消息长度限制：10,000 字符
- 单次聊天消息数量限制：50 条

## 示例代码

### JavaScript/Node.js

```javascript
const axios = require('axios');

// 登录
const login = async () => {
  try {
    const response = await axios.post('https://advisor.sanva.tk/api/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    const { token } = response.data.data;
    return token;
  } catch (error) {
    console.error('Login failed:', error.response.data);
  }
};

// 发送聊天消息
const sendMessage = async (token, message) => {
  try {
    const response = await axios.post('https://advisor.sanva.tk/api/chat', {
      messages: [{ role: 'user', content: message }]
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data.data;
  } catch (error) {
    console.error('Chat failed:', error.response.data);
  }
};
```

### cURL

```bash
# 登录
curl -X POST https://advisor.sanva.tk/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# 发送聊天消息
curl -X POST https://advisor.sanva.tk/api/chat \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"messages":[{"role":"user","content":"Hello"}]}'
```
