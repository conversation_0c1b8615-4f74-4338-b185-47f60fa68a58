# AI服务配置管理系统

## 概述

AI服务配置管理系统是ChatAdvisor的核心功能之一，用于管理多套AI服务配置，支持动态切换、热更新和统一管理。

## 功能特性

### 核心功能
- **多配置管理**：支持同时管理多套AI服务配置
- **动态切换**：无需重启服务即可切换AI配置
- **安全存储**：API密钥采用AES-256-GCM加密存储
- **连接测试**：支持配置连接测试和模型列表获取
- **使用统计**：详细的配置使用统计和监控
- **缓存机制**：智能缓存提升性能

### 支持的AI服务提供商
- OpenAI
- Anthropic
- Google AI
- Azure OpenAI
- 自定义服务

## 安装和配置

### 1. 环境变量配置

运行环境检查脚本：
```bash
npm run ai-config:check
```

该脚本会自动检查并生成所需的环境变量。将生成的配置添加到 `.env` 文件中：

```bash
# AI配置管理环境变量
AI_CONFIG_ENCRYPTION_KEY=your_32_byte_hex_key_here
```

### 2. 数据库迁移

系统会自动创建所需的数据库集合：
- `aiserviceconfigs` - AI服务配置
- `aiservicemodels` - AI模型配置
- `aiconfigusagelogs` - 使用日志

### 3. 初始配置

首次使用时，建议通过管理后台创建默认配置：

1. 访问管理后台：`http://localhost:33001/admin`
2. 导航到 "系统管理" > "AI配置管理"
3. 点击 "新增配置" 创建第一个配置
4. 设置为默认配置

## API接口

### 配置管理接口

#### 获取配置列表
```http
GET /api/admin/ai/configs
```

参数：
- `page`: 页码（可选）
- `limit`: 每页数量（可选）
- `search`: 搜索关键词（可选）
- `provider`: 服务提供商（可选）
- `isActive`: 是否启用（可选）

#### 创建配置
```http
POST /api/admin/ai/configs
```

请求体：
```json
{
  "name": "OpenAI配置",
  "description": "主要的OpenAI配置",
  "baseURL": "https://api.openai.com/v1",
  "apiKey": "sk-...",
  "provider": "openai",
  "maxRetries": 3,
  "timeout": 60000,
  "proxyConfig": {
    "enabled": false
  },
  "rateLimits": {
    "requestsPerMinute": 60,
    "tokensPerMinute": 100000
  }
}
```

#### 测试配置
```http
POST /api/admin/ai/configs/:id/test
```

#### 设置默认配置
```http
POST /api/admin/ai/configs/:id/set-default
```

### 模型管理接口

#### 同步模型列表
```http
POST /api/admin/ai/configs/:configId/sync-models
```

#### 获取模型列表
```http
GET /api/admin/ai/models?configId=:configId
```

### 统计接口

#### 获取使用统计
```http
GET /api/admin/ai/configs/:configId/usage-stats
```

参数：
- `startDate`: 开始日期（可选）
- `endDate`: 结束日期（可选）

## 使用指南

### 1. 创建AI配置

1. 在管理后台点击 "新增配置"
2. 填写配置信息：
   - **配置名称**：便于识别的名称
   - **描述**：配置的详细说明
   - **服务提供商**：选择AI服务提供商
   - **API端点**：AI服务的API地址
   - **API密钥**：服务商提供的密钥
3. 配置高级选项：
   - **重试次数**：请求失败时的重试次数
   - **超时时间**：请求超时时间
   - **速率限制**：每分钟请求数和token数限制
   - **代理设置**：如需要可配置代理
4. 点击 "创建" 保存配置

### 2. 测试配置

创建配置后，建议进行连接测试：

1. 在配置列表中点击 "测试" 按钮
2. 系统会尝试连接AI服务并获取可用模型
3. 测试成功后会显示响应时间和模型列表
4. 测试失败会显示具体错误信息

### 3. 设置默认配置

系统需要一个默认配置用于聊天服务：

1. 在配置列表中找到要设为默认的配置
2. 点击 "设为默认" 按钮
3. 系统会自动取消其他配置的默认状态

### 4. 管理模型

每个配置可以管理其支持的模型：

1. 切换到 "模型管理" 标签页
2. 选择要管理的配置
3. 点击 "同步模型" 自动获取模型列表
4. 可以编辑模型的显示名称、定价等信息

### 5. 查看统计

在 "使用统计" 标签页可以查看：
- 总请求数
- 成功/失败请求数
- Token使用量
- 成本统计
- 平均响应时间

## 安全考虑

### API密钥安全
- 所有API密钥使用AES-256-GCM加密存储
- 加密密钥存储在环境变量中
- 前端显示时自动脱敏

### 权限控制
- 只有系统管理员可以管理AI配置
- 所有操作都有审计日志
- 支持配置级别的访问控制

### 数据保护
- 使用日志自动过期（30天）
- 敏感信息不记录在日志中
- 支持配置备份和恢复

## 故障排除

### 常见问题

#### 1. 配置测试失败
- 检查API密钥是否正确
- 验证API端点地址
- 确认网络连接正常
- 检查代理设置

#### 2. 模型同步失败
- 确保配置测试通过
- 检查API权限
- 验证服务商API状态

#### 3. 聊天服务无法使用
- 确认有默认配置
- 检查默认配置是否启用
- 查看错误日志

### 日志查看

系统日志位置：
- 应用日志：`logs/app.log`
- 错误日志：`logs/error.log`
- AI配置日志：数据库中的使用日志

### 性能优化

- 配置缓存TTL：5分钟
- 客户端缓存TTL：30分钟
- 定期清理过期缓存
- 监控缓存命中率

## 最佳实践

1. **配置命名**：使用清晰的命名规范
2. **定期测试**：定期测试配置连接状态
3. **监控使用**：关注使用统计和成本
4. **备份配置**：定期备份重要配置
5. **安全审计**：定期检查访问日志

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的配置管理功能
- 实现加密存储和缓存机制
- 提供完整的管理界面
