# ChatAdvisor 管理后台 API 接口规范

## API 设计原则

### RESTful 设计
- 使用标准HTTP方法 (GET, POST, PUT, DELETE)
- 资源导向的URL设计
- 状态码语义化
- 无状态设计

### 统一前缀
所有管理后台API使用统一前缀：`/api/admin`

## 统一响应格式

### 成功响应格式
```json
{
  "success": true,
  "data": {
    // 具体数据内容
  },
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "uuid-string"
}
```

### 分页响应格式
```json
{
  "success": true,
  "data": {
    "items": [
      // 数据项数组
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "message": "查询成功",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "uuid-string"
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "数据验证失败",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "requestId": "uuid-string"
}
```

## 分页参数规范

### 请求参数
```
GET /api/admin/users?page=1&limit=20&sortBy=createdAt&sortOrder=desc
```

**标准分页参数：**
- `page`: 页码，从1开始，默认1
- `limit`: 每页数量，默认20，最大100
- `sortBy`: 排序字段，默认createdAt
- `sortOrder`: 排序方向，asc/desc，默认desc

### 搜索和筛选参数
```
GET /api/admin/users?search=john&status=active&dateFrom=2024-01-01&dateTo=2024-12-31
```

**通用筛选参数：**
- `search`: 关键词搜索
- `status`: 状态筛选
- `dateFrom`: 开始日期
- `dateTo`: 结束日期
- `createdBy`: 创建者筛选

## HTTP状态码规范

### 成功状态码
- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `204 No Content`: 删除成功，无返回内容

### 客户端错误状态码
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `409 Conflict`: 资源冲突
- `422 Unprocessable Entity`: 数据验证失败

### 服务器错误状态码
- `500 Internal Server Error`: 服务器内部错误
- `502 Bad Gateway`: 网关错误
- `503 Service Unavailable`: 服务不可用

## 错误代码规范

### 系统级错误 (1000-1999)
- `1000`: 系统内部错误
- `1001`: 数据库连接错误
- `1002`: 服务不可用
- `1003`: 请求超时

### 认证授权错误 (2000-2999)
- `2000`: 未认证
- `2001`: 认证失败
- `2002`: 权限不足
- `2003`: 账户被锁定
- `2004`: 会话过期

### 数据验证错误 (3000-3999)
- `3000`: 数据验证失败
- `3001`: 必填字段缺失
- `3002`: 数据格式错误
- `3003`: 数据长度超限
- `3004`: 数据重复

### 业务逻辑错误 (4000-4999)
- `4000`: 业务逻辑错误
- `4001`: 资源不存在
- `4002`: 操作不允许
- `4003`: 状态不正确
- `4004`: 余额不足

## API 路由设计

### 用户管理 API
```
GET    /api/admin/users                    # 获取用户列表
GET    /api/admin/users/:id               # 获取用户详情
POST   /api/admin/users                   # 创建用户
PUT    /api/admin/users/:id               # 更新用户
DELETE /api/admin/users/:id               # 删除用户
POST   /api/admin/users/:id/balance       # 调整用户余额
PUT    /api/admin/users/:id/status        # 更新用户状态
GET    /api/admin/users/stats             # 用户统计
POST   /api/admin/users/batch             # 批量操作
GET    /api/admin/users/export            # 导出用户数据
```

### 聊天管理 API
```
GET    /api/admin/chat/messages           # 获取消息列表
GET    /api/admin/chat/messages/:id       # 获取消息详情
DELETE /api/admin/chat/messages/:id       # 删除消息
GET    /api/admin/chat/sessions           # 获取会话列表
GET    /api/admin/chat/sessions/:id       # 获取会话详情
GET    /api/admin/chat/stats              # 聊天统计
```

### 财务管理 API
```
GET    /api/admin/financial/transactions  # 获取交易记录
GET    /api/admin/financial/transactions/:id # 获取交易详情
GET    /api/admin/financial/products      # 获取产品列表
POST   /api/admin/financial/products      # 创建产品
PUT    /api/admin/financial/products/:id  # 更新产品
DELETE /api/admin/financial/products/:id  # 删除产品
GET    /api/admin/financial/pricing       # 获取定价列表
POST   /api/admin/financial/pricing       # 创建定价
PUT    /api/admin/financial/pricing/:id   # 更新定价
GET    /api/admin/financial/stats         # 财务统计
```

### 系统管理 API
```
GET    /api/admin/system/config           # 获取系统配置
PUT    /api/admin/system/config           # 更新系统配置
GET    /api/admin/system/questions        # 获取问题列表
POST   /api/admin/system/questions        # 创建问题
PUT    /api/admin/system/questions/:id    # 更新问题
DELETE /api/admin/system/questions/:id    # 删除问题
GET    /api/admin/system/health           # 系统健康检查
```

### 日志管理 API
```
GET    /api/admin/logs/requests           # 获取请求日志
GET    /api/admin/logs/responses          # 获取响应日志
GET    /api/admin/logs/errors             # 获取错误日志
GET    /api/admin/logs/stats              # 日志统计
DELETE /api/admin/logs/cleanup            # 清理日志
```

### 统计报表 API
```
GET    /api/admin/analytics/dashboard     # 仪表板数据
GET    /api/admin/analytics/users         # 用户统计
GET    /api/admin/analytics/chat          # 聊天统计
GET    /api/admin/analytics/financial     # 财务统计
GET    /api/admin/analytics/system        # 系统统计
POST   /api/admin/analytics/export        # 导出报表
```

## 请求头规范

### 必需请求头
```
Authorization: Bearer <jwt-token>
Content-Type: application/json
Accept: application/json
```

### 可选请求头
```
X-Request-ID: <uuid>              # 请求追踪ID
X-Client-Version: <version>       # 客户端版本
X-User-Agent: <user-agent>        # 用户代理
```

## 数据验证规范

### 输入验证
- 所有输入数据必须进行验证
- 使用express-validator进行参数验证
- 支持自定义验证规则
- 提供详细的验证错误信息

### 输出过滤
- 敏感字段自动过滤（如密码）
- 支持字段选择性返回
- 数据脱敏处理

## 缓存策略

### 缓存级别
- **无缓存**: 实时数据（如余额、状态）
- **短期缓存**: 统计数据（5分钟）
- **长期缓存**: 配置数据（1小时）

### 缓存键规范
```
admin:users:list:page:1:limit:20
admin:stats:users:daily:2024-01-01
admin:config:system
```

## 限流规范

### 限流策略
- **全局限流**: 1000请求/分钟
- **用户限流**: 100请求/分钟
- **敏感操作**: 10请求/分钟

### 限流响应
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "请求频率超限，请稍后重试",
    "retryAfter": 60
  }
}
```

## 日志记录规范

### 访问日志
- 记录所有API请求
- 包含请求参数、响应状态、耗时
- 敏感数据脱敏

### 操作日志
- 记录所有数据变更操作
- 包含操作者、操作时间、变更内容
- 支持操作回滚

### 错误日志
- 记录所有系统错误
- 包含错误堆栈、请求上下文
- 支持错误告警

## 版本控制

### API版本策略
- 使用URL路径版本控制：`/api/v1/admin/`
- 向后兼容原则
- 版本废弃通知机制

### 版本迁移
- 提供版本迁移指南
- 支持多版本并存
- 平滑升级策略

## 安全规范

### 认证机制
- JWT Token认证
- Token自动刷新
- 多设备登录控制

### 权限控制
- 基于角色的权限控制(RBAC)
- 细粒度权限设计
- 权限继承机制

### 数据安全
- 敏感数据加密存储
- 传输数据HTTPS加密
- 数据访问审计

## 监控指标

### 性能指标
- API响应时间
- 请求成功率
- 并发用户数
- 数据库查询性能

### 业务指标
- 用户活跃度
- 功能使用率
- 错误发生率
- 数据增长趋势

## 开发工具

### API文档
- 使用Swagger/OpenAPI规范
- 自动生成API文档
- 提供在线测试功能

### 测试工具
- 单元测试覆盖
- 集成测试验证
- 性能测试评估
