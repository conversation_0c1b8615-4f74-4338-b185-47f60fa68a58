# ChatAdvisor 数据模型分析文档

## 概述
本文档详细分析了ChatAdvisor项目中的所有数据模型，包括字段结构、数据类型、约束条件和模型之间的关系。

## 数据模型清单

### 1. User（用户模型）
**文件位置：** `src/models/User.ts`
**集合名称：** `users`

#### 主要字段：
- `_id`: ObjectId（主键）
- `username`: String（用户名，可选）
- `password`: String（密码，可选）
- `email`: String（邮箱，必填，唯一）
- `fullName`: String（全名，可选）
- `birthDate`: Date（生日，可选）
- `gender`: String（性别：Male/Female/Other，可选）
- `phone`: String（电话，可选）
- `address`: Object（地址信息，可选）
  - `street`: String
  - `city`: String
  - `state`: String
  - `country`: String
  - `postalCode`: String
- `language`: String（语言，默认zh_CN）
- `timeZone`: String（时区，可选）
- `occupation`: String（职业，可选）
- `company`: String（公司，可选）
- `allergies`: Array[String]（过敏信息）
- `medicalConditions`: Array[String]（医疗状况）
- `balance`: Number（余额，默认值来自配置）
- `isDelete`: Boolean（软删除标记，默认false）
- `hasPurchase`: Boolean（是否有购买记录，默认false）
- `externalAccounts`: Object（外部账户绑定）
  - `weChatId`: String（微信ID，唯一）
  - `qqId`: String（QQ ID，唯一）
  - `appleInfo`: Object（Apple登录信息）
  - `googleInfo`: Object（Google登录信息）
  - `facebookInfo`: Object（Facebook登录信息）
  - `twitterInfo`: Object（Twitter登录信息）
  - `tikTokInfo`: Object（TikTok登录信息）

#### 特殊功能：
- 虚拟属性：`userId`（_id的字符串格式）
- 后置钩子：保存用户时自动创建余额记录

### 2. ChatMessage（聊天消息模型）
**文件位置：** `src/models/ChatMessage.ts`
**集合名称：** `chatmessages`

#### 主要字段：
- `id`: String（消息ID，必填）
- `chatId`: String（聊天ID，必填）
- `role`: String（角色：assistant/user/other，必填）
- `content`: String（消息内容，必填）
- `createdTime`: Date（创建时间，默认当前时间）
- `isComplete`: Boolean（是否完成，默认false）

### 3. Balance（用户余额模型）
**文件位置：** `src/models/Balance.ts`
**集合名称：** `userbalances`

#### 主要字段：
- `userId`: ObjectId（关联用户ID，必填）
- `balance`: Number（余额，默认值来自配置）

### 4. BalanceTransaction（余额交易记录模型）
**文件位置：** `src/models/BalanceTransaction.ts`
**集合名称：** `balancetransactions`

#### 主要字段：
- `userId`: String（用户ID，必填）
- `amount`: Number（交易金额，必填）
- `reason`: String（交易原因，必填）
- `timestamp`: Date（时间戳，默认当前时间）
- `modelId`: ObjectId（关联定价模型ID，可选）
- `type`: Number（交易类型：1-充值，2-入账，3-出账，必填）

### 5. Product（产品模型）
**文件位置：** `src/models/Product.ts`
**集合名称：** `products`

#### 主要字段：
- `productIdentifier`: String（产品标识符，必填，唯一）
- `amount`: Number（金额，必填）
- `isEnable`: Boolean（是否启用，必填，默认true）
- `nation`: String（国家，必填，默认'en'）

### 6. RequestLog（请求日志模型）
**文件位置：** `src/models/RequestLog.ts`
**集合名称：** `requestlogs`

#### 主要字段：
- `url`: String（请求URL，必填）
- `method`: String（请求方法，必填）
- `requestBody`: Mixed（请求体，可选）
- `requestTime`: Date（请求时间，默认当前时间）

### 7. ResponseLog（响应日志模型）
**文件位置：** `src/models/ResponseLog.ts`
**集合名称：** `responselogs`

#### 主要字段：
- `requestLogId`: ObjectId（关联请求日志ID，必填）
- `requestBody`: Mixed（请求体，可选）
- `responseBody`: Mixed（响应体，可选）
- `responseStatus`: Number（响应状态码，必填）
- `responseTime`: Date（响应时间，默认当前时间）

### 8. ErrorLog（错误日志模型）
**文件位置：** `src/models/ErrorLog.ts`
**集合名称：** `errorlogs`

#### 主要字段：
- `requestId`: ObjectId（关联请求ID，必填）
- `timestamp`: Date（时间戳，默认当前时间）
- `error`: String（错误信息，必填）
- `errorCode`: Number（错误代码，必填）
- `stack`: String（错误堆栈，可选）

### 9. Config（配置模型）
**文件位置：** `src/models/Config.ts`
**集合名称：** `configs`

#### 主要字段：
- `privacyPolicy`: String（隐私政策，必填）
- `termsOfService`: String（服务条款，必填）
- `appVersion`: String（应用版本，必填）
- `supportEmail`: String（支持邮箱，必填）
- `featureFlags`: Map[Boolean]（功能开关，必填）
- `mainSolgan`: Map[Array[String]]（主标语，多语言，必填）
- `registerSolgan`: Map[Array[String]]（注册标语，多语言，必填）
- `emailLoginSolgan`: Map[Array[String]]（邮箱登录标语，多语言，必填）
- `rechargeMessages`: Map[Array[String]]（充值消息，多语言，必填）
- `hideMessage`: Map[Array[String]]（隐藏消息，多语言，必填）
- `rechargeDescription`: Map[String]（充值描述，多语言，必填）
- `promotLocal`: Map[String]（本地推广，多语言，可选）
- `promotCloud`: Map[String]（云端推广，多语言，可选）
- `compressRate`: Number（压缩率，必填）

### 10. Pricing（定价模型）
**文件位置：** `src/models/Pricing.ts`
**集合名称：** `pricings`

#### 主要字段：
- `modelName`: String（模型名称，必填）
- `inPrice`: Number（输入价格，必填）
- `outPrice`: Number（输出价格，必填）
- `count`: Number（计数，必填）
- `alias`: Map[String]（别名，多语言，必填）
- `intro`: Map[String]（介绍，多语言，可选）

### 11. Question（问题模型）
**文件位置：** `src/models/Question.ts`
**集合名称：** `questions`

#### 主要字段：
- `sketch`: Map[String]（概要，多语言，必填）
- `content`: Map[String]（内容，多语言，必填）
- `question`: Map[String]（问题，多语言，必填）

### 12. UserVerification（用户验证模型）
**文件位置：** `src/models/UserVerification.ts`
**集合名称：** `userverifications`

#### 主要字段：
- `userId`: String（用户ID，必填）
- `verificationResult`: ObjectId（关联验证结果，必填）
- `amount`: Number（金额，必填）
- `createdAt`: Date（创建时间，默认当前时间）

### 13. VerificationCode（验证码模型）
**文件位置：** `src/models/VerificationCode.ts`
**集合名称：** `verificationcodes`

#### 主要字段：
- `email`: String（邮箱，必填）
- `code`: String（验证码，必填）
- `expiresAt`: Date（过期时间，必填）

#### 特殊功能：
- TTL索引：自动过期删除

### 14. VerificationResult（验证结果模型）
**文件位置：** `src/models/VerificationResult.ts`
**集合名称：** `verificationresults`

#### 主要字段：
- `receipt`: Object（收据信息，复杂嵌套结构）
- `environment`: String（环境，必填）
- `status`: Number（状态，必填）
- `createdAt`: Date（创建时间，默认当前时间）

### 15. DataDeletionRecord（数据删除记录模型）
**文件位置：** `src/models/DataDeletionRecord.ts`
**集合名称：** `datadeletionrecords`

#### 主要字段：
- `userId`: String（用户ID，必填）
- `confirmationCode`: String（确认码，必填）
- `createdAt`: Date（创建时间，1小时后自动过期）

### 16. ChatCompletionChunk（聊天完成块模型）
**文件位置：** `src/models/ChatCompletionChunk.ts`
**集合名称：** `chatcompletionchunks`

#### 主要字段：
- `id`: String（ID，必填）
- `object`: String（对象类型，可选）
- `created`: Number（创建时间戳，可选）
- `model`: String（模型名称，可选）
- `systemFingerprint`: String（系统指纹，可选）
- `choices`: Array[Object]（选择项数组）

## 数据模型关系图

### 核心关系：
1. **User** ↔ **Balance**：一对一关系
2. **User** ↔ **BalanceTransaction**：一对多关系
3. **User** ↔ **ChatMessage**：一对多关系（通过chatId关联）
4. **User** ↔ **UserVerification**：一对多关系
5. **RequestLog** ↔ **ResponseLog**：一对一关系
6. **RequestLog** ↔ **ErrorLog**：一对多关系
7. **Pricing** ↔ **BalanceTransaction**：一对多关系
8. **VerificationResult** ↔ **UserVerification**：一对多关系

### 独立模型：
- **Product**：产品信息
- **Config**：系统配置
- **Question**：问题库
- **VerificationCode**：验证码（临时数据）
- **DataDeletionRecord**：删除记录（临时数据）
- **ChatCompletionChunk**：聊天完成块

## 管理后台需求分析

基于以上数据模型分析，管理后台需要提供以下功能模块：

### 1. 用户管理模块
- 用户列表、详情、编辑
- 用户余额管理
- 外部账户绑定管理
- 用户状态管理（启用/禁用）

### 2. 聊天数据管理模块
- 聊天消息查看、搜索、删除
- 聊天会话管理
- 聊天完成块管理

### 3. 财务管理模块
- 余额交易记录查询
- 产品管理
- 定价策略管理
- 用户验证和购买记录

### 4. 系统管理模块
- 系统配置管理
- 问题库管理
- 日志管理（请求、响应、错误）

### 5. 统计报表模块
- 用户统计（注册、活跃、地域分布）
- 聊天数据统计
- 财务数据统计
- 系统性能监控

## 下一步计划

1. 设计统一的API接口规范
2. 创建各个数据模型的CRUD控制器
3. 开发现代化的前端管理界面
4. 实现统计报表和数据可视化功能
