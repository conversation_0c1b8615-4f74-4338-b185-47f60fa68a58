# ChatAdvisor 部署指南

本文档详细说明了如何在不同环境中部署 ChatAdvisor 服务。

## 🏗 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Server    │    │    Database     │
│    (Nginx)      │───▶│   (Node.js)     │───▶│   (MongoDB)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 系统要求

### 最低要求
- **CPU**: 2 核心
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+

### 推荐配置
- **CPU**: 4 核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **操作系统**: Ubuntu 22.04 LTS

## 🛠 环境准备

### 1. 安装 Node.js

```bash
# 使用 NodeSource 仓库安装 Node.js 20
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 2. 安装 MongoDB

```bash
# 导入 MongoDB 公钥
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# 添加 MongoDB 仓库
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# 安装 MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# 启动 MongoDB 服务
sudo systemctl start mongod
sudo systemctl enable mongod
```

### 3. 配置 MongoDB 副本集

```bash
# 编辑 MongoDB 配置文件
sudo nano /etc/mongod.conf

# 添加副本集配置
replication:
  replSetName: "rs0"

# 重启 MongoDB
sudo systemctl restart mongod

# 初始化副本集
mongosh --eval "rs.initiate()"
```

### 4. 安装 PM2

```bash
npm install -g pm2
```

## 🚀 部署步骤

### 1. 克隆项目

```bash
git clone <repository-url>
cd ChatAdvisorServer
```

### 2. 安装依赖

```bash
npm install --production
```

### 3. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

**生产环境配置示例：**

```env
# 服务器配置
NODE_ENV=production
PORT=53011

# xAI API 配置
OPENAI_API_KEY=your_production_xai_key
OPENAI_BASE_URL=https://api.x.ai/v1/

# 数据库配置
MONGODB_URI_PRODUCTION=mongodb://localhost:27017/ChatAdvisor

# JWT 配置
JWT_SECRET=your_super_secure_jwt_secret_key
JWT_EXPIRES_IN=604800

# 应用配置
BASE_URL=https://advisor.sanva.tk
SUPPORT_EMAIL=<EMAIL>
DEFAULT_BALANCE=50

# CORS配置
ALLOWED_ORIGINS=https://advisor.sanva.tk,https://advisor.sanva.top

# 第三方登录配置
GOOGLE_CLIENT_ID=your_google_client_id
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
# ... 其他配置
```

### 4. 构建项目

```bash
npm run build
```

### 5. 使用 PM2 启动

```bash
# 启动生产环境
npm run pm-release

# 查看运行状态
pm2 status

# 查看日志
pm2 logs
```

## 🔧 Nginx 配置

### 1. 安装 Nginx

```bash
sudo apt-get install nginx
```

### 2. 配置反向代理

创建 Nginx 配置文件：

```bash
sudo nano /etc/nginx/sites-available/chatadvisor
```

```nginx
server {
    listen 80;
    server_name advisor.sanva.tk;

    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name advisor.sanva.tk;

    # SSL 证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # 代理到 Node.js 应用
    location / {
        proxy_pass http://localhost:53011;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 文件上传大小限制
    client_max_body_size 10M;
}
```

### 3. 启用配置

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/chatadvisor /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

## 🔒 SSL 证书配置

### 使用 Let's Encrypt

```bash
# 安装 Certbot
sudo apt-get install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d advisor.sanva.tk

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和日志

### 1. PM2 监控

```bash
# 安装 PM2 监控
pm2 install pm2-logrotate

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

### 2. 系统监控

```bash
# 安装系统监控工具
sudo apt-get install htop iotop

# 查看系统资源使用情况
htop
```

### 3. 日志管理

```bash
# 查看应用日志
pm2 logs

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 查看系统日志
sudo journalctl -u mongod -f
```

## 🔄 更新部署

### 1. 零停机更新

```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install --production

# 构建项目
npm run build

# 重启应用
pm2 reload all
```

### 2. 回滚部署

```bash
# 查看 PM2 进程
pm2 list

# 回滚到上一个版本
git checkout HEAD~1
npm run build
pm2 reload all
```

## 🛡 安全配置

### 1. 防火墙配置

```bash
# 启用 UFW
sudo ufw enable

# 允许必要端口
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# 拒绝其他端口
sudo ufw default deny incoming
sudo ufw default allow outgoing
```

### 2. 系统安全

```bash
# 更新系统
sudo apt-get update && sudo apt-get upgrade

# 配置自动安全更新
sudo apt-get install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

## 🔧 故障排除

### 常见问题

1. **应用无法启动**
   ```bash
   # 检查日志
   pm2 logs
   
   # 检查环境变量
   pm2 env 0
   ```

2. **数据库连接失败**
   ```bash
   # 检查 MongoDB 状态
   sudo systemctl status mongod
   
   # 检查副本集状态
   mongosh --eval "rs.status()"
   ```

3. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 重启应用
   pm2 restart all
   ```

## 📈 性能优化

### 1. Node.js 优化

```bash
# 设置 Node.js 内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
```

### 2. MongoDB 优化

```javascript
// 创建索引
db.users.createIndex({ "email": 1 }, { unique: true })
db.chatmessages.createIndex({ "userId": 1, "createdAt": -1 })
```

### 3. Nginx 优化

```nginx
# 启用 gzip 压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# 连接池优化
upstream backend {
    server localhost:53011;
    keepalive 32;
}
```

## 📞 支持

如果在部署过程中遇到问题，请联系：
- 邮箱：<EMAIL>
- 文档：查看项目 README.md
