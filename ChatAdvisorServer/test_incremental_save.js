/**
 * 测试增量保存功能
 * 模拟客户端发送全量消息的场景
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:33001';
const TEST_TOKEN = 'mock_jwt_token_1754289527003'; // 从浏览器获取的有效token

async function testIncrementalSave() {
    const chatId = `test-chat-${Date.now()}`;
    
    console.log('🧪 开始测试增量保存功能...');
    console.log(`📝 使用测试会话ID: ${chatId}`);
    
    try {
        // 第一次请求：发送第一条消息
        console.log('\n📤 第一次请求：发送第一条消息');
        const firstMessages = [
            { role: 'user', content: '你好，我是第一条消息' }
        ];
        
        await sendChatRequest(chatId, firstMessages);
        
        // 等待一秒
        await sleep(1000);
        
        // 第二次请求：发送全量消息（包含第一条 + 第二条）
        console.log('\n📤 第二次请求：发送全量消息（第一条 + 第二条）');
        const secondMessages = [
            { role: 'user', content: '你好，我是第一条消息' }, // 重复的消息
            { role: 'user', content: '这是第二条新消息' }        // 新消息
        ];
        
        await sendChatRequest(chatId, secondMessages);
        
        // 等待一秒
        await sleep(1000);
        
        // 第三次请求：发送全量消息（包含前两条 + 第三条）
        console.log('\n📤 第三次请求：发送全量消息（前两条 + 第三条）');
        const thirdMessages = [
            { role: 'user', content: '你好，我是第一条消息' }, // 重复的消息
            { role: 'user', content: '这是第二条新消息' },     // 重复的消息
            { role: 'user', content: '这是第三条最新消息' }     // 新消息
        ];
        
        await sendChatRequest(chatId, thirdMessages);
        
        // 检查数据库中的消息数量
        console.log('\n🔍 检查数据库中保存的消息...');
        await checkSavedMessages(chatId);
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

async function sendChatRequest(chatId, messages) {
    try {
        const response = await axios.post(`${BASE_URL}/api/chat`, {
            messages: messages
        }, {
            headers: {
                'Authorization': `Bearer ${TEST_TOKEN}`,
                'Content-Type': 'application/json',
                'chatid': chatId,
                'modelname': 'gpt-4o-mini'
            },
            timeout: 10000
        });
        
        console.log(`✅ 请求成功，发送了 ${messages.length} 条消息`);
        return response.data;
    } catch (error) {
        if (error.response) {
            console.error(`❌ 请求失败: ${error.response.status} - ${error.response.statusText}`);
            console.error('响应数据:', error.response.data);
        } else {
            console.error(`❌ 请求失败: ${error.message}`);
        }
        throw error;
    }
}

async function checkSavedMessages(chatId) {
    try {
        const response = await axios.get(`${BASE_URL}/api/admin/chat/messages`, {
            headers: {
                'Authorization': `Bearer ${TEST_TOKEN}`,
                'Content-Type': 'application/json'
            },
            params: {
                chatId: chatId,
                limit: 100
            }
        });
        
        if (response.data.success) {
            const messages = response.data.data.messages || [];
            const userMessages = messages.filter(msg => msg.role === 'user');
            
            console.log(`📊 数据库中共有 ${messages.length} 条消息`);
            console.log(`👤 其中用户消息 ${userMessages.length} 条`);
            
            if (userMessages.length > 0) {
                console.log('\n📝 用户消息列表:');
                userMessages.forEach((msg, index) => {
                    console.log(`  ${index + 1}. ${msg.content}`);
                });
            }
            
            // 验证结果
            if (userMessages.length === 3) {
                console.log('\n✅ 增量保存功能正常！应该有3条不重复的用户消息');
            } else {
                console.log(`\n⚠️  预期3条用户消息，实际保存了${userMessages.length}条`);
            }
        } else {
            console.error('❌ 获取消息失败:', response.data.message);
        }
    } catch (error) {
        console.error('❌ 检查消息失败:', error.message);
    }
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行测试
testIncrementalSave();
