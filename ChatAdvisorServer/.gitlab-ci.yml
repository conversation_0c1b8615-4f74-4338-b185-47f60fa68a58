# /Users/<USER>/gitlab1/bangliaotian/ChatAdvisorServer/.gitlab-ci.yml

variables:
  # 基础配置
  NODE_VERSION: "21"
  SSH_USER: "ubuntu"
  SSH_HOST: "**************"

  # 生产环境配置
  DEPLOY_PATH: "/home/<USER>/gitlab/production/chat-advisor-server-v2"
  SERVICE_NAME: "chat-advisor-server-v2"
  PORT: "53011"

stages:
  - deploy
  - test
  - notify

# --- 通用模板 -----
.ssh_setup:
  before_script:
    - |
      echo "🔧 Setting up SSH connection..."
      eval $(ssh-agent -s)
      echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
      mkdir -p ~/.ssh
      chmod 700 ~/.ssh
      ssh-keyscan -H "$SSH_HOST" >> ~/.ssh/known_hosts
      chmod 644 ~/.ssh/known_hosts
      echo "✅ SSH setup complete."

# --- BUILD AND DEPLOY STAGE ---
build_and_deploy:
  stage: deploy
  tags:
    - Eva
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^release:/
  extends: .ssh_setup
  script:
    - |
      echo "🏗️ Building project locally..."
      set -e

      # 设置 Node.js 环境
      echo "� Setting up Node.js v$NODE_VERSION..."
      export NVM_DIR="$HOME/.nvm"
      [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
      nvm use $NODE_VERSION

      # 安装依赖并构建
      echo "📦 Installing dependencies..."
      sudo chmod -R 777 .

      # 安装全局依赖
      npm install -g yarn typescript

      # 使用package.json安装所有依赖
      echo "📦 Installing dependencies from package.json..."
      yarn install

      echo "🔨 Building TypeScript for production..."
      # 设置生产环境变量
      export NODE_ENV=production
      rm -rf dist/
      yarn build:prod
      echo "✅ Build complete."

      # 显示构建统计
      echo "📊 Build artifacts summary:"
      du -sh dist/ 2>/dev/null || true
      echo "📁 Checking build output structure:"
      ls -la dist/src/ 2>/dev/null || echo "⚠️  dist/src/ directory not found"
      echo "🔍 Looking for entry files:"
      [ -f "dist/src/index.js" ] && echo "✅ Found dist/src/index.js" || echo "❌ Missing dist/src/index.js"
      [ -f "dist/src/app.js" ] && echo "✅ Found dist/src/app.js" || echo "❌ Missing dist/src/app.js"

      echo "💾 Backing up existing production deployment..."
      # 备份现有部署
      ssh -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "
        if [ -d '$DEPLOY_PATH' ]; then
          BACKUP_NAME='${DEPLOY_PATH}_backup_$(date +%Y%m%d_%H%M%S).tar.gz'
          echo 'Creating backup: \$BACKUP_NAME'
          tar -czf \"\$BACKUP_NAME\" -C '$DEPLOY_PATH' . 2>/dev/null || true
          echo '✅ Backup created successfully'
        else
          echo 'No existing deployment to backup'
        fi
        mkdir -p '$DEPLOY_PATH/logs'
        touch '$DEPLOY_PATH/logs/release-err.log' '$DEPLOY_PATH/logs/release-out.log'
      "

      echo "🚀 Deploying $SERVICE_NAME..."
      # 参考 Jenkins 脚本的文件传输方式
      echo "🚚 Transferring files to remote server: $SSH_USER@$SSH_HOST:$DEPLOY_PATH"

      # 确保远程目录存在并设置权限
      ssh -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "
        mkdir -p '$DEPLOY_PATH' && sudo chmod -R 777 '$DEPLOY_PATH'
      "

      # 传输构建产物和运行时文件到远程服务器
      echo "📦 Transferring built artifacts and runtime files..."

      # 首先传输dist目录（构建产物）
      rsync -avz \
        --include 'dist/' \
        --include 'dist/**' \
        --exclude '*' \
        -e "ssh -o StrictHostKeyChecking=no" \
        ./ "$SSH_USER@$SSH_HOST:$DEPLOY_PATH/"

      # 然后传输运行时必需的文件
      rsync -avz \
        --include 'package.json' \
        --include 'yarn.lock' \
        --include 'pm2.config.cjs' \
        --include '.env*' \
        --include 'pem/' \
        --include 'pem/**' \
        --include 'locales/' \
        --include 'locales/**' \
        --include 'scripts/' \
        --include 'scripts/**' \
        --include 'src/' \
        --include 'src/views/' \
        --include 'src/views/**' \
        --exclude '*' \
        -e "ssh -o StrictHostKeyChecking=no" \
        ./ "$SSH_USER@$SSH_HOST:$DEPLOY_PATH/"

      # 在远程服务器上安装依赖并启动服务（参考 Jenkins）
      echo "🔄 Installing dependencies and restarting service on remote server..."
      ssh -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "
        set -e
        cd '$DEPLOY_PATH'

        # 设置生产环境变量
        echo '🔧 Setting production environment variables...'
        export NODE_ENV=production

        # 安装运行时依赖
        echo '📦 Installing production dependencies on remote...'
        export NVM_DIR=\"\$HOME/.nvm\"
        [ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"
        nvm use $NODE_VERSION
        yarn add tsconfig-paths ts-node
        yarn install --production

        # 测试PM2配置
        echo '🔍 Testing PM2 configuration...'
        if [ -f 'scripts/test-pm2-config.cjs' ]; then
          node scripts/test-pm2-config.cjs
        else
          echo '⚠️  PM2 test script not found, skipping configuration test'
        fi

        # 启动 PM2 服务 - 直接启动而不重新构建
        echo '🚀 Starting PM2 service: $SERVICE_NAME'
        yarn pm-start-only
        pm2 save
        echo '✅ Service $SERVICE_NAME started successfully'
      "
      echo "✅ Deployment of $SERVICE_NAME completed successfully."

# --- TEST STAGE ---
health_check:
  stage: test
  tags:
    - Eva
  timeout: 1m
  rules:
    # 仅在 release: 分支触发健康检查
    - if: $CI_COMMIT_MESSAGE =~ /^release:/

  extends: .ssh_setup
  needs:
    - build_and_deploy
  script:
    - |
      echo "🏥 Performing remote health check for $SERVICE_NAME..."
      set -e

      echo "⏳ Waiting 15 seconds for service to become available..."
      sleep 15

      # 步骤 1: 检查 PM2 服务是否在线
      echo "🔍 Checking PM2 status on remote..."
      if ! ssh -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "pm2 describe $SERVICE_NAME | grep -q 'online'"; then
        echo "❌ FATAL: PM2 service '$SERVICE_NAME' is not online."
        echo "📋 Tailing last 50 lines of logs from remote:"
        ssh -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "pm2 logs $SERVICE_NAME --lines 50 --nostream"
        exit 1 # 明确中断流水线
      fi
      echo "✅ PM2 service is online."

      # 步骤 2: 检查健康检查端点是否响应
      echo "🌐 Curling health check endpoint on remote..."
      if ! ssh -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "curl -f --max-time 10 http://127.0.0.1:$PORT/health"; then
        echo "❌ FATAL: Health check endpoint failed to respond correctly."
        echo "📋 Tailing last 50 lines of logs from remote (service may have crashed):"
        ssh -o StrictHostKeyChecking=no "$SSH_USER@$SSH_HOST" "pm2 logs $SERVICE_NAME --lines 50 --nostream"
        exit 1 # 明确中断流水线
      fi

      echo "✅ Health check passed! Service is running and healthy."
