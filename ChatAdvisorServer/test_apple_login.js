/**
 * 苹果登录数据格式测试脚本
 * 用于验证新用户和老用户登录时返回的数据格式是否一致
 */

const mongoose = require('mongoose');
const User = require('./src/models/User').default;

// 模拟测试数据
const mockAppleInfo = {
    iss: 'https://appleid.apple.com',
    aud: 'com.sanva.chatadvisor',
    exp: Math.floor(Date.now() / 1000) + 3600,
    iat: Math.floor(Date.now() / 1000),
    sub: 'test_apple_user_123',
    c_hash: 'test_hash',
    email: '<EMAIL>',
    email_verified: true,
    is_private_email: false,
    auth_time: Math.floor(Date.now() / 1000),
    nonce_supported: true
};

async function testUserDataFormat() {
    try {
        console.log('🧪 开始测试用户数据格式...\n');

        // 测试1: 创建新用户
        console.log('📝 测试1: 创建新用户');
        const newUser = new User({
            email: '<EMAIL>',
            externalAccounts: {
                appleInfo: mockAppleInfo
            }
        });

        // 不实际保存到数据库，只测试数据格式
        const newUserData = newUser.toObject();
        console.log('新用户数据格式:');
        console.log('- userId类型:', typeof newUserData.userId);
        console.log('- userId值:', newUserData.userId);
        console.log('- _id类型:', typeof newUserData._id);
        console.log('- _id值:', newUserData._id);
        console.log('- email:', newUserData.email);
        console.log('- balance:', newUserData.balance);
        console.log('- language:', newUserData.language);
        console.log('- isDelete:', newUserData.isDelete);

        // 测试2: 验证数据格式化函数
        console.log('\n📝 测试2: 数据格式化函数');
        const formatUserData = (user, token) => {
            const userData = user.toObject();
            return {
                ...userData,
                userId: userData._id.toString(),
                _id: userData._id.toString(),
                token,
                balance: userData.balance || 0,
                language: userData.language || 'zh_CN',
                isDelete: userData.isDelete || false
            };
        };

        const formattedData = formatUserData(newUser, 'test_token_123');
        console.log('格式化后的数据:');
        console.log('- userId类型:', typeof formattedData.userId);
        console.log('- userId值:', formattedData.userId);
        console.log('- _id类型:', typeof formattedData._id);
        console.log('- _id值:', formattedData._id);
        console.log('- token:', formattedData.token);
        console.log('- balance:', formattedData.balance);
        console.log('- language:', formattedData.language);
        console.log('- isDelete:', formattedData.isDelete);

        // 测试3: JSON序列化
        console.log('\n📝 测试3: JSON序列化测试');
        const jsonString = JSON.stringify(formattedData);
        const parsedData = JSON.parse(jsonString);
        console.log('JSON序列化后解析:');
        console.log('- userId类型:', typeof parsedData.userId);
        console.log('- userId值:', parsedData.userId);
        console.log('- 数据完整性:', parsedData.email === formattedData.email ? '✅ 通过' : '❌ 失败');

        // 测试4: 客户端期望的数据结构验证
        console.log('\n📝 测试4: 客户端数据结构验证');
        const requiredFields = ['userId', 'email', 'token', 'balance'];
        const missingFields = requiredFields.filter(field => !formattedData.hasOwnProperty(field));
        
        if (missingFields.length === 0) {
            console.log('✅ 所有必需字段都存在');
        } else {
            console.log('❌ 缺少字段:', missingFields);
        }

        // 验证字段类型
        const typeChecks = {
            userId: typeof formattedData.userId === 'string',
            email: typeof formattedData.email === 'string',
            token: typeof formattedData.token === 'string',
            balance: typeof formattedData.balance === 'number'
        };

        console.log('字段类型检查:');
        Object.entries(typeChecks).forEach(([field, isCorrect]) => {
            console.log(`- ${field}: ${isCorrect ? '✅' : '❌'} ${typeof formattedData[field]}`);
        });

        console.log('\n🎉 测试完成！');
        
        // 总结
        const allTestsPassed = Object.values(typeChecks).every(Boolean) && missingFields.length === 0;
        console.log(`\n📊 测试结果: ${allTestsPassed ? '✅ 全部通过' : '❌ 存在问题'}`);

        return allTestsPassed;

    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return false;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    testUserDataFormat().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testUserDataFormat };
