# ChatAdvisor 后台管理系统 PM2 快速启动指南

## 🚀 快速启动

### 一键启动所有服务
```bash
# 在 ChatAdvisorServer 目录下执行
npm run pm-admin:start-all
```

### 分别启动服务
```bash
# 启动生产环境前端 (端口 34001)
npm run pm-admin:start-release

# 启动开发环境前端 (端口 54001)  
npm run pm-admin:start-debug
```

## 📋 服务端口

| 服务 | 端口 | 环境 | 访问地址 |
|------|------|------|----------|
| admin-frontend-release | 34001 | 生产 | http://localhost:34001 |
| admin-frontend-debug | 54001 | 开发 | http://localhost:54001 |

## 🔧 常用命令

### NPM 脚本命令
```bash
# 启动服务
npm run pm-admin:start-release  # 启动生产环境
npm run pm-admin:start-debug    # 启动开发环境
npm run pm-admin:start-all      # 启动所有服务

# 管理服务
npm run pm-admin:stop           # 停止所有服务
npm run pm-admin:status         # 查看服务状态

# 查看帮助
npm run pm-admin                # 显示所有可用命令
```

### 管理脚本命令
```bash
# 启动服务
./scripts/pm2-admin.sh start-release    # 启动生产环境
./scripts/pm2-admin.sh start-debug      # 启动开发环境
./scripts/pm2-admin.sh start-all        # 启动所有服务

# 停止服务
./scripts/pm2-admin.sh stop-release     # 停止生产环境
./scripts/pm2-admin.sh stop-debug       # 停止开发环境
./scripts/pm2-admin.sh stop-all         # 停止所有服务

# 重启服务
./scripts/pm2-admin.sh restart-release  # 重启生产环境
./scripts/pm2-admin.sh restart-debug    # 重启开发环境
./scripts/pm2-admin.sh restart-all      # 重启所有服务

# 查看状态和日志
./scripts/pm2-admin.sh status           # 查看服务状态
./scripts/pm2-admin.sh logs-release     # 查看生产环境日志
./scripts/pm2-admin.sh logs-debug       # 查看开发环境日志

# 构建项目
./scripts/pm2-admin.sh build            # 构建前端项目

# 查看帮助
./scripts/pm2-admin.sh help             # 显示帮助信息
```

## 📊 服务状态检查

```bash
# 查看 PM2 服务状态
pm2 status

# 查看特定服务状态
pm2 status admin-frontend-release
pm2 status admin-frontend-debug

# 查看服务日志
pm2 logs admin-frontend-release
pm2 logs admin-frontend-debug
```

## 🔍 故障排除

### 端口被占用
```bash
# 查看端口占用
lsof -i :34001
lsof -i :54001

# 杀死占用进程
lsof -ti:34001 | xargs kill -9
lsof -ti:54001 | xargs kill -9
```

### 服务启动失败
```bash
# 查看详细错误日志
pm2 logs admin-frontend-release --lines 50
pm2 logs admin-frontend-debug --lines 50

# 重新构建并启动
./scripts/pm2-admin.sh build
./scripts/pm2-admin.sh restart-all
```

### 重置所有服务
```bash
# 停止并删除所有前端服务
pm2 stop admin-frontend-release admin-frontend-debug
pm2 delete admin-frontend-release admin-frontend-debug

# 重新启动
./scripts/pm2-admin.sh start-all
```

## 📁 日志文件位置

```
logs/
├── admin-release-info.log    # 生产环境综合日志
├── admin-release-err.log     # 生产环境错误日志
├── admin-release-out.log     # 生产环境输出日志
├── admin-debug-info.log      # 开发环境综合日志
├── admin-debug-err.log       # 开发环境错误日志
└── admin-debug-out.log       # 开发环境输出日志
```

## ⚡ 性能优化建议

### 生产环境
- 使用 `start-release` 启动生产环境（已构建优化）
- 定期清理日志文件
- 监控内存使用情况

### 开发环境
- 使用 `start-debug` 启动开发环境（支持热更新）
- 开发完成后及时停止服务释放资源

## 🔄 更新部署流程

```bash
# 1. 停止服务
./scripts/pm2-admin.sh stop-all

# 2. 更新代码
git pull

# 3. 安装依赖
npm install
cd ../admin-frontend && npm install && cd ../ChatAdvisorServer

# 4. 重新启动
./scripts/pm2-admin.sh start-all
```

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 检查端口是否被占用
3. 确认依赖是否正确安装
4. 参考完整文档：`docs/PM2_ADMIN_DEPLOYMENT.md`
