/**
 * 简单的增量保存测试
 * 直接测试数据库层面的增量保存逻辑
 */

const mongoose = require('mongoose');

// 连接数据库
async function connectDB() {
    try {
        await mongoose.connect('mongodb://localhost:27017/chatadvisor');
        console.log('✅ 数据库连接成功');
    } catch (error) {
        console.error('❌ 数据库连接失败:', error);
        process.exit(1);
    }
}

// 定义ChatMessage模型
const ChatMessageSchema = new mongoose.Schema({
    id: { type: String, required: true },
    chatId: { type: String, required: true },
    userId: { type: String, required: false },
    role: { type: String, enum: ['user', 'assistant'], required: true },
    content: { type: String, required: true },
    createdTime: { type: Date, default: Date.now },
    isComplete: { type: Boolean, default: false }
}, {
    versionKey: false
});

const ChatMessage = mongoose.model('ChatMessage', ChatMessageSchema);

// 模拟增量保存逻辑
async function simulateIncrementalSave(chatId, userId, messages) {
    console.log(`\n📝 模拟会话 ${chatId} 的增量保存...`);
    
    // 获取该会话中已存在的用户消息数量
    const existingUserMessagesCount = await ChatMessage.countDocuments({
        chatId: chatId,
        userId: userId,
        role: 'user'
    });

    console.log(`📊 会话中已存在 ${existingUserMessagesCount} 条用户消息`);

    // 提取当前请求中的所有用户消息
    const userMessages = messages.filter(msg => msg.role === 'user');
    
    // 计算需要保存的新消息（从已存在的消息数量开始）
    const newMessages = userMessages.slice(existingUserMessagesCount);
    
    console.log(`📤 当前请求包含 ${userMessages.length} 条用户消息，需要保存 ${newMessages.length} 条新消息`);

    // 保存新的用户消息
    let savedCount = 0;
    for (const message of newMessages) {
        const trimmedContent = message.content.trim();
        if (trimmedContent) {
            const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            await ChatMessage.create({
                id: messageId,
                chatId: chatId,
                userId: userId,
                createdTime: new Date(),
                role: 'user',
                content: trimmedContent,
                isComplete: true
            });

            savedCount++;
            console.log(`💾 保存新用户消息 ${savedCount}/${newMessages.length}: ${messageId}, 内容: "${trimmedContent}"`);
        }
    }

    if (savedCount > 0) {
        console.log(`✅ 会话 ${chatId} 增量保存了 ${savedCount} 条新用户消息`);
    } else {
        console.log(`ℹ️  会话 ${chatId} 没有新的用户消息需要保存`);
    }
    
    return savedCount;
}

// 检查保存的消息
async function checkSavedMessages(chatId, userId) {
    const messages = await ChatMessage.find({
        chatId: chatId,
        userId: userId,
        role: 'user'
    }).sort({ createdTime: 1 });
    
    console.log(`\n🔍 会话 ${chatId} 中保存的用户消息:`);
    messages.forEach((msg, index) => {
        console.log(`  ${index + 1}. [${msg.createdTime.toISOString()}] ${msg.content}`);
    });
    
    return messages.length;
}

// 主测试函数
async function runTest() {
    await connectDB();
    
    const testChatId = `test-incremental-${Date.now()}`;
    const testUserId = '685df4bbe71d9595e5de164f'; // 使用现有用户ID
    
    console.log('🧪 开始增量保存测试...');
    console.log(`📝 测试会话ID: ${testChatId}`);
    console.log(`👤 测试用户ID: ${testUserId}`);
    
    try {
        // 第一次请求：发送第一条消息
        console.log('\n=== 第一次请求 ===');
        const firstMessages = [
            { role: 'user', content: '你好，我是第一条消息' }
        ];
        await simulateIncrementalSave(testChatId, testUserId, firstMessages);
        let totalMessages = await checkSavedMessages(testChatId, testUserId);
        
        // 第二次请求：发送全量消息（包含第一条 + 第二条）
        console.log('\n=== 第二次请求 ===');
        const secondMessages = [
            { role: 'user', content: '你好，我是第一条消息' }, // 重复的消息
            { role: 'user', content: '这是第二条新消息' }        // 新消息
        ];
        await simulateIncrementalSave(testChatId, testUserId, secondMessages);
        totalMessages = await checkSavedMessages(testChatId, testUserId);
        
        // 第三次请求：发送全量消息（包含前两条 + 第三条）
        console.log('\n=== 第三次请求 ===');
        const thirdMessages = [
            { role: 'user', content: '你好，我是第一条消息' }, // 重复的消息
            { role: 'user', content: '这是第二条新消息' },     // 重复的消息
            { role: 'user', content: '这是第三条最新消息' }     // 新消息
        ];
        await simulateIncrementalSave(testChatId, testUserId, thirdMessages);
        totalMessages = await checkSavedMessages(testChatId, testUserId);
        
        // 验证结果
        console.log('\n🎯 测试结果验证:');
        if (totalMessages === 3) {
            console.log('✅ 增量保存功能正常！成功保存了3条不重复的用户消息');
        } else {
            console.log(`❌ 增量保存功能异常！预期3条消息，实际保存了${totalMessages}条`);
        }
        
        // 清理测试数据
        await ChatMessage.deleteMany({ chatId: testChatId });
        console.log('🧹 测试数据已清理');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await mongoose.disconnect();
        console.log('👋 数据库连接已关闭');
    }
}

// 运行测试
runTest();
