# 环境配置说明

本文档说明如何配置和管理ChatAdvisor项目的开发和生产环境，确保数据隔离。

## 问题背景

之前的配置导致开发和生产环境使用同一数据库，造成线上站点显示开发数据的问题。

## 解决方案

通过环境自动检测和独立的数据库配置，实现开发和生产环境的完全隔离。

## 环境配置

### 前端 (admin-frontend)

#### 开发环境
- **配置文件**: `.env.development`
- **API端点**: `http://localhost:33001`
- **构建命令**: `npm run dev`
- **特点**: 连接本地后端服务

#### 生产环境
- **配置文件**: `.env.production`
- **API端点**: `https://admin.sanva.top/api`
- **构建命令**: `npm run build:prod`
- **特点**: 连接线上后端服务

### 后端 (ChatAdvisorServer)

#### 开发环境
- **配置文件**: `.env.development`
- **数据库**: `ChatAdvisor_test`
- **端口**: `33001`
- **特点**: 使用测试数据库，可随意修改

#### 生产环境
- **配置文件**: `.env.production`
- **数据库**: `ChatAdvisor?replicaSet=rs0`
- **端口**: `53011`
- **特点**: 使用生产数据库副本集

## 自动环境检测

### 前端检测逻辑
```typescript
// 基于构建模式自动检测
const isProduction = process.env.NODE_ENV === 'production' || import.meta.env.PROD;

// 自动选择API端点
const baseURL = isProduction 
  ? 'https://admin.sanva.top/api'  // 生产环境
  : 'http://localhost:33001';      // 开发环境
```

### 后端检测逻辑
```typescript
// 简化配置：只使用MONGODB_URI，根据NODE_ENV自动选择数据库
const mongoUri = (() => {
    // 如果环境变量中有MONGODB_URI，直接使用
    if (process.env.MONGODB_URI) {
        return process.env.MONGODB_URI;
    }

    // 否则根据环境提供默认值
    if (env.isProduction) {
        return 'mongodb://localhost:27017/ChatAdvisor?replicaSet=rs0';
    } else {
        return 'mongodb://localhost:27017/ChatAdvisor_test';
    }
})();
```

## 使用方法

### 开发环境启动

1. **启动后端**:
   ```bash
   cd ChatAdvisorServer
   NODE_ENV=development npm run dev
   ```

2. **启动前端**:
   ```bash
   cd admin-frontend
   npm run dev
   ```

### 生产环境部署

1. **构建前端**:
   ```bash
   cd admin-frontend
   npm run build:prod
   ```

2. **部署后端**:
   ```bash
   cd ChatAdvisorServer
   NODE_ENV=production npm start
   ```

## 环境切换工具

使用提供的脚本快速检查和切换环境：

```bash
# 检查当前环境状态
./admin-frontend/scripts/env-switch.sh status

# 切换到开发环境
./admin-frontend/scripts/env-switch.sh dev

# 切换到生产环境
./admin-frontend/scripts/env-switch.sh prod
```

## 环境指示器

前端界面左侧边栏底部会显示当前环境标识：
- 🟡 **开发环境**: 黄色标识，显示详细配置信息
- 🟢 **生产环境**: 绿色标识，仅在需要时显示

## 数据库隔离

### 测试数据库
- **名称**: `ChatAdvisor_test`
- **用途**: 开发和测试
- **特点**: 可以随意修改和重置，开发环境默认使用

### 生产数据库
- **名称**: `ChatAdvisor`
- **配置**: 使用副本集 `?replicaSet=rs0`
- **用途**: 线上服务
- **特点**: 包含真实用户数据，需要谨慎操作

## 注意事项

1. **简化配置**: 只使用 `MONGODB_URI` 一个变量，根据 `NODE_ENV` 自动选择数据库
2. **环境变量优先级**: 环境变量文件中的 `MONGODB_URI` > 默认配置
3. **自动检测**: 无需手动切换，基于运行环境自动选择
4. **数据安全**: 开发环境操作不会影响生产数据
5. **配置统一**: 通用配置（如API密钥）可以在两个环境间共享

## 故障排除

### 问题1: 开发环境连接到生产数据库
**解决**: 检查 `NODE_ENV` 环境变量是否正确设置为 `development`

### 问题2: 前端无法连接后端
**解决**: 确保后端服务在正确端口运行（开发：33001，生产：53011）

### 问题3: 环境指示器显示错误
**解决**: 清除浏览器缓存，重新构建前端项目

## 环境切换功能

### 可用环境
- **开发环境**: `http://localhost:33001` (本地开发)
- **生产环境**: `https://admin.sanva.top/api` (线上服务)

### 使用方法
1. 点击左侧边栏底部的环境指示器
2. 选择要切换的环境
3. 确认刷新页面以应用新配置

### 控制台命令
```javascript
// 获取当前环境
environmentManager.getCurrentEnvironment()

// 切换到生产环境
environmentManager.switchEnvironment('production')

// 重置到默认环境
environmentManager.resetToDefault()
```

## 验证环境配置

运行以下命令验证配置是否正确：

```bash
# 检查前端环境
cd admin-frontend && npm run dev
# 查看控制台输出的API配置信息

# 检查后端环境
cd ChatAdvisorServer && NODE_ENV=development npm run dev
# 查看数据库连接信息
```
