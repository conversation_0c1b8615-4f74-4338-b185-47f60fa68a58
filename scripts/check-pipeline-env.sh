#!/bin/bash

# 流水线环境配置检查脚本
# 验证GitLab CI/CD流水线是否正确传递环境参数

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查GitLab CI配置
check_gitlab_ci() {
    local project_path=$1
    local project_name=$2
    
    log_info "检查 $project_name 的GitLab CI配置..."
    
    local ci_file="$project_path/.gitlab-ci.yml"
    
    if [[ ! -f "$ci_file" ]]; then
        log_error "$project_name 缺少 .gitlab-ci.yml 文件"
        return 1
    fi
    
    log_success "找到 $ci_file"
    
    # 检查触发条件
    if grep -q "release:" "$ci_file"; then
        log_success "✓ 配置了 release: 触发条件"
    else
        log_warning "⚠ 未找到 release: 触发条件"
    fi
    
    # 检查NODE_ENV设置
    if grep -q "NODE_ENV=production" "$ci_file"; then
        log_success "✓ 配置了 NODE_ENV=production"
    else
        log_warning "⚠ 未找到 NODE_ENV=production 设置"
    fi
    
    # 检查构建命令
    if grep -q "build:prod\|build --mode production" "$ci_file"; then
        log_success "✓ 使用生产环境构建命令"
    else
        log_warning "⚠ 未使用生产环境构建命令"
    fi
    
    echo ""
}

# 检查PM2配置
check_pm2_config() {
    local project_path=$1
    local project_name=$2
    
    log_info "检查 $project_name 的PM2配置..."
    
    local pm2_file="$project_path/pm2.config.cjs"
    
    if [[ ! -f "$pm2_file" ]]; then
        log_error "$project_name 缺少 pm2.config.cjs 文件"
        return 1
    fi
    
    log_success "找到 $pm2_file"
    
    # 检查NODE_ENV设置
    if grep -q "NODE_ENV.*production" "$pm2_file"; then
        log_success "✓ PM2配置了 NODE_ENV: 'production'"
    else
        log_warning "⚠ PM2未配置 NODE_ENV: 'production'"
    fi
    
    # 检查端口配置
    local port_pattern="PORT.*[0-9]+"
    if grep -q "$port_pattern" "$pm2_file"; then
        local port=$(grep -o "PORT.*[0-9]\+" "$pm2_file" | head -1)
        log_success "✓ 配置了端口: $port"
    else
        log_warning "⚠ 未找到端口配置"
    fi
    
    echo ""
}

# 检查环境配置文件
check_env_files() {
    local project_path=$1
    local project_name=$2
    
    log_info "检查 $project_name 的环境配置文件..."
    
    # 检查.env.production文件
    local prod_env="$project_path/.env.production"
    if [[ -f "$prod_env" ]]; then
        log_success "✓ 找到 .env.production"
        
        # 检查关键配置
        if grep -q "VITE_IS_PRODUCTION=true\|NODE_ENV=production" "$prod_env"; then
            log_success "✓ 生产环境标识配置正确"
        else
            log_warning "⚠ 生产环境标识配置可能有问题"
        fi
    else
        log_warning "⚠ 未找到 .env.production 文件"
    fi
    
    # 检查.env.development文件
    local dev_env="$project_path/.env.development"
    if [[ -f "$dev_env" ]]; then
        log_success "✓ 找到 .env.development"
    else
        log_warning "⚠ 未找到 .env.development 文件"
    fi
    
    echo ""
}

# 检查package.json脚本
check_package_scripts() {
    local project_path=$1
    local project_name=$2
    
    log_info "检查 $project_name 的package.json脚本..."
    
    local package_file="$project_path/package.json"
    
    if [[ ! -f "$package_file" ]]; then
        log_error "$project_name 缺少 package.json 文件"
        return 1
    fi
    
    # 检查构建脚本
    if grep -q "build:prod" "$package_file"; then
        log_success "✓ 配置了 build:prod 脚本"
    else
        log_warning "⚠ 未找到 build:prod 脚本"
    fi
    
    # 检查PM2脚本
    if grep -q "pm-start-only\|pm-release" "$package_file"; then
        log_success "✓ 配置了PM2启动脚本"
    else
        log_warning "⚠ 未找到PM2启动脚本"
    fi
    
    echo ""
}

# 生成流水线触发示例
show_pipeline_examples() {
    log_info "流水线触发示例:"
    echo ""
    echo "🚀 触发生产环境部署:"
    echo "   git commit -m \"release: 发布版本 v1.0.4\""
    echo "   git push origin main"
    echo ""
    echo "📋 提交消息格式:"
    echo "   - release: [描述] - 触发生产环境部署"
    echo "   - feat: [描述] - 不触发部署"
    echo "   - fix: [描述] - 不触发部署"
    echo ""
    echo "🔧 环境变量传递流程:"
    echo "   1. GitLab CI 设置 NODE_ENV=production"
    echo "   2. 构建时使用生产环境配置"
    echo "   3. PM2启动时继承环境变量"
    echo "   4. 应用自动选择生产数据库"
    echo ""
}

# 主函数
main() {
    echo "🔍 ChatAdvisor 流水线环境配置检查"
    echo "====================================="
    echo ""
    
    # 检查项目根目录
    if [[ ! -d "admin-frontend" ]] || [[ ! -d "ChatAdvisorServer" ]]; then
        log_error "请在项目根目录下运行此脚本"
        exit 1
    fi
    
    # 检查前端配置
    log_info "🎨 检查前端项目配置"
    echo "========================"
    check_gitlab_ci "admin-frontend" "前端"
    check_pm2_config "admin-frontend" "前端"
    check_env_files "admin-frontend" "前端"
    check_package_scripts "admin-frontend" "前端"
    
    # 检查后端配置
    log_info "⚙️ 检查后端项目配置"
    echo "========================"
    check_gitlab_ci "ChatAdvisorServer" "后端"
    check_pm2_config "ChatAdvisorServer" "后端"
    check_env_files "ChatAdvisorServer" "后端"
    check_package_scripts "ChatAdvisorServer" "后端"
    
    # 显示使用示例
    show_pipeline_examples
    
    log_success "✅ 配置检查完成"
}

# 运行主函数
main "$@"
