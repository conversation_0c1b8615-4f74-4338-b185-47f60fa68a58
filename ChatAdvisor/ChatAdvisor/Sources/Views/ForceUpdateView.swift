//
//  ForceUpdateView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import SwiftUI
import UIKit
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ForceUpdateView")

/// 强制升级弹窗 - 不可关闭的模态弹窗
struct ForceUpdateView: View {
    let versionControl: VersionControl
    let onUpdate: () -> Void
    
    @State private var pulseAnimation = false
    @State private var scaleAnimation = false
    
    var body: some View {
        ZStack {
            // 全屏半透明黑色背景，防止用户点击其他区域
            Color.black.opacity(0.8)
                .ignoresSafeArea()
                .onTapGesture {
                    // 点击背景不做任何操作，防止关闭弹窗
                }
            
            // 中央升级卡片
            VStack(spacing: 0) {
                upgradeCard
            }
            .scaleEffect(scaleAnimation ? 1.0 : 0.9)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: scaleAnimation)
        }
        .onAppear {
            startAnimations()
            logger.info("显示强制升级弹窗: \(versionControl.latestVersion)")
        }
    }
    
    // MARK: - 升级卡片
    private var upgradeCard: some View {
        VStack(spacing: 24) {
            // 顶部图标和标题
            headerSection
            
            // 版本信息
            versionSection
            
            // 升级说明
            messageSection
            
            // 升级按钮
            updateButton
        }
        .padding(32)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.3), radius: 20, x: 0, y: 10)
        )
        .padding(.horizontal, 40)
    }
    
    // MARK: - 头部区域
    private var headerSection: some View {
        VStack(spacing: 16) {
            // 升级图标
            ZStack {
                Circle()
                    .fill(Color.red.gradient)
                    .frame(width: 80, height: 80)
                    .scaleEffect(pulseAnimation ? 1.1 : 1.0)
                
                Image(systemName: "arrow.up.circle.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.white)
            }
            
            // 标题
            Text("force_update_title".localized())
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
        }
    }
    
    // MARK: - 版本信息区域
    private var versionSection: some View {
        VStack(spacing: 12) {
            Text("version_info".localized())
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            HStack(spacing: 16) {
                // 当前版本
                versionItem(
                    label: "version_current".localized(),
                    version: getCurrentVersion(),
                    isHighlight: false
                )
                
                // 箭头
                Image(systemName: "arrow.right")
                    .font(.title3)
                    .foregroundColor(.red)
                    .scaleEffect(pulseAnimation ? 1.2 : 1.0)
                
                // 新版本
                versionItem(
                    label: "version_latest".localized(),
                    version: versionControl.latestVersion,
                    isHighlight: true
                )
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.quaternary.opacity(0.5))
            )
        }
    }
    
    // MARK: - 版本项
    private func versionItem(label: String, version: String, isHighlight: Bool) -> some View {
        VStack(spacing: 4) {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(version)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(isHighlight ? .red : .primary)
        }
    }
    
    // MARK: - 升级说明
    private var messageSection: some View {
        VStack(spacing: 12) {
            Text("force_update_message".localized())
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            if !versionControl.updateMessage.isEmpty {
                Text(versionControl.updateMessage)
                    .font(.body)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 8)
            }
        }
    }
    
    // MARK: - 升级按钮
    private var updateButton: some View {
        Button(action: {
            logger.info("用户点击强制升级按钮")
            onUpdate()
        }) {
            HStack(spacing: 12) {
                Image(systemName: "arrow.down.app.fill")
                    .font(.title3)
                
                Text("force_update_button".localized())
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 56)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.red.gradient)
                    .shadow(color: .red.opacity(0.3), radius: 8, x: 0, y: 4)
            )
        }
        .scaleEffect(pulseAnimation ? 1.02 : 1.0)
    }
    
    // MARK: - 动画
    private func startAnimations() {
        scaleAnimation = true
        
        // 脉冲动画
        withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
            pulseAnimation = true
        }
    }
    
    // MARK: - 辅助方法
    private func getCurrentVersion() -> String {
        return Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0.0"
    }
}

// MARK: - 本地化字符串扩展
private extension String {
    func localized() -> String {
        switch self {
        case "force_update_title":
            return "需要更新应用"
        case "version_info":
            return "版本信息"
        case "version_current":
            return "当前版本"
        case "version_latest":
            return "最新版本"
        case "force_update_message":
            return "为了获得更好的使用体验和最新功能，请立即更新到最新版本。"
        case "force_update_button":
            return "立即更新"
        default:
            return self
        }
    }
}

// MARK: - Preview
struct ForceUpdateView_Previews: PreviewProvider {
    static var previews: some View {
        ForceUpdateView(
            versionControl: VersionControl(
                needUpdate: true,
                updateType: .force,
                latestVersion: "2.1.0",
                minimumVersion: "2.0.0",
                updateMessage: "此版本包含重要的安全更新和性能优化，请立即更新。",
                downloadUrl: "https://apps.apple.com/us/app/chat-advisor/id6526465428",
                versionCheckEnabled: true
            ),
            onUpdate: {
                print("用户点击了更新按钮")
            }
        )
    }
}
