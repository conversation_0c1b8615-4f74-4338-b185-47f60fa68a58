//
//  MessageBubblePreview.swift
//  ChatAdvisor
//
//  Created by zweiteng on 2025/3/14.
//

import Foundation
import MarkdownUI
import Splash
import SwiftUI

struct MessageBubblePreview: View {
    @State var preventCrash = false
    var message: ChatMessage
    @Environment(\.colorScheme) private var colorScheme
    @State private var copiedContents: [String: Bool] = [:] // 使用字典存储复制状态

    var body: some View {
        VStack {
            HStack {
                if message.role == .user {
                    HStack(alignment: .top) {
                        if preventCrash {
                            Text(message.content)
                        } else {
                            Markdown(message.content)
                                .font(.caption)
                                .markdownBlockStyle(\.codeBlock) {
                                    codeBlock($0)
                                }
                                .markdownCodeSyntaxHighlighter(.splash(theme: theme))
//                                .padding(4)
//                                .cornerRadius(5)
                        }
                        Spacer()
                    }
                } else {
                    HStack(alignment: .top) {
                        if preventCrash {
                            Text(message.content)
                        } else {
                            Markdown(message.content)
                                .font(.caption)
                                .markdownBlockStyle(\.codeBlock) {
                                    codeBlock($0)
                                }
                                .markdownCodeSyntaxHighlighter(.splash(theme: theme))
//                                .padding(4)
//                                .cornerRadius(5)
                        }
                        Spacer()
                    }
                }
            }
        }
//        .padding(.horizontal, AppTheme.padding)
        .padding(.vertical, AppThemes.padding / 2)
        .background(message.role == .user ? (colorScheme == .dark ? Color(.systemGray5) : Color(.systemGray6)) : .clear)
    }

    @ViewBuilder
    private func codeBlock(_ configuration: CodeBlockConfiguration) -> some View {
        let isCopied = copiedContents[configuration.content] ?? false // 获取当前内容的复制状态

        VStack(spacing: 0) {
            HStack {
                Text(configuration.language ?? "plain text")
                    .font(.system(.caption2, design: .monospaced))
                    .fontWeight(.medium)
                    .foregroundColor(Color(theme.plainTextColor))
                    .padding(.leading, 8)
                Spacer()
                HStack(spacing: 4) {
                    if isCopied, configuration.content == UIPasteboard.general.string {
                        Text("已复制".localized())
                            .font(.system(.caption))
                            .foregroundColor(Color(theme.plainTextColor))
                    } else {
                        Image(systemName: "clipboard")
                            .onTapGesture {
                                copyToClipboard(configuration.content)
                                copiedContents[configuration.content] = true // 更新当前内容的复制状态
                                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                                    copiedContents[configuration.content] = false // 2秒后重置当前内容的复制状态
                                }
                            }
                    }
                }
                .frame(height: 33)
                .padding(.trailing, 8)
//                Spacer()
            }
//            .padding(.horizontal)
//            .padding(.vertical, 8)
            .background(.secondary)

            Divider()

            ScrollView(.horizontal) {
                configuration.label
                    .relativeLineSpacing(.em(0.25))
                    .markdownTextStyle {
                        FontFamilyVariant(.monospaced)
                        FontSize(.em(0.6))
                    }
                    .padding()
            }
        }
        .background(Color(.secondarySystemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .markdownMargin(top: .zero, bottom: .em(0.4))
    }

    private var theme: Splash.Theme {
        switch colorScheme {
        case .dark:
            .wwdc17(withFont: .init(size: 9))
        default:
            .sunset(withFont: .init(size: 9))
        }
    }

    private func copyToClipboard(_ string: String) {
        UIPasteboard.general.string = string
    }
}
