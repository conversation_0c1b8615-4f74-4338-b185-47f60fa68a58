//
//  VerificationView.swift
//
//
//  Created by zwt on 2024/4/11.
//

import Foundation
import Localize_Swift
import SimpleToast
import SwiftUI

struct VerificationView: View {
    @Binding var isLoggedIn: Bool
    @ObservedObject var viewModel: VerificationViewModel
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        VStack {
            Spacer()
            Text("\("请输入".localized()) \(viewModel.email) \("收到的的验证码".localized())")
                .foregroundColor(.mainDark)
                .padding()

            HStack(spacing: 10) {
                PinCodeInputView(completeCode: $viewModel.verifyCodes, numberOfDigits: viewModel.codes.count, foregroundColor: .mainLight) {
                    viewModel.verifyCode()
                }
                .padding()
            }
            .padding(.horizontal)

            Button(action: {
                viewModel.verifyCode()
            }, label: {
                HStack {
                    Text("验证".localized())
                    if viewModel.isLoading {
                        ProgressView()
                            .tint(.mainDark)
                            .padding()
                    }
                }
            })
            .frame(maxWidth: 375)
            .frame(height: 44)
            .background(viewModel.verifyButtonDisable ? Color.mainLight : Color.mainDark)
            .foregroundColor(.reverse)
            .cornerRadius(5)
            .disabled(viewModel.verifyButtonDisable)
            .onChange(of: viewModel.verifyCodes) { value in
                viewModel.verifyButtonDisable = value.count < 6
            }
            .contentShape(Rectangle())
            Spacer()
            Spacer()
        }
        .ignoresSafeArea()
        .simpleToast(isPresented: $viewModel.showToast, options: AppThemes.toastOptions) {
            Label(viewModel.toastMessage, systemImage: "exclamationmark.triangle")
                .padding()
                .background(viewModel.isRequestError ? Color.red.opacity(0.8) : Color.mainDark)
                .foregroundColor(.white)
                .cornerRadius(10)
                .padding(.top)
        }
    }
}
