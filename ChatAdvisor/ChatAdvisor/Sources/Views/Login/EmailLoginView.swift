//
//  EmailLoginView.swift
//  JunShi
//
//  Created by md on 2024/5/10.
//

import Foundation
import Localize_Swift
import SimpleToast
import SwiftUI

struct EmailLoginView: View {
    @Binding var isLoggedIn: Bool
    @FocusState private var isEmailFocused
    @FocusState private var isPasswordFocused
    @StateObject var authViewModel: AuthViewModel = .init()

    var body: some View {
        NavigationView {
            VStack {
                Spacer()
                Text("请输入你的邮箱地址和密码".localized())
                    .foregroundColor(.mainDark)

                VStack(spacing: 20) {
                    InputField(placeholder: "邮箱".localized(),
                               text: $authViewModel.email,
                               isValid: authViewModel.isEmailValid,
                               keyboardType: .emailAddress,
                               textContentType: .emailAddress)
                        .focused($isEmailFocused)
                        .disabled(authViewModel.isEditingDisabled)
                        .onChange(of: authViewModel.email) { _ in
                            if isEmailFocused, authViewModel.email.isValidEmail {
                                authViewModel.isEmailValid = true
                            }
                            authViewModel.loginButtonDisable = !(authViewModel.email.isValidEmail && authViewModel.password.count >= 6 && authViewModel.isPasswordValid)
                        }
                        .onChange(of: isEmailFocused) { isFocused in
                            if !isFocused {
                                authViewModel.isEmailValid = authViewModel.email.isValidEmail
                            }
                        }

                    InputField(placeholder: "密码".localized(),
                               text: $authViewModel.password,
                               isSecure: true,
                               isValid: authViewModel.isPasswordValid,
                               textContentType: .password)
                        .focused($isPasswordFocused)
                        .disabled(authViewModel.isEditingDisabled)
                        .onChange(of: authViewModel.password) { _ in
                            if authViewModel.password.count >= 6 {
                                authViewModel.isPasswordValid = true
                            } else {
                                authViewModel.isPasswordValid = false
                            }
                            authViewModel.loginButtonDisable = !(authViewModel.isEmailValid && authViewModel.isPasswordValid)
                        }
                        .onChange(of: isPasswordFocused) { isFocused in
                            if !isFocused {
                                authViewModel.isPasswordValid = authViewModel.password.count >= 6
                            }
                        }

                    Button(action: {
                        authViewModel.login()
                    }) {
                        Text("登录".localized())
                            .frame(width: AppThemes.buttonMaxWidth, height: AppThemes.buttonHeight)
                            .background(authViewModel.loginButtonDisable ? Color.mainDark.opacity(0.6) : .blue)
                            .foregroundColor(.white)
                            .font(.system(size: AppThemes.fontSize, weight: .medium))
                            .cornerRadius(AppThemes.cornerRadius)
                            .overlay(
                                RoundedRectangle(cornerRadius: AppThemes.cornerRadius)
                                    .stroke(Color.mainBackground.opacity(0.2), lineWidth: 1)
                            )
                            .animation(.easeInOut(duration: 0.2), value: authViewModel.loginButtonDisable)
                    }
                    .disabled(authViewModel.loginButtonDisable)
                    .padding(.horizontal, AppThemes.padding)
                    .padding(.top, AppThemes.padding)
                }
                .padding(.horizontal, AppThemes.padding)
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.2), value: authViewModel.showToast)
                Spacer()
            }
            .padding(.vertical, AppThemes.padding)
            .simpleToast(isPresented: $authViewModel.showToast, options: AppThemes.toastOptions) {
                Label(authViewModel.toastMessage, systemImage: "exclamationmark.triangle")
                    .padding()
                    .background(authViewModel.isRequestError ? Color.red.opacity(0.8) : Color.mainBackground)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                    .padding(.top)
            }
            .navigationBarTitle("邮箱登录".localized(), displayMode: .inline)
            .padding(AppThemes.padding / 2)
            .contentShape(Rectangle())
            .onAppear {
                isEmailFocused = true
            }
        }
    }
}
