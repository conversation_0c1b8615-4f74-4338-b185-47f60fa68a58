import SwiftUI
import UIKit

struct PinCodeInputView: View {
    @Binding var completeCode: String
    let numberOfDigits: Int
    let foregroundColor: Color
    var inputComplete: (() -> Void)?

    @State private var digits: [String]
    @State private var focusedIndex: Int = 0

    init(completeCode: Binding<String>,
         numberOfDigits: Int,
         foregroundColor: Color,
         inputComplete: (() -> Void)? = nil)
    {
        _completeCode = completeCode
        self.numberOfDigits = numberOfDigits
        self.foregroundColor = foregroundColor
        self.inputComplete = inputComplete
        _digits = State(initialValue: Array(repeating: "", count: numberOfDigits))
    }

    var body: some View {
        HStack(spacing: 10) {
            ForEach(0 ..< numberOfDigits, id: \.self) { index in
                CustomTextField(
                    text: $digits[index],
                    isFirstResponder: Binding(
                        get: { focusedIndex == index },
                        set: { newValue in
                            if newValue { focusedIndex = index }
                        }
                    ),
                    onDeleteBackward: {
                        handleDeletion(at: index)
                    }
                )
                .frame(width: 50, height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .foregroundColor(foregroundColor)
                .multilineTextAlignment(.center)
                .font(.system(size: 24, weight: .medium))
                .onChange(of: digits[index]) { newValue in
                    handleInputChange(newValue, at: index)
                }
            }
        }
        .onAppear {
            focusedIndex = 0
        }
    }

    private func handleInputChange(_ newValue: String, at index: Int) {
        // 限制单个输入框只能输入1个字符
        if newValue.count > 1 {
            digits[index] = String(newValue.prefix(1))
        }
        completeCode = digits.joined()

        // 如果输入了数字，则推进到下一个输入框
        if !digits[index].isEmpty {
            if index < numberOfDigits - 1 {
                focusedIndex = index + 1
            } else {
                inputComplete?()
                focusedIndex = -1 // 输入完成后取消聚焦
            }
        }
    }

    private func handleDeletion(at index: Int) {
        // 当当前输入框为空并且不是第一个时，删除键回退到前一个输入框
        if digits[index].isEmpty, index > 0 {
            digits[index - 1] = ""
            completeCode = digits.joined()
            focusedIndex = index - 1
        }
    }
}

struct CustomTextField: UIViewRepresentable {
    @Binding var text: String
    @Binding var isFirstResponder: Bool
    var onDeleteBackward: (() -> Void)?

    func makeUIView(context: Context) -> UITextField {
        let textField = BackspaceDetectingTextField()
        textField.delegate = context.coordinator
        textField.addTarget(
            context.coordinator,
            action: #selector(Coordinator.textDidChange(_:)),
            for: .editingChanged
        )
        textField.onDeleteBackward = onDeleteBackward
        textField.textAlignment = .center
        textField.font = UIFont.systemFont(ofSize: 24, weight: .medium)
        textField.keyboardType = .numberPad
        return textField
    }

    func updateUIView(_ uiView: UITextField, context _: Context) {
        if uiView.text != text {
            uiView.text = text
        }
        // 仅在需要激活的输入框上调用 becomeFirstResponder，
        // 不再主动调用 resignFirstResponder，从而保持键盘持续弹出。
        DispatchQueue.main.async {
            if self.isFirstResponder && !uiView.isFirstResponder {
                uiView.becomeFirstResponder()
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(text: $text, isFirstResponder: $isFirstResponder)
    }

    class Coordinator: NSObject, UITextFieldDelegate {
        @Binding var text: String
        @Binding var isFirstResponder: Bool

        init(text: Binding<String>, isFirstResponder: Binding<Bool>) {
            _text = text
            _isFirstResponder = isFirstResponder
        }

        @objc func textDidChange(_ sender: UITextField) {
            text = sender.text ?? ""
        }
    }
}

class BackspaceDetectingTextField: UITextField {
    var onDeleteBackward: (() -> Void)?

    override func deleteBackward() {
        if text?.isEmpty ?? true {
            onDeleteBackward?()
        }
        super.deleteBackward()
    }
}
