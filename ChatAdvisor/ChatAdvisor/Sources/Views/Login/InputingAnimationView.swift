//
//  InputingAnimationView.swift
//  JunShi
//
//  Created by md on 2024/5/11.
//

import Foundation
import SwiftUI

struct InputingAnimationView: View {
    let colorChange: Bool
    let slogans: [String]
    let subtitle: String?
    let font: Font

    // Add properties to store the timers
    @State private var typingTimer: Timer?
    @State private var deletingTimer: Timer?
    @State private var cursorBlinkingTimer: Timer?

    @State private var typedText = ""
    @State private var currentTextIndex = 0
    @Environment(\.colorScheme) private var colorScheme
    private let typingInterval = 0.1 // 调整打字速度，单位为秒
    private let deleteInterval = 1.0 // 调整删除速度，单位为秒
    @State private var showCursor = true
    @State private var cursorOpacity = 1.0
    @Binding var backgroundColor: Color

    var body: some View {
        ZStack {
            if colorChange {
                withAnimation {
                    backgroundColor
                        .ignoresSafeArea()
                }
            }
            VStack {
                Spacer()
                HStack(alignment: .bottom) {
                    if colorChange {
                        Text("\(typedText)")
                            .font(font)
                            .padding(.trailing, -5)
                    } else {
                        Text("\(typedText)")
                            .font(font)
                            .padding(.trailing, -5)
                            .foregroundColor(Color.mainDark)
                    }
                }
                .padding(.top, 44)
                .padding(.bottom, AppThemes.padding)
                .padding(.horizontal, AppThemes.padding)
                .onAppear {
                    startTypingAnimation()
                    startCursorBlinking()
                }
                .onDisappear {
                    stopTypingAnimation()
                }
                if let subtitle {
                    Text(subtitle)
                }
                Spacer()
                Spacer()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)) { _ in
            stopTypingAnimation()
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
            startTypingAnimation()
        }
    }

    private func stopTypingAnimation() {
        // This method will stop both the typing and deleting animations.
        typingTimer?.invalidate()
        typingTimer = nil
        deletingTimer?.invalidate()
        deletingTimer = nil
        cursorBlinkingTimer?.invalidate()
        cursorBlinkingTimer = nil
    }

    private func startTypingAnimation() {
        guard slogans.count > 0 else { return }

        let textToType = slogans[currentTextIndex]
        var currentIndex = 0

        stopTypingAnimation()

        typingTimer = Timer.scheduledTimer(withTimeInterval: typingInterval, repeats: true) { timer in
            if currentIndex <= textToType.count {
                let nextIndex = textToType.index(textToType.startIndex, offsetBy: currentIndex)
                typedText = String(textToType.prefix(upTo: nextIndex))
                currentIndex += 1
            } else {
                timer.invalidate()
                DispatchQueue.main.asyncAfter(deadline: .now() + deleteInterval) {
                    deleteTextAnimation()
                }
            }
        }
    }

    private func deleteTextAnimation() {
        var currentIndex = typedText.count - 1

        deletingTimer = Timer.scheduledTimer(withTimeInterval: typingInterval, repeats: true) { timer in
            if currentIndex >= 0 {
                // 安全索引检查
                if let nextIndex = typedText.index(typedText.startIndex, offsetBy: currentIndex, limitedBy: typedText.endIndex) {
                    typedText = String(typedText.prefix(upTo: nextIndex))
                }
                currentIndex -= 1
            } else {
                timer.invalidate()
                DispatchQueue.main.asyncAfter(deadline: .now() + deleteInterval) {
                    currentTextIndex = (currentTextIndex + 1) % slogans.count // 循环播放文本动画
                    withAnimation {
                        backgroundColor = getRandomColor() // 随机切换背景颜色
                    }
                    startTypingAnimation()
                }
            }
        }
    }

    private func startCursorBlinking() {
        cursorBlinkingTimer = Timer.scheduledTimer(withTimeInterval: 0.25, repeats: true) { _ in
            cursorOpacity = cursorOpacity == 1.0 ? 0.0 : 1.0
        }
    }

    private func getRandomColor() -> Color {
        let randomRed = Double.random(in: 0 ..< 1)
        let randomGreen = Double.random(in: 0 ..< 1)
        let randomBlue = Double.random(in: 0 ..< 1)
        return Color(red: randomRed, green: randomGreen, blue: randomBlue)
    }
}
