//
//  LoginView.swift
//
//
//  Created by zwt on 2024/4/10.
//

import AuthenticationServices
import GoogleSignInSwift
import SwiftUI

public struct LoginView: View {
    @Binding var isLoggedIn: Bool
    @State private var isShowingEmailLogin = false
    @State private var isShowingRegister = false
    @State private var isShowingPrivacyPolicy = false
    @State private var isShowingUserTerms = false
    @ObservedObject private var viewModel = AuthViewModel.shared
    @ObservedObject private var bootConfigManager = BootManager.shared
    @Environment(\.colorScheme) private var colorScheme

    private let iconSize: CGFloat = 44

    public init(isLoggedIn: Binding<Bool>) {
        _isLoggedIn = isLoggedIn
    }

    public var body: some View {
        ZStack {
            VStack(spacing: 20) {
                Spacer()
                Image(.logo)
                Spacer()
                Text("帮你聊天".localized())
                    .font(.title)
                Spacer()
                
                // SignInWithAppleButton
                LoginButton(icon: "applelogo", title: "Apple登录") {
                    viewModel.startSignInWithAppleFlow()
                }

                LoginButton(icon: "envelope", title: "邮箱登录") {
                    isShowingEmailLogin = true
                }
                .sheet(isPresented: $isShowingEmailLogin) {
                    EmailLoginView(isLoggedIn: $isLoggedIn)
                }

                LoginButton(icon: "person.badge.plus", title: "注册") {
                    isShowingRegister = true
                }
                .sheet(isPresented: $isShowingRegister) {
                    RegisterView(isLoggedIn: $isLoggedIn)
                        .ignoresSafeArea()
                }
                .onChange(of: isLoggedIn) { isLogin in
                    isShowingRegister = !isLogin
                }

                HStack {
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(.gray)
                    Text("or".localized())
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(.gray)
                }
                HStack {
                    Spacer()
                    Button(action: viewModel.handleGoogleSignIn) {
                        Image(uiImage: loadImageFromBundle(named: "google"))
                            .resizable()
                            .frame(width: iconSize, height: iconSize)
                    }
                    if viewModel.hasTwitterInstalled {
                        Spacer()
                        Button(action: viewModel.handleTwitterSignIn) {
                            Image(.twitter)
                                .resizable()
                                .frame(width: iconSize, height: iconSize)
                        }
                    }
                    Spacer()
                }
                VStack {
                    Text("继续使用".localized())
                        .font(.footnote)
                    HStack {
                        Button(action: {
                            isShowingPrivacyPolicy = true
                        }) {
                            Text("隐私政策".localized())
                                .font(.footnote)
                        }
                        .sheet(isPresented: $isShowingPrivacyPolicy) {
                            WebView(url: NetworkURL.privacyPolicy)
                                .navigationTitle("隐私政策".localized())
                        }
                        Text("&")
                            .font(.footnote)
                        Button(action: {
                            isShowingUserTerms = true
                        }) {
                            Text("服务条款".localized())
                                .font(.footnote)
                        }
                        .sheet(isPresented: $isShowingUserTerms) {
                            WebView(url: NetworkURL.userTerms)
                                .navigationTitle("服务条款".localized())
                        }
                    }
                }

                Spacer()
            }
            .padding(.horizontal, AppThemes.padding)
//            .background {
//                Color(uiColor: UIColor(hexString: "#70FF72")!)
//            }
            .simpleToast(isPresented: $viewModel.showToast, options: AppThemes.toastOptions) {
                Label(viewModel.toastMessage, systemImage: "exclamationmark.triangle")
                    .padding()
                    .background(viewModel.isRequestError ? Color.red.opacity(0.8) : Color.mainBackground)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                    .padding(.top, 120)
            }
            .ignoresSafeArea()
            if viewModel.isLoading {
                // 让加载视图在顶部 且阻止点击
                Color.black.opacity(0.2)
                    .ignoresSafeArea()
                ProgressView()
                    .ignoresSafeArea()
                    .tint(.mainBackground)
            }
        }
        .onAppear {
            FirebaseManager.shared.logPageView(pageName: "登录")
        }
    }

    func loadImageFromBundle(named name: String) -> UIImage {
        let bundlePath = Bundle.main.path(forResource: "GoogleSignIn_GoogleSignIn", ofType: "bundle")!
        let bundle = Bundle(path: bundlePath)!
        let image = UIImage(named: name, in: bundle, compatibleWith: nil)
        return image ?? UIImage()
    }
}

struct LoginButton: View {
    let icon: String
    let title: String
    let action: () -> Void
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        Button(action: action) {
            HStack {
                Spacer()
                Image(systemName: icon)
                Text(title.localized())
                Spacer()
            }
        }
        .customButtonStyle(colorScheme: colorScheme)
    }
}

private extension View {
    func customButtonStyle(colorScheme _: ColorScheme) -> some View {
        return frame(width: AppThemes.buttonMaxWidth, height: AppThemes.buttonHeight)
            .contentShape(Rectangle()) // 扩展点击区域
            .foregroundColor(.mainDark)
            .font(.system(size: 16, weight: .medium))
            .cornerRadius(AppThemes.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppThemes.cornerRadius)
                    .stroke(Color.mainBackground, lineWidth: 1)
            )
    }
}
