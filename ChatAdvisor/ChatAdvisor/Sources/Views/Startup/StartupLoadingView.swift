//
//  StartupLoadingView.swift
//  ChatAdvisor
//
//  Created by Assistant on 2024/12/28.
//  应用启动加载界面
//

import SwiftUI
import Localize_Swift

struct StartupLoadingView: View {
    @ObservedObject private var startupManager = AppStartupManager.shared
    @State private var animationRotation: Double = 0
    @State private var progressDots = ""
    @State private var progressTimer: Timer?
    
    var body: some View {
        ZStack {
            // 背景色
            Color(UIColor.systemBackground)
                .ignoresSafeArea()
            
            VStack(spacing: 24) {
                // {{ AURA-X: Modify - 使用应用图标替代系统图标 }}
                // App 图标或 Logo
                Image(.logo)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 60, height: 60)
                    .rotationEffect(.degrees(animationRotation))
                    .animation(
                        Animation.linear(duration: 2.0)
                            .repeatForever(autoreverses: false),
                        value: animationRotation
                    )
                
                // 加载状态文本
                VStack(spacing: 8) {
                    Text(loadingStatusText)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(progressDots)
                        .font(.title2)
                        .foregroundColor(.secondary)
                        .frame(width: 60, alignment: .leading)
                }
                
                // 进度条（可选）
                if case .failed(let error) = startupManager.currentPhase {
                    VStack(spacing: 12) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.orange)
                        
                        Text("启动遇到问题")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text(error.localizedDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
            }
        }
        .onAppear {
            startAnimation()
            startProgressDots()
        }
        .onDisappear {
            stopProgressDots()
        }
    }
    
    private var loadingStatusText: String {
        switch startupManager.currentPhase {
        case .launching:
            return "正在启动..."
        case .initializing:
            return "正在初始化..."
        case .ready:
            return "准备完成"
        case .failed(_):
            return "启动失败"
        }
    }
    
    private func startAnimation() {
        withAnimation {
            animationRotation = 360
        }
    }
    
    private func startProgressDots() {
        progressTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            if progressDots.count < 3 {
                progressDots += "."
            } else {
                progressDots = ""
            }
        }
    }
    
    private func stopProgressDots() {
        progressTimer?.invalidate()
        progressTimer = nil
    }
}

#Preview {
    StartupLoadingView()
}