import Foundation
import Localize_Swift
import MarkdownUI
import Splash
import SwiftUI

struct MessageBubble: View {
    @State var preventCrash = false
    var message: ChatMessage
    @Environment(\.colorScheme) private var colorScheme
    @State private var copiedContents: [String: Bool] = [:]
    @EnvironmentObject var chatViewModel: ChatViewModel // 新增：获取ChatViewModel

    var body: some View {
        HStack(alignment: .top) {
            if message.role == .user {
                Spacer()
                messageContent
                    .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .trailing) // 限制宽度
            } else {
                messageContent
                    .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .leading)
                Spacer()
            }
        }
        .padding(.vertical, 4) // 减小垂直间距
        .onTapGesture {
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
    }

    @ViewBuilder
    private var messageContent: some View {
        VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 4) {
            // 消息内容 - 支持打字机效果
            if preventCrash {
                Text(displayContent)
                    .padding(10) // 增加内边距
                    .fixedSize(horizontal: false, vertical: true)
            } else {
                Markdown(displayContent)
                    .markdownBlockStyle(\.codeBlock) {
                        codeBlock($0)
                    }
                    .markdownCodeSyntaxHighlighter(.splash(theme: theme))
                    .padding(10) // 增加内边距
                    .fixedSize(horizontal: false, vertical: true)
                    .background(colorScheme == .dark ? Color(.systemGray5) : Color(.systemGray6))
                    .cornerRadius(8)
            }

            // 消息状态指示器
            if message.status != .completed {
                MessageStatusIndicator(status: message.status)
                    .padding(.horizontal, 4)
            }
        }
    }

    /// 获取要显示的内容（支持打字机效果）
    private var displayContent: String {
        // 如果是助手消息且显示未完成，使用打字机效果的内容
        if message.role == .assistant && !message.isDisplayComplete {
            let streamingContent = chatViewModel.getStreamingMessageDisplayContent(for: message.id)
            // 防护机制：如果流式内容为空，fallback到消息原始内容
            return streamingContent.isEmpty ? message.content : streamingContent
        }
        // 否则显示完整内容
        return message.content
    }

    @ViewBuilder
    private func codeBlock(_ configuration: CodeBlockConfiguration) -> some View {
        let isCopied = copiedContents[configuration.content] ?? false

        VStack(spacing: 0) {
            HStack {
                Text(configuration.language ?? "plain text")
                    .font(.system(.caption, design: .monospaced))
                    .fontWeight(.semibold)
                    .foregroundColor(Color(theme.plainTextColor))
                Spacer()
                HStack(spacing: 4) {
                    if isCopied, configuration.content == UIPasteboard.general.string {
                        Text("已复制".localized())
                            .font(.system(.caption))
                            .foregroundColor(Color(theme.plainTextColor))
                    } else {
                        Image(systemName: "clipboard")
                            .onTapGesture {
                                copyToClipboard(configuration.content)
                                copiedContents[configuration.content] = true
                                DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                    copiedContents[configuration.content] = false
                                }
                            }
                    }
                }
                .frame(height: 33)
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            .background(.secondary)

            Divider()

            ScrollView(.horizontal) {
                configuration.label
                    .relativeLineSpacing(.em(0.25))
                    .markdownTextStyle {
                        FontFamilyVariant(.monospaced)
                        FontSize(.em(0.85))
                    }
                    .padding()
            }
        }
        .background(Color(.secondarySystemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .markdownMargin(top: .zero, bottom: .em(0.8))
    }

    private var theme: Splash.Theme {
        switch colorScheme {
        case .dark:
            .wwdc18(withFont: .init(size: 9))
        default:
            .sunset(withFont: .init(size: 9))
        }
    }

    private func copyToClipboard(_ string: String) {
        UIPasteboard.general.string = string
    }
}
