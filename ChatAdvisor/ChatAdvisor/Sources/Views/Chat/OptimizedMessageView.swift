//
//  OptimizedMessageView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/06/27.
//

import SwiftUI

/// 优化的消息视图，减少重渲染和内存占用
struct OptimizedMessageView: View {
    let message: ChatMessage
    let isFirst: Bool
    let onFirstMessageAppear: () -> Void
    let onCopy: (String) -> Void
    let onReadAloud: (String) -> Void
    let onSelectText: (ChatMessage) -> Void
    
    @State private var hasAppeared = false
    
    var body: some View {
        MessageBubble(message: message)
            .contextMenu {
                contextMenuItems
            }
            .onAppear {
                // 防止重复触发
                guard !hasAppeared else { return }
                hasAppeared = true
                
                if isFirst {
                    onFirstMessageAppear()
                }
            }
    }
    
    @ViewBuilder
    private var contextMenuItems: some View {
        Button(action: { onCopy(message.content) }) {
            Label("复制".localized(), systemImage: "doc.on.doc")
        }
        
        But<PERSON>(action: { onReadAloud(message.content) }) {
            Label("朗读".localized(), systemImage: "speaker.wave.2")
        }
        
        Button(action: { onSelectText(message) }) {
            Label("选择文本".localized(), systemImage: "text.cursor")
        }
    }
}

/// 消息内容缓存管理器
class MessageContentCache: ObservableObject {
    static let shared = MessageContentCache()
    
    private var cache: [String: String] = [:]
    private let maxCacheSize = 100
    
    private init() {}
    
    func getCachedContent(for messageId: String) -> String? {
        return cache[messageId]
    }
    
    func setCachedContent(for messageId: String, content: String) {
        // 如果缓存超过限制，清理最旧的条目
        if cache.count >= maxCacheSize {
            let oldestKey = cache.keys.first
            if let key = oldestKey {
                cache.removeValue(forKey: key)
            }
        }
        cache[messageId] = content
    }
    
    func clearCache() {
        cache.removeAll()
    }
}

/// 优化的消息气泡视图
struct OptimizedMessageBubble: View {
    let message: ChatMessage
    @StateObject private var contentCache = MessageContentCache.shared
    @State private var renderedContent: String = ""
    
    var body: some View {
        VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 4) {
            HStack {
                if message.role == .user {
                    Spacer(minLength: 60)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    // 消息内容
                    Text(renderedContent)
                        .font(.body)
                        .foregroundColor(message.role == .user ? .white : .primary)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(message.role == .user ? Color.blue : Color.gray.opacity(0.2))
                        )
                    
                    // 消息状态指示器
                    if message.role == .assistant {
                        MessageStatusIndicator(status: message.status)
                    }
                }
                
                if message.role == .assistant {
                    Spacer(minLength: 60)
                }
            }
        }
        .onAppear {
            loadContent()
        }
        .onChange(of: message.content) { _ in
            loadContent()
        }
    }
    
    private func loadContent() {
        // 检查缓存
        if let cachedContent = contentCache.getCachedContent(for: message.id) {
            renderedContent = cachedContent
            return
        }
        
        // 处理内容并缓存
        let processedContent = processMessageContent(message.content)
        contentCache.setCachedContent(for: message.id, content: processedContent)
        renderedContent = processedContent
    }
    
    private func processMessageContent(_ content: String) -> String {
        // 这里可以添加内容处理逻辑，如Markdown渲染等
        return content
    }
}

/// 虚拟化列表项，用于大量消息的性能优化
struct VirtualizedMessageItem: View {
    let message: ChatMessage
    let isVisible: Bool
    
    var body: some View {
        Group {
            if isVisible {
                OptimizedMessageBubble(message: message)
            } else {
                // 占位符，保持布局稳定
                Rectangle()
                    .fill(Color.clear)
                    .frame(height: estimatedHeight)
            }
        }
    }
    
    private var estimatedHeight: CGFloat {
        // 根据消息内容估算高度
        let contentLength = message.content.count
        let baseHeight: CGFloat = 44
        let additionalHeight = CGFloat(contentLength / 50) * 20
        return min(baseHeight + additionalHeight, 200)
    }
}

/// 消息列表虚拟化管理器
class MessageVirtualizationManager: ObservableObject {
    @Published var visibleRange: Range<Int> = 0..<50
    
    private let bufferSize = 10
    private let maxVisibleItems = 50
    
    func updateVisibleRange(for scrollPosition: CGFloat, totalItems: Int) {
        let itemHeight: CGFloat = 60 // 估算的平均项目高度
        let visibleItemsCount = min(maxVisibleItems, totalItems)
        
        let startIndex = max(0, Int(scrollPosition / itemHeight) - bufferSize)
        let endIndex = min(totalItems, startIndex + visibleItemsCount + bufferSize * 2)
        
        let newRange = startIndex..<endIndex
        if newRange != visibleRange {
            visibleRange = newRange
        }
    }
    
    func isItemVisible(_ index: Int) -> Bool {
        return visibleRange.contains(index)
    }
}
