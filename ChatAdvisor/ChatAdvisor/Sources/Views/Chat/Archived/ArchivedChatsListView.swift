//
//  ArchivedChatsListView.swift
//  JunShi
//
//  Created by md on 2024/5/6.
//

import Foundation
import SwifterSwift
import SwiftUI

struct ArchivedChatsListView: View {
    @FocusState private var isTextFieldFocused: Bool
    @ObservedObject var viewModel: ArchivedChatViewModel

    var body: some View {
        VStack {
            // {{ AURA-X: Modify - 统一搜索框样式，与ChatsListView保持一致 }}
            HStack {
                TextField("搜索聊天".localized(), text: $viewModel.searchText)
                    .padding(AppThemes.padding)
                    .background(Color(UIColor(light: .systemGray4, dark: .systemGray2)))
                    .cornerRadius(8)
                    .padding(.horizontal)
                    .onChange(of: viewModel.searchText) { newValue in
                        if newValue.isEmpty {
                            viewModel.refreshChats()
                        } else {
                            viewModel.searchChats()
                        }
                    }
                    .focused($isTextFieldFocused)
                    .onChange(of: isTextFieldFocused) { isFocused in
                        if isFocused == false {
                            viewModel.refreshChats()
                        }
                    }
            }
            // {{ AURA-X: Modify - 使用与ChatsListView一致的现代化列表设计 }}
            ScrollView {
                LazyVStack(spacing: 8) {
                    // 使用 forEach 遍历分组
                    ForEach(Array(viewModel.groupedChats.keys.sorted(by: >)), id: \.self) { date in
                        Section {
                            ForEach(viewModel.groupedChats[date] ?? [], id: \.id) { chat in
                                ArchivedChatRowView(
                                    chat: chat,
                                    isSelected: chat.id == viewModel.currentChat.id,
                                    onTap: {
                                        viewModel.setCurrentChat(chat: chat)
                                    },
                                    onRename: {
                                        viewModel.isRenaming = true
                                        viewModel.newChatTitle = viewModel.getChatTitle(id: chat.id)
                                        viewModel.renamingChatId = chat.id
                                    },
                                    onUnarchive: {
                                        viewModel.unarchiveChat(id: chat.id)
                                    },
                                    onDelete: {
                                        viewModel.deleteChat(id: chat.id)
                                    }
                                )
                                .id(chat.id)
                                .padding(.horizontal, 8)
                            }
                            .listRowSeparator(.hidden)
                        } header: {
                            // {{ AURA-X: Modify - 使用与ChatsListView一致的分组头部样式 }}
                            ArchivedGroupHeaderView(date: date)
                        }
                    }
                    .ignoresSafeArea()
                    if !viewModel.isLoadFinished {
                        ProgressView()
                            .tint(.accentColor)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .onAppear {
                                viewModel.fetchMoreChat()
                            }
                    }
                }
            }
            .onAppear {
                viewModel.fetchMoreChat()
            }
        }
        .alert("重命名".localized(), isPresented: $viewModel.isRenaming) {
            TextField(viewModel.newChatTitle, text: $viewModel.newChatTitle)
            Button("取消".localized(), role: .cancel) {
                viewModel.isRenaming = false
            }
            Button("确认".localized()) {
                viewModel.renameChat()
            }
        } message: {
            Text("请输入新的聊天标题".localized())
        }
    }
}

// MARK: - ArchivedChatRowView

/// {{ AURA-X: Add - 归档聊天行视图组件，与OptimizedChatRowView保持一致的现代化设计 }}
struct ArchivedChatRowView: View {
    let chat: Chat
    let isSelected: Bool
    let onTap: () -> Void
    let onRename: () -> Void
    let onUnarchive: () -> Void
    let onDelete: () -> Void

    // UI状态管理
    @State private var isHovering = false
    @State private var isPressed = false

    // 缓存消息预览，避免重复计算
    @State private var cachedMessagePreview: String?
    @State private var lastChatMessagesCount: Int = 0

    // 缓存计算结果以提高性能
    private var displayTitle: String {
        chat.title.isEmpty ? "没有标题的会话".localized() : chat.title
    }

    private var lastMessagePreview: String? {
        let currentMessagesCount = chat.messages.count

        if cachedMessagePreview == nil || lastChatMessagesCount != currentMessagesCount {
            let preview = chat.messages.last(where: { $0.role == .user || $0.role == .assistant })?.content
            DispatchQueue.main.async {
                cachedMessagePreview = preview
                lastChatMessagesCount = currentMessagesCount
            }
            return preview
        }

        return cachedMessagePreview
    }

    // 动态颜色计算
    private var backgroundColor: Color {
        if isSelected {
            return Color.accentColor.opacity(0.85)
        } else if isHovering {
            return Color.accentColor.opacity(0.08)
        } else {
            return Color.clear
        }
    }

    private var shadowColor: Color {
        isSelected ? Color.accentColor.opacity(0.3) : Color.clear
    }

    var body: some View {
        Button(action: onTap) {
            // {{ AURA-X: Modify - 针对归档列表优化布局，确保标题和预览完全展示 }}
            VStack(alignment: .leading, spacing: 8) {
                // 标题行：包含标题、归档图标和选中状态
                HStack(spacing: 8) {
                    Text(displayTitle)
                        .font(.system(.body, design: .rounded, weight: isSelected ? .semibold : .medium))
                        .foregroundColor(isSelected ? .white : .primary)
                        .lineLimit(2) // 允许标题换行显示
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    // 右侧图标组：归档标识 + 选中状态
                    HStack(spacing: 4) {
                        // 归档标识 - 更小更精致
                        Image(systemName: "archivebox.fill")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)

                        // 选中状态指示器 - 更小的尺寸
                        if isSelected {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white)
                                .transition(.scale.combined(with: .opacity))
                        }
                    }
                }

                // 消息预览 - 独立一行，确保完整显示
                if let preview = lastMessagePreview {
                    Text(preview)
                        .font(.system(.caption, design: .rounded))
                        .foregroundColor(isSelected ? .white.opacity(0.85) : .secondary)
                        .lineLimit(3) // 增加行数限制，确保更多内容可见
                        .multilineTextAlignment(.leading)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle())
        .padding(.vertical, 12) // 增加垂直内边距，为多行内容提供更多空间
        .padding(.horizontal, 12) // 稍微减少水平内边距，为内容留出更多空间
        .frame(minHeight: 80) // 增加最小高度，适应多行标题和预览
        .background(
            // {{ AURA-X: Modify - 针对归档列表优化背景设计，使用更小的圆角 }}
            RoundedRectangle(cornerRadius: 8) // 减小圆角，适应较窄的列表
                .fill(backgroundColor)
                .shadow(
                    color: shadowColor,
                    radius: isSelected ? 6 : 0, // 减小阴影半径
                    x: 0,
                    y: isSelected ? 1 : 0 // 减小阴影偏移
                )
        )
        .overlay(
            // {{ AURA-X: Modify - 调整边框圆角以匹配背景 }}
            RoundedRectangle(cornerRadius: 8) // 与背景圆角保持一致
                .stroke(
                    LinearGradient(
                        colors: isSelected ? [
                            Color.white.opacity(0.3),
                            Color.white.opacity(0.1)
                        ] : [Color.clear],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: isSelected ? 1 : 0
                )
        )
        .scaleEffect(isPressed ? 0.97 : 1.0)
        .contextMenu {
            contextMenuContent
        }
        // 优化动画机制，减少冲突和闪动
        .animation(.spring(response: 0.3, dampingFraction: 0.9, blendDuration: 0), value: isSelected)
        .animation(.easeInOut(duration: 0.12), value: isHovering)
        .animation(.easeInOut(duration: 0.08), value: isPressed)
        // 交互手势
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
            onTap()
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
    }

    @ViewBuilder
    private var contextMenuContent: some View {
        Button(action: onRename) {
            Label("重命名".localized(), systemImage: "pencil")
        }

        Button(action: onUnarchive) {
            Label("取消归档".localized(), systemImage: "archivebox")
        }

        Divider()

        Button(action: onDelete) {
            Label("删除".localized(), systemImage: "trash")
        }
        .foregroundColor(.red)
    }
}

// MARK: - ArchivedGroupHeaderView

/// {{ AURA-X: Add - 归档聊天分组头部视图，针对较窄列表优化设计 }}
struct ArchivedGroupHeaderView: View {
    let date: Date

    var body: some View {
        VStack(spacing: 4) {
            // {{ AURA-X: Modify - 针对归档列表优化分组头部，使用垂直布局节省空间 }}
            HStack {
                Text(date.chatDateLabel())
                    .font(.system(.caption, design: .rounded, weight: .semibold)) // 使用更小的字体
                    .foregroundColor(.primary.opacity(0.8))

                Spacer()
            }

            // 分割线单独一行，确保完整显示
            Rectangle()
                .fill(Color.secondary.opacity(0.2))
                .frame(height: 0.5) // 更细的分割线
        }
        .padding(.horizontal, 12) // 减少水平内边距
        .padding(.vertical, 8) // 减少垂直内边距
        .background(
            // 毛玻璃效果背景
            Rectangle()
                .fill(.ultraThinMaterial)
                .opacity(0.6) // 降低透明度，减少视觉干扰
        )
    }
}
