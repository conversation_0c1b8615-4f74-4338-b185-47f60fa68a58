//
//  ArchivedChatView.swift
//
//
//  Created by zwt on 2024/4/8.
//

import Foundation
import SwiftUI

struct ArchivedChatView: View {
    @ObservedObject var viewModel: ArchivedChatViewModel
    @Environment(\.colorScheme) private var colorScheme

    @GestureState private var gestureOffset: CGFloat = 0

    var body: some View {
        if viewModel.chats.isEmpty {
            HStack {
                Spacer()
                VStack {
                    Spacer()
                    Text("暂无归档".localized())
                        .font(.headline)
                    Spacer()
                }
                Spacer()
            }
            .onAppear {
                FirebaseManager.shared.logPageView(pageName: "归档")
                viewModel.fetchMoreChat {
                    if let chat = viewModel.chats.first {
                        viewModel.setCurrentChat(chat: chat)
                    }
                }
            }
        } else {
            GeometryReader { geometry in
                HStack(alignment: .top) {
                    ArchivedChatsListView(viewModel: viewModel)
                        .frame(width: geometry.size.width / 3) // 1/3 of parent width

                    VStack {
                        ArchivePreviewChatView(chat: viewModel.currentChat)
                    }
                    .frame(width: geometry.size.width * 2 / 3) // 2/3 of parent width
                }
                .navigationBarTitle("已归档".localized(), displayMode: .inline)
                .onAppear {
                    FirebaseManager.shared.logPageView(pageName: "归档")
                }
            }
        }
    }
}
