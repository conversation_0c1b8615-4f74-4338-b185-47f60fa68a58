//
//  ArchivedChatViewModel.swift
//
//
//  Created by zwt on 2024/4/8.
//

import AVFoundation
import Combine
import Foundation
import OSLog
import SwifterSwift
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ArchivedChatViewModel")

class ArchivedChatViewModel: ObservableObject {
    @Published var currentChat: Chat = AdvisorDatabaseManager.shared.startNewChat()
    @Published var chats: [Chat] = []
    @Published var groupedChats: [Date: [Chat]] = [:]
    @Published var searchText = ""
    @Published var newChatTitle = ""
    @Published var renamingChatId = ""
    @Published var readChatText = ""
    @Published var isAnswering = false
    @Published var isLoadingPricing = false
    @Published var isRenaming = false
    @Published var isLoadFinished: Bool = true

    func setCurrentChat(chat: Chat) {
        currentChat = chat
        Task {
            await fetchCurrentChatMessages()
        }
    }

    func refreshChats() {
        Task {
            let chats = await AdvisorDatabaseManager.shared.fetchArchivedChats()
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                groupedChats = [:]
                self.chats = chats
                integrateNewChats(chats)
            }
        }
    }

    func searchChats() {
        Task {
            FirebaseManager.shared.logSearch(query: searchText, isArchived: true)
            let chats = await AdvisorDatabaseManager.shared.searchChats(keyword: searchText, archive: true)
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                groupedChats = [:]
                self.chats = chats
                integrateNewChats(chats)
            }
        }
    }

    func getChatTitle(id: String) -> String {
        guard let chat = chats.first(where: { $0.id == id }) else {
            return ""
        }
        return chat.title
    }

    func deleteChat(id: String) {
        removeMemoryChat(id: id)
        AdvisorDatabaseManager.shared.deleteChat(id: id)
    }

    func unarchiveChat(id: String) {
        removeMemoryChat(id: id)
        AdvisorDatabaseManager.shared.unarchiveChat(id: id)
    }

    func renameChat() {
        guard !newChatTitle.isEmpty else {
            return
        }
        updateMemoryChatTitle()
        AdvisorDatabaseManager.shared.updateChatTitle(id: renamingChatId, title: newChatTitle)
        isRenaming = false
        renamingChatId = ""
    }

    func updateMemoryChatTitle() {
        if var chat = chats.first(where: { $0.id == renamingChatId }) {
            chat.title = newChatTitle
            updateChatInGroupedChats(newChat: chat)
        }
    }

    func removeMemoryChat(id: String) {
        // 从原始的聊天列表中移除
        chats.removeAll { $0.id == id }
        removeChatFromGroupedChats(chatId: id)
    }

    func updateChatInGroupedChats(newChat: Chat) {
        let chatId = newChat.id
        for (date, var chats) in groupedChats {
            if let index = chats.firstIndex(where: { $0.id == chatId }) {
                chats[index] = newChat
                groupedChats[date] = chats
                break
            }
        }
    }

    func removeChatFromGroupedChats(chatId: String) {
        for (date, chats) in groupedChats {
            let filteredChats = chats.filter { $0.id != chatId }
            if filteredChats.isEmpty {
                groupedChats.removeValue(forKey: date)
            } else {
                groupedChats[date] = filteredChats
            }
        }
    }

    func fetchCurrentChatMessages() async {
        let messages = await AdvisorDatabaseManager.shared.fetchMessages(chatId: currentChat.id)
        // 将更新操作调度到主线程以更新UI
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            currentChat.messages = messages
        }
    }

    func fetchMoreChat(completion: (() -> Void)? = nil) {
        Task {
            let newChats = await AdvisorDatabaseManager.shared.fetchArchivedChats(offset: chats.count)
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                chats.append(contentsOf: newChats)
                integrateNewChats(newChats)
                if newChats.count != 0 {
                    isLoadFinished = false
                } else {
                    isLoadFinished = true
                }
                completion?()
            }
        }
    }

    func fecthMoreCurrentChatMessages() async {
        let messages = await AdvisorDatabaseManager.shared.fetchMessages(chatId: currentChat.id, offset: currentChat.messages.count)
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            currentChat.messages.append(contentsOf: messages)
        }
    }

    private func integrateNewChats(_ newChats: [Chat]) {
        let calendar = Calendar.current
        for chat in newChats {
            let date = Date.dateFromTimestamp(chat.createdTime)
            let startOfDay = calendar.startOfDay(for: date)

            if groupedChats[startOfDay] == nil {
                groupedChats[startOfDay] = []
            }

            // 检查是否已经存在相同的 Chat
            if !(groupedChats[startOfDay]?.contains(where: { $0.id == chat.id }) ?? false) {
                groupedChats[startOfDay]?.append(chat)
            }
        }

        // 保持分组内的排序（如果需要）
        for (key, _) in groupedChats {
            groupedChats[key]?.sort(by: { $0.createdTime > $1.createdTime })
        }
    }
}
