//  ChatsListView.swift
//  JunShi
//
//  Created by md on 2024/5/6.
//

import Foundation
import SwifterSwift
import SwiftUI

struct ChatListView: View {
    @Binding var showSideMenu: Bool
    @Binding var showMultiStepForm: Bool
    @Binding var sideBarWidth: CGFloat
    @Binding var chatViewModels: [ChatViewModel]
    @Binding var selectedChatID: String?
    @EnvironmentObject var contentViewModel: ContentViewModel
    @EnvironmentObject var chatListViewModel: ChatListViewModel
    @FocusState private var isTextFieldFocused: Bool
    @State private var showSettings = false
    @State private var lastSelectionTime = Date()
    @State private var isSelecting = false
    private let selectionDebounceInterval: TimeInterval = 0.3

    var body: some View {
        VStack {
            HStack {
                if AccountManager.shared.currentUser != nil {
                    TextField("搜索聊天".localized(), text: $chatListViewModel.searchText)
                        .padding(AppThemes.padding)
                        .background(Color(UIColor(light: .systemGray4, dark: .systemGray2)))
                        .cornerRadius(8)
                        .padding(.horizontal)
                        .onChange(of: chatListViewModel.searchText) { newValue in
                            if newValue.isEmpty {
                                chatListViewModel.refreshChats()
                            } else {
                                chatListViewModel.searchChats()
                            }
                        }
                        .focused($isTextFieldFocused)
                        .onChange(of: isTextFieldFocused) { isFocused in
                            if isFocused == false {
                                chatListViewModel.refreshChats()
                            }
                            
                            withAnimation {
                                if isFocused {
                                    sideBarWidth = UIScreen.main.bounds.width
                                } else {
                                    sideBarWidth = AppThemes.sideMenuWidth
                                }
                            }
                        }
                }
                if isTextFieldFocused {
                    Button(action: {
                       withAnimation {
                           isTextFieldFocused = false
                       }
                   }) {
                       withAnimation {
                           Image(systemName: "xmark")
                               .foregroundColor(.mainDark)
                       }
                   }
                }
                Spacer()
            }
            .padding(.bottom, AppThemes.padding)

            // 显示加载状态
            if chatListViewModel.isLoading && chatListViewModel.groupedChats.isEmpty {
                VStack {
                    Spacer()
                    ProgressView("loading_conversations".localized())
                        .tint(.mainDark)
                    Spacer()
                }
            }
            // 显示错误状态
            else if chatListViewModel.hasError {
                VStack {
                    Spacer()
                    Image(systemName: "exclamationmark.triangle")
                        .font(.largeTitle)
                        .foregroundColor(.orange)
                    Text(chatListViewModel.errorMessage)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding()
                    Button("重试") {
                        chatListViewModel.retryRefresh()
                    }
                    .buttonStyle(.borderedProminent)
                    .tint(.mainDark)
                    Spacer()
                }
            }
            // 显示会话列表
            else if chatListViewModel.groupedChats.count > 0 {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        // {{ AURA: Modify - 优化列表渲染性能和间距 }}
                        // 使用 forEach 遍历分组
                        ForEach(Array(chatListViewModel.groupedChats.keys.sorted(by: >)), id: \.self) { date in
                            Section {
                                ForEach(chatListViewModel.groupedChats[date] ?? [], id: \.id) { chat in
                                    OptimizedChatRowView(
                                        chat: chat,
                                        isSelected: chat.id == selectedChatID,
                                        onTap: { 
                                            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                                            selectChat(chat)
                                        },
                                        onRename: {
                                            chatListViewModel.isRenaming = true
                                            chatListViewModel.newChatTitle = chatListViewModel.getChatTitle(id: chat.id)
                                            chatListViewModel.renamingChatId = chat.id
                                        },
                                        onArchive: {
                                            chatListViewModel.archiveChat(id: chat.id)
                                        },
                                        onDelete: {
                                            chatListViewModel.deleteChat(id: chat.id)
                                        }
                                    )
                                    .id(chat.id) // {{ AURA: Add - 添加稳定的ID以优化SwiftUI性能 }}
                                    .padding(.horizontal, 8)
                                }
                                .listRowSeparator(.hidden)
                            } header: {
                                // {{ AURA: Modify - 优化分组头部显示，使用更轻量的实现 }}
                                GroupHeaderView(date: date)
                            }
                        }
                        // 分页加载指示器
                        if chatListViewModel.searchText.isEmpty {
                            if chatListViewModel.isLoadingMore {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("loading_more".localized())
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .frame(height: 50)
                                .frame(maxWidth: .infinity)
                            } else if chatListViewModel.loadMore && chatListViewModel.hasMoreData {
                                // 触发加载更多的区域
                                Color.clear
                                    .frame(height: 20)
                                    .onAppear {
                                        chatListViewModel.fetchMoreChats()
                                    }
                            } else if chatListViewModel.showNoMoreDataMessage && !chatListViewModel.chats.isEmpty {
                                Text("all_conversations_displayed".localized())
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(height: 40)
                                    .frame(maxWidth: .infinity)
                            }
                        }
                    }
                }
            } else {
                Spacer()
                Text("empty_session".localized())
                    .foregroundColor(.secondary)
                    .padding(20)
                if UIDevice.current.userInterfaceIdiom == .pad {
                    Spacer()
                } else {
                    Button(action: {
                        if AccountManager.shared.currentUser == nil {
                            withAnimation {
                                AccountManager.shared.needLoggedIn = true
                            }
                            return
                        }
                        showSideMenu.toggle()
                        showMultiStepForm.toggle()
                    }) {
                        HStack {
                            Spacer()
                            // {{ AURA-X: Modify - 使用应用图标替代系统图标 }}
                            Image(.logo)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 20, height: 20)
                            Text("开始新的对话吧".localized())
                                .foregroundColor(.mainDark)
                            Spacer()
                        }
                        .padding()
                    }
                    Spacer()
                }
            }

            Divider()
            HStack {
                HStack {
                    Spacer()
                    Text(AccountManager.shared.currentUser?.fullName ?? AccountManager.shared.currentUser?.email.emailPrefix ?? "Guest".localized())
                        .padding(.horizontal, AppThemes.padding)
                        .foregroundColor(.mainDark)
                    Spacer()
                }
                Image(systemName: "gearshape")
                    .frame(width: 44, height: 44)
                    .padding(.horizontal, AppThemes.padding)
                    .foregroundColor(.mainDark)
            }
            .onTapGesture {
                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                showSettings = true
            }
            .padding(.horizontal)
        }
        .onChange(of: AccountManager.shared.currentUser) { new in
            if new != nil {
                chatListViewModel.fetchMoreChats()
            }
        }
        .onAppear {
            // 确保视图出现时加载数据，但避免重复加载
            if AccountManager.shared.currentUser != nil && !chatListViewModel.isInitialLoadCompleted {
                chatListViewModel.performInitialLoad()
            }
        }
        .alert("重命名".localized(), isPresented: $chatListViewModel.isRenaming) {
            TextField(chatListViewModel.newChatTitle, text: $chatListViewModel.newChatTitle)
            Button("取消", role: .cancel) {
                chatListViewModel.isRenaming = false
            }
            Button("好的".localized()) {
                chatListViewModel.renameChat()
            }
        } message: {
            Text("请输入新的聊天标题".localized())
        }
        .sheet(isPresented: $showSettings) {
            NavigationView {
                SettingsView(showSettings: $showSettings)
                    .navigationBarTitleDisplayMode(.inline)
            }
        }
    }

    // MARK: - Helper Methods

    private func selectChat(_ chat: Chat) {
        // {{ AURA: Modify - 优化防抖机制，减少不必要的检查 }}
        let now = Date()
        guard now.timeIntervalSince(lastSelectionTime) > selectionDebounceInterval else { return }
        lastSelectionTime = now

        // 防止重复选择同一个会话
        guard selectedChatID != chat.id else { return }

        // 防止并发选择
        guard !isSelecting else { return }
        isSelecting = true

        // {{ AURA: Modify - 优化触觉反馈，使用更轻量的反馈 }}
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.prepare() // 预加载反馈生成器
        impactFeedback.impactOccurred()

        Task {
            do {
                // {{ AURA: Modify - 优化状态设置，减少不必要的UI更新 }}
                await MainActor.run {
                    contentViewModel.isLoadingChatDetails = true
                    selectedChatID = chat.id // 立即更新选中状态，提供即时反馈
                }

                // {{ AURA: Modify - 使用优化的会话选择方法 }}
                try await contentViewModel.selectChatOptimized(chat: chat, chatListViewModel: chatListViewModel)

                // {{ AURA: Modify - 简化最终状态更新 }}
                await MainActor.run {
                    // 确保chatViewModels绑定是最新的
                    if let chatViewModel = contentViewModel.currentChatViewModel,
                       !chatViewModels.contains(where: { $0.currentChat.id == chat.id }) {
                        chatViewModels.append(chatViewModel)
                    }

                    contentViewModel.isLoadingChatDetails = false
                    isSelecting = false
                }

            } catch {
                // {{ AURA: Modify - 优化错误处理，减少重复的状态设置 }}
                await MainActor.run {
                    contentViewModel.isLoadingChatDetails = false
                    isSelecting = false
                    selectedChatID = nil // 重置选中状态

                    // 设置详细的错误信息
                    contentViewModel.chatLoadingError = "选择会话失败: \(error.localizedDescription)"
                }
            }
        }
    }
}
