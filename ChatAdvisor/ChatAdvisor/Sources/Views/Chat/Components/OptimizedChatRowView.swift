//
//  OptimizedChatRowView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import SwiftUI

/// 优化的聊天行视图组件 - 现代化美观设计
struct OptimizedChatRowView: View {
    let chat: Chat
    let isSelected: Bool
    let onTap: () -> Void
    let onRename: () -> Void
    let onArchive: () -> Void
    let onDelete: () -> Void
    
    // {{ AURA: Add - UI状态管理 }}
    @State private var isHovering = false
    @State private var isPressed = false
    
    // {{ AURA: Add - 缓存消息预览，避免重复计算 }}
    @State private var cachedMessagePreview: String?
    @State private var lastChatMessagesCount: Int = 0
    
    // {{ AURA: Add - 缓存计算结果以提高性能 }}
    private var displayTitle: String {
        chat.title.isEmpty ? "没有标题的会话".localized() : chat.title
    }
    
    private var lastMessagePreview: String? {
        // {{ AURA: Modify - 使用缓存的消息预览，只在消息变化时重新计算 }}
        let currentMessagesCount = chat.messages.count
        
        if cachedMessagePreview == nil || lastChatMessagesCount != currentMessagesCount {
            let preview = chat.messages.last(where: { $0.role == .user || $0.role == .assistant })?.content
            DispatchQueue.main.async {
                cachedMessagePreview = preview
                lastChatMessagesCount = currentMessagesCount
            }
            return preview
        }
        
        return cachedMessagePreview
    }
    
    // {{ AURA: Add - 动态颜色计算 }}
    private var backgroundColor: Color {
        if isSelected {
            return Color.accentColor.opacity(0.85)
        } else if isHovering {
            return Color.accentColor.opacity(0.08)
        } else {
            return Color.clear
        }
    }
    
    private var shadowColor: Color {
        isSelected ? Color.accentColor.opacity(0.3) : Color.clear
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 14) {
                // {{ AURA: Modify - 固定内容区域宽度，避免选中图标影响布局 }}
                VStack(alignment: .leading, spacing: 6) {
                    Text(displayTitle)
                        .font(.system(.body, design: .rounded, weight: isSelected ? .semibold : .medium))
                        .foregroundColor(isSelected ? .white : .primary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .lineLimit(1)
                    
                    // {{ AURA: Modify - 优化消息预览样式，固定宽度避免闪动 }}
                    if let preview = lastMessagePreview {
                        Text(preview)
                            .font(.system(.caption, design: .rounded))
                            .foregroundColor(isSelected ? .white.opacity(0.85) : .secondary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading) // {{ AURA: Add - 确保内容区域占据固定空间 }}
                
                // {{ AURA: Modify - 固定选中指示器区域，避免布局变化 }}
                ZStack {
                    // 占位透明区域，确保布局稳定
                    Circle()
                        .fill(Color.clear)
                        .frame(width: 28, height: 28)
                    
                    // 实际选中指示器
                    if isSelected {
                        ZStack {
                            Circle()
                                .fill(Color.white.opacity(0.2))
                                .frame(width: 28, height: 28)
                            
                            Image(systemName: "checkmark")
                                .foregroundColor(.white)
                                .font(.system(size: 14, weight: .semibold))
                        }
                        .transition(.asymmetric(
                            insertion: .scale(scale: 0.1).combined(with: .opacity),
                            removal: .scale(scale: 0.1).combined(with: .opacity)
                        ))
                    }
                }
                .frame(width: 28, height: 28) // {{ AURA: Add - 固定选中区域尺寸 }}
            }
        }
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle())
        .padding(.vertical, 10)
        .padding(.horizontal, 14)
        .frame(minHeight: 68)
        .background(
            // {{ AURA: Modify - 现代化背景设计 }}
            RoundedRectangle(cornerRadius: 12)
                .fill(backgroundColor)
                .shadow(
                    color: shadowColor,
                    radius: isSelected ? 8 : 0,
                    x: 0,
                    y: isSelected ? 2 : 0
                )
        )
        .overlay(
            // {{ AURA: Modify - 精致的边框效果 }}
            RoundedRectangle(cornerRadius: 12)
                .stroke(
                    LinearGradient(
                        colors: isSelected ? [
                            Color.white.opacity(0.3),
                            Color.white.opacity(0.1)
                        ] : [Color.clear],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: isSelected ? 1 : 0
                )
        )
        .scaleEffect(isPressed ? 0.97 : 1.0)
        .contextMenu {
            contextMenuContent
        }
        // {{ AURA: Modify - 优化动画机制，减少冲突和闪动 }}
        .animation(.spring(response: 0.3, dampingFraction: 0.9, blendDuration: 0), value: isSelected)
        .animation(.easeInOut(duration: 0.12), value: isHovering)
        .animation(.easeInOut(duration: 0.08), value: isPressed)
        // {{ AURA: Add - 交互手势 }}
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
            onTap()
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
    }
    
    // {{ AURA: Add - 分离上下文菜单内容，提高代码可读性 }}
    @ViewBuilder
    private var contextMenuContent: some View {
        Button(action: onRename) {
            Label("重命名".localized(), systemImage: "pencil")
        }
        
        Button(action: onArchive) {
            Label("归档".localized(), systemImage: "archivebox")
        }
        
        Button(action: onDelete) {
            Label("删除".localized(), systemImage: "trash")
        }
    }
}

/// 优化的分组头部视图 - 现代化设计
struct GroupHeaderView: View {
    let date: Date
    
    var body: some View {
        HStack {
            // {{ AURA: Add - 现代化分组标题设计 }}
            Text(date.chatDateLabel())
                .font(.system(.subheadline, design: .rounded, weight: .semibold))
                .foregroundColor(.primary.opacity(0.8))
            
            Spacer()
            
            // {{ AURA: Add - 装饰性分割线 }}
            Rectangle()
                .fill(Color.secondary.opacity(0.2))
                .frame(height: 1)
                .frame(maxWidth: .infinity)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            // {{ AURA: Add - 毛玻璃效果背景 }}
            Rectangle()
                .fill(.ultraThinMaterial)
                .opacity(0.8)
        )
    }
}
