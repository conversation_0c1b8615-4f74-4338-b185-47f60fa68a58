//
//  RecognizeSmallView.swift
//  ChatAdvisor
//
//  Created by md on 2024/7/4.
//

import Foundation
import SwiftUI

struct RecognizeSmallView: View {
    var showTextLayer: Bool
    var imageData: Data
    var recognizedTexts: [RecognizedText]
    var geometry: GeometryProxy
    var onDelete: () -> Void
    var onFullScreen: () -> Void

    var body: some View {
        if let uiImage = UIImage(data: imageData) {
            let imageSize = uiImage.size
            let scale = min(geometry.size.width / imageSize.width, geometry.size.height / imageSize.height)
            let scaledImageSize = CGSize(width: imageSize.width * scale, height: imageSize.height * scale)
            ZStack(alignment: .topLeading) {
                Spacer()
                Image(uiImage: uiImage)
                    .resizable()
                    .scaledToFit()
                    .frame(width: scaledImageSize.width, height: scaledImageSize.height)
                    .cornerRadius(6.0)
                    .clipped()
                if showTextLayer {
                    ForEach(recognizedTexts, id: \.self) { recognizedText in
                        let boundingBox = recognizedText.boundingBox
                        let rect = CGRect(
                            x: boundingBox.minX * scaledImageSize.width,
                            y: (1 - boundingBox.maxY) * scaledImageSize.height,
                            width: boundingBox.width * scaledImageSize.width,
                            height: boundingBox.height * scaledImageSize.height
                        )

                        Text(recognizedText.string)
                            .font(.caption2)
                            .padding(2)
                            .background(Color.mainDark.opacity(0.7))
                            .position(x: rect.midX, y: rect.midY)
                            .frame(width: rect.width, height: rect.height)
                    }
                }

                HStack {
                    Spacer()
                    Button(action: onDelete) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.red)
                    }
                }
            }
            .frame(width: scaledImageSize.width, height: scaledImageSize.height)
            .onTapGesture {
                onFullScreen()
            }
        }
    }
}
