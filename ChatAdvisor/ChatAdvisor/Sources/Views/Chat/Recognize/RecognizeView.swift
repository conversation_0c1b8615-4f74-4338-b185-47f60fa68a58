import Localize_Swift
import MarkdownUI
import PhotosUI
import SwiftUI
import Vision

struct RecognizeView: View {
    @ObservedObject var viewModel: RecognizeViewModel
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack {
            if viewModel.showPhotosPicker {
                VStack {

                    HStack {
                        PhotosPicker(
                            selection: $viewModel.selectedItemsLocal,
                            matching: .images,
                            photoLibrary: .shared()
                        ) {
                            Label("Import_local".localized(), systemImage: "photo")
                                .padding(.horizontal, AppThemes.padding)
                                .padding(.vertical)
                                .font(.subheadline)
                                .background(Color.mainDark)
                                .foregroundColor(.white)
                                .cornerRadius(10)
                                .lineLimit(1)
                                .frame(maxWidth: .infinity)
                        }
                        .onChange(of: viewModel.selectedItemsLocal) { newItems in
                            viewModel.onChangeOfSelectedItems(newItems, isLocalRecognition: true)
                        }

                        PhotosPicker(
                            selection: $viewModel.selectedItemsRemote,
                            matching: .images,
                            photoLibrary: .shared()
                        ) {
                            Label("Import_remote".localized(), systemImage: "cloud.fill")
                                .padding(.horizontal, AppThemes.padding)
                                .padding(.vertical)
                                .font(.subheadline)
                                .background(Color.mainDark)
                                .foregroundColor(.white)
                                .cornerRadius(10)
                                .lineLimit(1)
                                .frame(maxWidth: .infinity)
                        }
                        .onChange(of: viewModel.selectedItemsRemote) { newItems in
                            viewModel.onChangeOfSelectedItems(newItems, isLocalRecognition: false)
                        }
                    }
                }
            } else {
                HStack {
                    Text("Import_remote".localized())
                        .foregroundColor(viewModel.isLocalRecognition ? .gray : .mainDark)
                        .onTapGesture {
                            if !viewModel.isProcessingImage {
                                viewModel.isLocalRecognition = false
                            }
                        }
                    Toggle("", isOn: $viewModel.isLocalRecognition)
                        .labelsHidden()
                        .toggleStyle(SwitchToggleStyle(tint: .clear))
                        .disabled(viewModel.isProcessingImage)
                    Text("Import_local".localized())
                        .foregroundColor(viewModel.isLocalRecognition ? .mainDark : .gray)
                        .onTapGesture {
                            if !viewModel.isProcessingImage {
                                viewModel.isLocalRecognition = true
                            }
                        }
                }
                .onChange(of: viewModel.isLocalRecognition) { isLocalRecognition in
                    if isLocalRecognition {
                        if viewModel.selectedItemsLocal.count != viewModel.selectedItemsRemote.count {
                            viewModel.selectedItemsLocal = viewModel.selectedItemsRemote
                        }
                    } else {
                        if viewModel.selectedItemsLocal.count != viewModel.selectedItemsRemote.count {
                            viewModel.selectedItemsRemote = viewModel.selectedItemsLocal
                        }
                    }
                }
                .padding(.horizontal)
            }
            let targetItems = viewModel.isLocalRecognition ? viewModel.selectedItemsLocal : viewModel.selectedItemsRemote
            if !targetItems.isEmpty {
                Divider()
                GeometryReader { geometry in
                    ScrollView(.horizontal) {
                        LazyHStack {
                            ForEach(targetItems.indices, id: \.self) { index in
                                if index < viewModel.recognizedTextsByImage.count {
                                    RecognizeSmallView(
                                        showTextLayer: viewModel.isLocalRecognition,
                                        imageData: viewModel.selectedImagesData[index],
                                        recognizedTexts: viewModel.recognizedTextsByImage[index],
                                        geometry: geometry,
                                        onDelete: {
                                            viewModel.onDelete(index: index)
                                        },
                                        onFullScreen: {
                                            viewModel.selectedImageIndex = index
                                            withAnimation {
                                                viewModel.isFullScreen.toggle()
                                            }
                                        }
                                    )
                                    .cornerRadius(6.0)
                                    .clipped()
                                } else {
                                    ZStack {
                                        ProgressView()
                                            .frame(width: 44)
                                            .cornerRadius(6.0)
                                            .clipped()
                                            .tint(.mainDark)
                                    }
                                }
                                Divider()
                            }
                        }
                        .cornerRadius(6.0)
                        .clipped()
                    }
                    .padding(.horizontal, AppThemes.padding / 2)
                    .fullScreenCover(isPresented: $viewModel.isFullScreen) {
                        TabView(selection: $viewModel.selectedImageIndex) {
                            ForEach(viewModel.selectedImagesData.indices, id: \.self) { index in
                                RecognizeFullScreenView(
                                    showtextLayer: viewModel.isLocalRecognition,
                                    imageIndex: index,
                                    isFullScreen: $viewModel.isFullScreen,
                                    imageData: viewModel.selectedImagesData[index],
                                    recognizedTexts: index < viewModel.recognizedTextsByImage.count ? viewModel.recognizedTextsByImage[index] : [],
                                    onDismiss: { viewModel.isFullScreen.toggle() },
                                    onDelete: { _, textIndex in
                                        viewModel.deleteRecognizedText(from: index, textIndex: textIndex)
                                    }
                                )
                                .tag(index)
                                .frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height)
                            }
                        }
                        .tabViewStyle(PageTabViewStyle())
                        .onAppear {
                            UIPageControl.appearance().currentPageIndicatorTintColor = Color.mainDark.uiColor
                            UIPageControl.appearance().pageIndicatorTintColor = Color.mainDark.uiColor.withAlphaComponent(0.2)
                        }
                    }
                }
                .frame(height: 120)
                // Display progress bar for text recognition
                if viewModel.selectedItemsLocal.count > viewModel.recognizedTextsByImage.count {
                    ProgressView(value: Double(viewModel.recognizedTextsByImage.count) / Double(viewModel.selectedItemsLocal.count))
                        .padding(.horizontal)
                }
            }

            if !viewModel.chatMessages.isEmpty {
                Divider()
                ZStack {
                    List {
                        Section(header: HStack {
                            Text("对方".localized())
                            Spacer()
//                            Text("Me".localized())
                        }
                        ) {
                            ForEach(viewModel.chatMessages.indices, id: \.self) { imageIndex in
                                ForEach(viewModel.chatMessages[imageIndex].sorted(by: { $0.createdTime < $1.createdTime })) { message in
                                    HStack {
                                        RecognizeMessageView(showDelete: viewModel.isLocalRecognition, imageIndex: imageIndex, message: message, onDelete: viewModel.deleteMessage)
                                            .offset(x: viewModel.dragState?.message.id == message.id ? viewModel.dragState?.translation.width ?? 0 : 0)
//                                            .gesture(viewModel.dragGesture(for: message))
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                    }
                                    .listRowInsets(.init(top: 0, leading: 0, bottom: 6, trailing: 0))
                                    .listRowSeparator(.hidden)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, AppThemes.padding / 2)
                    .listStyle(.inset)
                }

            } else {
                let targetItems = viewModel.isLocalRecognition ? viewModel.selectedItemsLocal : viewModel.selectedItemsRemote
                if !targetItems.isEmpty {
                    ProgressView()
                        .frame(width: 44)
                        .cornerRadius(6.0)
                        .clipped()
                        .tint(.mainDark)
                }
                Spacer()
            }
        }
    }
}

private extension View {
    @ViewBuilder
    func iOS17DefaultScrollBehavior() -> some View {
        if #available(iOS 17.0, *) {
            self.scrollTargetBehavior(.paging)
        } else {
            self
        }
    }
}
