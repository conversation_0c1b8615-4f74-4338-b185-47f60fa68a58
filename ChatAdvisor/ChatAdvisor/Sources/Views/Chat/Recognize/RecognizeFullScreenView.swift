//  RecognizeFullScreenView.swift
//  ChatAdvisor
//
//  Created by md on 2024/7/4.
//

import Foundation
import SwiftUI

struct RecognizeFullScreenView: View {
    var showtextLayer: Bool
    var imageIndex: Int
    @Binding var isFullScreen: Bool
    var imageData: Data
    var recognizedTexts: [RecognizedText]
    var onDismiss: () -> Void
    var onDelete: (String, Int) -> Void

    var body: some View {
        if let uiImage = UIImage(data: imageData) {
            let imageSize = uiImage.size
            GeometryReader { geometry in
                let scale = min(geometry.size.width / imageSize.width, geometry.size.height / imageSize.height)
                let xOffset = (geometry.size.width - imageSize.width * scale) / 2
                let yOffset = (geometry.size.height - imageSize.height * scale) / 2

                ZStack(alignment: .topLeading) {
                    Spacer()

                    Image(uiImage: uiImage)
                        .resizable()
                        .scaledToFit()
                        .frame(width: geometry.size.width, height: geometry.size.height)

                    if showtextLayer {
                        ForEach(recognizedTexts.indices, id: \.self) { index in
                            let boundingBox = recognizedTexts[index].boundingBox
                            let rect = CGRect(
                                x: boundingBox.minX * imageSize.width * scale + xOffset,
                                y: (1 - boundingBox.maxY) * imageSize.height * scale + yOffset,
                                width: boundingBox.width * imageSize.width * scale,
                                height: boundingBox.height * imageSize.height * scale
                            )

                            HStack {
                                Text(recognizedTexts[index].string)
                                    .font(.caption2)
                                    .padding(2)
                                    .background(Color.mainDark.opacity(0.7))
                                    .cornerRadius(4)
                                    .clipped()
                                Button(action: {
                                    onDelete(recognizedTexts[index].string, index)
                                }) {
                                    Image(systemName: "xmark.circle.fill")
                                        .foregroundColor(.red)
                                }
                            }
                            .position(x: rect.midX, y: rect.midY)
                            .frame(width: rect.width + 100, height: rect.height)
                        }
                    }
                }
                .ignoresSafeArea()
                .contentShape(Rectangle())
                .onTapGesture {
                    withAnimation {
                        isFullScreen.toggle()
                    }
                }
                .gesture(DragGesture().onEnded { value in
                    // 如果是下滑手势
                    if value.translation.height > 0 {
                        withAnimation {
                            isFullScreen.toggle()
                        }
                    }
                })
            }
        }
    }
}
