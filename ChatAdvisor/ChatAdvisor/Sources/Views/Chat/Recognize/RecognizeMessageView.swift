//
//  RecognizeMessageView.swift
//  ChatAdvisor
//
//  Created by md on 2024/7/4.
//

import Foundation
import SwiftUI

struct RecognizeMessageView: View {
    var showDelete: Bool
    var imageIndex: Int
    var message: ChatMessage
    var onDelete: (ChatMessage, Int) -> Void

    var body: some View {
        HStack(alignment: .top) {
            if message.role == .assistant {
                Spacer()
            }
            HStack {
                Text(message.content)
                    .font(.caption2)
                    .padding()
                    .background(message.role == .assistant ? Color.green : Color.mainDark)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                if showDelete {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.red)
                        .padding(5)
                        .onTapGesture {
                            onDelete(message, imageIndex)
                        }
                }
            }
            if message.role == .user {
                Spacer()
            }
        }
    }
}
