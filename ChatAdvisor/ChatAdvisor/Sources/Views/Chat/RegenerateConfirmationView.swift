//
//  RegenerateConfirmationView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import SwiftUI

// {{ AURA-X: Add - 重新生成中间消息的确认对话框 }}
/// 重新生成中间消息的确认对话框
struct RegenerateConfirmationView: View {
    @EnvironmentObject var chatViewModel: ChatViewModel
    @State private var skipFutureWarnings = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题
            VStack(spacing: 8) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.orange)
                
                Text("重新生成确认")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.primary)
            }
            
            // 提示信息
            VStack(spacing: 12) {
                Text("重新生成此消息将清空后续的聊天内容")
                    .font(.system(size: 16))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                
                Text("这个操作无法撤销，是否继续？")
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 复选框
            HStack(spacing: 12) {
                Button(action: {
                    skipFutureWarnings.toggle()
                    chatViewModel.skipFutureWarnings = skipFutureWarnings
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: skipFutureWarnings ? "checkmark.square.fill" : "square")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(skipFutureWarnings ? .accentColor : .secondary)
                        
                        Text("下次不再提示")
                            .font(.system(size: 14))
                            .foregroundColor(.secondary)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                
                Spacer()
            }
            
            // 按钮组
            HStack(spacing: 16) {
                // 取消按钮
                Button(action: {
                    chatViewModel.cancelRegenerate()
                }) {
                    Text("取消")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())
                
                // 确定按钮
                Button(action: {
                    chatViewModel.confirmRegenerate()
                }) {
                    Text("确定")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.orange)
                        .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(
                    color: .black.opacity(0.15),
                    radius: 20,
                    x: 0, y: 10
                )
        )
        .padding(.horizontal, 40)
        .onAppear {
            // 同步复选框状态
            skipFutureWarnings = chatViewModel.skipFutureWarnings
        }
    }
}

// {{ AURA-X: Add - 预览 }}
#Preview {
    ZStack {
        Color.black.opacity(0.3)
            .ignoresSafeArea()
        
        RegenerateConfirmationView()
            .environmentObject(ChatViewModel(chatListViewModel: nil))
    }
}
