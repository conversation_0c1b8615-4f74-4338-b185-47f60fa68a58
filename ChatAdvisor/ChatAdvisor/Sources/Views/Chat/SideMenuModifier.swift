//
//  SideMenuModifier.swift
//  JunShi
//
//  Created by md on 2024/5/23.
//

import Foundation
import SwiftUI

struct SideMenuModifier: ViewModifier {
    @ObservedObject var contentViewModel: ContentViewModel
    @State var showMenu: Bool = false
    @State var offset: CGFloat = 0
    @State var lastStoredOffset: CGFloat = 0
    @State var sideBarWidth = AppThemes.sideMenuWidth
    @GestureState var gestureOffset: CGFloat = 0

    func body(content: Content) -> some View {
        content
            .frame(width: UIScreen.main.bounds.width)
            .overlay(
                Rectangle()
                    .fill(
                        Color.primary.opacity((offset / sideBarWidth) / 5.0)
                    )
                    .ignoresSafeArea(.container, edges: .all)
                    .onTapGesture {
                        showMenu.toggle()
                    }
            )
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarHidden(true)
            .frame(width: sideBarWidth + UIScreen.main.bounds.width)
            .offset(x: -sideBarWidth / 2)
            .offset(x: offset)
            .gesture(
                DragGesture()
                    .updating($gestureOffset, body: { value, out, _ in
                        out = value.translation.width
                    })
                    .onEnded(onEnd(value:))
            )
            .animation(.linear(duration: 0.15), value: offset == 0)
            .onChange(of: showMenu) { _ in
                if showMenu, offset == 0 {
                    offset = sideBarWidth
                    lastStoredOffset = offset
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }

                if !showMenu, offset == sideBarWidth {
                    offset = 0
                    lastStoredOffset = 0
                }
            }
            .onChange(of: gestureOffset) { _ in
                if gestureOffset != 0 {
                    if gestureOffset + lastStoredOffset < sideBarWidth, (gestureOffset + lastStoredOffset) > 0 {
                        offset = lastStoredOffset + gestureOffset
                    } else {
                        if gestureOffset + lastStoredOffset < 0 {
                            offset = 0
                        }
                    }
                }
            }
    }

    func onEnd(value: DragGesture.Value) {
        withAnimation(.spring(duration: 0.15)) {
            if value.translation.width > 0 {
                if value.translation.width > sideBarWidth / 2 {
                    offset = sideBarWidth
                    lastStoredOffset = sideBarWidth
                    showMenu = true
                } else {
                    if value.translation.width > sideBarWidth, showMenu {
                        offset = 0
                        showMenu = false
                    } else {
                        if value.velocity.width > 800 {
                            offset = sideBarWidth
                            showMenu = true
                        } else if showMenu == false {
                            // showSideMenu == false状態で、指を離した位置が半分以下なら 元に戻す
                            offset = 0
                            showMenu = false
                        }
                    }
                }
            } else {
                if -value.translation.width > sideBarWidth / 2 {
                    offset = 0
                    showMenu = false
                } else {
                    guard showMenu else { return }
                    if -value.velocity.width > 800 {
                        offset = 0
                        showMenu = false
                    } else {
                        offset = sideBarWidth
                        showMenu = true
                    }
                }
            }
        }

        lastStoredOffset = offset
    }
}

extension View {
    func applySideViewStyle(contentViewModel: ContentViewModel) -> some View {
        modifier(SideMenuModifier(contentViewModel: contentViewModel))
    }
}
