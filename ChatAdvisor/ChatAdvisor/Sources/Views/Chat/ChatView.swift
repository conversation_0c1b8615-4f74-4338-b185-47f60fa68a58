import Foundation
import Localize_Swift
import SimpleToast
import SwiftUI

struct ChatView: View {
    @Binding var showSideMenu: Bool
    @ObservedObject var bootManager = BootManager.shared
    @EnvironmentObject var viewModel: ChatViewModel
    @EnvironmentObject var contentViewModel: ContentViewModel

    var body: some View {
        VStack {
            // 新增：网络状态指示器
            NetworkStatusIndicator()

            ZStack(alignment: .center) {
                // 聊天详情加载指示器和错误显示（只在初始加载时显示）
                if contentViewModel.isLoadingChatDetails || viewModel.isLoadingInitialMessages {
                    VStack {
                        Spacer()
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)
                                .tint(.accentColor)
                            Text("正在加载聊天记录...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(20)
                        .background(Color(.systemBackground).opacity(0.95))
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                        Spacer()
                    }
                    .transition(.opacity.combined(with: .scale))
                    .zIndex(1)
                }

                // 错误状态显示
                if let errorMessage = contentViewModel.chatLoadingError {
                    VStack {
                        Spacer()
                        VStack(spacing: 16) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.largeTitle)
                                .foregroundColor(.orange)
                            Text(errorMessage)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                            Button("重试") {
                                contentViewModel.chatLoadingError = nil
                            }
                            .buttonStyle(.borderedProminent)
                            .tint(.accentColor)
                        }
                        .padding(20)
                        .background(Color(.systemBackground).opacity(0.95))
                        .cornerRadius(16)
                        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                        Spacer()
                    }
                    .transition(.opacity.combined(with: .scale))
                    .zIndex(1)
                }
                ScrollViewReader { proxy in
                    ScrollView {
                        scrollContentView
                        .onChange(of: viewModel.currentChat.messages.count) { _ in
                            if viewModel.scrollManager.shouldAutoScroll {
                                withAnimation {
                                    proxy.scrollTo("bottom", anchor: .bottom)
                                }
                            }
                        }
                        .onChange(of: viewModel.currentChat.messages.last?.content) { _ in
                            if viewModel.scrollManager.shouldAutoScroll {
                                withAnimation {
                                    proxy.scrollTo("bottom", anchor: .bottom)
                                }
                            }
                        }
                    }
                    .coordinateSpace(name: "scroll")
                    // {{ AURA-X: Remove - 移除可能导致滚动冲突的动画 }}
                    // .animation(nil, value: viewModel.currentChat.messages.count)
                    .sheet(item: Binding(
                        get: { viewModel.selectedMessage },
                        set: { viewModel.selectMessage($0) }
                    )) { message in
                        TextSelectionView(message: Binding(
                            get: { viewModel.selectedMessage },
                            set: { viewModel.selectMessage($0) }
                        ))
                    }
                }
            }


            HStack {
                InputBottomView()
                    .environmentObject(viewModel)
            }
            .padding(.horizontal, AppThemes.padding / 2)
        }
        .simpleToast(isPresented: Binding(
            get: { viewModel.showToast },
            set: { _ in /* Toast会自动隐藏 */ }
        ), options: AppThemes.toastOptions) {
            toastLabel
        }
        // {{ AURA-X: Modify - 优化背景色，使用微信风格的聊天背景 }}
        .background(
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(.systemGray6).opacity(0.3),
                    Color(.systemBackground)
                ]),
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .coordinateSpace(name: "frameLayer")
        .onAppear {
            handleOnAppear()
        }
        .onChange(of: contentViewModel.selectedChatID) { newChatID in
            // 当选中的会话ID改变时，确保消息内容正确加载
            if let newChatID = newChatID {
                // 如果当前ChatView显示的会话与选中的会话ID匹配，但消息为空，则加载消息
                if viewModel.currentChat.id == newChatID &&
                   viewModel.currentChat.messages.isEmpty &&
                   !viewModel.isLoadingInitialMessages &&
                   !viewModel.isLoadingMessage {
                    Task {
                        await viewModel.fetchCurrentChatMessages()
                    }
                }
                // 注意：ChatView的environmentObject会根据ContentView的逻辑自动更新
                // 所以会话ID不匹配是正常的过渡状态，不需要警告
            }
        }
    }

    // MARK: - 视图组件

    private var scrollContentView: some View {
        // {{ AURA-X: Modify - 使用智能间距的消息列表布局 }}
        LazyVStack(spacing: 0) { // 使用0间距，由组件内部控制
            emptyStateView
            smartSpacingMessageListView

            // {{ AURA-X: Remove - 移除独立的AI状态气泡，状态已集成到消息中 }}

            ScrollPositionReader(
                onPositionChange: { isNearBottom in
                    viewModel.scrollManager.updateUserPosition(isNearBottom: isNearBottom)
                },
                onDetailedPositionChange: { position in
                    viewModel.scrollManager.updateScrollPosition(position)

                    if viewModel.scrollManager.shouldTriggerPreload {
                        viewModel.loadMoreIfNeeds()
                    }
                }
            )

            // {{ AURA-X: Modify - 最小化底部间距，实现极致紧密布局 }}
            Color.clear
                .frame(height: 4) // 最小化底部间距
                .id("bottom")
        }
        .padding(.horizontal, AppThemes.Chat.messageHorizontalPadding)
    }

    private var emptyStateView: some View {
        Group {
            if viewModel.currentChat.messages.filter({ $0.role != .system }).isEmpty && !viewModel.isLoadingInitialMessages {
                // {{ AURA-X: Modify - 优化空状态视图，使用微信风格设计 }}
                VStack(spacing: 24) {
                    Spacer()

                    // 聊天图标
                    ZStack {
                        Circle()
                            .fill(AppThemes.Chat.aiBubbleColor)
                            .frame(width: 80, height: 80)
                            .shadow(
                                color: .black.opacity(0.05),
                                radius: 8,
                                x: 0, y: 2
                            )

                        Image(systemName: "message.circle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(AppThemes.Chat.userBubbleColor)
                    }

                    VStack(spacing: 12) {
                        Text("开始新的对话")
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.primary)

                        Text("在下方输入框中输入消息开始聊天")
                            .font(.system(size: 16))
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .lineLimit(2)
                    }

                    Spacer()
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, 32)
            }
        }
    }

    // {{ AURA-X: Add - 智能间距的消息列表，根据角色变化调整间距 }}
    // {{ AURA-X: Modify - 优化消息间距，实现紧密排列效果 }}
    private var smartSpacingMessageListView: some View {
        let messages = viewModel.currentChat.messages.filter { $0.role != .system }

        return ForEach(Array(messages.enumerated()), id: \.element.id) { index, message in
            let previousMessage = index > 0 ? messages[index - 1] : nil
            let isRoleChanged = previousMessage?.role != message.role
            // {{ AURA-X: Modify - 使用固定间距，避免布局抖动 }}
            // 同角色间距1pt，不同角色间距4pt，最小化间距以减少布局变化
            let topSpacing: CGFloat = index == 0 ? 0 : (isRoleChanged ? 4 : 1)

            // {{ AURA-X: Debug - 调试间距应用情况 }}
            // print("Message \(index): role=\(message.role), isRoleChanged=\(isRoleChanged), topSpacing=\(topSpacing)")

            WeChatStyleMessageBubble(message: message, previousMessage: previousMessage)
                .environmentObject(viewModel)
                .contextMenu {
                    weChatStyleContextMenu(for: message)
                }
                .id(message.id)
                // {{ AURA-X: Modify - 简化过渡动画，避免滚动冲突 }}
                .transition(.opacity)
                .onAppear {
                    if message == messages.first {
                        viewModel.loadMoreIfNeeds()
                    }
                }
        }
    }

    // 保留原有的消息列表视图作为备用
    private var messageListView: some View {
        ForEach(viewModel.currentChat.messages.filter { $0.role != .system }, id: \.id) { message in
            WeChatStyleMessageBubble(message: message, previousMessage: nil)
                .environmentObject(viewModel)
                .contextMenu {
                    weChatStyleContextMenu(for: message)
                }
                .id(message.id)
                .transition(.asymmetric(
                    insertion: .scale(scale: AppThemes.Chat.bubbleScaleEffect)
                        .combined(with: .opacity)
                        .animation(.easeOut(duration: AppThemes.Chat.messageAppearDuration)),
                    removal: .opacity
                ))
                .onAppear {
                    if message == viewModel.currentChat.messages.filter({ $0.role != .system }).first {
                        viewModel.loadMoreIfNeeds()
                    }
                }
        }
    }

    // {{ AURA-X: Modify - 创建微信风格的上下文菜单，优化图标和布局 }}
    @ViewBuilder
    private func weChatStyleContextMenu(for message: ChatMessage) -> some View {
        Button(action: {
            UIPasteboard.general.string = message.content
            // 添加触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            Label("复制".localized(), systemImage: "doc.on.doc.fill")
                .font(.system(size: 14, weight: .medium))
        }

        Button(action: {
            viewModel.readAloud(text: message.content)
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            Label("朗读".localized(), systemImage: "speaker.wave.3.fill")
                .font(.system(size: 14, weight: .medium))
        }

        Button(action: {
            viewModel.selectMessage(message)
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }) {
            Label("选择文本".localized(), systemImage: "text.cursor")
                .font(.system(size: 14, weight: .medium))
        }

        // 如果是AI消息，添加重新生成选项
        if message.role == .assistant && message.isComplete {
            Divider()
            Button(action: {
                // {{ AURA-X: Modify - 实现重新生成功能 }}
                viewModel.regenerateMessage(messageId: message.id)
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }) {
                Label("重新生成", systemImage: "arrow.clockwise")
                    .font(.system(size: 14, weight: .medium))
            }
        }
    }

    private var toastLabel: some View {
        // {{ AURA-X: Modify - 优化Toast样式，使用微信风格设计 }}
        HStack(spacing: 8) {
            Image(systemName: viewModel.isRequestError ? "exclamationmark.triangle.fill" : "checkmark.circle.fill")
                .font(.system(size: 16, weight: .medium))

            Text(viewModel.toastMessage)
                .font(.system(size: 14, weight: .medium))
                .lineLimit(2)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
                .fill(viewModel.isRequestError ? Color.red.opacity(0.9) : AppThemes.Chat.userBubbleColor)
                .shadow(
                    color: .black.opacity(0.15),
                    radius: 8,
                    x: 0, y: 4
                )
        )
        .foregroundColor(.white)
        .padding(.top, 120)
    }

    private var helpLabel: some View {
        Label("chat_help".localized(), systemImage: "questionmark.circle")
            .padding()
            .background(viewModel.isRequestError ? Color.red.opacity(0.8) : Color.mainDark)
            .foregroundColor(.white)
            .cornerRadius(10)
            .padding(.top, 120)
    }

    // MARK: - 私有方法

    private func handleOnAppear() {
        if ChatViewModel.allModels.count == 0 {
            viewModel.getPricing()
        } else {
            viewModel.currentModel = ChatViewModel.allModels.first ?? ChatsModel.default
        }
        Task {
            await viewModel.loadOlderMessages()
        }
    }

    private func scrollToBottomSmoothly(with proxy: ScrollViewProxy) {
        withAnimation(.easeInOut(duration: 0.25)) {
            proxy.scrollTo("bottom", anchor: .bottom)
        }
    }
}

// MARK: - 微信风格UI组件

// {{ AURA-X: Add - 微信风格消息气泡组件 }}
/// 微信风格消息气泡
struct WeChatStyleMessageBubble: View {
    let message: ChatMessage
    let previousMessage: ChatMessage?
    @Environment(\.colorScheme) private var colorScheme
    @EnvironmentObject var chatViewModel: ChatViewModel
    @State private var isPressed = false
    @State private var animatingDots: [Bool] = [false, false, false]

    var body: some View {
        // {{ AURA-X: Modify - 简化间距逻辑，使用固定值提升布局稳定性 }}
        let isRoleChanged = previousMessage?.role != message.role
        let topSpacing: CGFloat = previousMessage == nil ? 8 : (isRoleChanged ? 12 : 4)

        // {{ AURA-X: Modify - 确保HStack spacing为0，实现紧密排列 }}
        HStack(alignment: .top, spacing: 0) {
            if message.role == .user {
                Spacer(minLength: UIScreen.main.bounds.width * (1 - AppThemes.Chat.messageBubbleMaxWidth))
                messageContent
            } else {
                messageContent
                Spacer(minLength: UIScreen.main.bounds.width * (1 - AppThemes.Chat.messageBubbleMaxWidth))
            }
        }
        .padding(.top, topSpacing)
    }

    @ViewBuilder
    private var messageContent: some View {
        // {{ AURA-X: Modify - 重新设计消息内���，集成AI状态和快捷操作 }}
        VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 0) {
            // 消息气泡（包含AI状态）
            messageBubbleWithStatus

            // 消息快捷操作按钮
            if message.role == .assistant {
                MessageQuickActions(message: message)
                    .environmentObject(chatViewModel)
            } else if message.role == .user {
                // {{ AURA-X: Add - 用户消息快捷操作按钮 }}
                UserMessageQuickActions(message: message)
                    .environmentObject(chatViewModel)
            }
        }
    }

    @ViewBuilder
    private var messageBubbleWithStatus: some View {
        // {{ AURA-X: Modify - 集成AI状态到消息气泡中，避免单独的状态气泡 }}
        VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 0) {
            // 主要消息内容
            if shouldShowContent {
                Text(displayContent)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(message.role == .user ? AppThemes.Chat.userTextColor : AppThemes.Chat.aiTextColor)
                    .padding(AppThemes.Chat.bubbleInnerPadding)
                    .background(bubbleBackground)
                    .fixedSize(horizontal: false, vertical: true)
            }

            // AI状态指示器（当没有内容或正在输入时显示）
            if shouldShowAIStatus {
                aiStatusInBubble
            }
        }
    }

    @ViewBuilder
    private var bubbleBackground: some View {
        RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
            .fill(message.role == .user ? AppThemes.Chat.userBubbleColor : AppThemes.Chat.aiBubbleColor)
            .overlay(
                // AI消息添加边框
                message.role == .assistant ?
                RoundedRectangle(cornerRadius: AppThemes.Chat.bubbleCornerRadius)
                    .stroke(AppThemes.Chat.aiBubbleBorderColor, lineWidth: 0.5) : nil
            )
            .shadow(
                color: .black.opacity(AppThemes.Chat.bubbleShadowOpacity),
                radius: AppThemes.Chat.bubbleShadowRadius,
                x: 0, y: 1
            )
    }

    @ViewBuilder
    private var aiStatusInBubble: some View {
        // {{ AURA-X: Modify - 简化AI状态显示，仅使用省略号和简单动画 }}
        HStack(spacing: 3) {
            // 简化的动画点点（省略号效果）
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .fill(AppThemes.Chat.aiTextColor.opacity(0.6))
                    .frame(width: 4, height: 4)
                    .scaleEffect(animatingDots[index] ? 1.2 : 0.8)
                    .animation(
                        Animation.easeInOut(duration: 0.8)
                            .repeatForever()
                            .delay(Double(index) * 0.3),
                        value: animatingDots[index]
                    )
            }
        }
        .padding(AppThemes.Chat.bubbleInnerPadding)
        .background(bubbleBackground)
        .onAppear {
            startDotAnimation()
        }
    }

    // {{ AURA-X: Fix - 完全重构显示逻辑，确保UI状态一致性 }}
    
    /// 最终应该显示的内容
    private var displayContent: String {
        // 用户消息总是显示其内容
        if message.role == .user {
            return message.content
        }
        
        // 对于AI消息，优先显示流式内容
        if chatViewModel.currentStreamingMessageId == message.id {
            let streamingContent = chatViewModel.getStreamingMessageDisplayContent(for: message.id)
            // 如果流式内容不为空，则显示；否则显示消息自身的内容（处理重生成时清空的情况）
            return !streamingContent.isEmpty ? streamingContent : message.content
        }
        
        // 对于已完成或历史AI消息，显示其最终内容
        return message.content
    }

    /// 是否应该显示消息内容的气泡
    private var shouldShowContent: Bool {
        // 如果最终显示内容不为空，则显示��容气泡
        return !displayContent.isEmpty
    }

    /// 是否应该显示AI状态指示器（省略号动画）
    private var shouldShowAIStatus: Bool {
        // 仅在以下情况显示省略号：
        // 1. 是AI助手消息
        // 2. AI正在思考或打字（非空闲）
        // 3. 是当前正在流式处理的消息
        // 4. 并且，当前还没有任何内容可显示
        return message.role == .assistant &&
               chatViewModel.aiTypingState != .idle &&
               chatViewModel.currentStreamingMessageId == message.id &&
               displayContent.isEmpty
    }

    /// 启动点点动画（简化版省略号动画）
    private func startDotAnimation() {
        // {{ AURA-X: Modify - 简化动画逻辑，适用于所有非空闲状态 }}
        guard chatViewModel.aiTypingState != .idle else { return }

        for i in 0..<3 {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.3) {
                withAnimation(.easeInOut(duration: 0.8).repeatForever()) {
                    animatingDots[i] = true
                }
            }
        }
    }
}

// {{ AURA-X: Add - 快捷操作按钮组件 }}
/// AI消息快捷操作按钮
struct MessageQuickActions: View {
    let message: ChatMessage
    @EnvironmentObject var chatViewModel: ChatViewModel
    @State private var showCopyFeedback = false

    var body: some View {
        if message.role == .assistant && message.isComplete && !message.content.isEmpty {
                Spacer()
                .frame(height: 6)
            HStack(spacing: 16) {
                
                // 复制按钮
                Button(action: copyMessage) {
                    HStack(spacing: 4) {
                        Image(systemName: showCopyFeedback ? "checkmark" : "doc.on.doc")
                            .font(.system(size: 12, weight: .medium))
                    }
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())

                // 重新生成按钮
                Button(action: regenerateMessage) {
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.clockwise")
                            .font(.system(size: 12, weight: .medium))
                    }
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())

                Spacer()
            }
            .padding(.top, 1) // 进一步减少顶部间距，实现更紧密排列
            .padding(.leading, message.role == .user ? 0 : 12) // 与消息气泡对齐
        }
    }

    private func copyMessage() {
        UIPasteboard.general.string = message.content

        // 显示复制反馈
        withAnimation(.easeInOut(duration: 0.2)) {
            showCopyFeedback = true
        }

        // 2秒后恢复原状
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeInOut(duration: 0.2)) {
                showCopyFeedback = false
            }
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }

    private func regenerateMessage() {
        chatViewModel.regenerateMessage(messageId: message.id)

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// {{ AURA-X: Add - 用户消息快捷操作按钮组件 }}
/// 用户消息快捷操作按钮
struct UserMessageQuickActions: View {
    let message: ChatMessage
    @EnvironmentObject var chatViewModel: ChatViewModel
    @State private var showCopyFeedback = false

    var body: some View {
        if message.role == .user && !message.content.isEmpty {
            Spacer()
                .frame(height: 6)
            // {{ AURA-X: Modify - 用户消息按钮居右对齐 }}
            HStack {
                Spacer() // 推送按钮到右侧

                HStack(spacing: 16) {
                    // 编辑按钮
                    Button(action: editMessage) {
                        HStack(spacing: 4) {
                            Image(systemName: "pencil")
                                .font(.system(size: 12, weight: .medium))
                        }
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.top, 1)
            .padding(.trailing, 12) // 与用户消息气泡对齐
        }
    }

    private func copyMessage() {
        UIPasteboard.general.string = message.content

        // 显示复制反馈
        withAnimation(.easeInOut(duration: 0.2)) {
            showCopyFeedback = true
        }

        // 2秒后恢复原状
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(.easeInOut(duration: 0.2)) {
                showCopyFeedback = false
            }
        }

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }

    private func editMessage() {
        chatViewModel.startEditingMessage(message)

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// {{ AURA-X: Remove - 移除独立的打字指示器，已集成到消息气泡中 }}


