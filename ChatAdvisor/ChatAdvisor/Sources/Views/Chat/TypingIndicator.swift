//
//  TypingIndicator.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/06/27.
//

import SwiftUI

// AITypingState现在在ChatUIStateManager.swift中定义，这里添加扩展
extension AITypingState {
    // {{ AURA-X: Modify - 简化AI状态显示，统一使用省略号 }}
    var displayText: String {
        switch self {
        case .connecting, .thinking, .typing:
            return "..." // 统一使用简单的省略号
        case .idle:
            return ""
        }
    }

    var shouldShow: Bool {
        return self != .idle
    }
}

/// 改进的正在输入指示器
struct TypingIndicator: View {
    let state: AITypingState
    @State private var animationOffset: CGFloat = 0
    @State private var dotOpacity: [Double] = [0.3, 0.3, 0.3]
    
    var body: some View {
        if state.shouldShow {
            HStack(alignment: .top, spacing: 0) {
                // AI头像区域
                VStack {
                    Circle()
                        .fill(Color.mainDark.opacity(0.1))
                        .frame(width: 32, height: 32)
                        .overlay(
                            Image(systemName: "brain.head.profile")
                                .font(.system(size: 16))
                                .foregroundColor(.mainDark)
                        )
                    Spacer()
                }
                
                // 消息内容区域
                VStack(alignment: .leading, spacing: 4) {
                    // 状态文本
                    Text(state.displayText)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.mainDark.opacity(0.7))
                    
                    // 动画指示器
                    HStack(spacing: 4) {
                        if state == .connecting {
                            // 连接中的旋转指示器
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(.mainDark)
                        } else {
                            // 思考中和输入中的跳动点点
                            ForEach(0..<3, id: \.self) { index in
                                Circle()
                                    .fill(Color.mainDark)
                                    .frame(width: 6, height: 6)
                                    .opacity(dotOpacity[index])
                                    .animation(
                                        Animation.easeInOut(duration: 0.6)
                                            .repeatForever()
                                            .delay(Double(index) * 0.2),
                                        value: dotOpacity[index]
                                    )
                            }
                        }
                    }
                    .padding(.leading, 2)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
                .padding(.leading, 8)
                
                Spacer()
            }
            .padding(.horizontal, AppThemes.padding)
            .padding(.vertical, 4)
            .transition(.asymmetric(
                insertion: .scale(scale: 0.8).combined(with: .opacity),
                removal: .opacity
            ))
            .onAppear {
                startDotAnimation()
            }
        }
    }
    
    private func startDotAnimation() {
        guard state == .thinking || state == .typing else { return }
        
        // 启动跳动动画
        Timer.scheduledTimer(withTimeInterval: 0.6, repeats: true) { _ in
            for i in 0..<3 {
                DispatchQueue.main.asyncAfter(deadline: .now() + Double(i) * 0.2) {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        dotOpacity[i] = dotOpacity[i] == 0.3 ? 1.0 : 0.3
                    }
                }
            }
        }
    }
}

/// 消息状态指示器（用于消息气泡）
struct MessageStatusIndicator: View {
    let status: MessageStatus
    
    var body: some View {
        HStack(spacing: 4) {
            switch status {
            case .sending:
                ProgressView()
                    .scaleEffect(0.6)
                    .tint(.mainDark.opacity(0.6))
                Text("发送中")
                    .font(.caption2)
                    .foregroundColor(.mainDark.opacity(0.6))
                
            case .sent:
                Image(systemName: "checkmark")
                    .font(.caption2)
                    .foregroundColor(.mainDark.opacity(0.6))
                
            case .receiving:
                HStack(spacing: 2) {
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .fill(Color.mainDark.opacity(0.6))
                            .frame(width: 3, height: 3)
                    }
                }
                
            case .completed:
                EmptyView()
                
            case .failed:
                Button(action: {
                    // TODO: 实现重试逻辑
                }) {
                    HStack(spacing: 2) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.caption2)
                        Text("重试")
                            .font(.caption2)
                    }
                    .foregroundColor(.red)
                }
                
            case .cancelled:
                Text("已取消")
                    .font(.caption2)
                    .foregroundColor(.gray)
            }
        }
    }
}
