//
//  MessageRenderer.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import SwiftUI
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "MessageRenderer")

/// 消息视图项
struct MessageViewItem: Identifiable {
    let id: String
    let message: ChatMessage
    let isVisible: Bool
    let estimatedHeight: CGFloat
}

/// 渲染配置
struct RenderConfig {
    let visibleBufferSize: Int = 10 // 可见区域外的缓冲消息数量
    let estimatedMessageHeight: CGFloat = 100 // 估算的消息高度
    let maxCachedViews: Int = 50 // 最大缓存视图数量
    let recycleThreshold: Int = 100 // 回收阈值
}

/// 消息渲染器 - 负责优化消息列表的渲染性能
@MainActor
class MessageRenderer: ObservableObject {
    // MARK: - Published Properties
    @Published var visibleItems: [MessageViewItem] = []
    @Published var totalContentHeight: CGFloat = 0
    @Published var scrollOffset: CGFloat = 0
    
    // MARK: - Private Properties
    private let config = RenderConfig()
    private var allMessages: [ChatMessage] = []
    private var viewCache: [String: AnyView] = [:]
    private var heightCache: [String: CGFloat] = [:]
    private var visibleRange: Range<Int> = 0..<0
    private var containerHeight: CGFloat = 0
    
    // MARK: - Performance Monitoring
    private var renderCount: Int = 0
    private var lastRenderTime: Date = Date()
    private var averageRenderTime: TimeInterval = 0
    
    // MARK: - Public Methods
    
    /// 更新消息列表
    func updateMessages(_ messages: [ChatMessage]) {
        let startTime = Date()
        
        allMessages = messages
        updateVisibleItems()
        calculateTotalHeight()
        
        // 性能监控
        let renderTime = Date().timeIntervalSince(startTime)
        updatePerformanceMetrics(renderTime: renderTime)
        
        logger.debug("消息渲染完成: \(messages.count) 条消息, 耗时: \(renderTime * 1000)ms")
    }
    
    /// 更新容器高度
    func updateContainerHeight(_ height: CGFloat) {
        guard height != containerHeight else { return }
        
        containerHeight = height
        updateVisibleItems()
    }
    
    /// 更新滚动偏移
    func updateScrollOffset(_ offset: CGFloat) {
        guard abs(offset - scrollOffset) > 10 else { return } // 防止频繁更新
        
        scrollOffset = offset
        updateVisibleItems()
    }
    
    /// 获取消息的估算高度
    func getEstimatedHeight(for messageId: String) -> CGFloat {
        return heightCache[messageId] ?? config.estimatedMessageHeight
    }
    
    /// 缓存消息高度
    func cacheHeight(_ height: CGFloat, for messageId: String) {
        heightCache[messageId] = height
        
        // 重新计算总高度
        calculateTotalHeight()
    }
    
    /// 预加载消息视图
    func preloadMessages(in range: Range<Int>) async {
        let messagesToPreload = Array(allMessages[range]).prefix(config.visibleBufferSize)
        
        await withTaskGroup(of: Void.self) { group in
            for message in messagesToPreload {
                group.addTask { [weak self] in
                    await self?.preloadMessageView(message)
                }
            }
        }
    }
    
    /// 回收离屏视图
    func recycleOffscreenViews() {
        guard viewCache.count > config.maxCachedViews else { return }
        
        let visibleIds = Set(visibleItems.map { $0.id })
        let cacheKeys = Array(viewCache.keys)
        
        // 移除不在可见范围内的视图
        for key in cacheKeys {
            if !visibleIds.contains(key) {
                viewCache.removeValue(forKey: key)
                
                if viewCache.count <= config.recycleThreshold {
                    break
                }
            }
        }
        
        logger.debug("视图回收完成，缓存数量: \(viewCache.count)")
    }
    
    /// 清理所有缓存
    func clearCache() {
        viewCache.removeAll()
        heightCache.removeAll()
        visibleItems.removeAll()
        totalContentHeight = 0
        
        logger.info("渲染器缓存已清理")
    }
    
    /// 获取性能统计
    func getPerformanceStats() -> (renderCount: Int, averageRenderTime: TimeInterval, cacheSize: Int) {
        return (
            renderCount: renderCount,
            averageRenderTime: averageRenderTime,
            cacheSize: viewCache.count
        )
    }
    
    /// 强制重新渲染
    func forceRerender() {
        clearCache()
        updateMessages(allMessages)
    }
    
    // MARK: - Private Methods
    
    private func updateVisibleItems() {
        guard !allMessages.isEmpty else {
            visibleItems = []
            return
        }
        
        let newVisibleRange = calculateVisibleRange()
        
        // 只有当可见范围发生变化时才更新
        guard newVisibleRange != visibleRange else { return }
        
        visibleRange = newVisibleRange
        
        let newVisibleItems = allMessages[visibleRange].enumerated().map { index, message in
            let globalIndex = visibleRange.lowerBound + index
            let isVisible = isMessageVisible(at: globalIndex)
            
            return MessageViewItem(
                id: message.id,
                message: message,
                isVisible: isVisible,
                estimatedHeight: getEstimatedHeight(for: message.id)
            )
        }
        
        visibleItems = newVisibleItems
        
        // 异步预加载和回收
        Task {
            await preloadMessages(in: visibleRange)
            recycleOffscreenViews()
        }
    }
    
    private func calculateVisibleRange() -> Range<Int> {
        guard containerHeight > 0 && !allMessages.isEmpty else {
            return 0..<min(config.visibleBufferSize, allMessages.count)
        }
        
        // 计算可见区域的消息索引范围
        let visibleStartY = scrollOffset
        let visibleEndY = scrollOffset + containerHeight
        
        var currentY: CGFloat = 0
        var startIndex = 0
        var endIndex = allMessages.count
        
        // 找到开始索引
        for (index, message) in allMessages.enumerated() {
            let messageHeight = getEstimatedHeight(for: message.id)
            
            if currentY + messageHeight >= visibleStartY {
                startIndex = max(0, index - config.visibleBufferSize)
                break
            }
            
            currentY += messageHeight
        }
        
        // 找到结束索引
        currentY = 0
        for (index, message) in allMessages.enumerated() {
            let messageHeight = getEstimatedHeight(for: message.id)
            currentY += messageHeight
            
            if currentY >= visibleEndY {
                endIndex = min(allMessages.count, index + config.visibleBufferSize + 1)
                break
            }
        }
        
        return startIndex..<endIndex
    }
    
    private func isMessageVisible(at index: Int) -> Bool {
        guard index >= 0 && index < allMessages.count else { return false }
        
        let bufferStart = max(0, visibleRange.lowerBound - config.visibleBufferSize)
        let bufferEnd = min(allMessages.count, visibleRange.upperBound + config.visibleBufferSize)
        
        return index >= bufferStart && index < bufferEnd
    }
    
    private func calculateTotalHeight() {
        totalContentHeight = allMessages.reduce(0) { total, message in
            total + getEstimatedHeight(for: message.id)
        }
    }
    
    private func preloadMessageView(_ message: ChatMessage) async {
        // 如果视图已经缓存，跳过
        guard viewCache[message.id] == nil else { return }
        
        // 这里可以预创建消息视图并缓存
        // 由于SwiftUI的限制，实际的视图创建需要在主线程进行
        await MainActor.run {
            // 预计算消息高度
            let estimatedHeight = calculateMessageHeight(message)
            heightCache[message.id] = estimatedHeight
        }
    }
    
    private func calculateMessageHeight(_ message: ChatMessage) -> CGFloat {
        // 基于消息内容估算高度
        let baseHeight: CGFloat = 60 // 基础高度
        let characterHeight: CGFloat = 0.5 // 每个字符的估算高度
        let contentHeight = CGFloat(message.content.count) * characterHeight
        
        return baseHeight + contentHeight
    }
    
    private func updatePerformanceMetrics(renderTime: TimeInterval) {
        renderCount += 1
        
        // 计算平均渲染时间
        if renderCount == 1 {
            averageRenderTime = renderTime
        } else {
            averageRenderTime = (averageRenderTime * Double(renderCount - 1) + renderTime) / Double(renderCount)
        }
        
        // 性能警告
        if renderTime > 0.1 { // 100ms
            logger.warning("渲染性能警告: 耗时 \(renderTime * 1000)ms")
        }
    }
}

/// 虚拟化消息列表视图
struct VirtualizedMessageList: View {
    @StateObject private var renderer = MessageRenderer()
    @Binding var messages: [ChatMessage]
    let onMessageTap: ((ChatMessage) -> Void)?
    
    init(messages: Binding<[ChatMessage]>, onMessageTap: ((ChatMessage) -> Void)? = nil) {
        self._messages = messages
        self.onMessageTap = onMessageTap
    }
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVStack(spacing: 0) {
                    ForEach(renderer.visibleItems) { item in
                        if item.isVisible {
                            MessageBubble(message: item.message)
                                .onTapGesture {
                                    onMessageTap?(item.message)
                                }
                                .background(
                                    GeometryReader { messageGeometry in
                                        Color.clear
                                            .onAppear {
                                                renderer.cacheHeight(messageGeometry.size.height, for: item.id)
                                            }
                                    }
                                )
                        } else {
                            // 占位视图
                            Rectangle()
                                .fill(Color.clear)
                                .frame(height: item.estimatedHeight)
                        }
                    }
                }
            }
            .onAppear {
                renderer.updateContainerHeight(geometry.size.height)
                renderer.updateMessages(messages)
            }
            .onChange(of: messages) { newMessages in
                renderer.updateMessages(newMessages)
            }
            .onChange(of: geometry.size.height) { newHeight in
                renderer.updateContainerHeight(newHeight)
            }
        }
    }
}
