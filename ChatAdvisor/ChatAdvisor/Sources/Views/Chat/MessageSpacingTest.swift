//
//  MessageSpacingTest.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import SwiftUI

// {{ AURA-X: Add - 消息间距测试预览组件 }}
/// 消息间距测试预览
struct MessageSpacingTest: View {
    @State private var testMessages: [ChatMessage] = [
        ChatMessage(
            id: "1",
            chatId: "test",
            role: .user,
            content: "第一条用户消息",
            isComplete: true
        ),
        ChatMessage(
            id: "2",
            chatId: "test",
            role: .user,
            content: "第二条用户消息（同角色）",
            isComplete: true
        ),
        ChatMessage(
            id: "3",
            chatId: "test",
            role: .assistant,
            content: "AI回复消息",
            isComplete: true
        ),
        ChatMessage(
            id: "4",
            chatId: "test",
            role: .assistant,
            content: "第二条AI消息（同角色）",
            isComplete: true
        ),
        ChatMessage(
            id: "5",
            chatId: "test",
            role: .user,
            content: "用户再次发言",
            isComplete: true
        )
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 间距说明
                VStack(alignment: .leading, spacing: 6) {
                    Text("消息间距优化效果对比")
                        .font(.headline)
                        .foregroundColor(.primary)

                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            Text("修复前：")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.red)
                            Text("• 同角色：8pt（过大）")
                                .font(.caption)
                            Text("• 不同角色：12pt（过大）")
                                .font(.caption)
                            Text("• VStack内部：4pt（额外间距）")
                                .font(.caption)
                            Text("• HStack：8pt（额外间距）")
                                .font(.caption)
                        }

                        Spacer()

                        VStack(alignment: .leading, spacing: 2) {
                            Text("修复后：")
                                .font(.subheadline)
                                .fontWeight(.semibold)
                                .foregroundColor(.green)
                            Text("• 同角色：\(AppThemes.Chat.messageVerticalSpacing)pt（紧凑）")
                                .font(.caption)
                            Text("• 不同角色：\(AppThemes.Chat.messageGroupSpacing)pt（适中）")
                                .font(.caption)
                            Text("• VStack内部：0pt（无额外间距）")
                                .font(.caption)
                            Text("• HStack：0pt（无额外间距）")
                                .font(.caption)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                
                // 消息列表
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(Array(testMessages.enumerated()), id: \.element.id) { index, message in
                            let previousMessage = index > 0 ? testMessages[index - 1] : nil
                            let isRoleChanged = previousMessage?.role != message.role
                            let topSpacing = index == 0 ? 0 : (isRoleChanged ? AppThemes.Chat.messageGroupSpacing : AppThemes.Chat.messageVerticalSpacing)
                            
                            VStack(spacing: 0) {
                                // 间距指示器
                                if index > 0 {
                                    HStack {
                                        Text("↑ \(topSpacing)pt")
                                            .font(.system(size: 10))
                                            .foregroundColor(isRoleChanged ? .red : .blue)
                                        Spacer()
                                        Text(isRoleChanged ? "角色变化" : "同角色")
                                            .font(.system(size: 10))
                                            .foregroundColor(isRoleChanged ? .red : .blue)
                                    }
                                    .padding(.horizontal, 20)
                                }
                                
                                WeChatStyleMessageBubble(message: message)
                                    .environmentObject(MockChatViewModel())
                                    .contextMenu {
                                        Button("复制") {
                                            UIPasteboard.general.string = message.content
                                        }
                                    }
                            }
                            .padding(.top, topSpacing)
                        }
                        
                        Color.clear.frame(height: 20)
                    }
                    .padding(.horizontal, AppThemes.Chat.messageHorizontalPadding)
                }
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(.systemGray6).opacity(0.3),
                            Color(.systemBackground)
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
            }
            .navigationTitle("消息间距测试")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("重置") {
                        // 可以添加重置逻辑
                    }
                }
            }
        }
    }
}

// 简化的模拟ChatViewModel
class MockChatViewModel: ChatViewModel {
    override init() {
        let mockStepFormViewModel = MultiStepFormViewModel()
        let mockChat = Chat(id: "test", messages: [], title: "测试聊天")
        super.init(stepFormViewModel: mockStepFormViewModel, currentChat: mockChat)
    }
    
    override func getStreamingMessageDisplayContent(for messageId: String) -> String {
        return ""
    }
}

#Preview {
    MessageSpacingTest()
}
