//
//  ScrollStateManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/06/27.
//

import SwiftUI
import Combine

/// 高级滚动状态管理器
class ScrollStateManager: ObservableObject {
    static let shared = ScrollStateManager()
    
    @Published var currentChatScrollState: ChatScrollState?
    @Published var isAutoScrollEnabled: Bool = true
    @Published var showNewMessageIndicator: Bool = false
    @Published var newMessageCount: Int = 0
    
    private var scrollStates: [String: ChatScrollState] = [:]
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupCleanupTimer()
    }
    
    /// 聊天滚动状态
    struct ChatScrollState {
        let chatId: String
        var lastKnownPosition: CGFloat
        var lastVisibleMessageId: String?
        var isUserScrolling: Bool
        var autoScrollPreference: AutoScrollPreference
        var unreadMessageCount: Int
        var lastUpdateTime: Date
        
        enum AutoScrollPreference {
            case always
            case onlyWhenAtBottom
            case never
        }
        
        init(chatId: String) {
            self.chatId = chatId
            self.lastKnownPosition = 0
            self.lastVisibleMessageId = nil
            self.isUserScrolling = false
            self.autoScrollPreference = .onlyWhenAtBottom
            self.unreadMessageCount = 0
            self.lastUpdateTime = Date()
        }
    }
    
    /// 设置当前聊天
    func setCurrentChat(_ chatId: String) {
        // 保存当前聊天的状态
        if let currentState = currentChatScrollState {
            scrollStates[currentState.chatId] = currentState
        }
        
        // 加载或创建新聊天的状态
        currentChatScrollState = scrollStates[chatId] ?? ChatScrollState(chatId: chatId)
        
        // 重置新消息指示器
        showNewMessageIndicator = false
        newMessageCount = 0
    }
    
    /// 更新滚动位置
    func updateScrollPosition(_ position: CGFloat, visibleMessageId: String?) {
        guard var state = currentChatScrollState else { return }
        
        state.lastKnownPosition = position
        state.lastVisibleMessageId = visibleMessageId
        state.lastUpdateTime = Date()
        
        currentChatScrollState = state
    }
    
    /// 标记用户开始滚动
    func userDidStartScrolling() {
        guard var state = currentChatScrollState else { return }
        state.isUserScrolling = true
        currentChatScrollState = state
    }
    
    /// 标记用户停止滚动
    func userDidStopScrolling() {
        guard var state = currentChatScrollState else { return }
        state.isUserScrolling = false
        currentChatScrollState = state
    }
    
    /// 添加新消息
    func addNewMessage(messageId: String, shouldAutoScroll: Bool = true) {
        guard var state = currentChatScrollState else { return }
        
        if shouldAutoScroll && canAutoScroll() {
            // 自动滚动到底部
            triggerAutoScroll()
        } else {
            // 显示新消息指示器
            state.unreadMessageCount += 1
            newMessageCount = state.unreadMessageCount
            showNewMessageIndicator = true
        }
        
        state.lastUpdateTime = Date()
        currentChatScrollState = state
    }
    
    /// 判断是否可以自动滚动
    private func canAutoScroll() -> Bool {
        guard let state = currentChatScrollState else { return false }
        
        switch state.autoScrollPreference {
        case .always:
            return isAutoScrollEnabled
        case .onlyWhenAtBottom:
            return isAutoScrollEnabled && !state.isUserScrolling && isNearBottom()
        case .never:
            return false
        }
    }
    
    /// 判断是否接近底部
    private func isNearBottom() -> Bool {
        guard let state = currentChatScrollState else { return true }
        return state.lastKnownPosition >= -100 // 100像素的阈值
    }
    
    /// 触发自动滚动
    private func triggerAutoScroll() {
        // 这个方法会被ChatView监听并执行实际的滚动
        objectWillChange.send()
    }
    
    /// 用户手动滚动到底部
    func userScrolledToBottom() {
        guard var state = currentChatScrollState else { return }
        
        // 清除未读消息计数
        state.unreadMessageCount = 0
        newMessageCount = 0
        showNewMessageIndicator = false
        
        currentChatScrollState = state
    }
    
    /// 设置自动滚动偏好
    func setAutoScrollPreference(_ preference: ChatScrollState.AutoScrollPreference) {
        guard var state = currentChatScrollState else { return }
        state.autoScrollPreference = preference
        currentChatScrollState = state
    }
    
    /// 获取恢复滚动位置的信息
    func getRestoreScrollInfo() -> (position: CGFloat, messageId: String?)? {
        guard let state = currentChatScrollState else { return nil }
        return (state.lastKnownPosition, state.lastVisibleMessageId)
    }
    
    /// 清理过期的滚动状态
    private func setupCleanupTimer() {
        Timer.publish(every: 300, on: .main, in: .common) // 每5分钟清理一次
            .autoconnect()
            .sink { [weak self] _ in
                self?.cleanupExpiredStates()
            }
            .store(in: &cancellables)
    }
    
    private func cleanupExpiredStates() {
        let cutoffTime = Date().addingTimeInterval(-3600) // 1小时前
        scrollStates = scrollStates.filter { _, state in
            state.lastUpdateTime > cutoffTime
        }
    }
    
    /// 导出滚动状态用于持久化
    func exportScrollStates() -> Data? {
        do {
            return try JSONEncoder().encode(scrollStates)
        } catch {
            return nil
        }
    }
    
    /// 导入滚动状态
    func importScrollStates(from data: Data) {
        do {
            scrollStates = try JSONDecoder().decode([String: ChatScrollState].self, from: data)
        } catch {
            // 静默处理导入失败，不影响用户体验
        }
    }
}

// MARK: - Codable Support

extension ScrollStateManager.ChatScrollState: Codable {
    enum CodingKeys: String, CodingKey {
        case chatId, lastKnownPosition, lastVisibleMessageId
        case isUserScrolling, autoScrollPreference, unreadMessageCount, lastUpdateTime
    }
}

extension ScrollStateManager.ChatScrollState.AutoScrollPreference: Codable {}

/// 滚动状态持久化管理器
class ScrollStatePersistenceManager {
    static let shared = ScrollStatePersistenceManager()
    
    private let userDefaults = UserDefaults.standard
    private let scrollStateKey = "chat_scroll_states"
    
    private init() {}
    
    /// 保存滚动状态
    func saveScrollStates() {
        guard let data = ScrollStateManager.shared.exportScrollStates() else { return }
        userDefaults.set(data, forKey: scrollStateKey)
    }
    
    /// 加载滚动状态
    func loadScrollStates() {
        guard let data = userDefaults.data(forKey: scrollStateKey) else { return }
        ScrollStateManager.shared.importScrollStates(from: data)
    }
    
    /// 清除保存的滚动状态
    func clearScrollStates() {
        userDefaults.removeObject(forKey: scrollStateKey)
    }
}

/// 新消息指示器视图
struct NewMessageIndicator: View {
    let count: Int
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 6) {
                Image(systemName: "arrow.down")
                    .font(.system(size: 12, weight: .medium))
                
                Text("\(count) 条新消息")
                    .font(.system(size: 12, weight: .medium))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                Capsule()
                    .fill(Color.blue)
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
        }
        .transition(.scale.combined(with: .opacity))
    }
}

/// 滚动位置恢复视图修饰符
struct ScrollPositionRestorer: ViewModifier {
    let chatId: String
    let scrollProxy: ScrollViewProxy
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                restoreScrollPosition()
            }
            .onDisappear {
                saveScrollPosition()
            }
    }
    
    private func restoreScrollPosition() {
        if let (position, messageId) = ScrollStateManager.shared.getRestoreScrollInfo() {
            if let messageId = messageId {
                scrollProxy.scrollTo(messageId, anchor: .center)
            }
        }
    }
    
    private func saveScrollPosition() {
        // 保存当前滚动位置
        ScrollStatePersistenceManager.shared.saveScrollStates()
    }
}

extension View {
    func restoreScrollPosition(chatId: String, scrollProxy: ScrollViewProxy) -> some View {
        modifier(ScrollPositionRestorer(chatId: chatId, scrollProxy: scrollProxy))
    }
}
