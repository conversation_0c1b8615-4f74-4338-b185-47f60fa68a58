//
//  ChatViewPreview.swift
//  JunShi
//
//  Created by md on 2024/5/7.
//

import Foundation
import SwiftUI

struct ChatViewPreview: View {
    @State var chat: Chat

    var body: some View {
        Spacer()
        VStack {
            Text(chat.title != "" ? chat.title : "没有标题的会话".localized())
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.top, AppThemes.padding)
            ScrollViewReader { _ in
                VStack {
                    ForEach(chat.messages, id: \.id) { message in
                        MessageBubble(message: message)
                    }
                }
            }
        }
        .onAppear {
            Task {
                let messages = await AdvisorDatabaseManager.shared.fetchMessages(chatId: chat.id)
                DispatchQueue.main.async {
                    chat.messages = messages
                }
            }
        }

        Spacer()
            .padding()
    }
}

struct ArchivePreviewChatView: View {
    let chat: Chat

    var body: some View {
        VStack {
            HStack {
                Text(chat.title)
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
            }
            .padding(.leading, AppThemes.padding)
            ScrollView {
                ForEach(chat.messages, id: \.id) { message in
                    HStack {
                        MessageBubblePreview(message: message)
                    }
                }
            }
        }
    }
}
