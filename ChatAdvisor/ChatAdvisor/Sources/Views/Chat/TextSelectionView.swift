//
//  TextSelectionView.swift
//  ChatAdvisor
//
//  Created by zweiteng on 2025/3/14.
//

import SwiftUI

struct TextSelectionView: View {
    @Binding var message: ChatMessage?

    var body: some View {
        if let message = message {
            TextEditor(text: .constant(message.content))
                .background(Color(.systemGray6))
                .cornerRadius(8)
                .padding()
        }
    }
}
