//
//  InputField.swift
//  ChatAdvisor
//
//  Created by md on 2025/2/17.
//

import SwiftUI

struct InputField: View {
    let placeholder: String
    @Binding var text: String
    var isSecure: Bool = false
    var isValid: Bool = true
    var keyboardType: UIKeyboardType = .default
    var textContentType: UITextContentType? = nil

    var body: some View {
        Group {
            if isSecure {
                SecureField("", text: $text)
            } else {
                TextField("", text: $text)
                    .keyboardType(keyboardType)
                    .textContentType(textContentType)
            }
        }
        .placeholder(placeholder, when: text.isEmpty, color: Color.gray.opacity(0.8))
        .padding(.horizontal, 16)
        .frame(maxWidth: 375)
        .frame(height: 44)
        .background(Color.clear)
        .overlay(
            RoundedRectangle(cornerRadius: AppThemes.cornerRadius)
                .stroke(isValid ? Color.mainBackground.opacity(0.2) : Color.red, lineWidth: 1)
                .animation(.easeInOut(duration: 0.2), value: isValid)
        )
        .foregroundColor(.mainDark)
        .autocapitalization(.none)
        .disableAutocorrection(true)
        .transition(.opacity)
        .animation(.easeInOut(duration: 0.2), value: text.isEmpty)
    }
}

extension View {
    func placeholder<Content: View>(
        _ placeholder: String,
        when shouldShow: Bool,
        color: Color = .gray,
        @ViewBuilder _: () -> Content
    ) -> some View {
        ZStack(alignment: .leading) {
            if shouldShow {
                Text(placeholder)
                    .foregroundColor(color)
//                    .padding(.horizontal, 16)
            }
            self
        }
    }

    func placeholder(
        _ placeholder: String,
        when shouldShow: Bool,
        color: Color = .gray
    ) -> some View {
        self.placeholder(placeholder, when: shouldShow, color: color) {
            Text(placeholder)
        }
    }
}
