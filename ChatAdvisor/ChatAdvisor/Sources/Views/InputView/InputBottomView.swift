import SwiftUI

struct InputBottomView: View {
    @EnvironmentObject var chatViewModel: ChatViewModel
    @StateObject private var inputViewModel: InputViewModel = .init()
    @FocusState private var isInputFocused: Bool

    var body: some View {
        VStack {
            // {{ AURA-X: Add - 编辑状态指示器 }}
            if chatViewModel.isEditingMessage {
                HStack {
                    Image(systemName: "pencil.circle.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    Text("正在编辑消息")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Button("取消") {
                        chatViewModel.cancelEditingMessage()
                    }
                    .font(.caption)
                    .foregroundColor(.orange)
                }
                .padding(.horizontal)
                .padding(.vertical, 4)
                .background(Color(.systemGray6))
            }

            Divider()
            switch inputViewModel.inputViewMode {
            case .text:
                HStack {
                    But<PERSON>(action: {
                        withAnimation {
                            inputViewModel.inputViewMode = .recording
                        }
                    }) {
                        Image(systemName: "mic")
                            .padding(.horizontal)
                            .foregroundColor(.mainDark)
                    }
                    TextField("", text: chatViewModel.isEditingMessage ? $chatViewModel.textInput : $inputViewModel.textInput)
                        .placeholder(chatViewModel.isEditingMessage ? "编辑消息...".localized() : "请输入".localized(),
                                   when: (chatViewModel.isEditingMessage ? chatViewModel.textInput : inputViewModel.textInput).isEmpty == true,
                                   color: .mainLight)
                        .foregroundColor(.mainDark)
                        .padding(.leading, AppThemes.padding)
                        .frame(height: 40)
                        .background(
                            RoundedRectangle(cornerRadius: 5)
                                .stroke(chatViewModel.isEditingMessage ? Color.orange : Color.mainDark, lineWidth: 1)
                        )
                        // {{ AURA-X: Add - 添加聚焦状态管理 }}
                        .focused($isInputFocused)
                        .onChange(of: chatViewModel.shouldFocusInput) { shouldFocus in
                            if shouldFocus {
                                isInputFocused = true
                                chatViewModel.shouldFocusInput = false
                            }
                        }
                        .onChange(of: isInputFocused) { focused in
                            // {{ AURA-X: Add - 当失去焦点且正在编辑时，取消编辑状态 }}
                            if !focused && chatViewModel.isEditingMessage {
                                chatViewModel.cancelEditingMessage()
                            }
                        }
                    Button(action: {
                        let viewModel = chatViewModel
                        // {{ AURA-X: Modify - 根据编辑状态处理不同逻辑 }}
                        if !viewModel.isEditingMessage {
                            viewModel.textInput = inputViewModel.textInput
                        }
                        viewModel.sendMessage()
                        if AccountManager.shared.currentUser != nil && !viewModel.isEditingMessage {
                            inputViewModel.textInput = ""
                        }
                    }) {
                        // {{ AURA-X: Modify - 保持原图标，编辑状态使用橙色 }}
                        Image(systemName: "paperplane.fill")
                            .foregroundColor(chatViewModel.isEditingMessage ? .orange : .mainDark)
                            .padding(.horizontal)
                    }
                    .disabled(chatViewModel.isAnswering)
                }
                .padding(.vertical, AppThemes.padding / 2)
            case .recording:
                ZStack {
                    if inputViewModel.isPlaying == false, inputViewModel.isRecording == false {
                        HStack {
                            Button(action: {
                                withAnimation {
                                    inputViewModel.inputViewMode = .text
                                    inputViewModel.resetRecording()
                                }
                            }) {
                                Image(systemName: "keyboard")
                                    .foregroundColor(.mainDark)
                            }
                            Spacer()
                        }
                    }
                    HStack {
                        Spacer()
                        if inputViewModel.isPreview {
                            PlaybackButton()
                                .environmentObject(inputViewModel)
                        } else {
                            RecordingButton()
                                .environmentObject(inputViewModel)
                        }
                        Spacer()
                    }
                    if inputViewModel.isPreview, inputViewModel.isPlaying == false, inputViewModel.isRecording == false {
                        HStack {
                            Spacer()
                            Button(action: {
                                inputViewModel.resetRecording()
                            }) {
                                Image(systemName: "trash.fill")
                                    .foregroundColor(.mainDark)
                            }
                            Button(action: {
                                let viewModel = chatViewModel
                                viewModel.sendRecording(assetURL: inputViewModel.audioFileURL)
                                inputViewModel.resetRecording()
                            }) {
                                Image(systemName: "paperplane.fill")
                                    .foregroundColor(.mainDark)
                                    .padding(.trailing)
                            }
                            .disabled(chatViewModel.isAnswering)
                            .padding(.leading)
                        }
                    }
                }
                .onChange(of: chatViewModel.isAnswering) { value in
                    if value {
                        inputViewModel.resetRecording()
                    }
                }
                .padding(.vertical, AppThemes.padding / 2)
//                .frame(height: 64)
            }
        }
    }
}

struct RecordingButton: View {
    @EnvironmentObject var inputChatModel: InputViewModel
    @EnvironmentObject var chatViewModel: ChatViewModel

    var body: some View {
        ZStack {
            if inputChatModel.isRecording {
                Circle()
                    .stroke(lineWidth: 3)
                    .foregroundColor(Color(uiColor: .systemGray5))
                    .frame(width: 44, height: 44)
                Circle()
                    .trim(from: 0, to: inputChatModel.recordingDuration / inputChatModel.maxRecordingDuration)
                    .stroke(lineWidth: 3)
                    .foregroundColor(.mainDark)
                    .frame(width: 44, height: 44)
                    .rotationEffect(.degrees(-90))
                    .animation(.linear, value: inputChatModel.recordingDuration)
            }
            Image(systemName: inputChatModel.isRecording ? "stop.fill" : "record.circle")
                .font(.system(size: 24))
                .foregroundColor(.red)
        }
        .onTapGesture {
            if inputChatModel.isRecording {
                inputChatModel.stopRecording()
            } else {
                inputChatModel.startRecording { success in
                    if !success {
                        // 读取InfoPlist的NSMicrophoneUsageDescription
                        let message = Bundle.main.object(forInfoDictionaryKey: "NSMicrophoneUsageDescription") as? String ?? "请允许访问麦克风".localized()
                        let viewModel = chatViewModel
                        viewModel.failedToast(message)
                    } else {
                        inputChatModel.isRecording.toggle()
                    }
                }
            }
        }
    }
}

struct PlaybackButton: View {
    @EnvironmentObject var inputChatModel: InputViewModel

    var body: some View {
        ZStack {
            if inputChatModel.isPlaying {
                Circle()
                    .stroke(lineWidth: 3)
                    .foregroundColor(Color(uiColor: .systemGray5))
                    .frame(width: 44, height: 44)
                Circle()
                    .trim(from: 0, to: inputChatModel.playbackDuration / inputChatModel.maxRecordingDuration)
                    .stroke(lineWidth: 3)
                    .foregroundColor(.mainDark)
                    .frame(width: 44, height: 44)
                    .rotationEffect(.degrees(-90))
                    .animation(.linear, value: inputChatModel.playbackDuration)
            }
            Image(systemName: inputChatModel.isPlaying ? "stop.fill" : "play.fill")
                .font(.system(size: 24))
                .foregroundColor(.red)
        }
        .onTapGesture {
            if inputChatModel.isPlaying {
                inputChatModel.stopPlayback()
            } else {
                inputChatModel.startPlayback()
            }
        }
    }
}
