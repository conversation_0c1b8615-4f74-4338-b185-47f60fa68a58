import AVFoundation
import Foundation
import SwiftUI

class InputViewModel: NSObject, ObservableObject {
    enum InputViewMode: String {
        case text
        case recording
    }

    @Published var inputViewMode: InputViewMode = Preferences.inputViewMode.value {
        didSet {
            Preferences.inputViewMode.value = inputViewMode
            FirebaseManager.shared.logInputType(type: inputViewMode.rawValue)
        }
    }

    @Published var isRecording = false
    @Published var isPreview = false
    @Published var isPlaying = false
    @Published var textInput = ""
    @Published var recordingDuration: CGFloat = 0
    @Published var playbackDuration: CGFloat = 0
    let maxRecordingDuration: CGFloat = 60
    let minRecordingDuration: CGFloat = 3
    private var recordingTimer: Timer?
    private var playbackTimer: Timer?
    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?
    private let recordingSession = AVAudioSession.sharedInstance()

    lazy var audioFileURL: URL = generateAudioFileURL()

    override init() {
        super.init()
    }

    func generateAudioFileURL() -> URL {
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = UUID().uuidString
        // mkdir audio
        let audioDir = documentsDirectory.appendingPathComponent("audio")
        do {
            try FileManager.default.createDirectory(at: audioDir, withIntermediateDirectories: true, attributes: nil)
        } catch {
            print("Failed to create audio directory: \(error.localizedDescription)")
        }
        return audioDir.appendingPathComponent("\(fileName).m4a")
    }

    func requestMicrophoneAccess(completion: ((Bool) -> Void)? = nil) {
        if #available(iOS 17.0, *) {
            AVAudioApplication.requestRecordPermission { [weak self] granted in
                guard let self else { return }
                if granted {
                    print("Microphone access granted")
                    do {
                        try recordingSession.setCategory(.playAndRecord, mode: .default, options: [])
                        try recordingSession.setActive(true)
                    } catch {
                        print("Failed to setup audio session: \(error.localizedDescription)")
                    }
                } else {
                    print("Microphone access denied")
                }
                completion?(granted)
            }
        } else {
            AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
                guard let self else { return }
                if granted {
                    print("Microphone access granted")
                    do {
                        try recordingSession.setCategory(.playAndRecord, mode: .default, options: [])
                        try recordingSession.setActive(true)
                    } catch {
                        print("Failed to setup audio session: \(error.localizedDescription)")
                    }
                } else {
                    print("Microphone access denied")
                }
                completion?(granted)
            }
        }
    }

    func recordAfterPermission() {
        audioFileURL = generateAudioFileURL()
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.playAndRecord, mode: .default)
            try audioSession.setActive(true)
        } catch {
            print("Failed to set up audio session: \(error.localizedDescription)")
            return
        }

        let settings: [String: Any] = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 12000,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue,
        ]

        do {
            if FileManager.default.fileExists(atPath: audioFileURL.path) {
                try FileManager.default.removeItem(at: audioFileURL)
            }

            audioRecorder = try AVAudioRecorder(url: audioFileURL, settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.record()

            recordingDuration = 0
            recordingTimer?.invalidate()
            recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
                if self.recordingDuration < self.maxRecordingDuration {
                    withAnimation {
                        self.recordingDuration += 0.1
                    }
                } else {
                    self.stopRecording()
                    self.isPreview = true
                }
            }
        } catch {
            print("Failed to start recording: \(error.localizedDescription)")
        }
    }

    func startRecording(completion: ((Bool) -> Void)? = nil) {
        requestMicrophoneAccess { [weak self] granted in
            guard let self else { return }
            if granted {
                DispatchQueue.main.async {
                    self.recordAfterPermission()
                }
                completion?(true)
            } else {
                completion?(false)
            }
        }
    }

    func stopRecording() {
        isRecording = false
        isPreview = true
        audioRecorder?.stop()
        audioRecorder = nil

        recordingTimer?.invalidate()
        recordingTimer = nil

        print("Recording stopped. File saved at \(audioFileURL.path)")
        print("File exists after recording: \(FileManager.default.fileExists(atPath: audioFileURL.path))")
    }

    func startPlayback() {
        do {
            isPlaying = true
            print("Attempting to play file at \(audioFileURL.path)")
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [])

            audioPlayer = try AVAudioPlayer(contentsOf: audioFileURL)
            audioPlayer?.delegate = self
            audioPlayer?.play()

            playbackDuration = 0
            playbackTimer?.invalidate()
            playbackTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { _ in
                if let player = self.audioPlayer {
                    if self.playbackDuration < CGFloat(player.duration) {
                        withAnimation {
                            self.playbackDuration += 0.1
                        }
                    } else {
                        self.stopPlayback()
                        self.isPlaying = false
                    }
                }
            }
        } catch {
            print("Failed to start playback: \(error.localizedDescription)")
        }
    }

    func stopPlayback() {
        isPlaying = false
        audioPlayer?.stop()
        audioPlayer = nil

        playbackTimer?.invalidate()
        playbackTimer = nil
    }

    func resetRecording() {
        isRecording = false
        isPlaying = false
        isPreview = false
        recordingDuration = 0
        playbackDuration = 0

        do {
            if FileManager.default.fileExists(atPath: audioFileURL.path) {
                try FileManager.default.removeItem(at: audioFileURL)
            }
        } catch {
            print("Failed to delete recording: \(error.localizedDescription)")
        }
    }
}

extension InputViewModel: AVAudioRecorderDelegate, AVAudioPlayerDelegate {
    func audioRecorderDidFinishRecording(_: AVAudioRecorder, successfully flag: Bool) {
        if flag {
            print("Recording finished successfully")
        } else {
            print("Recording failed to finish")
        }
    }

    func audioPlayerDidFinishPlaying(_: AVAudioPlayer, successfully flag: Bool) {
        if flag {
            print("Playback finished successfully")
        } else {
            print("Playback failed to finish")
        }
        stopPlayback()
        isPlaying = false
    }
}
