//
//  SideMenu.swift
//  ChatAdvisor
//
//  Created by md on 2024/11/12.
//

import Foundation
import SwiftUI

public struct SideMenu<MenuContent: View>: ViewModifier {
    @Binding var isShowing: Bool
    @Binding var menuWidth: CGFloat
    private let menuContent: () -> MenuContent
    
    public init(isShowing: Binding<Bool>,
                menuWidth: Binding<CGFloat>,
                @ViewBuilder menuContent: @escaping () -> MenuContent) {
        _isShowing = isShowing
        _menuWidth = menuWidth
        self.menuContent = menuContent
    }
    
    public func body(content: Content) -> some View {
        let drag = DragGesture().onEnded { event in
            if event.location.x < 200 && abs(event.translation.height) < 50 && abs(event.translation.width) > 50 {
                withAnimation {
                    self.isShowing = event.translation.width > 0
                }
            }
        }
        
        return GeometryReader { geometry in
            ZStack(alignment: .leading) {
                Color.clear
                    .contentShape(Rectangle()) // 设置点击区域
                    .onTapGesture {
                        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                        withAnimation {
                            isShowing = false
                        }
                    }
                    .edgesIgnoringSafeArea(.all)
                
                content
                    .disabled(isShowing)
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .offset(x: self.isShowing ? menuWidth : 0)
                

                menuContent()
                    .frame(width: menuWidth)
                    .transition(.move(edge: .leading))
                    .offset(x: self.isShowing ? 0 : -menuWidth)
            }
            .gesture(drag)
        }
    }
}



public extension View {
    func sideMenu<MenuContent: View>(
        isShowing: Binding<Bool>,
        menuWidth: Binding<CGFloat>,
        @ViewBuilder menuContent: @escaping () -> MenuContent
    ) -> some View {
        self.modifier(
            SideMenu(isShowing: isShowing, menuWidth: menuWidth, menuContent: menuContent)
        )
    }
}
