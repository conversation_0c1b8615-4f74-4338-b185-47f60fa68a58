//
//  ContentView.swift
//  JunShi
//
//  Created by zwt on 2024/4/8.
//

import Combine
import Localize_Swift
import SwiftUI

struct ContentView: View {
    @ObservedObject private var authManager = AccountManager.shared
    @ObservedObject private var startupManager = AppStartupManager.shared
    @StateObject private var contentViewModel = ContentViewModel()
    @StateObject private var chatListViewModel = ChatListViewModel()
    @State private var showSideMenu: Bool = false
    @State private var sideBarWidth = AppThemes.sideMenuWidth
    @State private var showMultiStepForm = false
    @State private var showNavigationSplitView: NavigationSplitViewVisibility = .all
    @GestureState private var gestureOffset: CGFloat = 0
    @State private var originViewModel: ChatViewModel? = nil
    @State private var visibility: NavigationSplitViewVisibility = .detailOnly

    var body: some View {
        Group {
            // 根据启动状态显示不同内容
            if !startupManager.isStartupComplete {
                // 显示启动加载页面
                StartupLoadingView()
            } else {
                // 显示主界面
                if UIDevice.current.isPad {
                    NavigationSplitView(columnVisibility: $visibility) {
                        if UIDevice.current.isPad, authManager.currentUser != nil {
                            leftMenu
                        }
                    } detail: {
                        mainContentView
                    }
                    // {{ AURA-X: Add - 为 iPad NavigationSplitView 设置不透明导航栏样式. Approval: mcp-feedback-enhanced(ID:feedback_003). }}
                    .toolbarBackground(.regularMaterial, for: .navigationBar)
                    .toolbarBackground(.visible, for: .navigationBar)
                    .toolbarColorScheme(.light, for: .navigationBar)
                } else {
                    NavigationView {
                        mainContentView
                    }
                    .navigationViewStyle(StackNavigationViewStyle())
                    // {{ AURA-X: Add - 为 NavigationView 设置不透明导航栏样式. Approval: mcp-feedback-enhanced(ID:feedback_003). }}
                    .onAppear {
                        let appearance = UINavigationBarAppearance()
                        appearance.configureWithOpaqueBackground()
                        appearance.backgroundColor = .systemBackground
                        UINavigationBar.appearance().standardAppearance = appearance
                        UINavigationBar.appearance().scrollEdgeAppearance = appearance
                    }
                    .sideMenu(isShowing: $showSideMenu, menuWidth: $sideBarWidth) {
                        leftMenu
                    }
                }
            }
        }
    }

    private var mainContentView: some View {
        Group {
            if let selectChatViewModel = contentViewModel.currentChatViewModel {
                ChatView(showSideMenu: $showSideMenu)
                    .environmentObject(selectChatViewModel)
                    .environmentObject(contentViewModel)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .fullScreenCover(isPresented: $showMultiStepForm) {
                        createMultiStepFormView(selectChatViewModel)
                    }
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            } else {
                // {{ AURA-X: Modify - 根据预加载状态智能显示界面，避免闪烁. Approval: mcp-feedback-enhanced(ID:20250129009). }}
                // 根据ChatSessionManager的预加载状态决定显示内容
                if ChatSessionManager.shared.hasHistoryData && contentViewModel.isLoadingChatDetails {
                    // 有历史数据且正在加载中，显示加载状态
                    ProgressView("正在加载对话...")
                        .transition(.opacity)
                } else {
                    // 没有历史数据或加载完成，显示空状态
                    EmptySessionView()
                        .transition(.opacity)
                }
            }
        }
        .animation(.easeInOut(duration: 0.3), value: contentViewModel.currentChatViewModel != nil)
        .frame(maxHeight: .infinity)
        .navigationBarItems(
            leading: leadingNavBarButtons(),
            trailing: trailingNavBarButtons()
        )
        .toolbar {
            if let chatViewModel = contentViewModel.currentChatViewModel {
                ToolbarItem(placement: .principal) {
                    Button {
                        FirebaseManager.shared.logEvent("点击编辑情境")
                        chatViewModel.stepFormViewModel.isEditing = true
                        // {{ AURA-X: Fix - 从聊天页面跳转到情景时默认为预览模式. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
                        chatViewModel.stepFormViewModel.isPreviewMode = true
                        showMultiStepForm.toggle()
                    } label: {
                        HStack(spacing: 4) {
                            if Preferences.launchCount.value <= 6 || chatViewModel.stepFormViewModel.navigationtitle.isEmpty {
                                Text("\("edit".localized()) \("Context".localized()) - \(truncateTitle(chatViewModel.stepFormViewModel.navigationtitle))")
                                    .foregroundColor(.mainDark)
                                    .font(.headline)
                                    .lineLimit(1)
                                    .truncationMode(.tail)
                            } else {
                                Text("\(truncateTitle(chatViewModel.stepFormViewModel.navigationtitle))")
                                    .foregroundColor(.mainDark)
                                    .font(.headline)
                                    .lineLimit(1)
                                    .truncationMode(.tail)
                            }
                            Image(systemName: "chevron.right")
                                .foregroundColor(.mainDark)
                                .font(.caption)
                        }
                        .frame(maxWidth: UIScreen.main.bounds.width - 120) // 为两侧图标预留空间
                    }
                }
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            // 只有在启动完成后才进行聊天初始化
            if startupManager.isStartupComplete {
                updateChatViewModels()
            }
        }
        // 监听启动完成状态
        .onReceive(startupManager.$isStartupComplete) { isComplete in
            if isComplete {
                updateChatViewModels()
            }
        }
        // 监听.logout
        .onReceive(NotificationCenter.default.publisher(for: .logout)) { _ in
            DispatchQueue.main.async {
                withAnimation {
                    showSideMenu = false
                }
                // 重置启动状态
                startupManager.reset()
            }
        }
    }

    private var leftMenu: some View {
        // 左侧菜单替换为 `ChatListView`
        ChatListView(showSideMenu: $showSideMenu, showMultiStepForm: $showMultiStepForm, sideBarWidth: $sideBarWidth, chatViewModels: $contentViewModel.chatViewModels, selectedChatID: $contentViewModel.selectedChatID)
            .environmentObject(contentViewModel)
            .environmentObject(chatListViewModel)
            .background(Color(UIColor(light: .systemGray5, dark: .systemGray4)))
    }



    private func updateChatViewModels() {
        // 使用新的初始化方法
        contentViewModel.initializeIfNeeded(chatListViewModel: chatListViewModel)
    }

    private func createMultiStepFormView(_ viewModel: ChatViewModel) -> some View {
        let targetViewModel: ChatViewModel! = if viewModel.stepFormViewModel.isEditing {
            viewModel
        } else {
            ChatViewModel(chatListViewModel: chatListViewModel)
        }
        // {{ AURA-X: Modify - 替换为新的统一表单视图，提供更好的用户体验. Source: 重构需求 }}
        return UnifiedStepFormView {
            showMultiStepForm = false
            viewModel.stepFormViewModel.isEditing = false
        }
        .environmentObject(contentViewModel)
        .environmentObject(chatListViewModel)
        .environmentObject(targetViewModel)
    }

    // MARK: - Leading Navigation Bar Buttons

    private func leadingNavBarButtons() -> some View {
        HStack {
            if UIDevice.current.isPad == false {
                Button(action: {
                    withAnimation {
                        showSideMenu.toggle()
                    }
                }) {
                    Image(systemName: "line.horizontal.3")
                        .imageScale(.large)
                }
            }
        }
    }

    // MARK: - Trailing Navigation Bar Buttons

    private func trailingNavBarButtons() -> some View {
        Button(action: {
            FirebaseManager.shared.logEvent("新聊天")
            contentViewModel.currentChatViewModel?.stepFormViewModel.isEditing = false
            // {{ AURA-X: Fix - 新建聊天时默认为编辑模式，以便用户可以直接配置情景. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
            contentViewModel.currentChatViewModel?.stepFormViewModel.isPreviewMode = false
            showMultiStepForm.toggle()
        }) {
            Image(systemName: "square.and.pencil")
                .foregroundColor(.mainDark)

        }
    }

    // MARK: - Helper Methods

    /// 截断标题以防止覆盖导航栏图标
    private func truncateTitle(_ title: String) -> String {
        let maxLength = 25 // 根据不同语言调整最大长度
        if title.count > maxLength {
            return String(title.prefix(maxLength)) + "..."
        }
        return title
    }
}
