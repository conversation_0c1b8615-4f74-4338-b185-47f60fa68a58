//
//  PreferLanguageView.swift
//  ChatAdvisor
//
//  Created by md on 2024/11/11.
//

import Localize_Swift
import SwiftUI

struct PreferLanguageView: View {
    @Binding var selectedLanguage: String
    @State private var availableLanguages = Bundle.main.localizations.filter{
        $0 != "Base"
    }
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        List {
            Section(header: Text("选择语言".localized())) {
                ForEach(availableLanguages, id: \.self) { language in
                    HStack {
                        Text(language.languageName())
                        Spacer()
                        if language == selectedLanguage {
                            Image(systemName: "checkmark")
                                .foregroundColor(.blue)
                        }
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        FirebaseManager.shared.logEvent("选择语言_\(language)")
                        selectedLanguage = language
                        Localize.setCurrentLanguage(language)
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
        .navigationBarTitle("选择语言".localized(), displayMode: .inline)
    }
}
