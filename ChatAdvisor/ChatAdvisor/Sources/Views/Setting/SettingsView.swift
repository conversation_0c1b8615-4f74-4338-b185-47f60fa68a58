//
//  SettingsView.swift
//  JunShi
//
//  Created by md on 2024/5/13.
//

import Foundation
import Localize_Swift
import SimpleToast
import SwiftUI

struct SettingsView: View {
    @Binding var showSettings: Bool
    @State private var showTip = false
    @State private var tipMessage = ""
    @State private var showDeleteError = false
    @State var feedbackEnabled: Bool = Preferences.feedbackEnabled.value
    @State private var confirmDelete = false
    @State private var showAlert = false
    @State private var inputText = ""
    @State private var passwordText = ""
    @State private var showInputAlert = false
    @State private var showPassWordAlert = false
    // 仅用于更新价格
    @StateObject private var authManager = AccountManager.shared
    @Environment(\.colorScheme) private var colorScheme
    @State private var selectedColor: Color = .init(uiColor: AppThemes.primaryColor)
    @State private var selectedLanguage = Preferences.preferLanguage.value

    #if DEBUG
        @State private var isRelease = Preferences.isRelease.value
    #endif

    var body: some View {
        ZStack {
            Form {
                Section {
                    AppInfoRow(title: "用户".localized(), value: AccountManager.shared.currentUser?.email ?? "Guest".localized())
                    if AccountManager.shared.currentUser != nil {
                        NavigationLink(destination: RechargeView()) {
                            AppInfoRow(title: "余额".localized(), icon: colorScheme == .dark ? "money" : "money_dark", value: "\(String(format: "%.3f", (authManager.currentUser?.balance ?? 0)))")
                        }
                        NavigationLink(destination: RechargeView()) {
                            Text("充值".localized())
                        }
                        Toggle(isOn: $feedbackEnabled) {
                            HStack {
                                Text("震动反馈".localized())
                            }
                        }
                        .onChange(of: feedbackEnabled) { value in
                            Preferences.feedbackEnabled.value = value
                        }
                    }
                //     HStack {
                //         Text("主色调".localized())
                //         Spacer()
                //         ColorPicker("", selection: $selectedColor)
                //             .labelsHidden()
                //     }
                //     .onChange(of: selectedColor) { selectedColor in
                //         AppThemes.primaryColor = UIColor(color: selectedColor)
                //     }
                    NavigationLink(destination: PreferLanguageView(selectedLanguage: $selectedLanguage)) {
                        HStack {
                            Text("选择语言".localized())
                            Spacer()
                            Text(selectedLanguage.languageName())
                        }
                    }
                    .onChange(of: selectedLanguage) { value in
                        Preferences.preferLanguage.value = value
                    }


                    if AccountManager.shared.currentUser != nil {
                        NavigationLink(destination: ArchivedChatView(viewModel: ArchivedChatViewModel())) {
                            Text("已归档".localized())
                        }
                    }
                }

                #if DEBUG
                    Section(header: Text("开发")) {
                        Toggle(isOn: $isRelease) {
                            Text("线上")
                        }
                        .onChange(of: isRelease) { value in
                            Preferences.isRelease.value = value
                        }
                        .ignoresSafeArea()
                    }
                #endif
                Section {
                    NavigationLink(destination: PricingView()) {
                        Text("价格".localized())
                    }
                    // webview navigate to https://api.sanva.tk
                    NavigationLink(destination:
                        WebView(url: NetworkURL.privacyPolicy)
                            .navigationTitle("隐私政策".localized())
                    ) {
                        Text("隐私政策".localized())
                    }
                    NavigationLink(destination:
                        WebView(url: NetworkURL.userTerms)
                            .navigationTitle("服务条款".localized())
                    ) {
                        Text("服务条款".localized())
                    }
                    if AccountManager.shared.currentUser != nil {
                        Button(action: {
                            FirebaseManager.shared.logUserClickEvent(event: "删除账号")
                            showAlert = true
                        }) {
                            Text("删除账号".localized())
                        }
                    }
                    NavigationLink(destination:
                        AboutView()
                    ) {
                        Text("关于".localized())
                    }
//                    AppInfoRow(title: "版本".localized(), value: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown")
                }

                Section(header: Text("意见与建议".localized())) {
                    // 调用系统发送邮件
                    Button(action: {
                        if let url = URL(string: "mailto:<EMAIL>") {
                            if UIApplication.shared.canOpenURL(url) {
                                UIApplication.shared.open(url, options: [:]) { _ in
                                    FirebaseManager.shared.logEvent("发送邮件")
                                }
                            }
                        }
                    }) {
                        Text("发送邮件".localized())
                    }
                }

                if AccountManager.shared.currentUser == nil {
                    // log out
                    Button(action: {
                        showSettings = false
                        DispatchQueue.main.async {
                            withAnimation {
                                AccountManager.shared.needLoggedIn = true
                            }
                        }
                    }) {
                        HStack {
                            Spacer()
                            Text("登录".localized())
                                .font(.headline)
                                .foregroundColor(.mainDark)
                            Spacer()
                        }
                    }
                } else {
                    // log out
                    Button(action: {
                        showSettings = false
                        FirebaseManager.shared.logEvent("登出")
                        _ = AdvisorDatabaseManager.shared.startNewChat()
                        AccountManager.shared.logout()
                    }) {
                        HStack {
                            Spacer()
                            Text("登出".localized())
                                .font(.headline)
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                }
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text("账号删除后无法恢复".localized())
                        .foregroundColor(.red),
                    message: Text("您确定要删除账号吗？".localized()),
                    primaryButton: .destructive(Text("删除".localized()), action: {
                        showInputAlert = true
                    }),
                    secondaryButton: .cancel()
                )
            }
            .alert("删除确认".localized(), isPresented: $showInputAlert) {
                TextField("我确定删除账号".localized(), text: $inputText)
                    .font(.caption2)
                Button("取消".localized(), action: cancel)
                Button("确定".localized(), action: inputSubmit)
                    .foregroundColor(.red)
            } message: {
                Text("\("请输入".localized())“\("我确定删除账号".localized())”\("以确认删除".localized())")
                    .foregroundColor(.red)
            }
            .alert("密码".localized(), isPresented: $showPassWordAlert) {
                SecureField("密码".localized(), text: $passwordText)
                Button("取消".localized(), action: cancel)
                Button("确定".localized(), action: deleteSubmit)
                    .foregroundColor(.red)
            } message: {
                Text("\("请输入".localized())\("密码".localized())")
                    .foregroundColor(.red)
            }

            if authManager.isLoading {
                ProgressView()
                    .ignoresSafeArea()
                    .tint(.mainDark)
            }
        }
        .simpleToast(isPresented: $showTip, options: AppThemes.toastOptions) {
            Text(tipMessage)
                .padding()
                .background(Color.mainDark)
                .foregroundColor(.white)
                .cornerRadius(10)
                .padding(.top)
        }
        .simpleToast(isPresented: $showDeleteError, options: AppThemes.toastOptions) {
            Text("确认删除输入错误, 请重新输入".localized())
                .padding()
                .background(Color.red.opacity(0.8))
                .foregroundColor(.white)
                .cornerRadius(10)
                .padding(.top)
        }
        .simpleToast(isPresented: $authManager.showToast, options: AppThemes.toastOptions) {
            Text(authManager.toastMessage)
                .padding()
                .background(authManager.isRequestError ? Color.red.opacity(0.8) : Color.mainDark)
                .foregroundColor(.white)
                .cornerRadius(10)
                .padding(.top)
        }
        .navigationBarTitle("设置".localized())
        .onAppear {
            if AccountManager.shared.currentUser != nil {
                authManager.getBalance()
            }
            FirebaseManager.shared.logPageView(pageName: "设置")
        }
    }

    private func cancel() {
        showInputAlert = false
    }

    private func inputSubmit() {
        if inputText == "我确定删除账号".localized() {
            // 非苹果账号输入密码后删除
            if AccountManager.shared.currentUser?.externalAccounts?.appleInfo == nil {
                showPassWordAlert = true
            } else { // 苹果账号直接删除
                authManager.deleteAccount(password: nil)
            }
        } else {
            showDeleteError = true
            showInputAlert = false
        }
    }

    private func deleteSubmit() {
        authManager.deleteAccount(password: passwordText)
    }

    private func openNotificationSettings() {
        let urlString: String = if #available(iOS 16.0, *) {
            UIApplication.openNotificationSettingsURLString
        } else {
            UIApplication.openSettingsURLString
        }

        guard let url = URL(string: urlString) else {
            return
        }

        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
    }
}

private struct AppInfoRow: View {
    let title: String
    var icon: String? = nil
    let value: String
    var showRight: Bool = false

    var body: some View {
        HStack {
            Text(title)
            Spacer()
            if let icon {
                Image(uiImage: UIImage(named: icon)!)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 20, height: 20)
                    .foregroundColor(.mainDark)
            }
            Text(value)
                .foregroundColor(.gray)
            if showRight {
                Image(systemName: "chevron.right")
                    .foregroundColor(.gray)
            }
        }
    }
}
