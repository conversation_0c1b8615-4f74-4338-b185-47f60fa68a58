//
//  AppListView.swift
//  ChatAdvisor
//
//  Created by md on 2024/11/7.
//

import SwiftUI
import UIKit

struct AppInfo {
    let name: String
    let iosLink: String
    let iconUrl: String
    let description: String
    let scheme: String
}

struct AppListView: View {
    private var apps: [AppInfo] = [
        AppInfo(name: "chat4o", iosLink: "https://apps.apple.com/app/chat4o/id6503635889", iconUrl: "fan", description: "chat4o_description", scheme:"junshi://"),
        AppInfo(name: "textGame", iosLink: "https://apps.apple.com/app/story-maker/id6615064736", iconUrl: "book", description: "textGame_description", scheme:"textgame://"),
    ]

    var body: some View {
        List(apps, id: \.name) { app in
            HStack {
                Image(uiImage: UIImage(named: app.iconUrl) ?? UIImage())
                    .resizable()
                    .scaledToFit()
                    .frame(width: 44, height: 44)
                VStack(alignment: .leading) {
                    Text(app.name.localized())
                        .fontWeight(.bold)
                    Text(app.description.localized())
                        .font(.subheadline)
                        .padding(.vertical, 2)
                }
                .padding(.horizontal)
                Spacer()
                Button(action: {
                    openAppLink(app)
                }) {
                    if UIApplication.shared.canOpenURL(URL(string: app.scheme)!) {
                        Text("打开".localized())
                            .foregroundColor(.blue)
                    } else {
                        Text("下载".localized())
                            .foregroundColor(.blue)
                    }
                }
            }
        }
        .listStyle(.inset)
    }

    // 打开App商店链接
    func openAppLink(_ app: AppInfo) {
        if let url = URL(string: app.scheme), UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:]) { _ in
                FirebaseManager.shared.logEvent("打开_\(app.name)", parameters: ["app": app.name])
            }
            return
        }
        guard let url = URL(string: app.iosLink) else { return }
        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:]) { _ in
                FirebaseManager.shared.logEvent("打开_\(app.name)", parameters: ["app": app.iosLink])
            }
        } else {
            print("Could not open URL: \(url)")
        }
    }
}
