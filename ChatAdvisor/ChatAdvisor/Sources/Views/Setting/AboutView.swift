//
//  AboutView.swift
//  ChatAdvisor
//
//  Created by md on 2024/11/7.
//

import Foundation
import SwiftUI

struct AboutView: View {
    @State private var appName: String = ""
    @State private var appVersion: String = ""
    @State private var appBuild: String = ""
    @State private var isLoading: Bool = true
    @State private var showTip: Bool = false
    let beianString = "闽ICP备2024074836号-3A"
    // 用来获取应用的信息
    func loadAppInfo() {
        if let bundleInfo = Bundle.main.infoDictionary {
            appName = bundleInfo["CFBundleName"] as? String ?? "Unknown"
            appVersion = bundleInfo["CFBundleShortVersionString"] as? String ?? "Unknown"
            appBuild = bundleInfo["CFBundleVersion"] as? String ?? "Unknown"
            isLoading = false
        }
    }

    var body: some View {
        VStack {
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                    .padding()
            } else {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Version: \(appVersion) (\(appBuild))")
                    Button(action: {
                        if let url = URL(string: "mailto:<EMAIL>"), UIApplication.shared.canOpenURL(url) {
                            UIApplication.shared.open(url, options: [:]) { _ in
                                FirebaseManager.shared.logEvent("关于_发送邮件")
                            }
                        }
                    }) {
                        Text("\("邮箱".localized()): <EMAIL>")
                            .foregroundColor(.blue)
                    }
                    if BootManager.shared.isChina {
                        Text("备案信息: \(beianString)")
                            .foregroundColor(.blue)
                            .onTapGesture {
                                if let url = URL(string: "https://beian.miit.gov.cn/"), UIApplication.shared.canOpenURL(url) {
                                    UIApplication.shared.open(url, options: [:]) { _ in
                                        FirebaseManager.shared.logEvent("关于_查询备案")
                                    }
                                }
                            }
                            // 长按直接复制
                            .onLongPressGesture {
                                showTip = true
                                UIPasteboard.general.string = beianString
                            }
                    }
                    Divider()
                    Text("Other Apps".localized())
                        .font(.title2)
                        .fontWeight(.bold)
                    AppListView()

                }
                .padding(16)
            }
        }.onAppear {
            loadAppInfo()
        }
        .simpleToast(isPresented: $showTip, options: AppThemes.toastOptions) {
            Text("已复制".localized())
                .padding()
                .background(Color.mainDark)
                .foregroundColor(.white)
                .cornerRadius(10)
                .padding(.top)
        }
    }
}
