//
//  PricingView.swift
//  JunShi
//
//  Created by md on 2024/5/14.
//

import Foundation
import Localize_Swift
import MarkdownUI
import Splash
import SwiftUI

private struct FullScreenTextView: View {
    let title: String
    let textToDisplay: String

    var body: some View {
        ScrollView {
            Text(textToDisplay)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .navigationBarTitle(title, displayMode: .inline)
    }
}

struct PricingView: View {
    @Environment(\.colorScheme) private var colorScheme

    private let models: [ChatsModel] = ChatViewModel.allModels
    var body: some View {
        List {
            ForEach(models, id: \.id) { model in
                NavigationLink(destination: FullScreenTextView(title: model.alias, textToDisplay: model.intro)) {
                    PricingRow(model: model)
                        .frame(maxWidth: .infinity)
                }
                .frame(height: 60)
            }
        }
        .navigationTitle("收费标准".localized())
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            FirebaseManager.shared.logPageView(pageName: "收费标准")
        }
        .listStyle(.inset)
    }

    private var theme: Splash.Theme {
        switch colorScheme {
        case .dark:
            .wwdc17(withFont: .init(size: 16))
        default:
            .sunset(withFont: .init(size: 16))
        }
    }
}

struct PricingRow: View {
    let model: ChatsModel
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        VStack(alignment: .leading) {
            HStack(alignment: .center, spacing: 0) {
                HStack {
                    Text(model.alias)
                        .font(.footnote)
                        .bold()
                }
                .padding(.top, AppThemes.padding / 2)
                .padding(.trailing, AppThemes.padding / 2)
                Divider()
                HStack {
                    Text("$1=")
                        .font(.footnote)
                        .bold()
                    Image(colorScheme == .dark ? .money : .moneyDark)
                        .resizable()
                        .scaledToFit()
                        .frame(width: 20, height: 20)
                    Text("100")
                        .font(.footnote)
                        .bold()
                }
                .padding(.top, AppThemes.padding / 2)
                .padding(.horizontal, AppThemes.padding / 2)
                Spacer()
            }
            VStack(alignment: .center, spacing: 5) {
                pricingInfoRow(title: "输入:".localized(), price: model.inPrice)
                pricingInfoRow(title: "输出:".localized(), price: model.outPrice)
            }
            .padding(.bottom, AppThemes.padding / 2)
        }
    }

    private func pricingInfoRow(title: String, price: Float) -> some View {
        HStack {
            Text("\(title) \("约".localized())")
                .font(.footnote)
            HStack {
                Image(colorScheme == .dark ? .money : .moneyDark)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 20, height: 20)
                Text("\(String(format: "%.3f", price))")
                    .font(.footnote)
            }
            Text(" / \("每".localized()) \(model.count) \("字".localized())")
                .font(.footnote)
        }
    }
}
