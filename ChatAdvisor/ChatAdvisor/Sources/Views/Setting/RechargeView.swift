//  RechargeView.swift
//  JunShi
//
//  Created by zweiteng on 2024/5/18.
//

import Foundation
import StoreKit
import SwiftUI

struct RechargeView: View {
    @State private var showAlert = false
    @State private var alertMessage = ""
    @StateObject private var storeManager = ShopManager.shared // 使用 @StateObject 订阅单例对象的变化
    @StateObject private var bootConfigManager = BootManager.shared
    @Environment(\.colorScheme) private var colorScheme
    @State private var showRetryButton = false // 添加一个状态变量来跟踪是否显示重试按钮

    var body: some View {
        ZStack(alignment: .center) {
            VStack {
                InputingAnimationView(colorChange: false, slogans: bootConfigManager.config.rechargeMessages, subtitle: nil, font: AppThemes.animationFont, backgroundColor: .constant(.clear))
                    .frame(maxHeight: 40)
                Spacer()
                    .frame(maxHeight: AppThemes.padding / 2)
                VStack {
                    HStack(alignment: .bottom) {
                        Spacer()
                        Text("当前余额:".localized())
                            .font(.headline)
                            .fontWeight(.bold)
                        Image(uiImage: UIImage(named: colorScheme == .dark ? "money" : "money_dark")!)
                            .resizable()
                            .scaledToFit()
                            .frame(width: 40, height: 40)
                            .foregroundColor(.mainDark)
                        Text("\(String(format: "%.3f", (AccountManager.shared.currentUser?.balance ?? 0)))")
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.mainDark)
                        Spacer()
                    }
                    HStack(alignment: .bottom) {
                        Text("充值金额".localized() + ":")
                            .font(.headline)
                            .fontWeight(.bold)
                            .padding(.leading, AppThemes.padding)
                        Spacer()
                    }
                }

                // 显示金额选项按钮，排列为两行三列
                VStack {
                    Spacer()
                        .frame(height: AppThemes.padding)
                    if showRetryButton {
                        Button(action: {
                            showRetryButton = false
                            storeManager.getProducts()
                        }) {
                            Text("重试".localized())
                                .font(.headline)
                                .padding()
                                .background(Color.mainDark)
                                .foregroundColor(.white)
                                .cornerRadius(10)
                        }
                    } else {
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: AppThemes.padding / 2), count: 3), spacing: AppThemes.padding / 2) {
                            ForEach(storeManager.products.sorted(by: { $0.price.doubleValue < $1.price.doubleValue }), id: \.self) { product in
                                let firstProduct = storeManager.products.sorted(by: { $0.price.doubleValue < $1.price.doubleValue }).first
                                let baseAmount = storeManager.backendProducts.filter { $0.productIdentifier == firstProduct?.productIdentifier }.first?.amount ?? 1
                                let basePrice = firstProduct?.price.doubleValue ?? 1
                                let currentAmount = storeManager.backendProducts.filter { $0.productIdentifier == product.productIdentifier }.first?.amount ?? 1
                                let percentage = ((Double(currentAmount) / Double(baseAmount)) / (product.price.doubleValue / basePrice) - 1.0) * 100.0

                                Button(action: {
                                    if AccountManager.shared.currentUser == nil {
                                        withAnimation {
                                            AccountManager.shared.needLoggedIn = true
                                        }
                                        return
                                    }
                                    storeManager.selectProduct = product
                                    storeManager.isLoading = true
                                    storeManager.buyProduct(product)
                                }) {
                                    VStack(spacing: 0) {
                                        Spacer()
                                        Text("\(product.localizedPrice ?? "")")
                                            .font(.subheadline)
                                            .fontWeight(.bold)
                                            .frame(maxWidth: .infinity)
                                            .frame(height: 24)
                                            .foregroundColor(colorScheme == .dark ? .white : .black)
//                                            .padding(.top)
//                                            .padding(.trailing)
                                        Divider()
                                            .frame(height: 1)
                                        HStack {
                                            Spacer()
                                            Image(uiImage: UIImage(named: colorScheme == .dark ? "money" : "money_dark")!)
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: 24, height: 24)
                                            Text("\(currentAmount)")
                                                .font(.body)
                                                .fontWeight(.semibold)
                                                .foregroundColor(storeManager.selectProduct == product ? Color(.systemGray5) : Color.mainDark)
                                            Spacer()
                                        }
                                        Spacer()
                                    }
                                    .background(storeManager.selectProduct == product ? Color.mainDark : Color(.systemGray5))
                                    .cornerRadius(10)
                                    .overlay(
                                        Group {
                                            if percentage > 1 {
                                                Text(String(format: "+%.0f%%", percentage))
                                                    .font(.caption2)
                                                    .fontWeight(.bold)
                                                    .foregroundColor(storeManager.selectProduct == product ? Color(.systemGray5) : Color.mainDark)
                                                    .padding(.trailing, 2)
                                                    .padding(.top, 2)
                                            } else {
                                                Color.clear
                                            }
                                        },
                                        alignment: .topTrailing
                                    )
                                }
                            }
                        }
                        .padding(.horizontal, AppThemes.padding / 2)
                    }
                }
                .onReceive(storeManager.$purchaseState) { state in
                    switch state {
                    case .success:
                        break
                    case let .failed(isCancel, message):
                        if !isCancel {
                            alertMessage = message
                            showAlert = true
                            showRetryButton = true
                        }
                    case .restored:
                        alertMessage = "Purchases restored!"
                        showAlert = true
                    case .idle:
                        break
                    }
                    storeManager.isLoading = false
                }
                VStack {
                    HStack {
                        Text("充值说明".localized())
                            .font(.caption)
                            .bold()
                            .padding(.leading, AppThemes.padding)
                            .padding(.top, AppThemes.padding / 2)
                        Spacer()
                    }
                    ScrollView {
                        Text(bootConfigManager.config.rechargeDescription)
                            .font(.caption2)
                            .padding(.horizontal, AppThemes.padding)
                    }
                    Spacer()
                }
                Spacer()
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text("充值结果".localized()),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("确定".localized()))
                )
            }
            .onAppear {
                AccountManager.shared.getBalance()
                storeManager.getProducts()
                FirebaseManager.shared.logPageView(pageName: "充值")
            }
            if storeManager.isLoading {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                ProgressView()
                    .tint(.mainDark)
                    .padding()
            }
        }
        .navigationBarTitle("充值".localized(), displayMode: .inline)
    }
}
