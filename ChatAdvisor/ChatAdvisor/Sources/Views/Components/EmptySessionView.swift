//
//  EmptySessionView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import SwiftUI
import Localize_Swift

/// 空会话状态视图 - 当没有选中会话时显示
struct EmptySessionView: View {
    @State private var animationOffset: CGFloat = 0
    @State private var pulseScale: CGFloat = 1.0
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            // {{ AURA-X: Modify - 使用应用图标替代系统图标 }}
            // 图标动画
            VStack(spacing: 20) {
                Image(.logo)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 80, height: 80)
                    .opacity(0.6)
                    .scaleEffect(pulseScale)
                    .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: pulseScale)
                
                Text("empty_session_title".localized())
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text("empty_session_subtitle".localized())
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
            
            // 提示信息
            VStack(spacing: 12) {
                HStack(spacing: 12) {
                    Text("empty_session_tip_click".localized())
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Image(systemName: "square.and.pencil")
                        .foregroundColor(.mainDark)
                    Text("empty_session_tip_create".localized())
                        .font(.caption)
                        .foregroundColor(.secondary)

                }
                
                HStack(spacing: 12) {
                    Text("empty_session_tip_menu".localized())
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Image(systemName: "line.horizontal.3")
                        .foregroundColor(.mainDark)
                    Text("empty_session_tip_select".localized())
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.bottom, 50)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
        .onAppear {
            pulseScale = 1.1
        }
    }
}
