//
//  MultiStepFormViewModel.swift
//  test
//
//  Created by md on 2024/7/5.
//

import Combine
import Foundation
import Localize_Swift
import SwiftUI
import WCDBSwift

final class MultiStepFormViewModel: ObservableObject, Codable {
    var chatId: String
    var stepFormId: String
    weak var recognizeViewModel: RecognizeViewModel?
    func generateTitle() -> [String] {
        var nonEmptyFields: [String] = []
        for stepViewModel in stepViewModels {
            for (key, value) in stepViewModel.fields {
                if !value.isEmpty {
                    nonEmptyFields.append("\(key.localized()):\(value.localized())")
                    break
                }
            }
            if !nonEmptyFields.isEmpty {
                break
            }
            for (key, value) in stepViewModel.customFields {
                if !value.isEmpty {
                    nonEmptyFields.append("\(key.localized()):\(value.localized())")
                    break
                }
            }
        }
        return nonEmptyFields
    }

    var title: String {
        let nonEmptyFields = generateTitle()
        return nonEmptyFields.isEmpty ? "没有标题的会话".localized() : nonEmptyFields.joined(separator: ", ")
    }

    var navigationtitle: String {
        let nonEmptyFields = generateTitle()
        return nonEmptyFields.isEmpty ? "" : "\(nonEmptyFields.joined(separator: ", "))"
    }

    @Published var isLocalRecognition = true
    @Published var isProcessing = false
    @Published var disableComplete = false
    @Published var isEditing = false
    // {{ AURA-X: Add - 添加预览模式状态支持，用于统一编辑器的模式切换功能. Source: 重构需求 }}
    @Published var isPreviewMode = false
    @Published var currentStep = 4 {
        didSet {
            // hide keyboard
            DispatchQueue.main.async {
                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
            }
        }
    }

    @Published var stepViewModels: [StepViewModel] {
        didSet {
            cancellables.forEach { $0.cancel() }
            for stepViewModel in stepViewModels {
                stepViewModel.objectWillChange
                    .sink { [weak self] _ in
                        self?.objectWillChange.send()
                    }
                    .store(in: &cancellables)
            }
        }
    }

    @Published var options: [StepOption] = []
    private var cancellables = Set<AnyCancellable>()

    private var originalSteps: [Step] = []
    private var originalStepViewModels: [StepViewModel] = []

    // 初始化或其他方法中保存初始状态
    func backupState() {
        originalStepViewModels = stepViewModels.compactMap { $0.deepCopy() }
    }

    func restoreState() {
        stepViewModels = originalStepViewModels
    }

    // Codable
    required init(stepFormId: String, chatId: String, stepViewModels: [StepViewModel] = []) {
        self.stepViewModels = stepViewModels
        self.chatId = chatId
        self.stepFormId = stepFormId
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(currentStep, forKey: .currentStep)
        try container.encode(stepViewModels, forKey: .stepViewModels)
        try container.encode(chatId, forKey: .chatId)
        try container.encode(stepFormId, forKey: .stepFormId)
        // {{ AURA-X: Add - 序列化预览模式状态. Source: 重构需求 }}
        try container.encode(isPreviewMode, forKey: .isPreviewMode)
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        currentStep = try container.decode(Int.self, forKey: .currentStep)
        stepViewModels = try container.decode([StepViewModel].self, forKey: .stepViewModels)
        chatId = try container.decode(String.self, forKey: .chatId)
        stepFormId = try container.decode(String.self, forKey: .stepFormId)
        // {{ AURA-X: Add - 反序列化预览模式状态，提供默认值确保兼容性. Source: 重构需求 }}
        isPreviewMode = try container.decodeIfPresent(Bool.self, forKey: .isPreviewMode) ?? false
    }

    func initailData() -> MultiStepFormViewModel {
        stepViewModels = []
        loadOptionsFromDatabase()
        for stepType in StepType.allCases {
            let stepViewModel = StepViewModel(chatId: chatId)
            stepViewModel.stepFormViewModel = self
            stepViewModel.stepType = stepType
            stepViewModel.stepFormId = stepFormId

            for field in stepType.fields {
                stepViewModel.setField(field, value: "")
            }

            for optionKey in stepType.predefinedOptions {
                stepViewModel.setPredefinedOptions(optionKey, options: getOptions(for: optionKey))
            }

            if stepType == .custom {
                stepViewModel.showCustomFields = true
            }

            if stepType == .import {
                stepViewModel.showCustomFields = false
            }

            stepViewModels.append(stepViewModel)
        }

        return self
    }

    var steps: [Step] {
        stepViewModels
    }

    func loadOptionsFromDatabase() {
        options = ChatConfigDatabaseManager.shared.loadOptionsFromDatabase()
    }

    private func getOptions(for key: String) -> [String] {
        options.filter { $0.key == key }.map(\.value)
    }

    func saveToDatabase() {
        ChatConfigDatabaseManager.shared.saveMultiStepFormViewModelToDatabase(self)
    }

    func loadFromDatabase() {
        if let multiStepFormViewModel = ChatConfigDatabaseManager.shared.loadMultiStepFormViewModelFromDatabase(chatId: chatId) {
            stepFormId = multiStepFormViewModel.stepFormId
            currentStep = multiStepFormViewModel.currentStep
            stepViewModels = multiStepFormViewModel.stepViewModels
            // {{ AURA-X: Fix - 从数据库加载时保持预览模式状态，确保编辑现有会话时的正确体验. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
            isPreviewMode = multiStepFormViewModel.isPreviewMode
            for stepViewModel in stepViewModels {
                stepViewModel.stepFormId = stepFormId
                // {{ AURA-X: Fix - 重新设置预定义选项，确保选项字段在编辑模式下正确显示为选择器. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
                for optionKey in stepViewModel.stepType.predefinedOptions {
                    stepViewModel.setPredefinedOptions(optionKey, options: getOptions(for: optionKey))
                }
            }
        }
    }

    func generatePrompt() -> String {
        var userInfo: [String: String] = [:]
        var chatPreferences: [String: String] = [:]
        var emotionAndAttitude: [String: String] = [:]
        var customFields: [String: String] = [:]

        for stepViewModel in stepViewModels {
            switch stepViewModel.stepType {
            case .information:
                for (key, value) in stepViewModel.fields where !value.isEmpty {
                    userInfo[key] = value
                }

            case .preferences:
                for (key, value) in stepViewModel.fields where !value.isEmpty {
                    chatPreferences[key] = value
                }

            case .emotion:
                for (key, value) in stepViewModel.fields where !value.isEmpty {
                    emotionAndAttitude[key] = value
                }

            case .custom:
                for (key, value) in stepViewModel.customFields where !value.isEmpty {
                    customFields[key] = value
                }

            case .import:
                break
            default:
                break
            }
        }

        var prompt = ""
//        Localize.setCurrentLanguage("en")

//        if isLocalRecognition {
//            prompt = """
//            \(Configs.default.promotLocal ?? "promot_local".localized(in: "en")),
//            """
//        } else {
//            prompt = """
//            \(Configs.default.promotCloud ?? "promot_cloud".localized()),
//            """
//        }
//        Localize.resetCurrentLanguageToDefault()

        if !userInfo.isEmpty {
//            prompt += "### \("Information".localized())\n"
            for (key, value) in userInfo {
                prompt += "\(key.localized()): \(value.localized()),"
            }
        }

        if !chatPreferences.isEmpty {
//            prompt += "\n### \("Preferences".localized())\n"
            for (key, value) in chatPreferences {
                prompt += "\(key.localized()): \(value.localized()),"
            }
        }

        if !emotionAndAttitude.isEmpty {
//            prompt += "\n### \("Emotion".localized())\n"
            for (key, value) in emotionAndAttitude {
                prompt += "\(key.localized()): \(value),"
            }
        }

        if !customFields.isEmpty {
//            prompt += "\n### \("Custom".localized())\n"
            for (key, value) in customFields {
                prompt += "\(key.localized()): \(value),"
            }
        }

//        for content in BootManager.shared.config.hideMessage {
//            prompt += "\(content),"
//        }

        return prompt
    }
}

extension MultiStepFormViewModel: TableCodable {
    enum CodingKeys: String, CodingTableKey {
        typealias Root = MultiStepFormViewModel
        case chatId
        case stepFormId
        case currentStep
        case stepViewModels
        // {{ AURA-X: Add - 添加预览模式序列化键. Source: 重构需求 }}
        case isPreviewMode

        static let objectRelationalMapping = TableBinding(CodingKeys.self) {
            BindColumnConstraint(chatId, isPrimary: true)
        }
    }
}
