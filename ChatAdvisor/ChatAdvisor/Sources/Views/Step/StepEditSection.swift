//
//  StepEditSection.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/30.
//  功能：支持预览/编辑模式切换的步骤编辑组件
//

import SwiftUI
import Localize_Swift

struct StepEditSection: View {
    @ObservedObject var viewModel: StepViewModel
    @Binding var isPreviewMode: Bool
    @State private var newFieldKey: String = ""
    @State private var newFieldValue: String = ""
    @FocusState private var isKeyFocused: Bool
    @FocusState private var isValueFocused: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppThemes.padding) {
            // 步骤标题头部
            StepHeaderView(stepType: viewModel.stepType, isComplete: viewModel.isComplete)
            
            // 根据模式显示不同内容
            if isPreviewMode {
                // 预览模式：显示已填写的内容
                PreviewContent()
            } else {
                // 编辑模式：显示可编辑表单
                EditingContent()
            }
        }
        .padding(AppThemes.padding)
        .background(
            RoundedRectangle(cornerRadius: AppThemes.cornerRadius)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .animation(.easeInOut(duration: 0.3), value: isPreviewMode)
    }
    
    // MARK: - 预览内容视图
    @ViewBuilder
    private func PreviewContent() -> some View {
        if viewModel.stepType == .import {
            // 导入步骤的预览
            if !viewModel.recognizeViewModel.recognizedTextsByImage.isEmpty {
                VStack(alignment: .leading, spacing: AppThemes.padding / 2) {
                    Text("step_imported_chat_records".localized())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("\(viewModel.recognizeViewModel.recognizedTextsByImage.count) 条记录")
                        .font(.caption)
                        .foregroundColor(.mainDark)
                }
            } else {
                Text("step_no_chat_records".localized())
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        } else {
            // 标准字段和自定义字段的预览
            VStack(alignment: .leading, spacing: AppThemes.padding / 2) {
                // 显示标准字段
                PreviewStandardFields()
                
                // 显示自定义字段
                if !viewModel.customFields.isEmpty {
                    Divider()
                    PreviewCustomFields()
                }
                
                // 如果没有任何内容
                if viewModel.fields.values.allSatisfy(\.isEmpty) && viewModel.customFields.isEmpty {
                    Text("step_no_content".localized())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .italic()
                }
            }
        }
    }
    
    // MARK: - 编辑内容视图
    @ViewBuilder
    private func EditingContent() -> some View {
        if viewModel.stepType == .import {
            // 导入步骤：使用现有的 RecognizeView
            RecognizeView(viewModel: viewModel.recognizeViewModel)
        } else {
            VStack(spacing: AppThemes.padding) {
                // 标准字段编辑
                if !viewModel.fields.isEmpty {
                    StandardFieldsEditView()
                }
                
                // 自定义字段编辑
                if viewModel.showCustomFields {
                    CustomFieldsEditView()
                }
            }
        }
    }
    
    // MARK: - 预览模式子视图
    @ViewBuilder
    private func PreviewStandardFields() -> some View {
        ForEach(Array(viewModel.fields.keys).sorted(), id: \.self) { field in
            if let value = viewModel.fields[field], !value.isEmpty {
                HStack {
                    Text(field.localized())
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .frame(width: 120, alignment: .leading)
                    
                    // {{ AURA-X: Fix - 修复选择器字段在预览时显示正确的本地化值. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
                    Text(getDisplayValue(field: field, value: value))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    Spacer()
                }
            }
        }
    }
    
    // {{ AURA-X: Add - 获取字段的正确显示值，处理选择器的本地化. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
    private func getDisplayValue(field: String, value: String) -> String {
        // 如果是预定义选项字段，返回本地化的值
        if viewModel.predefinedOptions.keys.contains(field) {
            return value.localized()
        }
        // 否则返回原始值的本地化
        return value.localized()
    }
    
    @ViewBuilder
    private func PreviewCustomFields() -> some View {
        Text("自定义字段".localized())
            .font(.caption)
            .foregroundColor(.mainDark)
        
        ForEach(Array(viewModel.customFields.keys).sorted(), id: \.self) { key in
            if let value = viewModel.customFields[key], !value.isEmpty {
                HStack {
                    Text(key.localized())
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .frame(width: 120, alignment: .leading)
                    
                    Text(value.localized())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    Spacer()
                }
            }
        }
    }
    
    // MARK: - 编辑模式子视图
    @ViewBuilder
    private func StandardFieldsEditView() -> some View {
        VStack(spacing: AppThemes.padding / 2) {
            // 文本输入字段
            ForEach(Array(viewModel.fields.keys.filter { !viewModel.predefinedOptions.keys.contains($0) }).sorted { $0.localized() > $1.localized() }, id: \.self) { field in
                HStack {
                    Text(field.localized())
                        .frame(width: 120, alignment: .leading)
                        .font(.subheadline)
                    
                    TextField("\("请输入".localized())\(field.localized())", text: Binding(
                        get: { viewModel.fields[field] ?? "" },
                        set: { viewModel.setField(field, value: $0) }
                    ))
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                }
            }
            
            // {{ AURA-X: Fix - 修改选择器字段为左右分布布局，与填空字段保持一致. Approval: mcp-feedback-enhanced(ID:feedback_002). }}
            // 选择器字段
            ForEach(Array(viewModel.predefinedOptions.keys).sorted { $0 > $1 }, id: \.self) { field in
                if let options = viewModel.predefinedOptions[field] {
                    HStack {
                        Text(field.localized())
                            .frame(width: 120, alignment: .leading)
                            .font(.subheadline)
                        
                        Picker(field.localized(), selection: Binding<String>(
                            get: { 
                                // 确保返回有效的选择值或空字符串
                                let currentValue = viewModel.fields[field] ?? ""
                                return currentValue.isEmpty ? "" : currentValue
                            },
                            set: { newValue in
                                viewModel.setField(field, value: newValue)
                            }
                        )) {
                            // 空选项
                            Text("请选择".localized())
                                .tag("")
                            
                            // 预定义选项
                            ForEach(options, id: \.self) { option in
                                Text(option.localized())
                                    .tag(option)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .tint(.mainDark)
                    }
                }
            }
        }
    }
    
    @ViewBuilder
    private func CustomFieldsEditView() -> some View {
        VStack(alignment: .leading, spacing: AppThemes.padding / 2) {
            Text("自定义字段".localized())
                .font(.headline)
                .foregroundColor(.mainDark)
            
            // 添加新字段
            HStack {
                TextField("字段名".localized(), text: $newFieldKey)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($isKeyFocused)
                
                Text(":")
                
                TextField("字段值".localized(), text: $newFieldValue)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($isValueFocused)
                
                Button(action: {
                    if !newFieldKey.isEmpty && !newFieldValue.isEmpty {
                        viewModel.setCustomField(newFieldKey, value: newFieldValue)
                        newFieldKey = ""
                        newFieldValue = ""
                    }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.mainDark)
                }
                .disabled(newFieldKey.isEmpty || newFieldValue.isEmpty)
            }
            
            // 显示已添加的自定义字段
            ForEach(viewModel.customFields.keys.sorted(), id: \.self) { key in
                HStack {
                    Text(key)
                        .frame(width: 120, alignment: .leading)
                    Text(":")
                    Text(viewModel.customFields[key] ?? "")
                        .lineLimit(1)
                    Spacer()
                    Button(role: .destructive) {
                        viewModel.historicalFields[key] = viewModel.customFields[key]
                        viewModel.removeCustomField(key)
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.red)
                    }
                }
                .padding(.horizontal, AppThemes.padding / 2)
            }
            
            // 历史字段
            if !viewModel.historicalFields.keys.filter({ !viewModel.customFields.keys.contains($0) }).isEmpty {
                Text("历史字段".localized())
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, AppThemes.padding / 2)
                
                ForEach(viewModel.historicalFields.keys.filter { !viewModel.customFields.keys.contains($0) }.sorted(), id: \.self) { key in
                    HStack {
                        Text(key)
                            .frame(width: 120, alignment: .leading)
                        Text(":")
                        Text(viewModel.historicalFields[key] ?? "")
                            .lineLimit(1)
                        Spacer()
                        
                        Button("使用".localized()) {
                            viewModel.setCustomField(key, value: viewModel.historicalFields[key] ?? "")
                            viewModel.historicalFields.removeValue(forKey: key)
                        }
                        .font(.caption)
                        .foregroundColor(.mainDark)
                        
                        Button(action: {
                            viewModel.historicalFields.removeValue(forKey: key)
                            viewModel.removeCustomField(key)
                            ChatConfigDatabaseManager.shared.removeHistoricalFieldFromDatabase(key)
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.red)
                        }
                    }
                    .padding(.horizontal, AppThemes.padding / 2)
                }
            }
        }
        .onChange(of: isKeyFocused) { focused in
            if !focused, !newFieldKey.isEmpty, !newFieldValue.isEmpty {
                viewModel.setCustomField(newFieldKey, value: newFieldValue)
                newFieldKey = ""
                newFieldValue = ""
            }
        }
        .onChange(of: isValueFocused) { focused in
            if !focused, !newFieldKey.isEmpty, !newFieldValue.isEmpty {
                viewModel.setCustomField(newFieldKey, value: newFieldValue)
                newFieldKey = ""
                newFieldValue = ""
            }
        }
    }
}

// MARK: - 步骤标题头部视图
struct StepHeaderView: View {
    let stepType: StepType
    let isComplete: Bool
    
    var body: some View {
        HStack {
            Image(systemName: iconName)
                .foregroundColor(isComplete ? .green : .mainDark)
                .font(.title2)
            
            Text(stepType.title.localized())
                .font(.headline)
                .foregroundColor(.primary)
            
            Spacer()
            
            // 完成状态指示器
            if isComplete {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
            } else {
                Image(systemName: "circle")
                    .foregroundColor(.gray)
            }
        }
    }
    
    private var iconName: String {
        switch stepType {
        case .information:
            return "person.circle"
        case .preferences:
            return "slider.horizontal.3"
        case .emotion:
            return "heart.circle"
        case .custom:
            return "pencil.circle"
        case .import:
            return "square.and.arrow.down.on.square"
        }
    }
}
