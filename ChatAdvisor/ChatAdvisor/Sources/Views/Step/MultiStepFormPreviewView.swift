//
//  MultiStepFormPreviewView.swift
//  ChatAdvisor
//
//  Created by md on 2024/7/12.
//

import Localize_Swift
import StepperView
import SwiftUI

struct MultiStepFormPreviewView: View {
    @EnvironmentObject private var chatViewModel: ChatViewModel

    var body: some View {
        VStack {
            ForEach(chatViewModel.stepFormViewModel.stepViewModels.filter { $0.isComplete || $0.isInProgress }.indices, id: \.self) { index in
                StepPreviewView(viewModel: chatViewModel.stepFormViewModel.stepViewModels[index])
                Divider()
            }
        }
    }
}
