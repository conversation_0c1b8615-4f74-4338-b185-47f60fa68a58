//
//  PredefinedOptionKey.swift
//  test
//
//  Created by md on 2024/7/8.
//

import Foundation
import WCDBSwift

enum PredefinedOptionKey: String, CaseIterable, Codable, TableCodable {
    case relationshipType = "Relationship_Type"
    case relationshipStatus = "Relationship_Status"
    case preferredChatStyle = "Preferred_Chat_Style"
    case preferredResponseLength = "Preferred_Response_Length"

    // TableCodable
    enum CodingKeys: String, CodingTableKey {
        typealias Root = PredefinedOptionKey
        case relationshipType
        case relationshipStatus
        case preferredChatStyle
        case preferredResponseLength

        static let objectRelationalMapping = TableBinding(CodingKeys.self)
    }
}
