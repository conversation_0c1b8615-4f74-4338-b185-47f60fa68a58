//
//  UnifiedStepFormView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/30.
//  功能：统一的多步骤表单编辑器，替换原有的分页式 TabView
//

import Localize_Swift
import StepperView
import SwiftUI
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "UnifiedStepFormView")
private let itemWidth: CGFloat = 20

struct UnifiedStepFormView: View {
    var completeAction: (() -> Void)? = nil

    @EnvironmentObject var chatViewModel: ChatViewModel
    @EnvironmentObject var contentViewModel: ContentViewModel
    @EnvironmentObject var chatListViewModel: ChatListViewModel

    var body: some View {
        NavigationStack {
            ZStack {
                VStack(alignment: .leading, spacing: 0) {
                    // 顶部分隔线
                    Divider()
                    
                    // {{ AURA-X: Modify - 替换 TabView 为 ScrollView + LazyVStack 统一布局. Source: 重构需求 }}
                    // {{ AURA-X: Modify - 移除顶部指示器和模式切换，简化布局. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
                    // 主内容区域：纯净的统一滚动视图
                    ScrollView(.vertical, showsIndicators: false) {
                        LazyVStack(spacing: AppThemes.padding) {
                            // 统一的步骤编辑区域（移除顶部指示器）
                            StepEditingSectionGroup()
                        }
                        .padding(.bottom, AppThemes.padding * 2) // 底部额外留白
                    }
                    
                    // 底部控制栏
                    BottomControlBar()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .navigationBarTitle(chatViewModel.stepFormViewModel.isEditing ? "\("edit".localized()) \("Context".localized())" : "Context".localized(), displayMode: .inline)
                // {{ AURA-X: Add - 设置导航栏为不透明样式，与应用主题保持一致. Approval: mcp-feedback-enhanced(ID:feedback_002). }}
                .toolbarBackground(.regularMaterial, for: .navigationBar)
                .toolbarBackground(.visible, for: .navigationBar)
                .toolbarColorScheme(.light, for: .navigationBar) // 确保导航栏文字清晰可见
                
                // 加载状态遮罩
                if chatViewModel.stepFormViewModel.isProcessing {
                    ProgressView()
                        .tint(.mainDark)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.black.opacity(0.3))
                        .ignoresSafeArea()
                }
            }
        }
        .onAppear {
            chatViewModel.stepFormViewModel.backupState()
        }
    }
    // {{ AURA-X: Remove - 移除步骤指示器区域，简化界面. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
    
    // MARK: - 步骤编辑区域组
    @ViewBuilder  
    private func StepEditingSectionGroup() -> some View {
        VStack(spacing: AppThemes.padding) {
            ForEach(chatViewModel.stepFormViewModel.stepViewModels.indices, id: \.self) { index in
                StepEditSection(
                    viewModel: chatViewModel.stepFormViewModel.stepViewModels[index],
                    isPreviewMode: $chatViewModel.stepFormViewModel.isPreviewMode
                )
                .environmentObject(chatViewModel.stepFormViewModel)
                // {{ AURA-X: Add - 添加流畅的出现动画. Source: SwiftUI 最佳实践 }}
                .transition(.asymmetric(
                    insertion: .scale(scale: 0.95).combined(with: .opacity),
                    removal: .scale(scale: 0.95).combined(with: .opacity)
                ))
                .animation(.easeInOut(duration: 0.3), value: chatViewModel.stepFormViewModel.isPreviewMode)
            }
        }
        .padding(.horizontal, AppThemes.padding)
    }
    
    // MARK: - 底部控制栏
    @ViewBuilder
    private func BottomControlBar() -> some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: AppThemes.padding) {
                // 取消按钮
                Button(action: {
                    withAnimation {
                        chatViewModel.stepFormViewModel.restoreState()
                        completeAction?()
                    }
                }) {
                    Text("取消".localized())
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: AppThemes.cornerRadius)
                                .fill(Color(.systemGray5))
                        )
                }
                
                Spacer()
                
                // {{ AURA-X: Add - 将模式切换移到完成按钮左边. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
                // 模式切换控件（紧凑版）
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        chatViewModel.stepFormViewModel.isPreviewMode.toggle()
                    }
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: chatViewModel.stepFormViewModel.isPreviewMode ? "pencil" : "eye")
                            .font(.system(size: 16))
                    }
                    .foregroundColor(.mainDark)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: AppThemes.cornerRadius)
                            .stroke(Color.mainDark, lineWidth: 1)
                    )
                }
                
                // {{ AURA-X: Modify - 更改完成按钮文案，避免用户误解. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
                // 保存并完成按钮
                Button(action: {
                    // {{ AURA-X: Modify - 保持原有完成逻辑不变，确保数据兼容性. Source: 重构需求 }}
                    let resumeBlock = {
                        chatViewModel.stepFormViewModel.isProcessing = true
                        chatViewModel.stepFormViewModel.saveToDatabase()

                        // 提取导入的聊天记录
                        let importStep = chatViewModel.stepFormViewModel.stepViewModels.filter { $0.recognizeViewModel.chatMessages.count > 0 }.compactMap { $0 }
                        for step in importStep {
                            for message in step.recognizeViewModel.chatMessages {
                                // 只添加不包含的部分
                                chatViewModel.currentChat.messages.append(contentsOf: message.filter { !chatViewModel.currentChat.messages.contains($0) }.compactMap { $0 })
                            }
                        }

                        if chatViewModel.stepFormViewModel.isEditing {
                            // 编辑仅保存
                            chatListViewModel.newChatTitle = chatViewModel.stepFormViewModel.title
                            chatListViewModel.renamingChatId = chatViewModel.currentChat.id
                            chatListViewModel.renameChat()
                            AdvisorDatabaseManager.shared.update(messages: chatViewModel.currentChat.messages)
                        } else {
                            // 新建区分本地和云端
                            // 本地
                            chatViewModel.saveCurrentChat()

                            // 确保ChatViewModel不重复添加
                            if !contentViewModel.chatViewModels.contains(where: { $0.currentChat.id == chatViewModel.currentChat.id }) {
                                contentViewModel.chatViewModels.append(chatViewModel)
                            }

                            chatViewModel.currentModel = ChatViewModel.allModels.first ?? ChatsModel.default

                            // 确保原子性操作：先保存会话，再更新列表，最后切换会话
                            Task { @MainActor in
                                do {
                                    // 1. 确保会话已保存到数据库
                                    AdvisorDatabaseManager.shared.update(chat: chatViewModel.currentChat)

                                    // 2. 立即更新会话列表内存数据，确保实时显示
                                    chatListViewModel.updateMemoryChat(newChat: chatViewModel.currentChat)

                                    // 3. 使用原子化的会话选择方法，确保数据同步
                                    try await contentViewModel.selectChatAtomically(chat: chatViewModel.currentChat, chatListViewModel: chatListViewModel)

                                    logger.info("新会话创建完成并成功切换: chatId=\(chatViewModel.currentChat.id)")

                                } catch {
                                    logger.error("新会话切换失败: \(error.localizedDescription)")
                                    // 如果切换失败，至少确保会话在列表中可见
                                    contentViewModel.selectedChatID = chatViewModel.currentChat.id
                                }
                            }

                            if chatViewModel.stepFormViewModel.isLocalRecognition {
                                if chatViewModel.currentChat.messages.filter({ $0.role != .system }).count > 0 {
                                    chatViewModel.postMessages(messages: chatViewModel.currentChat.messages)
                                }
                            } else {
                                if let message = chatViewModel.stepFormViewModel.recognizeViewModel?.constructRequestParameters() {
                                    chatViewModel.postMessages(message)
                                }
                            }
                        }
                        completeAction?()
                        chatViewModel.stepFormViewModel.isProcessing = false
                    }
                    if AccountManager.shared.currentUser == nil {
                        withAnimation {
                            AccountManager.shared.needLoggedIn = true
                            AccountManager.shared.resumeBlock = resumeBlock
                        }
                        return
                    }
                    resumeBlock()
                }) {
                    Text("step_save".localized())
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: AppThemes.cornerRadius)
                                .fill(chatViewModel.stepFormViewModel.disableComplete ? Color.gray : Color.mainDark)
                        )
                }
                .disabled(chatViewModel.stepFormViewModel.disableComplete)
            }
            .padding(AppThemes.padding)
        }
        .background(Color(.systemBackground))
    }
    
    // {{ AURA-X: Remove - 移除不再需要的指示器相关方法. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
}
