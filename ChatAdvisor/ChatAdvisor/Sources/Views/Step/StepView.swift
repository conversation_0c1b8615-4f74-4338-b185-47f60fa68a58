import SwiftUI

struct StepView: View {
    @ObservedObject var viewModel: StepViewModel
    @State private var newFieldKey: String = ""
    @State private var newFieldValue: String = ""
    @FocusState private var isKeyFocused: Bool
    @FocusState private var isValueFocused: Bool

    var body: some View {
        if viewModel.stepType == StepType.import {
            RecognizeView(viewModel: viewModel.recognizeViewModel)
        } else {
            VStack {
                Form {
                    if !viewModel.fields.isEmpty {
                        Section(header: Text(viewModel.stepType.title.localized())) {
                            ForEach(Array(viewModel.fields.keys.filter { !viewModel.predefinedOptions.keys.contains($0) }).sorted { $0.localized() > $1.localized() }, id: \.self) { field in
                                HStack {
                                    Text(field.localized())
                                        .frame(width: 150, alignment: .leading)
                                    TextField("\("请输入".localized())\(field.localized())", text: Binding(
                                        get: { viewModel.fields[field] ?? "" },
                                        set: { viewModel.setField(field, value: $0) }
                                    ))
                                    .frame(maxWidth: .infinity)
                                }
                            }
                            ForEach(Array(viewModel.predefinedOptions.keys).sorted { $0 > $1 }, id: \.self) { field in
                                if let options = viewModel.predefinedOptions[field] {
                                    Picker(selection: Binding<String?>(
                                        get: { viewModel.fields[field]?.isEmpty ?? true ? nil : viewModel.fields[field] },
                                        set: { newValue in
                                            viewModel.setField(field, value: newValue ?? "")
                                        }
                                    )) {
                                        Text("Choose Please".localized()).tag(nil as String?)
                                        ForEach(options.indices, id: \.self) { index in
                                            Text(options[index].localized())
                                                .tag(options[index] as String?)
                                        }
                                    } label: {
                                        Text(field.localized())
                                    }
                                    .frame(maxWidth: .infinity)
                                }
                            }
                        }
                    }

                    // 自定义字段部分
                    if viewModel.showCustomFields {
                        Section(header: Text("Custom".localized())) {
                            VStack {
                                HStack {
                                    TextField("Goal".localized(), text: $newFieldKey)
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .focused($isKeyFocused)
                                    Text(":")
                                    TextField("Become friends".localized(), text: $newFieldValue)
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .focused($isValueFocused)
                                    Button(action: {
                                        viewModel.setCustomField(newFieldKey, value: newFieldValue)
                                        newFieldKey = ""
                                        newFieldValue = ""
                                    }) {
                                        Image(systemName: "plus.circle.fill")
                                    }
                                    .disabled(newFieldKey.isEmpty || newFieldValue.isEmpty)
                                }
                            }
                            // 显示已添加的自定义字段
                            ForEach(viewModel.customFields.keys.sorted(), id: \.self) { key in
                                HStack {
                                    Text(key)
                                    Text(":")
                                    Text(viewModel.customFields[key] ?? "")
                                    Spacer()
                                    Button(role: .destructive) {
                                        viewModel.historicalFields[key] = viewModel.customFields[key] // 保存字段
                                        viewModel.removeCustomField(key) // 删除字段
                                    } label: {
                                        Image(systemName: "xmark.circle.fill")
                                    }
                                }
                                .padding(.horizontal, AppThemes.padding / 2)
                                .listRowSeparator(.hidden)
                            }
                        }
                    }

                    // 显示从数据库中加载的历史字段
                    if !viewModel.historicalFields.keys.filter({ !viewModel.customFields.keys.contains($0) }).isEmpty {
                        Section(header: Text("History".localized())) {
                            ForEach(viewModel.historicalFields.keys.filter { !viewModel.customFields.keys.contains($0) }.sorted(), id: \.self) { key in
                                HStack {
                                    Text(key)
                                    Text(":")
                                    Text(viewModel.historicalFields[key] ?? "")
                                    Spacer()
                                    Button(action: {
                                        viewModel.setCustomField(key, value: viewModel.historicalFields[key] ?? "")
                                        viewModel.historicalFields.removeValue(forKey: key)
                                    }) {
                                        Text("使用".localized())
                                    }

                                    Image(systemName: "xmark.circle.fill")
                                        .foregroundColor(.red)
                                        .padding(5)
                                        .onTapGesture {
                                            viewModel.historicalFields.removeValue(forKey: key)
                                            viewModel.removeCustomField(key)
                                            ChatConfigDatabaseManager.shared.removeHistoricalFieldFromDatabase(key)
                                        }
                                }
                                .padding(.horizontal, AppThemes.padding / 2)
                                .listRowSeparator(.hidden)
                                .frame(maxWidth: .infinity)
                            }
                        }
                    }
                }
                .listStyle(.inset)
                .onChange(of: isKeyFocused) { focused in
                    if !focused, !newFieldKey.isEmpty, !newFieldValue.isEmpty {
                        viewModel.setCustomField(newFieldKey, value: newFieldValue)
                        newFieldKey = ""
                        newFieldValue = ""
                    }
                }
                .onChange(of: isValueFocused) { focused in
                    if !focused, !newFieldKey.isEmpty, !newFieldValue.isEmpty {
                        viewModel.setCustomField(newFieldKey, value: newFieldValue)
                        newFieldKey = ""
                        newFieldValue = ""
                    }
                }
            }
        }
    }
}
