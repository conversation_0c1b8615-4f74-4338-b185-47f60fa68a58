import Foundation
import OSLog
import WCDBSwift

private enum DatabaseTable: String {
    case stepOption = "StepOption"
    case multiStepFormViewModel = "MultiStepFormViewModel"
    case historyFields = "HistoryFields"
}

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "DatabaseManager")

class ChatConfigDatabaseManager {
    static let shared = ChatConfigDatabaseManager()
    var database: Database?
    private let databaseQueue = DispatchQueue(label: "com.sanva.chatadvisor.database", qos: .background)

    func setupDatabase() {
        databaseQueue.sync { [weak self] in
            guard let self else { return }
            let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first!
            guard let emailHash = AccountManager.shared.currentUser?.email.md5 else {
                logger.error("User email is nil.")
                return
            }
            database = Database(at: path + "/ChatConfigDatabase_\(emailHash).db")
            createTablesIfNeeded()
            initializeDefaultOptions()
        }
    }

    func closeDatabase() {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            database?.close()
            database = nil
        }
    }

    func cleanDatabase() {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            guard let path = database?.path else { return }
            do {
                try database?.close(onClosed: {
                    try FileManager.default.removeItem(atPath: path)
                })
            } catch {
                logger.error("Clean Database Error: \(error.localizedDescription)")
            }
        }
    }

    private func createTablesIfNeeded() {
        do {
            try database?.create(table: DatabaseTable.stepOption.rawValue, of: StepOption.self)
            try database?.create(table: DatabaseTable.multiStepFormViewModel.rawValue, of: MultiStepFormViewModel.self)
            try database?.create(table: DatabaseTable.historyFields.rawValue, of: StepOption.self)
            logger.info("Database tables created successfully.")
        } catch {
            logger.error("Database Error: \(error.localizedDescription)")
        }
    }

    func newForm(chatId: String) -> MultiStepFormViewModel {
        let stepFormId = UUID().uuidString
        return MultiStepFormViewModel(stepFormId: stepFormId, chatId: chatId).initailData()
    }

    func saveDefaultOptionsToDatabaseIfNeeds(_ defaultOptions: [PredefinedOptionKey: [String]]) {
        for (key, values) in defaultOptions {
            let optionsToSave = values.map { value in
                StepOption(key: key.rawValue, value: value)
            }
            databaseQueue.async { [weak self] in
                guard let self else { return }
                do {
                    try database?.insertOrReplace(optionsToSave, intoTable: DatabaseTable.stepOption.rawValue)
                } catch {
                    logger.error("Failed to save default options to database: \(error.localizedDescription)")
                }
            }
        }
    }

    private func initializeDefaultOptions() {
        let defaultOptions: [PredefinedOptionKey: [String]] = [
            .relationshipType: [
                "Friend", "Family", "Colleague", "Stranger", "Neighbor",
                "Classmate", "Teammate", "Acquaintance", "Mentor", "Mentee",
                "Business Partner", "Romantic Partner", "Spouse", "Ex-Partner",
                "Sibling", "Parent", "Child", "Grandparent", "Grandchild", "In-law",
                "Coworker", "Boss", "Subordinate", "Client", "Tutor", "Roommate",
                "Lab Partner", "Study Buddy", "Project Teammate", "Online Friend",
                "Pen Pal", "Travel Companion", "Sports Teammate", "Doctor", "Patient",
                "Customer Service", "Supplier", "Landlord", "Tenant", "Business Competitor",
            ],
            .relationshipStatus: [
                "Close", "Casual", "Distant", "Professional", "Complicated",
                "Trusted", "Intimate",
            ],
            .preferredChatStyle: [
                "Formal", "Casual", "Friendly", "Professional", "Humorous",
                "Direct", "Empathetic", "Supportive", "Inquisitive",
            ],
            .preferredResponseLength: [
                "Short", "Medium", "Long", "Detailed",
            ],
        ]
        saveDefaultOptionsToDatabaseIfNeeds(defaultOptions)
    }

    func loadOptionsFromDatabase() -> [StepOption] {
        do {
            return try database?.getObjects(fromTable: DatabaseTable.stepOption.rawValue) ?? []
        } catch {
            logger.error("Failed to load options from database: \(error.localizedDescription)")
            return []
        }
    }

    func saveMultiStepFormViewModelToDatabase(_ multiStepFormViewModel: MultiStepFormViewModel) {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.insertOrReplace([multiStepFormViewModel], intoTable: DatabaseTable.multiStepFormViewModel.rawValue)
            } catch {
                logger.error("Failed to save multi step form view model: \(error.localizedDescription)")
            }
        }
    }

    func loadMultiStepFormViewModelFromDatabase(chatId: String) -> MultiStepFormViewModel? {
        do {
            return try database?.getObject(fromTable: DatabaseTable.multiStepFormViewModel.rawValue, where: MultiStepFormViewModel.Properties.chatId == chatId)
        } catch {
            logger.error("Failed to load multi step form view model: \(error.localizedDescription)")
            return nil
        }
    }

    func loadMultiStepFormViewModelFromDatabase(stepFormId: String) -> MultiStepFormViewModel? {
        do {
            return try database?.getObject(fromTable: DatabaseTable.multiStepFormViewModel.rawValue, where: MultiStepFormViewModel.Properties.stepFormId == stepFormId)
        } catch {
            logger.error("Failed to load multi step form view model: \(error.localizedDescription)")
            return nil
        }
    }

    func saveHistoricalFieldsToDatabase(_ historicalFields: [String: String]) {
        let fieldsToSave = historicalFields.map { key, value in
            StepOption(key: key, value: value)
        }

        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.insertOrReplace(fieldsToSave, intoTable: DatabaseTable.historyFields.rawValue)
            } catch {
                logger.error("Failed to save historical fields to database: \(error.localizedDescription)")
            }
        }
    }

    func removeHistoricalFieldFromDatabase(_ key: String) {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.delete(fromTable: DatabaseTable.historyFields.rawValue, where: StepOption.Properties.key == key)
            } catch {
                logger.error("Failed to remove historical field from database: \(error.localizedDescription)")
            }
        }
    }

    func loadHistoricalFields() -> [String: String] {
        do {
            let options: [StepOption] = try database?.getObjects(fromTable: DatabaseTable.historyFields.rawValue) ?? []
            var fields: [String: String] = [:]
            for option in options {
                fields[option.key] = option.value
            }
            return fields
        } catch {
            logger.error("Failed to load historical fields from database: \(error.localizedDescription)")
            return [:]
        }
    }
}

extension ChatConfigDatabaseManager {
    func searchChats(keyword: String, archive _: Bool = false) async -> [Chat] {
        let searchKeyword = "%\(keyword)%"
        var chatIds: [String] = []

        databaseQueue.sync {
            do {
                // 创建查询条件
                let condition = StepViewModel.Properties.fields.like(searchKeyword) ||
                    StepViewModel.Properties.customFields.like(searchKeyword)

                // 查询数据库
                if let results: [MultiStepFormViewModel] = try database?.getObjects(
                    fromTable: DatabaseTable.multiStepFormViewModel.rawValue,
                    where: condition
                ) {
                    chatIds = results.map(\.chatId)
                }
            } catch {
                logger.error("Failed to search chats in database: \(error.localizedDescription)")
            }
        }

        // 通过 AdvisorDatabaseManager 查找对应的 Chat 对象
        var chats: [Chat] = []
        for chatId in chatIds {
            if let chat = await AdvisorDatabaseManager.shared.fetchChat(id: chatId) {
                chats.append(chat)
            }
        }

        return chats
    }
}
