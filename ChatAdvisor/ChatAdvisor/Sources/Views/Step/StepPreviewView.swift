//
//  StepPreviewView.swift
//  ChatAdvisor
//
//  Created by md on 2024/7/12.
//

import Foundation
import Localize_Swift
import SwiftUI

struct StepPreviewView: View {
    @ObservedObject var viewModel: StepViewModel

    var body: some View {
        VStack {
            // 展示已填写的标准字段
            if !viewModel.fields.isEmpty {
                Section(header: Text(viewModel.stepType.title.localized())) {
                    ForEach(Array(viewModel.fields.keys).sorted(), id: \.self) { field in
                        if let value = viewModel.fields[field], !value.isEmpty {
                            HStack {
                                Text(field.localized())
                                    .frame(width: 150, alignment: .leading)
                                Text(value.localized())
                                Spacer()
                            }
                        }
                    }
                }
                .padding(AppThemes.padding / 2)
            }

            // 展示自定义字段
            if !viewModel.customFields.isEmpty {
                Section(header: Text("自定义".localized())) {
                    ForEach(Array(viewModel.customFields.keys).sorted(), id: \.self) { key in
                        if let value = viewModel.customFields[key], !value.isEmpty {
                            HStack {
                                Text(key.localized())
                                    .frame(width: 150, alignment: .leading)
                                Text(value.localized())
                                Spacer()
                            }
                        }
                    }
                }
                .padding(AppThemes.padding / 2)
            }

            // 展示历史字段
            if !viewModel.historicalFields.isEmpty {
                Section(header: Text("History".localized())) {
                    ForEach(Array(viewModel.historicalFields.keys).sorted(), id: \.self) { key in
                        if let value = viewModel.historicalFields[key], !value.isEmpty {
                            HStack {
                                Text(key.localized())
                                    .frame(width: 150, alignment: .leading)
                                Text(value.localized())
                                Spacer()
                            }
                        }
                    }
                }
                .padding(AppThemes.padding / 2)
            }
        }
        .padding(AppThemes.padding / 2)
    }
}
