//
//  StepProtocol.swift
//  test
//
//  Created by md on 2024/7/5.
//

import SwiftUI
import WCDBSwift

final class StepOption: TableCodable, Codable {
    var key: String = ""
    var value: String = ""

    enum CodingKeys: String, CodingTableKey {
        typealias Root = StepOption
        case key
        case value

        static let objectRelationalMapping = TableBinding(CodingKeys.self) {
            BindMultiPrimary(key.asIndex(orderBy: .descending), value)
        }
    }

    init(key: String, value: String) {
        self.key = key
        self.value = value
    }

    required init() {}
}

protocol Step {
    var isComplete: Bool { get }
    var isInProgress: Bool { get }
    var stepType: StepType { get }
//    var title: String { set get }
    var fields: [String: String] { get }
    var predefinedOptions: [String: [String]] { get }
}

enum StepType: String, CaseIterable, Codable, TableCodable {
    case information = "Information"
    case preferences = "Preferences"
    case emotion = "Emotion"
    case custom = "Custom"
    case `import` = "Chat_history"

    var fields: [String] {
        switch self {
        case .information:
            ["Your Age", "Chat Partner's Age", "Known Since"]
        case .preferences:
            ["Topics", "Goal"]
        case .emotion:
            ["Chat Partner's Emotion", "Preferred Emotion in Responses"]
        case .custom, .import:
            []
        }
    }

    var predefinedOptions: [String] {
        switch self {
        case .information:
            ["Relationship_Type", "Relationship_Status"]
        case .preferences:
            ["Preferred_Chat_Style", "Preferred_Response_Length"]
        case .emotion, .custom, .import:
            []
        }
    }

    var title: String {
        switch self {
        case .information:
            "Information"
        case .preferences:
            "Preferences"
        case .emotion:
            "Emotion"
        case .custom:
            "Custom"
        case .import:
            "Chat_history"
        }
    }

    enum CodingKeys: String, CodingTableKey {
        typealias Root = StepType
        case information
        case preferences
        case emotion
        case custom
        case `import`

        static let objectRelationalMapping = TableBinding(CodingKeys.self)
    }
}
