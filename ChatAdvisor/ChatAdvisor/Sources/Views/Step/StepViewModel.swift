import Combine
import Foundation
import SwiftUI
import WCDBSwift

final class StepViewModel: ObservableObject, Step, Codable {
    weak var stepFormViewModel: MultiStepFormViewModel? {
        didSet {
            recognizeViewModel.stepFormViewModel = stepFormViewModel
        }
    }

    var chatId: String
    var stepFormId: String = ""
    var stepType: StepType = .information {
        didSet {
            if stepType == .import {
                cancellables.forEach { $0.cancel() }
                recognizeViewModel.objectWillChange
                    .sink { [weak self] _ in
                        self?.objectWillChange.send()
                    }
                    .store(in: &cancellables)
            }
        }
    }

    private var title: String = ""
    lazy var recognizeViewModel: RecognizeViewModel = .init(chatId: chatId)
    @Published var disableComplete = false
    @Published var fields: [String: String] = [:]
    @Published var predefinedOptions: [String: [String]] = [:]
    @Published var customFields: [String: String] = [:]
    @Published var historicalFields: [String: String] = [:]
    @Published var showCustomFields: Bool = false {
        didSet {
            if showCustomFields == false { return }
            DispatchQueue.global().async { [weak self] in
                guard let self else { return }
                let data = loadHistoricalFields()
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    historicalFields = data
                }
            }
        }
    }

    private var cancellables = Set<AnyCancellable>()

    var isComplete: Bool {
        switch stepType {
        case .import:
            recognizeViewModel.recognizedTextsByImage.count > 0
        default:
            if showCustomFields {
                !fields.values.contains(where: \.isEmpty) && !customFields.values.contains(where: \.isEmpty) && customFields.keys.count > 0
            } else {
                !fields.values.contains(where: \.isEmpty)
            }
        }
    }

    var isInProgress: Bool {
        switch stepType {
        case .import:
            recognizeViewModel.recognizedTextsByImage.count == 0 && recognizeViewModel.selectedImagesData.count > 0
        default:
            if showCustomFields {
                fields.values.contains(where: { !$0.isEmpty }) && !isComplete
            } else {
                fields.values.contains(where: { !$0.isEmpty })
            }
        }
    }

    func setField(_ key: String, value: String) {
        fields[key] = value
    }

    func setPredefinedOptions(_ key: String, options: [String]) {
        predefinedOptions[key] = options
    }

    func setCustomField(_ key: String, value: String) {
        customFields[key] = value
        setHistoricalField(key, value: value)
    }

    func removeCustomField(_ key: String) {
        customFields.removeValue(forKey: key)
    }

    func setHistoricalField(_ key: String, value: String) {
        historicalFields[key] = value
        ChatConfigDatabaseManager.shared.saveHistoricalFieldsToDatabase(historicalFields)
    }

    func loadHistoricalFields() -> [String: String] {
        ChatConfigDatabaseManager.shared.loadHistoricalFields()
    }

//    func removeHistoricalField(_ key: String) {
//        historicalFields.removeValue(forKey: key)
//        ChatConfigDatabaseManager.shared.saveHistoricalFieldsToDatabase(historicalFields)
//    }

    // Codable
    required init(chatId: String) {
        self.chatId = chatId
    }

    deinit {
        cancellables.forEach { $0.cancel() }
    }

    init(chatId: String, stepFormId: String, title: String, fields: [String: String], predefinedOptions: [String: [String]], customFields: [String: String], showCustomFields: Bool, historicalFields: [String: String] = [:], recognizeViewModel: RecognizeViewModel) {
        self.chatId = chatId
        self.stepFormId = stepFormId
        self.title = title
        self.fields = fields
        // {{ AURA-X: Fix - 恢复预定义选项的保存，确保选项字段在重新加载时保持选择器形式. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
        self.predefinedOptions = predefinedOptions
        self.customFields = customFields
        self.showCustomFields = showCustomFields
        self.historicalFields = historicalFields
        self.recognizeViewModel = recognizeViewModel
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(chatId, forKey: .chatId)
        try container.encode(stepType, forKey: .stepType)
        try container.encode(stepFormId, forKey: .stepFormId)
        try container.encode(title, forKey: .title)
        try container.encode(fields, forKey: .fields)
        // {{ AURA-X: Fix - 序列化预定义选项，确保选项字段配置在数据库中正确保存. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
        try container.encode(predefinedOptions, forKey: .predefinedOptions)
        try container.encode(customFields, forKey: .customFields)
        try container.encode(showCustomFields, forKey: .showCustomFields)
        try container.encode(historicalFields, forKey: .historicalFields)
        try container.encode(recognizeViewModel, forKey: .recognizeViewModel)
    }

    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        chatId = try container.decode(String.self, forKey: .chatId)
        stepType = try container.decode(StepType.self, forKey: .stepType)
        stepFormId = try container.decode(String.self, forKey: .stepFormId)
        title = try container.decode(String.self, forKey: .title)
        fields = try container.decode([String: String].self, forKey: .fields)
        // {{ AURA-X: Fix - 反序列化预定义选项，确保从数据库加载时选项字段保持选择器形式. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
        predefinedOptions = try container.decodeIfPresent([String: [String]].self, forKey: .predefinedOptions) ?? [:]
        customFields = try container.decode([String: String].self, forKey: .customFields)
        showCustomFields = try container.decode(Bool.self, forKey: .showCustomFields)
        historicalFields = try container.decode([String: String].self, forKey: .historicalFields)
        recognizeViewModel = try container.decode(RecognizeViewModel.self, forKey: .recognizeViewModel)
    }
}

extension StepViewModel: TableCodable {
    enum CodingKeys: String, CodingTableKey {
        typealias Root = StepViewModel
        case chatId
        case stepType
        case stepFormId
        case title
        case fields
        // {{ AURA-X: Fix - 恢复预定义选项的序列化键，确保数据库正确保存选项配置. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
        case predefinedOptions
        case customFields
        case showCustomFields
        case historicalFields
        case recognizeViewModel

        static let objectRelationalMapping = TableBinding(CodingKeys.self)
    }
}

extension StepViewModel {
    func deepCopy() -> StepViewModel? {
        guard let data = try? JSONEncoder().encode(self),
              let copy = try? JSONDecoder().decode(StepViewModel.self, from: data)
        else {
            return nil
        }
        return copy
    }
}
