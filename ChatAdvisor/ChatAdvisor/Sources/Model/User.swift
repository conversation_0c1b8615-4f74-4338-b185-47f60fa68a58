//
//  User.swift
//
//
//  Created by zwt on 2024/4/10.
//

import Foundation

struct AppleInfo: Codable {
    let iss: String
    let aud: String
    let exp: Int
    let iat: Int
    let sub: String
    let cHash: String
    let email: String
    let emailVerified: Bool
    let isPrivateEmail: Bool?
    let authTime: Int
    let nonceSupported: Bool

    enum CodingKeys: String, CodingKey {
        case iss
        case aud
        case exp
        case iat
        case sub
        case cHash = "c_hash"
        case email
        case emailVerified = "email_verified"
        case isPrivateEmail = "is_private_email"
        case authTime = "auth_time"
        case nonceSupported = "nonce_supported"
    }
}

struct User: Codable, Equatable {
    var userId: String
    var username: String?
    var password: String?
    var email: String
    var fullName: String?
    var birthDate: Date?
    var gender: Gender?
    var phone: String?
    var address: Address?
    var language: String?
    var timeZone: String?
    var occupation: String?
    var company: String?
    var allergies: [String]?
    var medicalConditions: [String]?
    var externalAccounts: ExternalAccounts?
    var token: String?
    var balance: Double = 0

    enum Gender: String, Codable {
        case male = "Male"
        case female = "Female"
        case other = "Other"
    }

    struct Address: Codable {
        var street: String?
        var city: String?
        var state: String?
        var country: String?
        var postalCode: String?
    }

    struct ExternalAccounts: Codable {
        var weChatId: String?
        var qqId: String?
        var appleInfo: AppleInfo?

        enum CodingKeys: String, CodingKey {
            case weChatId
            case qqId
            case appleInfo
        }
    }

    static func == (lhs: User, rhs: User) -> Bool {
        lhs.email == rhs.email && lhs.balance == rhs.balance && lhs.token == rhs.token && lhs.username == rhs.username && lhs.password == rhs.password
    }
}
