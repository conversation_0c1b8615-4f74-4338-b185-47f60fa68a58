//
//  OpenAIModel.swift
//  JunShi
//
//  Created by md on 2024/4/29.
//

import Foundation
import WCDBSwift

// 定义 finish_reason 枚举
enum FinishReason: String, Codable, ColumnCodable {
    case stop
    case length
    case contentFilter = "content_filter"
    case none = "null"
    case error
    case noBalance = "no_balance"

    // Codable
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let value = try container.decode(String.self)
        self = FinishReason(rawValue: value) ?? .none
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        try container.encode(rawValue)
    }

    // CodingKeys
    enum CodingKeys: String, CodingKey {
        case stop, length, contentFilter = "content_filter", none, error, noBalance = "no_balance"
    }

    init?(with value: WCDBSwift.Value) {
        self.init(rawValue: value.stringValue)
    }

    func archivedValue() -> WCDBSwift.Value {
        WCDBSwift.Value(rawValue)
    }

    // 定义存储到数据库中的数据类型
    static var columnType: ColumnType {
        .text
    }
}

// openai 错误模型
// 定义顶级结构体来对应整个 JSON 响应
struct ErrorResponse: Codable {
    let error: ErrorDetail
}

// 定义错误详情的结构体
struct ErrorDetail: Codable {
    let message: String
    let type: String
    let param: String?
    let code: String
}

// openai 响应模型
// 定义顶级结构体
struct ChatCompletionChunk: Codable, Equatable {
    let id: String
    let object: String?
    let created: Int?
    let model: String?
    let systemFingerprint: String?
    let choices: [Choice]

    enum CodingKeys: String, CodingKey {
        case id, object, created, model, systemFingerprint = "system_fingerprint", choices
    }

    static func == (lhs: ChatCompletionChunk, rhs: ChatCompletionChunk) -> Bool {
        lhs.id == rhs.id
    }
}

struct Choice: Codable {
    let index: Int?
    let delta: Delta?
    let logprobs: String?
    let finishReason: FinishReason?

    enum CodingKeys: String, CodingKey {
        case index, delta, logprobs, finishReason = "finish_reason"
    }
}

struct Delta: Codable {
    let role: String?
    let content: String?
}

struct Question: Codable, Identifiable {
    var id = UUID()
    let sketch: String
    let content: String
    let question: String

    // codable
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        sketch = try container.decode(String.self, forKey: .sketch)
        content = try container.decode(String.self, forKey: .content)
        question = try container.decode(String.self, forKey: .question)
    }
}
