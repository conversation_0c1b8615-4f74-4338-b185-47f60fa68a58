import Foundation
import SwiftUI

import WCDBSwift

struct ChatsModel: Identifiable, Codable, Equatable, TableCodable {
    let id: String
    let modelName: String
    let inPrice: Float
    let outPrice: Float
    let count: Int
    let alias: String
    let intro: String

    static let `default` = ChatsModel(id: "gpt-4o-mini", modelName: "gpt-4o-mini", inPrice: 5, outPrice: 5, count: 1000, alias: "Chat", intro: "")

    init(id: String, modelName: String, inPrice: Float, outPrice: Float, count: Int, alias: String, intro: String) {
        self.id = id
        self.modelName = modelName
        self.inPrice = inPrice
        self.outPrice = outPrice
        self.count = count
        self.alias = alias
        self.intro = intro
    }

    static var customColumnMapping: [CodingKeys: String]? {
        [
            .id: "id",
            .modelName: "modelName",
            .inPrice: "inPrice",
            .outPrice: "outPrice",
            .count: "count",
            .alias: "alias",
            .intro: "intro",
        ]
    }

    enum CodingKeys: String, CodingTableKey {
        typealias Root = ChatsModel
        static let objectRelationalMapping = TableBinding(CodingKeys.self) {
            BindColumnConstraint(id, isPrimary: true, isUnique: true)
        }

        case id
        case modelName
        case inPrice
        case outPrice
        case count
        case alias
        case intro
    }

    // codable
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        modelName = try container.decode(String.self, forKey: .modelName)
        inPrice = try container.decode(Float.self, forKey: .inPrice)
        outPrice = try container.decode(Float.self, forKey: .outPrice)
        count = try container.decode(Int.self, forKey: .count)
        alias = try container.decode(String.self, forKey: .alias)
        intro = try container.decode(String.self, forKey: .intro)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(modelName, forKey: .modelName)
        try container.encode(inPrice, forKey: .inPrice)
        try container.encode(outPrice, forKey: .outPrice)
        try container.encode(count, forKey: .count)
        try container.encode(alias, forKey: .alias)
        try container.encode(intro, forKey: .intro)
    }
}
