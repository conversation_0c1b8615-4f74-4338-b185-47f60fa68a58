//
//  RecognizedText.swift
//  ChatAdvisor
//
//  Created by md on 2024/7/4.
//

import Foundation
import WCDBSwift

struct RecognizedText: Hashable, TableCodable, Codable {
    let string: String
    var boundingBox: CGRect

    // Keys for TableCodable
    enum CodingKeys: String, CodingTableKey {
        typealias Root = RecognizedText

        case string
        case minX
        case minY
        case maxX
        case maxY

        static let objectRelationalMapping = TableBinding(CodingKeys.self) {
            BindMultiPrimary(string, minX, minY, maxX, maxY)
        }
    }

    // Properties for Codable and TableCodable
    var minX: Double {
        Double(boundingBox.minX)
    }

    var minY: Double {
        Double(boundingBox.minY)
    }

    var maxX: Double {
        Double(boundingBox.maxX)
    }

    var maxY: Double {
        Double(boundingBox.maxY)
    }

    init(string: String, boundingBox: CGRect) {
        self.string = string
        self.boundingBox = boundingBox
    }

    // Custom initializer for decoding
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        string = try container.decode(String.self, forKey: .string)
        let minX = try container.decode(Double.self, forKey: .minX)
        let minY = try container.decode(Double.self, forKey: .minY)
        let maxX = try container.decode(Double.self, forKey: .maxX)
        let maxY = try container.decode(Double.self, forKey: .maxY)
        boundingBox = CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
    }

    // Custom encoding
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(string, forKey: .string)
        try container.encode(minX, forKey: .minX)
        try container.encode(minY, forKey: .minY)
        try container.encode(maxX, forKey: .maxX)
        try container.encode(maxY, forKey: .maxY)
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(string)
        hasher.combine(boundingBox.minX)
        hasher.combine(boundingBox.minY)
        hasher.combine(boundingBox.maxX)
        hasher.combine(boundingBox.maxY)
    }
}
