//
//  File.swift
//
//
//  Created by zwt on 2024/4/9.
//
import Alamofire
import Foundation
import Moya

struct NetworkResponse<T: Codable>: Codable {
    let code: Int
    let message: String?
    let data: T?

    var localizedDescription: String {
        // self to json
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        if let data = try? encoder.encode(self) {
            let json = String(data: data, encoding: .utf8)!
            return json
        } else {
            return "\(self)"
        }
    }

    var isSuccess: Bool {
        // 200 到 400 之间的为成功
        code >= 200 && code < 400
    }
}
