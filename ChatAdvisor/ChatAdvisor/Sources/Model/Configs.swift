//
//  Configs.swift
//  帮聊
//
//  Created by zweiteng on 2024/6/2.
//

import Foundation

// MARK: - 版本控制相关数据结构

/// 版本控制信息
struct VersionControl: Codable {
    let needUpdate: Bool                    // 是否需要更新
    let updateType: UpdateType              // 更新类型
    let latestVersion: String               // 最新版本号
    let minimumVersion: String              // 最低支持版本号
    let updateMessage: String               // 更新提示消息
    let downloadUrl: String?                 // 下载链接
    let versionCheckEnabled: Bool           // 是否启用版本检测

    /// 更新类型枚举
    enum UpdateType: String, Codable, CaseIterable {
        case none = "none"                  // 无需更新
        case optional = "optional"          // 可选更新
        case force = "force"                // 强制更新

        /// 是否为强制更新
        var isForceUpdate: Bool {
            return self == .force
        }

        /// 是否需要更新
        var needsUpdate: Bool {
            return self != .none
        }
    }

    /// 默认版本控制配置
    static let `default` = VersionControl(
        needUpdate: false,
        updateType: .none,
        latestVersion: "1.0.0",
        minimumVersion: "1.0.0",
        updateMessage: "发现新版本，建议立即更新以获得更好的体验",
        downloadUrl: "APP_STORE_URL_PLACEHOLDER",
        versionCheckEnabled: true
    )
}

struct Configs: Codable {
    let privacyPolicy: String
    let termsOfService: String
    let appVersion: String
    let supportEmail: String
    let featureFlags: [String: Bool]
    let mainSolgan: [String]
    let registerSolgan: [String]
    let emailLoginSolgan: [String]
    let rechargeMessages: [String]
    let hideMessage: [String]
    let rechargeDescription: String
    var promotCloud: String?
    var promotLocal: String?
    let compressRate: Double

    // 版本控制相关属性
    let versionControl: VersionControl?

    static let `default` = Configs(
        privacyPolicy: "https://advisor.sanva.tk/privacy.html",
        termsOfService: "https://advisortest.sanva.tk/userTerms.html",
        appVersion: "1.0.0",
        supportEmail: "<EMAIL>",
        featureFlags: [:],
        mainSolgan: Preferences.mainSolgan,
        registerSolgan: Preferences.registerSolgan,
        emailLoginSolgan: Preferences.emailLoginSolgan,
        rechargeMessages: Preferences.rechargeMessages,
        hideMessage: Preferences.hideMessages,
        rechargeDescription: Preferences.rechargeDescription,
        promotCloud: "promot_cloud".localized(),
        promotLocal: "promot_local".localized(),
        compressRate: 0.3,
        versionControl: VersionControl.default
    )
}
