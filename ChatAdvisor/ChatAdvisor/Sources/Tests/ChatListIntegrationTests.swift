//
//  ChatListIntegrationTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import XCTest
import Combine
@testable import ChatAdvisor

/// 聊天列表集成测试 - 验证重构后的组件集成
@MainActor
class ChatListIntegrationTests: XCTestCase {
    
    var viewModel: RefactoredChatListViewModel!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        try await super.setUp()
        
        // 设置测试环境
        cancellables = Set<AnyCancellable>()
        
        // 初始化测试数据库
        try await setupTestDatabase()
        
        // 创建ViewModel
        viewModel = RefactoredChatListViewModel(isArchived: false)
        
        // 启用调试模式
        DataFlowDebugger.shared.enable()
        // ChatListPerformanceMonitor已移除
    }
    
    override func tearDown() async throws {
        cancellables?.forEach { $0.cancel() }
        cancellables = nil
        viewModel = nil
        
        // 清理测试数据
        try await cleanupTestDatabase()
        
        try await super.tearDown()
    }
    
    // MARK: - 数据流集成测试
    
    func testDataFlowIntegration() async throws {
        // 测试完整的数据流：Repository -> DataSource -> ViewModel -> UI
        
        let expectation = XCTestExpectation(description: "数据流集成测试")
        
        // 监听数据变化
        viewModel.$chats
            .dropFirst() // 跳过初始空值
            .sink { chats in
                XCTAssertFalse(chats.isEmpty, "应该加载到聊天数据")
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // 执行初始加载
        viewModel.performInitialLoad()
        
        await fulfillment(of: [expectation], timeout: 5.0)
        
        // 验证数据流事件
        let events = DataFlowDebugger.shared.getAllEvents()
        XCTAssertFalse(events.isEmpty, "应该记录数据流事件")
        
        // 性能监控已移除
        // let stats = ChatListPerformanceMonitor.shared.getAllStats()
        // XCTAssertFalse(stats.isEmpty, "应该记录性能统计")
    }
    
    func testSearchIntegration() async throws {
        // 测试搜索功能的完整集成
        
        // 先加载初始数据
        viewModel.performInitialLoad()
        
        // 等待加载完成
        try await waitForCondition {
            self.viewModel.isInitialLoadCompleted
        }
        
        let searchExpectation = XCTestExpectation(description: "搜索集成测试")
        
        // 监听搜索结果
        viewModel.$showSearchResults
            .filter { $0 } // 只关注显示搜索结果的情况
            .sink { _ in
                searchExpectation.fulfill()
            }
            .store(in: &cancellables)
        
        // 执行搜索
        viewModel.searchText = "测试"
        
        await fulfillment(of: [searchExpectation], timeout: 3.0)
        
        // 验证搜索状态
        XCTAssertTrue(viewModel.showSearchResults, "应该显示搜索结果")
        XCTAssertFalse(viewModel.isSearching, "搜索应该已完成")
    }
    
    func testErrorHandlingIntegration() async throws {
        // 测试错误处理的完整集成
        
        // 模拟数据库错误
        let mockError = NSError(domain: "TestError", code: -1, userInfo: [NSLocalizedDescriptionKey: "模拟错误"])
        
        let errorExpectation = XCTestExpectation(description: "错误处理集成测试")
        
        // 监听错误状态
        viewModel.$hasError
            .filter { $0 } // 只关注有错误的情况
            .sink { _ in
                errorExpectation.fulfill()
            }
            .store(in: &cancellables)
        
        // 触发错误（这里需要根据实际实现调整）
        // 例如：断开数据库连接或模拟网络错误
        
        await fulfillment(of: [errorExpectation], timeout: 3.0)
        
        // 验证错误状态
        XCTAssertTrue(viewModel.hasError, "应该显示错误状态")
        XCTAssertFalse(viewModel.errorMessage.isEmpty, "应该有错误消息")
    }
    
    // MARK: - 性能集成测试
    
    func testPerformanceIntegration() async throws {
        // 测试性能监控的集成
        
        let startTime = Date()
        
        // 执行多个操作
        viewModel.performInitialLoad()
        
        try await waitForCondition {
            self.viewModel.isInitialLoadCompleted
        }
        
        viewModel.searchText = "性能测试"
        
        try await waitForCondition {
            self.viewModel.showSearchResults || self.viewModel.searchResults.isEmpty
        }
        
        let endTime = Date()
        let totalTime = endTime.timeIntervalSince(startTime)
        
        // 验证性能
        XCTAssertLessThan(totalTime, 5.0, "总操作时间应该在5秒内")
        
        // 性能监控已移除
        // let stats = ChatListPerformanceMonitor.shared.getAllStats()
        // XCTAssertFalse(stats.isEmpty, "应该有性能统计数据")
        
        // 验证关键操作的性能已移除
        // let loadStats = stats.first { $0.operationName.contains("LoadInitialData") }
        // XCTAssertNotNil(loadStats, "应该有初始加载的性能统计")
        // XCTAssertLessThan(loadStats?.averageDuration ?? 10.0, 3.0, "初始加载应该在3秒内完成")
    }
    
    func testMemoryManagement() async throws {
        // 测试内存管理
        
        weak var weakViewModel: RefactoredChatListViewModel?
        
        autoreleasepool {
            let testViewModel = RefactoredChatListViewModel(isArchived: false)
            weakViewModel = testViewModel
            
            // 执行一些操作
            testViewModel.performInitialLoad()
        }
        
        // 等待一段时间让异步操作完成
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
        
        // 验证对象已被释放
        XCTAssertNil(weakViewModel, "ViewModel应该被正确释放")
    }
    
    // MARK: - 缓存集成测试
    
    func testCacheIntegration() async throws {
        // 测试缓存系统的集成
        
        // 第一次加载
        viewModel.performInitialLoad()
        
        try await waitForCondition {
            self.viewModel.isInitialLoadCompleted
        }
        
        let firstLoadChats = viewModel.chats
        XCTAssertFalse(firstLoadChats.isEmpty, "第一次加载应该有数据")
        
        // 创建新的ViewModel（模拟应用重启）
        let newViewModel = RefactoredChatListViewModel(isArchived: false)
        
        // 第二次加载（应该从缓存获取）
        let secondLoadStart = Date()
        newViewModel.performInitialLoad()
        
        try await waitForCondition {
            newViewModel.isInitialLoadCompleted
        }
        
        let secondLoadTime = Date().timeIntervalSince(secondLoadStart)
        let secondLoadChats = newViewModel.chats
        
        // 验证缓存效果
        XCTAssertEqual(firstLoadChats.count, secondLoadChats.count, "缓存的数据应该一致")
        XCTAssertLessThan(secondLoadTime, 1.0, "从缓存加载应该更快")
    }
    
    // MARK: - 并发安全测试
    
    func testConcurrencyIntegration() async throws {
        // 测试并发操作的安全性
        
        let operationCount = 10
        let expectations = (0..<operationCount).map { i in
            XCTestExpectation(description: "并发操作 \(i)")
        }
        
        // 并发执行多个操作
        await withTaskGroup(of: Void.self) { group in
            for i in 0..<operationCount {
                group.addTask { [weak self] in
                    guard let self = self else { return }
                    
                    if i % 2 == 0 {
                        self.viewModel.performInitialLoad()
                    } else {
                        self.viewModel.searchText = "并发测试\(i)"
                    }
                    
                    expectations[i].fulfill()
                }
            }
        }
        
        await fulfillment(of: expectations, timeout: 10.0)
        
        // 验证数据一致性
        XCTAssertFalse(viewModel.chats.isEmpty, "并发操作后应该有数据")
        XCTAssertFalse(viewModel.hasError, "并发操作不应该导致错误")
    }
    
    // MARK: - 辅助方法
    
    private func setupTestDatabase() async throws {
        // 设置测试数据库
        // 这里需要根据实际的数据库设置进行实现
        
        // 创建测试聊天数据
        let testChats = createTestChats()
        for chat in testChats {
            try await AdvisorDatabaseManager.shared.update(chat: chat)
        }
    }
    
    private func cleanupTestDatabase() async throws {
        // 清理测试数据
        // 这里需要根据实际的数据库清理进行实现
    }
    
    private func createTestChats() -> [Chat] {
        return (1...10).map { i in
            var chat = Chat(id: "test-chat-\(i)", title: "测试聊天\(i)")
            chat.messages = [
                ChatMessage(id: "msg-\(i)-1", chatId: chat.id, role: .user, content: "用户消息\(i)", isComplete: true),
                ChatMessage(id: "msg-\(i)-2", chatId: chat.id, role: .assistant, content: "助手回复\(i)", isComplete: true)
            ]
            return chat
        }
    }
    
    private func waitForCondition(timeout: TimeInterval = 3.0, condition: @escaping () -> Bool) async throws {
        let startTime = Date()
        
        while !condition() {
            if Date().timeIntervalSince(startTime) > timeout {
                throw XCTestError(.timeoutWhileWaiting)
            }
            
            try await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        }
    }
}

// MARK: - 性能基准测试
extension ChatListIntegrationTests {
    
    func testPerformanceBenchmark() async throws {
        // 性能基准测试
        
        measure {
            let expectation = XCTestExpectation(description: "性能基准测试")
            
            Task { @MainActor in
                self.viewModel.performInitialLoad()
                
                try await self.waitForCondition {
                    self.viewModel.isInitialLoadCompleted
                }
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    func testSearchPerformanceBenchmark() async throws {
        // 搜索性能基准测试
        
        // 先加载数据
        viewModel.performInitialLoad()
        try await waitForCondition {
            self.viewModel.isInitialLoadCompleted
        }
        
        measure {
            let expectation = XCTestExpectation(description: "搜索性能基准测试")
            
            Task { @MainActor in
                self.viewModel.searchText = "性能"
                
                try await self.waitForCondition {
                    self.viewModel.showSearchResults || self.viewModel.searchResults.isEmpty
                }
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 3.0)
        }
    }
}
