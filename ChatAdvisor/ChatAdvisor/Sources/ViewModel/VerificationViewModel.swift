//
//  VerificationViewModel.swift
//  JunShi
//
//  Created by md on 2024/4/28.
//

import Foundation
import OSLog
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "VerificationViewModel")

class VerificationViewModel: ObservableObject {
    let email: String
    let password: String
    @Published var codes: [String] = Array(repeating: "", count: 6)
    @Published var verifyCodes: String = ""
    @Published var toastMessage = ""
    @Published var isLoading = false
    @Published var verifyButtonDisable = true
    @Published var showToast = false
    @Published var isRequestError = false

    init(email: String, password: String) {
        self.email = email
        self.password = password
    }

    func verifyCode() {
        verifyButtonDisable = true
        guard !isLoading else { return }
        isLoading = true
        NetworkService.shared.requestMulti(AuthTarget.verifyEmailCode(email: email, code: verifyCodes, password: password)) { [weak self] (result: Result<NetworkResponse<User>, NetworkError>) in
            guard let self else { return }
            switch result {
            case let .success(response):
                if response.isSuccess {
                    successToast(response.message ?? "验证成功")
                    let user = response.data
                    AccountManager.shared.currentUser = user
                    AccountManager.shared.getBalance()
                    AccountManager.shared.afterLogin()

                } else {
                    failedToast(response.message ?? "验证失败")
                }
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isLoading = false
                    verifyButtonDisable = false
                }
                logger.info("verifyEmailCode successful: \(response.localizedDescription)")
            case let .failure(error):
                failedToast(error.errorMessage ?? "验证失败")
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isLoading = false
                    verifyButtonDisable = false
                }
                logger.error("verifyEmailCode failed: \(error.localizedDescription)")
            }
        }
    }

    func failedToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = true
            toastMessage = message
            showToast = true
        }
    }

    func successToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = false
            toastMessage = message
            showToast = true
        }
    }
}
