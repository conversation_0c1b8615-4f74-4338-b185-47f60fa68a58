//
//  AuthViewModel.swift
//  JunShi
//
//  Created by md on 2024/4/19.
//

import AuthenticationServices
import Foundation
import GoogleSignIn
import GoogleSignInSwift
import Moya
import OSLog
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "AuthViewModel")

class AuthViewModel: NSObject, ObservableObject {
    static let shared = AuthViewModel()

    @Published var email = ""
    @Published var password = ""
    @Published var fullName = ""
    @Published var toastMessage = ""
    @Published var isTimerActive = false
    @Published var isLoading = false
    @Published var showToast = false
    @Published var isRequestError = false
    @Published var loginButtonDisable = true
    @Published var isEmailValid: Bool = true
    @Published var isPasswordValid: Bool = true
    @Published var signInConfig: GIDConfiguration?
    @Published var isEditingDisabled = false



    let twitterApiKey = "*************************"
    let twitterApiSecret = "TmMqViKTVqgFAfTRwGNuXcKzbulPH7h4dPtUtudKE2DJyEJgUu"

    func handleAuthorizationAppleID(result: Result<ASAuthorization, Error>) {
        switch result {
        case let .success(authorization):
            if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
                loginWithApple(appleIDCredential)
            }
        case let .failure(error):
            // 处理错误情况
            logger.error("Authorization Failed: \(error)")
            isLoading = false
        }
    }

    // 登录功能
    func login() {
        let credentials = UserCredentials(email: email, password: password)

        login(credentials: credentials)
    }

    func login(credentials: UserCredentials) {
        guard email.isValidEmail else {
            failedToast("请输入正确的邮箱")
            return
        }
        guard password.count >= 6 else {
            failedToast("密码长度至少为6位")
            return
        }
        loginButtonDisable = true
        guard !isLoading else { return }
        isLoading = true
        NetworkService.shared.requestMulti(AuthTarget.login(credentials: credentials)) { [weak self] (result: Result<NetworkResponse<User>, NetworkError>) in
            guard let self else { return }
            switch result {
            case let .success(response):
                if response.isSuccess {
                    let user = response.data
                    AccountManager.shared.currentUser = user
                    getBalance()
                    AccountManager.shared.afterLogin()
                } else {
                    failedToast(response.message ?? "登录失败".localized())
                }
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isTimerActive = true
                    isLoading = false
                    loginButtonDisable = false
                }
                logger.info("Login successful: \(response.localizedDescription)")
            case let .failure(error):
                failedToast(error.errorMessage ?? "验证失败".localized())
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isLoading = false
                    loginButtonDisable = false
                }
                logger.error("Login failed: \(error.localizedDescription)")
            }
        }
    }

    func getBalance() {
        NetworkService.shared.requestMulti(PricingTarget.balance) { (result: Result<NetworkResponse<Balance>, NetworkError>) in
            switch result {
            case let .success(res):
                if res.isSuccess, let balance = res.data?.balance {
                    // todo fix me 会触发afterLogin,内部判断相同return
                    AccountManager.shared.currentUser?.balance = balance
                }
            case let .failure(error):
                logger.error("get user Balance error: \(error.localizedDescription)")
            }
        }
    }
    
    func handleLoginResult(result: Result<NetworkResponse<User>, NetworkError>) {
        switch result {
        case let .success(response):
            if response.isSuccess {
                let user = response.data
                AccountManager.shared.currentUser = user
                FirebaseManager.shared.logUserLogin(userId: user?.userId ?? "异常用户id")
                getBalance()
                AccountManager.shared.afterLogin()
            } else {
                failedToast(response.message ?? "登录失败".localized())
            }
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                isTimerActive = true
                isLoading = false
                loginButtonDisable = false
            }
            logger.info("Login successful: \(response.localizedDescription)")
        case let .failure(error):
            failedToast(error.errorMessage ?? "验证失败".localized())
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                isLoading = false
                loginButtonDisable = false
            }
            logger.error("Login failed: \(error.localizedDescription)")
        }
    }

    func failedToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = true
            toastMessage = message
            showToast = true
            isLoading = false
        }
    }

    func successToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = false
            toastMessage = message
            showToast = true
            isLoading = false
        }
    }
}

extension AuthViewModel {
    func handleGoogleSignIn() {
        isLoading = true
        if let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
           let plist = NSDictionary(contentsOfFile: path),
           let clientID = plist["CLIENT_ID"] as? String,
           let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let viewController = windowScene.windows.first?.rootViewController
        {
            GIDSignIn.sharedInstance.configuration = GIDConfiguration(clientID: clientID)
            GIDSignIn.sharedInstance.signIn(withPresenting: viewController) { [weak self] result, error in
                guard let self else { return }
                guard error == nil else {
                    self.failedToast(error?.localizedDescription ?? "登录失败".localized())
                    return
                }

                guard let user = result?.user,
                      let idToken = user.idToken?.tokenString
                else {
                    self.failedToast(error?.localizedDescription ?? "验证失败".localized())
                    return
                }

                NetworkService.shared.requestMulti(AuthTarget.loginWithGoogle(idToken: idToken), completion: handleLoginResult)
            }
        }
    }
}





extension AuthViewModel {
    var hasTwitterInstalled: Bool {
        guard let url = twitterApplication else {
            return false
        }
        return UIApplication.shared.canOpenURL(url)
    }

    var twitterApplication: URL? {
        URL(string: "twitterauth://authorize?consumer_key=\(twitterApiKey)&consumer_secret=\(twitterApiSecret)&oauth_callback=twitterkit-\(twitterApiKey)")
    }

    func handleTwitterSignIn() {
        isLoading = true
        guard hasTwitterInstalled, let url = twitterApplication else {
            return
        }
        UIApplication.shared.open(url, options: [:]) { [weak self] success in
            guard let self else { return }
            if !success {
                failedToast("验证失败".localized())
            }
            isLoading = false
        }
    }

    @discardableResult
    func twitterApplication(open url: URL) -> Bool {
        isLoading = true
        let isCanceled = (url.host == nil)
        if isCanceled {
            isLoading = false
            return false
        }
        let params = url.queryParams
        let secret = params["secret"]
        let token = params["token"]
        let _ = params["username"]
        if let token, let secret {
            NetworkService.shared.requestMulti(AuthTarget.loginWithTwitter(token: token, secret: secret), completion: handleLoginResult)
        } else {
            isLoading = false
            failedToast("验证失败".localized())
            return false
        }
        isLoading = false
        return true
    }
}

extension AuthViewModel: ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {
    func startSignInWithAppleFlow() {
        isLoading = true
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]

        let controller = ASAuthorizationController(authorizationRequests: [request])
        controller.delegate = self
        controller.presentationContextProvider = self
        controller.performRequests()
    }

    func authorizationController(controller _: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            loginWithApple(appleIDCredential)
        }
    }

    func authorizationController(controller _: ASAuthorizationController, didCompleteWithError error: Error) {
        isLoading = false
        // 处理错误情况
        print("Apple Sign in failed: \(error.localizedDescription)")
    }

    // MARK: - ASAuthorizationControllerPresentationContextProviding

    func presentationAnchor(for _: ASAuthorizationController) -> ASPresentationAnchor {
        // 返回当前应用的主窗口作为展示登录界面的锚点
        // 'windows' was deprecated in iOS 15.0: Use UIWindowScene.windows on a relevant window scene instead
        return (UIApplication.shared.connectedScenes.first as? UIWindowScene)?.windows.first ?? ASPresentationAnchor()
    }

//    func handleAuthorizationAppleID(result: Result<ASAuthorization, Error>) {
//        switch result {
//        case let .success(authorization):
//            if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
//                loginWithApple(appleIDCredential)
//            }
//        case let .failure(error):
//            // 处理错误情况
//            logger.error("Authorization Failed: \(error)")
//            isLoading = false
//        }
//    }

    func loginWithApple(_ appleIDCredential: ASAuthorizationAppleIDCredential) {
        // 验证必要的数据
        guard let identityTokenData = appleIDCredential.identityToken,
              let idToken = String(data: identityTokenData, encoding: .utf8) else {
            logger.error("Apple Sign In failed: Invalid identity token")
            failedToast("苹果登录失败：无效的身份令牌")
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                isLoading = false
                loginButtonDisable = false
            }
            return
        }

        logger.info("Starting Apple Sign In with token")

        NetworkService.shared.requestMulti(AuthTarget.loginWithApple(idToken: idToken)) { [weak self] (result: Result<NetworkResponse<User>, NetworkError>) in
            guard let self else { return }

            switch result {
            case let .success(response):
                if response.isSuccess {
                    guard var user = response.data else {
//                        self.logger.error("Apple login success but user data is nil")
                        self.failedToast("登录成功但用户数据异常，请重试")
                        DispatchQueue.main.async { [weak self] in
                            guard let self else { return }
                            isLoading = false
                            loginButtonDisable = false
                        }
                        return
                    }

                    // 验证用户数据的完整性
                    if user.userId.isEmpty || user.email.isEmpty {
//                        self.logger.error("Apple login: Invalid user data - userId: \(user.userId), email: \(user.email)")
                        self.failedToast("用户数据不完整，请重试")
                        DispatchQueue.main.async { [weak self] in
                            guard let self else { return }
                            isLoading = false
                            loginButtonDisable = false
                        }
                        return
                    }

                    // 设置用户信息
                    user.fullName = appleIDCredential.fullName?.givenName
                    AccountManager.shared.currentUser = user

//                    self.logger.info("Apple login successful for user: \(user.email)")
                    self.getBalance()
                    AccountManager.shared.afterLogin()
                } else {
                    let errorMessage = response.message ?? "登录失败".localized()
//                    self.logger.error("Apple login failed with message: \(errorMessage)")
                    self.failedToast(errorMessage)
                }

                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isTimerActive = true
                    isLoading = false
                    loginButtonDisable = false
                }

            case let .failure(error):
//                self.logger.error("Apple login network error: \(error)")

                // 根据错误类型提供更具体的错误信息
                let errorMessage: String
                switch error {
                case .lostConnect:
                    errorMessage = "网络连接失败，请检查网络后重试"
                case .timeout:
                    errorMessage = "网络请求超时，请稍后重试"
                case .hostError(let msg):
                    errorMessage = "服务器错误：\(msg ?? "服务器无响应")"
                case .badRequest(let msg):
                    errorMessage = "请求错误：\(msg ?? "请求参数有误")"
                case .cancelled:
                    errorMessage = "请求已取消"
                case .fileNotFound:
                    errorMessage = "请求的资源不存在"
                case .response(let code, let msg):
                    errorMessage = "服务器错误(\(code))：\(msg ?? "未知错误")"
                case .unknown:
                    errorMessage = "未知错误，请稍后重试"
                }

                self.failedToast(errorMessage)

                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isLoading = false
                    loginButtonDisable = false
                }
            }
        }
    }
}
