//
//  ChatUIStateManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import SwiftUI
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatUIStateManager")

/// AI输入状态
enum AITypingState {
    case idle
    case connecting
    case thinking
    case typing
}

/// 滚动位置
enum ScrollPosition {
    case top
    case middle
    case bottom
}

/// 滚动事件
enum ScrollEvent {
    case userScrolled(to: ScrollPosition)
    case messageAdded
    case messageUpdated
    case loadMoreTriggered
}

/// 聊天UI状态管理器
@MainActor
class ChatUIStateManager: ObservableObject {
    // MARK: - Published Properties
    @Published var isAnswering: Bool = false
    @Published var showTypingIndicator: Bool = false
    @Published var aiTypingState: AITypingState = .idle
    @Published var scrollPosition: ScrollPosition = .bottom
    @Published var shouldAutoScroll: Bool = true
    @Published var isLoadingInitialMessages: Bool = false
    @Published var showToast: Bool = false
    @Published var toastMessage: String = ""
    @Published var selectedMessage: ChatMessage?
    
    // MARK: - Private Properties
    private var lastScrollTime: Date = Date()
    private var scrollDebounceTimer: Timer?
    private let scrollDebounceInterval: TimeInterval = 0.3
    private var newMessageCount: Int = 0
    
    // MARK: - Public Methods
    
    /// 更新AI输入状态
    func updateAITypingState(_ state: AITypingState) {
        aiTypingState = state
        
        switch state {
        case .idle:
            isAnswering = false
            showTypingIndicator = false
        case .connecting:
            isAnswering = true
            showTypingIndicator = true
        case .thinking:
            isAnswering = true
            showTypingIndicator = true
        case .typing:
            isAnswering = true
            showTypingIndicator = false
        }
    }
    
    /// 处理滚动事件
    func handleScrollEvent(_ event: ScrollEvent) {
        switch event {
        case .userScrolled(let position):
            handleUserScroll(to: position)
        case .messageAdded:
            handleMessageAdded()
        case .messageUpdated:
            handleMessageUpdated()
        case .loadMoreTriggered:
            handleLoadMoreTriggered()
        }
    }
    
    /// 显示Toast消息
    func showToast(message: String, duration: TimeInterval = 2.0) {
        toastMessage = message
        showToast = true
        
        // 自动隐藏Toast
        DispatchQueue.main.asyncAfter(deadline: .now() + duration) { [weak self] in
            self?.hideToast()
        }
    }
    
    /// 隐藏Toast消息
    func hideToast() {
        showToast = false
        toastMessage = ""
    }
    
    /// 显示成功Toast
    func showSuccessToast(_ message: String) {
        showToast(message: "✅ \(message)")
    }
    
    /// 显示错误Toast
    func showErrorToast(_ message: String) {
        showToast(message: "❌ \(message)")
    }
    
    /// 显示警告Toast
    func showWarningToast(_ message: String) {
        showToast(message: "⚠️ \(message)")
    }
    
    /// 设置选中的消息
    func selectMessage(_ message: ChatMessage?) {
        selectedMessage = message
    }
    
    /// 开始加载初始消息
    func startLoadingInitialMessages() {
        isLoadingInitialMessages = true
        logger.debug("开始加载初始消息")
    }
    
    /// 完成加载初始消息
    func finishLoadingInitialMessages() {
        isLoadingInitialMessages = false
        logger.debug("完成加载初始消息")
    }
    
    /// 重置UI状态
    func resetUIState() {
        isAnswering = false
        showTypingIndicator = false
        aiTypingState = .idle
        scrollPosition = .bottom
        shouldAutoScroll = true
        isLoadingInitialMessages = false
        hideToast()
        selectedMessage = nil
        newMessageCount = 0
        
        logger.debug("UI状态已重置")
    }
    
    /// 处理消息数量变化
    func handleMessageCountChange(newCount: Int, completion: @escaping () -> Void) {
        // 防抖动处理
        scrollDebounceTimer?.invalidate()
        scrollDebounceTimer = Timer.scheduledTimer(withTimeInterval: scrollDebounceInterval, repeats: false) { _ in
            completion()
        }
    }
    
    /// 处理最后一条消息内容变化
    func handleLastMessageContentChange(newContent: String?, completion: @escaping () -> Void) {
        // 防抖动处理
        let now = Date()
        if now.timeIntervalSince(lastScrollTime) > scrollDebounceInterval {
            lastScrollTime = now
            completion()
        }
    }
    
    /// 清除新消息计数
    func clearNewMessages() {
        newMessageCount = 0
    }
    
    /// 检查是否应该自动滚动
    func shouldPerformAutoScroll() -> Bool {
        return shouldAutoScroll && scrollPosition == .bottom
    }
    
    /// 强制滚动到底部
    func forceScrollToBottom() {
        shouldAutoScroll = true
        scrollPosition = .bottom
    }
    
    /// 禁用自动滚动
    func disableAutoScroll() {
        shouldAutoScroll = false
    }
    
    /// 启用自动滚动
    func enableAutoScroll() {
        shouldAutoScroll = true
    }
    
    // MARK: - Private Methods
    
    private func handleUserScroll(to position: ScrollPosition) {
        scrollPosition = position
        
        // 用户主动滚动时，根据位置决定是否启用自动滚动
        switch position {
        case .bottom:
            shouldAutoScroll = true
        case .top, .middle:
            shouldAutoScroll = false
        }
    }
    
    private func handleMessageAdded() {
        newMessageCount += 1
        
        // 如果用户在底部，保持自动滚动
        if scrollPosition == .bottom {
            shouldAutoScroll = true
        }
    }
    
    private func handleMessageUpdated() {
        // 消息更新时，如果用户在底部且启用了自动滚动，则继续滚动
        if shouldAutoScroll && scrollPosition == .bottom {
            // 触发滚动
        }
        
        logger.debug("消息更新")
    }
    
    private func handleLoadMoreTriggered() {
        // 加载更多消息时，暂时禁用自动滚动
        let wasAutoScrollEnabled = shouldAutoScroll
        shouldAutoScroll = false
        
        // 加载完成后恢复自动滚动状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.shouldAutoScroll = wasAutoScrollEnabled
        }
        
        logger.debug("触发加载更多消息")
    }
}

/// 滚动管理器（保持向后兼容）
class ScrollManager: ObservableObject {
    @Published var shouldAutoScroll: Bool = true
    private var newMessageCount: Int = 0
    
    func clearNewMessages() {
        newMessageCount = 0
    }
    
    func addNewMessage() {
        newMessageCount += 1
    }
    
    func disableAutoScroll() {
        shouldAutoScroll = false
    }
    
    func enableAutoScroll() {
        shouldAutoScroll = true
    }
}
