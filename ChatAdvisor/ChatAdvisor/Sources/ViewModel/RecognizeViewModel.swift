import PhotosUI
import SwiftUI
import Vision
import WCDBSwift

struct DragState {
    var message: ChatMessage
    var translation: CGSize
}

final class RecognizeViewModel: ObservableObject, TableCodable, Codable {
    weak var stepFormViewModel: MultiStepFormViewModel? {
        didSet {
            stepFormViewModel?.recognizeViewModel = self
        }
    }

    var chatId: String
    @Published var selectedItemsLocal: [PhotosPickerItem] = []
    @Published var selectedItemsRemote: [PhotosPickerItem] = []
    @Published var selectedImagesData: [Data] = []
    @Published var recognizedTextsByImage: [[RecognizedText]] = []
    @Published var chatMessages: [[ChatMessage]] = []
    @Published var dragState: DragState?
    @Published var showPhotosPicker: Bool = true
    @Published var isFullScreen: Bool = false
    @Published var isProcessingImage: Bool = false
    @Published var selectedImageIndex: Int = 0
    @Published var itemIdentifiers: [String] = [] // New array for item identifiers
    @Published var isLocalRecognition = true {
        didSet {
            stepFormViewModel?.isLocalRecognition = isLocalRecognition
        }
    }

    // decodable
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
//        selectedImagesData = try container.decode([Data].self, forKey: .selectedImagesData)
        recognizedTextsByImage = try container.decode([[RecognizedText]].self, forKey: .recognizedTextsByImage)
        chatMessages = try container.decode([[ChatMessage]].self, forKey: .chatMessages)
        itemIdentifiers = try container.decode([String].self, forKey: .itemIdentifiers) // Decode itemIdentifiers
        chatId = try container.decode(String.self, forKey: .chatId)
    }

    // encodable
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
//        try container.encode(selectedImagesData, forKey: .selectedImagesData)
        try container.encode(recognizedTextsByImage, forKey: .recognizedTextsByImage)
        try container.encode(chatMessages, forKey: .chatMessages)
        try container.encode(itemIdentifiers, forKey: .itemIdentifiers) // Encode itemIdentifiers
        try container.encode(chatId, forKey: .chatId)
    }

    enum CodingKeys: String, CodingTableKey {
        typealias Root = RecognizeViewModel

//        case selectedImagesData
        case chatId
        case recognizedTextsByImage
        case chatMessages
        case itemIdentifiers // New coding key for itemIdentifiers

        static let objectRelationalMapping = TableBinding(CodingKeys.self) {
            BindMultiPrimary(itemIdentifiers) // Bind itemIdentifiers
        }
    }

    required init(chatId: String) {
        self.chatId = chatId
    }
}

extension RecognizeViewModel {
    // Method to create PhotosPickerItem from itemIdentifiers
    func createPhotosPickerItems(itemIdentifiers: [String]) -> [PhotosPickerItem] {
        var items: [PhotosPickerItem] = []
        for identifier in itemIdentifiers {
            let item = PhotosPickerItem(itemIdentifier: identifier)
            items.append(item)
        }
        return items
    }

    func recognizeText(from imageData: Data) async {
        guard let image = UIImage(data: imageData)?.cgImage else { return }

        let requestHandler = VNImageRequestHandler(cgImage: image, options: [:])
        let request = VNRecognizeTextRequest { request, _ in
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                guard let observations = request.results as? [VNRecognizedTextObservation] else { return }

                var recognizedStrings = [RecognizedText]()
                for observation in observations {
                    guard let topCandidate = observation.topCandidates(1).first else { continue }
                    let recognizedText = RecognizedText(string: topCandidate.string, boundingBox: observation.boundingBox)
                    recognizedStrings.append(recognizedText)
                }

                recognizedTextsByImage.append(recognizedStrings)
                generateChatMessages()
            }
        }
        request.recognitionLevel = .accurate
        request.automaticallyDetectsLanguage = true

        do {
            try requestHandler.perform([request])
        } catch {
            print("Failed to perform text recognition: \(error.localizedDescription)")
        }
    }

    func generateChatMessages() {
        var newChatMessages: [[ChatMessage]] = []

        for (imageIndex, recognizedTexts) in recognizedTextsByImage.enumerated() {
            let imageData = selectedImagesData[imageIndex]
            let image = UIImage(data: imageData)!
            var imageMessages: [ChatMessage] = []
            var combinedText = ""
            var previousBoundingBox: CGRect?

            // Calculate average height of all recognized texts' bounding boxes
            let averageHeight = recognizedTexts.map { $0.boundingBox.height + 0.025 }.reduce(0, +) / CGFloat(recognizedTexts.count)

            for (textIndex, recognizedText) in recognizedTexts.enumerated() {
                let currentBoundingBox = recognizedText.boundingBox

                // If the current text is close to the previous text vertically, combine them
                if let previousBoundingBox, abs(currentBoundingBox.minY - previousBoundingBox.maxY) < averageHeight {
                    combinedText += "\n" + recognizedText.string
                } else {
                    // If we have accumulated text, add it as a message
                    if !combinedText.isEmpty {
                        let role: Role = determineRole(from: previousBoundingBox!, imageSize: image.size)
                        let message = ChatMessage(
                            id: UUID().uuidString,
                            chatId: chatId,
                            role: role,
                            content: combinedText,
                            createdTime: Int64(Date().timeIntervalSince1970) - 100 + Int64(imageIndex * 10 + textIndex)
                        )
                        imageMessages.append(message)
                        combinedText = ""
                    }
                    combinedText = recognizedText.string
                }

                previousBoundingBox = currentBoundingBox
            }

            // Add the last accumulated message
            if !combinedText.isEmpty {
                let role: Role = determineRole(from: previousBoundingBox!, imageSize: image.size)
                let message = ChatMessage(
                    id: UUID().uuidString,
                    chatId: chatId,
                    role: role,
                    content: combinedText,
                    createdTime: Int64(Date().timeIntervalSince1970) - 100 + Int64(imageIndex * 10 + recognizedTexts.count)
                )
                imageMessages.append(message)
            }

            newChatMessages.append(imageMessages)
        }

        chatMessages = newChatMessages
    }

    private func determineRole(from _: CGRect, imageSize _: CGSize) -> Role {
        .user
    }

    func changeRole(of message: ChatMessage) {
        for (imageIndex, imageMessages) in chatMessages.enumerated() {
            if let index = imageMessages.firstIndex(where: { $0.id == message.id }) {
                chatMessages[imageIndex][index].role = (message.role == .assistant) ? .user : .assistant
            }
        }
    }

    func deleteMessage(_ message: ChatMessage, imageIndex: Int) {
        if let messageIndex = chatMessages[imageIndex].firstIndex(where: { $0.id == message.id }) {
            chatMessages[imageIndex].remove(at: messageIndex)
            if let recognizedTextIndex = recognizedTextsByImage[imageIndex].firstIndex(where: { $0.string == message.content }) {
                recognizedTextsByImage[imageIndex].remove(at: recognizedTextIndex)
            }
        }
    }

    func deleteRecognizedText(from imageIndex: Int, textIndex: Int) {
        recognizedTextsByImage[imageIndex].remove(at: textIndex)
        chatMessages[imageIndex].remove(at: textIndex)
    }

    func dragGesture(for message: ChatMessage) -> some Gesture {
        DragGesture()
            .onChanged { [weak self] value in
                guard let self else { return }
                dragState = DragState(message: message, translation: value.translation)
            }
            .onEnded { [weak self] value in
                guard let self else { return }
                if abs(value.translation.width) > 24 {
                    changeRole(of: message)
                }
                dragState = nil
            }
    }
}

extension RecognizeViewModel {
    enum CompressionMethod {
        case ratio(CGFloat)
        case size(CGFloat)
    }

    func compressImage(_ imageData: Data, method: CompressionMethod = .ratio(Configs.default.compressRate)) -> (data: Data?, width: CGFloat, height: CGFloat)? {
        guard let image = UIImage(data: imageData) else { return nil }

        let resizedImage: UIImage = switch method {
        case let .ratio(compressionRatio):
            resizeImage(image, toRatio: compressionRatio)
        case let .size(maxSize):
            resizeImage(image, toMaxSize: maxSize)
        }

        guard let compressedData = resizedImage.jpegData(compressionQuality: Configs.default.compressRate) else { return nil }

        return (data: compressedData, width: resizedImage.size.width, height: resizedImage.size.height)
    }

    private func resizeImage(_ image: UIImage, toRatio ratio: CGFloat) -> UIImage {
        let newSize = CGSize(width: image.size.width * ratio, height: image.size.height * ratio)
        UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return resizedImage ?? image
    }

    private func resizeImage(_ image: UIImage, toMaxSize maxSize: CGFloat) -> UIImage {
        let aspectRatio = image.size.width / image.size.height
        let newSize = if aspectRatio > 1 {
            CGSize(width: maxSize, height: maxSize / aspectRatio)
        } else {
            CGSize(width: maxSize * aspectRatio, height: maxSize)
        }
        UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
        image.draw(in: CGRect(origin: .zero, size: newSize))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return resizedImage ?? image
    }

    func calculateTokenCost(for imageData: Data) -> Int {
        guard let image = UIImage(data: imageData) else { return 85 }
        let (width, height) = (image.size.width, image.size.height)
        let detail = width > 2048 || height > 2048 ? "high" : "low"

        if detail == "low" {
            return 85
        }

        let scaledSize = min(2048, max(width, height))
        let resizeRatio = scaledSize / 768.0
        let scaledWidth = width / resizeRatio
        let scaledHeight = height / resizeRatio
        let numTiles = Int(ceil(scaledWidth / 512.0)) * Int(ceil(scaledHeight / 512.0))
        return 170 * numTiles + 85
    }

    func constructRequestParameters(with imageDataArray: [(data: Data, width: CGFloat, height: CGFloat, tokenCost: Int)]) -> [PostMessage.Content] {
        imageDataArray.map { data -> PostMessage.Content in
            let base64String = "data:image/jpeg;base64,\(data.data.base64EncodedString())"
            return PostMessage.Content(
                type: "image_url",
                text: nil,
                imageUrl: PostMessage.Content.ImageUrl(url: base64String, detail: "low"),
                width: data.width,
                height: data.height,
                token_cost: data.tokenCost
            )
        }
    }

    func constructRequestParameters() -> PostMessage {
        // 压缩画质并获取宽高和token消耗
        let compressedImages: [(data: Data, width: CGFloat, height: CGFloat, tokenCost: Int)] = selectedImagesData.compactMap {
            guard let result = compressImage($0), let data = result.data else {
                return (data: $0, width: 0, height: 0, tokenCost: 0)
            }
            let tokenCost = calculateTokenCost(for: data)
            return (data: data, width: result.width, height: result.height, tokenCost: tokenCost)
        }

        // 计算总token消耗
        let totalTokenCost = compressedImages.map(\.tokenCost).reduce(0, +)

        // 构造请求参数
        var content = constructRequestParameters(with: compressedImages)
        if let stepFormViewModel {
            content.insert(.init(type: "text", text: stepFormViewModel.generatePrompt(), imageUrl: nil, width: 0, height: 0, token_cost: 0), at: 0)
        }

        return PostMessage(
            role: .user,
            content: content,
            total_token_cost: totalTokenCost
        )
    }
}

extension RecognizeViewModel {
    func onChangeOfSelectedItems(_ newItems: [PhotosPickerItem], isLocalRecognition: Bool) {
        if isLocalRecognition {
            FirebaseManager.shared.logEvent("本地识别")
        } else {
            FirebaseManager.shared.logEvent("云端识别")
        }
        isProcessingImage = true
        self.isLocalRecognition = isLocalRecognition
        itemIdentifiers = newItems.compactMap(\.itemIdentifier)
        showPhotosPicker = false
        stepFormViewModel?.disableComplete = true
        selectedImagesData = []
        recognizedTextsByImage = []
        Task {
            for newItem in newItems {
                if let data = try? await newItem.loadTransferable(type: Data.self) {
                    DispatchQueue.main.async { [weak self] in
                        guard let self else { return }
                        selectedImagesData.append(data)
                    }
                    await recognizeText(from: data)
                }
            }
            DispatchQueue.main.async { [weak self] in
                guard let self else { return }
                stepFormViewModel?.disableComplete = false
                isProcessingImage = false
            }
        }
    }

    func onDelete(index: Int) {
        if isLocalRecognition {
            selectedItemsLocal.remove(at: index)
            if index < selectedItemsRemote.count {
                selectedItemsRemote.remove(at: index)
            }
        } else {
            selectedItemsRemote.remove(at: index)
            if index < selectedItemsLocal.count {
                selectedItemsLocal.remove(at: index)
            }
        }
        recognizedTextsByImage.remove(at: index)
        selectedImagesData.remove(at: index)
        chatMessages.remove(at: index)
        showPhotosPicker = selectedItemsLocal.isEmpty && selectedItemsRemote.isEmpty
    }
}
