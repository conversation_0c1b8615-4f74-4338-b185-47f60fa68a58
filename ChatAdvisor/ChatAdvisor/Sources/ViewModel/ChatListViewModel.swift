//
//  ChatListViewModel.swift
//  JunShi
//
//  Created by md on 2024/5/20.
//

import AVFoundation
import Combine
import Foundation
import OSLog
import SwifterSwift
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatListViewModel")

enum ChatListError: LocalizedError {
    case databaseNotInitialized
    case fetchFailed(Error)
    case noUserMessages

    var errorDescription: String? {
        switch self {
        case .databaseNotInitialized:
            return "数据库未初始化，请重新登录"
        case .fetchFailed(let error):
            return "获取会话失败: \(error.localizedDescription)"
        case .noUserMessages:
            return "没有找到有效的会话"
        }
    }
}

class ChatListViewModel: ObservableObject {
//    static let shared = ChatListViewModel()
    @Published var chats: [Chat] = []
    @Published var groupedChats: [Date: [Chat]] = [:]
    @Published var searchText = ""
    @Published var newChatTitle = ""
    @Published var renamingChatId = ""
    @Published var isRenaming = false
    @Published var loadMore: Bool = false
    @Published var isLoading: Bool = false
    @Published var isLoadingMore: Bool = false
    @Published var hasError: Bool = false
    @Published var errorMessage: String = ""
    @Published var hasMoreData: Bool = true
    @Published var showNoMoreDataMessage: Bool = false
    @Published var isInitialLoadCompleted: Bool = false
    private var isGetArchived = false
    private var cancellable = Set<AnyCancellable>()
    private var retryCount = 0
    private let maxRetryCount = 3
    private var isPerformingInitialLoad = false

    init(isGetArchived: Bool = false) {
        self.isGetArchived = isGetArchived

        NotificationCenter.default.publisher(for: .logout)
            .sink { [weak self] _ in
                guard let self else { return }
                chats = []
                groupedChats = [:]
                isInitialLoadCompleted = false
                isPerformingInitialLoad = false
            }.store(in: &cancellable)

        // 监听数据库设置完成通知，刷新聊天列表
        NotificationCenter.default.publisher(for: .databaseSetupCompleted)
            .sink { [weak self] _ in
                guard let self else { return }
                // 数据库设置完成后刷新聊天列表
                refreshChats()
            }.store(in: &cancellable)

        // 监听数据库设置失败通知，提供用户反馈
        NotificationCenter.default.publisher(for: .databaseSetupFailed)
            .sink { [weak self] _ in
                guard let self else { return }
                logger.error("数据库设置失败，会话列表可能无法正常加载")
                // 可以在这里添加用户提示或重试逻辑
            }.store(in: &cancellable)
    }

    func refreshChats() {
        Task {
            await refreshChatsWithRetry()
        }
    }

    /// 执行初始数据加载，防止重复加载
    func performInitialLoad() {
        // 防止重复的初始加载
        guard !isPerformingInitialLoad && !isInitialLoadCompleted else {
            logger.info("跳过重复的初始加载")
            return
        }

        isPerformingInitialLoad = true
        logger.info("开始执行初始数据加载")

        Task {
            await refreshChatsWithRetry()
            await MainActor.run {
                isInitialLoadCompleted = true
                isPerformingInitialLoad = false
                logger.info("初始数据加载完成")
            }
        }
    }

    @MainActor
    private func refreshChatsWithRetry() async {
        isLoading = true
        hasError = false
        errorMessage = ""

        // 重置分页状态
        hasMoreData = true
        showNoMoreDataMessage = false
        loadMore = false

        do {
            // 等待数据库准备完成，增加更详细的日志
            logger.info("等待数据库准备完成...")
            let isDatabaseReady = await AdvisorDatabaseManager.shared.waitForDatabaseReady()
            guard isDatabaseReady else {
                logger.error("数据库等待超时，无法加载会话列表")
                throw ChatListError.databaseNotInitialized
            }
            logger.info("数据库已准备完成，开始加载会话")

            let chats = await AdvisorDatabaseManager.shared.fetchChats(isArchived: isGetArchived)

            // 重置重试计数
            retryCount = 0

            // 更新UI
            groupedChats = [:]
            self.chats = chats
            addNewChats(chats)
            // 清理空会话
            cleanupEmptyChats()

            // 检查是否需要显示分页加载
            if chats.count >= AdvisorDatabaseManager.shared.limit {
                loadMore = true
                hasMoreData = true
            } else {
                loadMore = false
                hasMoreData = false
                if chats.count > 0 {
                    showNoMoreDataMessage = true
                }
            }

            logger.info("成功加载 \(chats.count) 个会话")

        } catch {
            logger.error("加载会话列表失败: \(error)")

            // 处理错误和重试逻辑
            await handleRefreshError(error)
        }

        isLoading = false
    }

    @MainActor
    private func handleRefreshError(_ error: Error) async {
        retryCount += 1

        if retryCount <= maxRetryCount {
//            logger.info("重试加载会话列表 (第 \(retryCount) 次)")

            // 延迟重试
            try? await Task.sleep(nanoseconds: UInt64(retryCount * 1_000_000_000)) // 1秒 * 重试次数

            await refreshChatsWithRetry()
        } else {
            // 达到最大重试次数，显示错误
            hasError = true
            if let chatListError = error as? ChatListError {
                errorMessage = chatListError.localizedDescription
            } else {
                errorMessage = "加载会话列表失败，请检查网络连接后重试"
            }

            logger.error("达到最大重试次数，停止重试")
        }
    }

    func retryRefresh() {
        retryCount = 0
        refreshChats()
    }

    func searchChats() {
        Task {
            await searchChatsWithErrorHandling()
        }
    }

    @MainActor
    private func searchChatsWithErrorHandling() async {
        do {
            // 检查数据库是否已初始化
            guard AdvisorDatabaseManager.shared.database != nil else {
                throw ChatListError.databaseNotInitialized
            }

            FirebaseManager.shared.logSearch(query: searchText, isArchived: false)
            let chats = await AdvisorDatabaseManager.shared.searchChats(keyword: searchText)
            // todo
//            let fromStepChats = await ChatConfigDatabaseManager.shared.searchChats(keyword: searchText)
//            let allChat = chats
//            chats.append(contentsOf: fromStepChats)

            groupedChats = [:]
            self.chats = chats
            addNewChats(chats)

            logger.info("搜索完成，找到 \(chats.count) 个匹配的会话")

        } catch {
            logger.error("搜索会话失败: \(error)")
            hasError = true
            errorMessage = "搜索失败，请重试"
        }
    }

    func getChatTitle(id: String) -> String {
        guard let chat = chats.first(where: { $0.id == id }) else {
            return ""
        }
        return chat.title
    }

    func deleteChat(id: String) {
        removeMemoryChat(id: id)
        AdvisorDatabaseManager.shared.deleteChat(id: id)
    }

    func archiveChat(id: String) {
        removeMemoryChat(id: id)
        AdvisorDatabaseManager.shared.archiveChat(id: id)
    }

    func renameChat() {
        guard !newChatTitle.isEmpty else {
            return
        }
        updateMemoryChatTitle()
        AdvisorDatabaseManager.shared.updateChatTitle(id: renamingChatId, title: newChatTitle)
        isRenaming = false
        renamingChatId = ""
    }

    func updateMemoryChatTitle() {
        if var chat = chats.first(where: { $0.id == renamingChatId }) {
            chat.title = newChatTitle
            updateChatInGroupedChats(newChat: chat)
        }
    }

    @MainActor
    func updateMemoryChat(newChat: Chat) {
        // 只有当会话包含用户消息时才添加到列表中
        let hasUserMessages = newChat.messages.contains { $0.role == .user }

        logger.info("更新会话列表: chatId=\(newChat.id), hasUserMessages=\(hasUserMessages), messagesCount=\(newChat.messages.count)")

        if let index = chats.firstIndex(where: { $0.id == newChat.id }) {
            if hasUserMessages {
                // 更新现有会话
                chats[index] = newChat
                updateChatInGroupedChats(newChat: newChat)
                logger.info("更新现有会话: \(newChat.id)")

                // {{ AURA-X: Remove - 移除强制UI更新，依赖@Published自动机制 }}
            } else {
                // 如果会话变为空（没有用户消息），从列表中移除
                removeMemoryChat(id: newChat.id)
                logger.info("移除空会话: \(newChat.id)")
            }
        } else if hasUserMessages {
            // 新会话应该插入到列表开头（最新的会话）
            chats.insert(newChat, at: 0)
            addNewChats([newChat])

            logger.info("添加新会话到列表顶部: \(newChat.id), 当前列表数量: \(self.chats.count)")

            // {{ AURA-X: Remove - 移除所有强制更新，依赖SwiftUI自动检测@Published变化 }}
        }
    }

    func removeMemoryChat(id: String) {
        // 从原始的聊天列表中移除
        chats.removeAll { $0.id == id }
        removeChatFromGroupedChats(chatId: id)
    }

    func fetchMoreChats() {
        Task {
            await fetchMoreChatsWithErrorHandling()
        }
    }

    @MainActor
    private func fetchMoreChatsWithErrorHandling() async {
        // 防止重复加载和检查是否还有更多数据
        guard !isLoading && !isLoadingMore && hasMoreData else { return }

        isLoadingMore = true
        showNoMoreDataMessage = false

        do {
            // 等待数据库准备完成
            let isDatabaseReady = await AdvisorDatabaseManager.shared.waitForDatabaseReady()
            guard isDatabaseReady else {
                throw ChatListError.databaseNotInitialized
            }

            let newChats = await AdvisorDatabaseManager.shared.fetchChats(offset: chats.count, isArchived: isGetArchived)

            if newChats.isEmpty {
                // 没有更多数据
                hasMoreData = false
                loadMore = false
                showNoMoreDataMessage = true
                logger.info("已加载所有会话，无更多数据")
            } else {
                chats.append(contentsOf: newChats)
                addNewChats(newChats)
                // 清理空会话
                cleanupEmptyChats()

                // 检查是否还有更多数据
                if newChats.count < AdvisorDatabaseManager.shared.limit {
                    hasMoreData = false
                    loadMore = false
                    showNoMoreDataMessage = true
                } else {
                    loadMore = true
                    hasMoreData = true
                }

                logger.info("成功加载更多会话: \(newChats.count) 个")
            }

        } catch {
            logger.error("加载更多会话失败: \(error)")
            loadMore = false
            hasError = true
            errorMessage = "加载更多会话失败"
        }

        isLoadingMore = false
    }

    private func updateChatInGroupedChats(newChat: Chat) {
        let chatId = newChat.id
        for (date, var chats) in groupedChats {
            if let index = chats.firstIndex(where: { $0.id == chatId }) {
                chats[index] = newChat
                groupedChats[date] = chats
                break
            }
        }
    }

    private func removeChatFromGroupedChats(chatId: String) {
        for (date, chats) in groupedChats {
            let filteredChats = chats.filter { $0.id != chatId }
            if filteredChats.isEmpty {
                groupedChats.removeValue(forKey: date)
            } else {
                groupedChats[date] = filteredChats
            }
        }
    }

    @MainActor
    private func addNewChats(_ newChats: [Chat]) {
        let calendar = Calendar.current
        // 过滤掉空会话（没有用户消息的会话）
        let validChats = newChats.filter { chat in
            chat.messages.contains { $0.role == .user }
        }

        logger.info("添加新会话到分组: 有效会话数量=\(validChats.count)")

        for chat in validChats {
            let date = Date.dateFromTimestamp(chat.createdTime)
            let startOfDay = calendar.startOfDay(for: date)

            if groupedChats[startOfDay] == nil {
                groupedChats[startOfDay] = []
            }

            // 检查是否已经存在相同的 Chat
            if !(groupedChats[startOfDay]?.contains(where: { $0.id == chat.id }) ?? false) {
                // 新会话插入到当天分组的开头（最新的在前）
                groupedChats[startOfDay]?.insert(chat, at: 0)
                logger.info("会话已添加到分组: chatId=\(chat.id), date=\(startOfDay)")
            }
        }

        // 保持分组内的排序（如果需要）
        for (key, _) in groupedChats {
            groupedChats[key]?.sort(by: { $0.createdTime > $1.createdTime })
        }

        // 立即触发UI更新
        objectWillChange.send()
    }

    /// 清理空会话
    func cleanupEmptyChats() {
        // 从内存中移除空会话
        chats.removeAll { chat in
            !chat.messages.contains { $0.role == .user }
        }

        // 从分组中移除空会话
        for (date, var chatsInGroup) in groupedChats {
            chatsInGroup.removeAll { chat in
                !chat.messages.contains { $0.role == .user }
            }

            if chatsInGroup.isEmpty {
                groupedChats.removeValue(forKey: date)
            } else {
                groupedChats[date] = chatsInGroup
            }
        }
    }
}
