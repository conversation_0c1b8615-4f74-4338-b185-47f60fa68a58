//
//  OptimizedChatListViewModel.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import Combine
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "OptimizedChatListViewModel")

/// 优化的聊天列表ViewModel
@MainActor
class OptimizedChatListViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var chats: [Chat] = []
    @Published var groupedChats: [Date: [Chat]] = [:]
    @Published var searchText: String = ""
    @Published var isLoading: Bool = false
    @Published var isLoadingMore: Bool = false
    @Published var hasError: Bool = false
    @Published var errorMessage: String = ""
    @Published var hasMoreData: Bool = true
    @Published var showNoMoreDataMessage: Bool = false
    @Published var loadMore: Bool = true
    
    // MARK: - Rename Properties
    @Published var isRenaming: Bool = false
    @Published var newChatTitle: String = ""
    @Published var renamingChatId: String = ""
    
    // MARK: - Performance Properties
    @Published private(set) var isInitialLoadCompleted: Bool = false
    @Published private(set) var lastRefreshTime: Date?
    
    // MARK: - Private Properties
    private let dataSource: ChatListDataSource
    // 移除性能监控依赖
    private var cancellables = Set<AnyCancellable>()
    private let pageSize: Int = 20
    private var currentOffset: Int = 0
    private var isSearchMode: Bool = false
    
    // 防抖处理
    private var searchWorkItem: DispatchWorkItem?
    private let searchDebounceTime: TimeInterval = 0.5
    
    // MARK: - Initialization
    init(chatRepository: ChatRepositoryProtocol, 
         messageRepository: MessageRepositoryProtocol,
         isArchived: Bool = false) {
        self.dataSource = ChatListDataSource(
            chatRepository: chatRepository,
            messageRepository: messageRepository,
            isArchived: isArchived
        )
        // 简化初始化，无需性能监控
        
        setupBindings()
        setupSearchDebounce()
    }
    
    // MARK: - Public Methods
    
    /// 执行初始加载
    func performInitialLoad() {
        guard !isInitialLoadCompleted else {
            logger.debug("初始加载已完成，跳过重复加载")
            return
        }
        
        Task {
            await loadInitialData()
        }
    }
    
    /// 刷新聊天列表
    func refreshChats() {
        Task {
            await refreshData()
        }
    }
    
    /// 重试刷新
    func retryRefresh() {
        hasError = false
        errorMessage = ""
        Task {
            await refreshData()
        }
    }
    
    /// 获取更多聊天
    func fetchMoreChats() {
        guard !isLoadingMore && hasMoreData && !isSearchMode else { return }
        
        Task {
            await loadMoreData()
        }
    }
    
    /// 搜索聊天
    func searchChats() {
        // 取消之前的搜索任务
        searchWorkItem?.cancel()
        
        let workItem = DispatchWorkItem { [weak self] in
            Task { @MainActor in
                await self?.performSearch()
            }
        }
        searchWorkItem = workItem
        
        // 延迟执行搜索
        DispatchQueue.main.asyncAfter(deadline: .now() + searchDebounceTime, execute: workItem)
    }
    
    /// 删除聊天
    func deleteChat(id: String) {
        Task {
            await performDeleteChat(id: id)
        }
    }
    
    /// 归档聊天
    func archiveChat(id: String) {
        Task {
            await performArchiveChat(id: id)
        }
    }
    
    /// 重命名聊天
    func renameChat() {
        guard !renamingChatId.isEmpty else { return }
        
        Task {
            await performRenameChat()
        }
    }
    
    /// 获取聊天标题
    func getChatTitle(id: String) -> String {
        return chats.first { $0.id == id }?.title ?? ""
    }
    
    /// 获取基本统计信息
    func getStats() -> (totalChats: Int, lastRefresh: Date?) {
        return (totalChats: chats.count, lastRefresh: lastRefreshTime)
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // 监听数据源变化
        dataSource.$chats
            .receive(on: DispatchQueue.main)
            .assign(to: \.chats, on: self)
            .store(in: &cancellables)
        
        dataSource.$groupedChats
            .receive(on: DispatchQueue.main)
            .assign(to: \.groupedChats, on: self)
            .store(in: &cancellables)
        
        dataSource.$hasMoreData
            .receive(on: DispatchQueue.main)
            .assign(to: \.hasMoreData, on: self)
            .store(in: &cancellables)
        
        // 监听数据事件
        dataSource.dataEventPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] event in
                self?.handleDataEvent(event)
            }
            .store(in: &cancellables)
    }
    
    private func setupSearchDebounce() {
        $searchText
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] searchText in
                if searchText.isEmpty {
                    self?.clearSearch()
                } else {
                    self?.searchChats()
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleDataEvent(_ event: ChatListDataEvent) {
        switch event {
        case .dataLoaded(let chats):
            logger.info("数据加载完成: \(chats.count) 个聊天")
            isInitialLoadCompleted = true
            lastRefreshTime = Date()
            
        case .loadMoreCompleted(let newChats):
            logger.info("加载更多完成: \(newChats.count) 个新聊天")
            currentOffset += newChats.count
            
        case .searchCompleted(let results):
            logger.info("搜索完成: \(results.count) 个结果")
            
        case .chatAdded(let chat):
            logger.debug("聊天已添加: \(chat.id)")
            
        case .chatUpdated(let chat):
            logger.debug("聊天已更新: \(chat.id)")
            
        case .chatRemoved(let chatId):
            logger.debug("聊天已移除: \(chatId)")
            
        case .dataCleared:
            logger.debug("数据已清空")
            currentOffset = 0
            isInitialLoadCompleted = false
        }
    }
    
    private func loadInitialData() async {
        guard !isLoading else { return }
        
        isLoading = true
        hasError = false
        
        do {
            try await dataSource.loadInitialData()
            currentOffset = chats.count
        } catch {
            handleError(error, message: "加载聊天列表失败")
        }
        
        isLoading = false
    }
    
    private func refreshData() async {
        guard !isLoading else { return }
        
        isLoading = true
        hasError = false
        currentOffset = 0
        isSearchMode = false
        searchText = ""
        
        do {
            try await dataSource.refreshData()
            currentOffset = chats.count
        } catch {
            handleError(error, message: "刷新聊天列表失败")
        }
        
        isLoading = false
    }
    
    private func loadMoreData() async {
        guard !isLoadingMore else { return }
        
        isLoadingMore = true
        
        do {
            try await dataSource.loadMoreData()
            currentOffset = chats.count
        } catch {
            handleError(error, message: "加载更多聊天失败")
        }
        
        isLoadingMore = false
    }
    
    private func performSearch() async {
        guard !searchText.isEmpty else { return }
        
        isLoading = true
        isSearchMode = true
        hasError = false
        
        do {
            try await dataSource.searchChats(keyword: searchText)
        } catch {
            handleError(error, message: "搜索聊天失败")
        }
        
        isLoading = false
    }
    
    private func clearSearch() {
        isSearchMode = false
        Task {
            await refreshData()
        }
    }
    
    private func performDeleteChat(id: String) async {
        do {
            try await dataSource.removeChat(id: id)
            logger.info("删除聊天成功: \(id)")
        } catch {
            handleError(error, message: "删除聊天失败")
        }
    }
    
    private func performArchiveChat(id: String) async {
        do {
            // 这里需要通过dataSource获取repository来执行归档操作
            // 暂时使用原有的数据库管理器
            AdvisorDatabaseManager.shared.archiveChat(id: id)
            try await dataSource.removeChat(id: id) // 从当前列表移除
            logger.info("归档聊天成功: \(id)")
        } catch {
            handleError(error, message: "归档聊天失败")
        }
    }
    
    private func performRenameChat() async {
        do {
            AdvisorDatabaseManager.shared.updateChatTitle(id: renamingChatId, title: newChatTitle)
            
            // 更新本地数据
            if let index = chats.firstIndex(where: { $0.id == renamingChatId }) {
                var updatedChat = chats[index]
                updatedChat.title = newChatTitle
                try await dataSource.updateChat(updatedChat)
            }
            
            // 重置重命名状态
            isRenaming = false
            renamingChatId = ""
            newChatTitle = ""
            
            logger.info("重命名聊天成功: \(renamingChatId)")
        } catch {
            handleError(error, message: "重命名聊天失败")
        }
    }
    
    private func handleError(_ error: Error, message: String) {
        hasError = true
        errorMessage = "\(message): \(error.localizedDescription)"
        logger.error("\(message): \(error.localizedDescription)")
    }
}