//
//  ChatListUIStateManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import SwiftUI
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatListUIStateManager")

/// 聊天列表加载状态
enum ChatListLoadingState {
    case idle
    case initialLoading
    case loadingMore
    case refreshing
    case searching
}

/// 聊天列表错误类型
enum ChatListErrorType {
    case networkError
    case databaseError
    case searchError
    case unknownError
}

/// 聊天列表UI状态管理器 - 专门管理UI相关的状态
@MainActor
class ChatListUIStateManager: ObservableObject {
    
    // MARK: - Loading States
    @Published var loadingState: ChatListLoadingState = .idle
    @Published var isInitialLoadCompleted: Bool = false
    @Published var showLoadingIndicator: Bool = false
    @Published var showRefreshIndicator: Bool = false
    
    // MARK: - Error States
    @Published var hasError: Bool = false
    @Published var errorType: ChatListErrorType = .unknownError
    @Published var errorMessage: String = ""
    @Published var showErrorAlert: Bool = false
    
    // MARK: - Search States
    @Published var searchText: String = ""
    @Published var isSearching: Bool = false
    @Published var searchResults: [Chat] = []
    @Published var showSearchResults: Bool = false
    
    // MARK: - Pagination States
    @Published var showLoadMoreIndicator: Bool = false
    @Published var showNoMoreDataMessage: Bool = false
    @Published var canLoadMore: Bool = true
    
    // MARK: - Selection States
    @Published var selectedChatIds: Set<String> = []
    @Published var isSelectionMode: Bool = false
    @Published var showDeleteConfirmation: Bool = false
    
    // MARK: - Rename States
    @Published var renamingChatId: String = ""
    @Published var newChatTitle: String = ""
    @Published var isRenaming: Bool = false
    @Published var showRenameDialog: Bool = false
    
    // MARK: - Toast States
    @Published var showToast: Bool = false
    @Published var toastMessage: String = ""
    @Published var toastType: ToastType = .info
    
    // MARK: - Performance States
    @Published var lastUpdateTime: Date = Date()
    @Published var updateCount: Int = 0
    
    // MARK: - Private Properties
    private var errorRetryCount: Int = 0
    private let maxRetryCount: Int = 3
    private var toastTimer: Timer?
    
    // MARK: - Loading State Management
    
    func startInitialLoading() {
        loadingState = .initialLoading
        showLoadingIndicator = true
        hasError = false
        errorMessage = ""
        
        logger.debug("开始初始加载")
    }
    
    func finishInitialLoading() {
        loadingState = .idle
        showLoadingIndicator = false
        isInitialLoadCompleted = true
        
        logger.debug("初始加载完成")
    }
    
    func startLoadingMore() {
        guard loadingState == .idle else { return }
        
        loadingState = .loadingMore
        showLoadMoreIndicator = true
        
        logger.debug("开始加载更多")
    }
    
    func finishLoadingMore(hasMoreData: Bool) {
        loadingState = .idle
        showLoadMoreIndicator = false
        canLoadMore = hasMoreData
        
        if !hasMoreData {
            showNoMoreDataMessage = true
        }
        
        logger.debug("加载更多完成，hasMoreData: \(hasMoreData)")
    }
    
    func startRefreshing() {
        loadingState = .refreshing
        showRefreshIndicator = true
        hasError = false
        errorMessage = ""
        errorRetryCount = 0
        
        logger.debug("开始刷新")
    }
    
    func finishRefreshing() {
        loadingState = .idle
        showRefreshIndicator = false
        
        logger.debug("刷新完成")
    }
    
    func startSearching() {
        loadingState = .searching
        isSearching = true
        
        logger.debug("开始搜索: \(self.searchText)")
    }
    
    func finishSearching(results: [Chat]) {
        loadingState = .idle
        isSearching = false
        searchResults = results
        showSearchResults = !searchText.isEmpty
        
        logger.debug("搜索完成: \(results.count) 条结果")
    }
    
    // MARK: - Error Management
    
    func handleError(_ error: Error, type: ChatListErrorType = .unknownError) {
        loadingState = .idle
        showLoadingIndicator = false
        showRefreshIndicator = false
        showLoadMoreIndicator = false
        
        hasError = true
        errorType = type
        errorMessage = getErrorMessage(for: error, type: type)
        showErrorAlert = true
    
    }
    
    func clearError() {
        hasError = false
        errorMessage = ""
        showErrorAlert = false
        errorRetryCount = 0
        
        logger.debug("清除错误状态")
    }
    
    func canRetry() -> Bool {
        return errorRetryCount < maxRetryCount
    }
    
    func incrementRetryCount() {
        errorRetryCount += 1
    }
    
    // MARK: - Search Management
    
    func updateSearchText(_ text: String) {
        searchText = text
        
        if text.isEmpty {
            clearSearchResults()
        }
    }
    
    func clearSearchResults() {
        searchResults.removeAll()
        showSearchResults = false
        isSearching = false
        
        if loadingState == .searching {
            loadingState = .idle
        }
        
        logger.debug("清除搜索结果")
    }
    
    // MARK: - Selection Management
    
    func enterSelectionMode() {
        isSelectionMode = true
        selectedChatIds.removeAll()
        
        logger.debug("进入选择模式")
    }
    
    func exitSelectionMode() {
        isSelectionMode = false
        selectedChatIds.removeAll()
        
        logger.debug("退出选择模式")
    }
    
    func toggleChatSelection(_ chatId: String) {
        if selectedChatIds.contains(chatId) {
            selectedChatIds.remove(chatId)
        } else {
            selectedChatIds.insert(chatId)
        }
        
        logger.debug("切换聊天选择: \(chatId), 当前选中: \(self.selectedChatIds.count)")
    }
    
    func selectAllChats(_ chatIds: [String]) {
        selectedChatIds = Set(chatIds)
        
        logger.debug("选择所有聊天: \(self.selectedChatIds.count)")
    }
    
    func showDeleteConfirmationDialog() {
        showDeleteConfirmation = true
    }
    
    func hideDeleteConfirmationDialog() {
        showDeleteConfirmation = false
    }
    
    // MARK: - Rename Management
    
    func startRenaming(chatId: String, currentTitle: String) {
        renamingChatId = chatId
        newChatTitle = currentTitle
        isRenaming = true
        showRenameDialog = true
        
        logger.debug("开始重命名聊天: \(chatId)")
    }
    
    func finishRenaming() {
        renamingChatId = ""
        newChatTitle = ""
        isRenaming = false
        showRenameDialog = false
        
        logger.debug("完成重命名")
    }
    
    func cancelRenaming() {
        finishRenaming()
        logger.debug("取消重命名")
    }
    
    // MARK: - Toast Management
    
    func showSuccessToast(_ message: String) {
        showToast(message, type: .success)
    }
    
    func showErrorToast(_ message: String) {
        showToast(message, type: .error)
    }
    
    func showInfoToast(_ message: String) {
        showToast(message, type: .info)
    }
    
    func showWarningToast(_ message: String) {
        showToast(message, type: .warning)
    }
    
    private func showToast(_ message: String, type: ToastType) {
        toastMessage = message
        toastType = type
        showToast = true
        
        // 自动隐藏Toast
        toastTimer?.invalidate()
        toastTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { [weak self] _ in
            self?.hideToast()
        }
        
        logger.debug("显示Toast: \(message)")
    }
    
    func hideToast() {
        showToast = false
        toastMessage = ""
        toastTimer?.invalidate()
        toastTimer = nil
    }
    
    // MARK: - State Reset
    
    func resetAllStates() {
        loadingState = .idle
        isInitialLoadCompleted = false
        showLoadingIndicator = false
        showRefreshIndicator = false
        
        clearError()
        clearSearchResults()
        exitSelectionMode()
        finishRenaming()
        hideToast()
        
        showNoMoreDataMessage = false
        canLoadMore = true
        
        logger.info("重置所有UI状态")
    }
    
    // MARK: - Performance Tracking
    
    func trackUpdate() {
        lastUpdateTime = Date()
        updateCount += 1
    }
    
    func getPerformanceStats() -> (updateCount: Int, lastUpdateTime: Date) {
        return (updateCount: updateCount, lastUpdateTime: lastUpdateTime)
    }
    
    // MARK: - Private Methods
    
    private func getErrorMessage(for error: Error, type: ChatListErrorType) -> String {
        switch type {
        case .networkError:
            return "网络连接失败，请检查网络设置后重试"
        case .databaseError:
            return "数据加载失败，请稍后重试"
        case .searchError:
            return "搜索失败，请重试"
        case .unknownError:
            return error.localizedDescription.isEmpty ? "发生未知错误，请重试" : error.localizedDescription
        }
    }
    
    deinit {
        toastTimer?.invalidate()
    }
}

/// Toast类型
enum ToastType {
    case success
    case error
    case warning
    case info
    
    var color: Color {
        switch self {
        case .success:
            return .green
        case .error:
            return .red
        case .warning:
            return .orange
        case .info:
            return .blue
        }
    }
    
    var icon: String {
        switch self {
        case .success:
            return "checkmark.circle.fill"
        case .error:
            return "xmark.circle.fill"
        case .warning:
            return "exclamationmark.triangle.fill"
        case .info:
            return "info.circle.fill"
        }
    }
}
