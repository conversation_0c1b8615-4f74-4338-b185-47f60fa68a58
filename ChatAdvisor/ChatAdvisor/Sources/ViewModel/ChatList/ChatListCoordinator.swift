//
//  ChatListCoordinator.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import Combine
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatListCoordinator")

/// 聊天列表协调器 - 负责协调数据源、UI状态和业务逻辑
@MainActor
class ChatListCoordinator: ObservableObject {
    
    // MARK: - Dependencies
    private let dataSource: ChatListDataSource
    private let uiStateManager: ChatListUIStateManager
    private let errorRecoveryManager: ErrorRecoveryManager
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private var searchDebounceTimer: Timer?
    private let searchDebounceInterval: TimeInterval = 0.5
    
    // MARK: - Performance Monitoring
    private var operationStartTime: Date = Date()
    private var operationCount: Int = 0
    // 移除性能监控依赖
    private let dataFlowDebugger = DataFlowDebugger.shared
    
    // MARK: - Initialization
    init(dataSource: ChatListDataSource,
         uiStateManager: ChatListUIStateManager,
         errorRecoveryManager: ErrorRecoveryManager? = nil) {
        self.dataSource = dataSource
        self.uiStateManager = uiStateManager
        self.errorRecoveryManager = errorRecoveryManager ?? ErrorRecoveryManager()
        
        setupBindings()
        setupNotificationObservers()
    }
    
    // MARK: - Public Methods
    
    /// 执行初始加载
    func performInitialLoad() async {
        guard !uiStateManager.isInitialLoadCompleted else {
            logger.info("跳过重复的初始加载")
            return
        }

        // 性能监控已移除
        logger.info("开始执行初始加载")
        dataFlowDebugger.logUserAction(source: "ChatListCoordinator", operation: "performInitialLoad")

        uiStateManager.startInitialLoading()

        do {
            try await dataSource.loadInitialData()
            uiStateManager.finishInitialLoading()

            dataFlowDebugger.logUIStateChange(source: "ChatListCoordinator", operation: "finishInitialLoading")
            logger.info("初始加载成功")

        } catch {
            await handleError(error, operation: "初始加载")
        }
    }
    
    /// 刷新数据
    func refreshData() async {
        logger.info("开始刷新数据")
        operationStartTime = Date()
        
        uiStateManager.startRefreshing()
        
        do {
            try await dataSource.refreshData()
            uiStateManager.finishRefreshing()
            uiStateManager.showSuccessToast("刷新成功")
            
            let refreshTime = Date().timeIntervalSince(operationStartTime)
            logger.info("数据刷新成功，耗时: \(refreshTime * 1000)ms")
            
        } catch {
            await handleError(error, operation: "刷新数据")
        }
    }
    
    /// 加载更多数据
    func loadMoreData() async {
        guard uiStateManager.canLoadMore && 
              uiStateManager.loadingState == .idle else {
            logger.debug("无法加载更多数据")
            return
        }
        
        logger.info("开始加载更多数据")
        operationStartTime = Date()
        
        uiStateManager.startLoadingMore()
        
        do {
            try await dataSource.loadMoreData()
            uiStateManager.finishLoadingMore(hasMoreData: dataSource.hasMoreData)
            
            let loadTime = Date().timeIntervalSince(operationStartTime)
            logger.info("加载更多成功，耗时: \(loadTime * 1000)ms")
            
        } catch {
            await handleError(error, operation: "加载更多")
        }
    }
    
    /// 搜索聊天
    func searchChats(keyword: String) {
        // 防抖动处理
        searchDebounceTimer?.invalidate()
        searchDebounceTimer = Timer.scheduledTimer(withTimeInterval: searchDebounceInterval, repeats: false) { [weak self] _ in
            Task { @MainActor in
                await self?.performSearch(keyword: keyword)
            }
        }
    }
    
    /// 添加聊天
    func addChat(_ chat: Chat) async {
        logger.info("协调器添加聊天: \(chat.id)")
        
        do {
            try await dataSource.addChat(chat)
            uiStateManager.showSuccessToast("聊天已添加")
            
        } catch {
            await handleError(error, operation: "添加聊天")
        }
    }
    
    /// 更新聊天
    func updateChat(_ chat: Chat) async {
        logger.info("协调器更新聊天: \(chat.id)")
        
        do {
            try await dataSource.updateChat(chat)
            
        } catch {
            await handleError(error, operation: "更新聊天")
        }
    }
    
    /// 删除选中的聊天
    func deleteSelectedChats() async {
        let selectedIds = Array(uiStateManager.selectedChatIds)
        guard !selectedIds.isEmpty else { return }
        
        logger.info("删除选中的聊天: \(selectedIds.count) 个")
        
        do {
            try await dataSource.removeChats(ids: selectedIds)
            uiStateManager.exitSelectionMode()
            uiStateManager.showSuccessToast("已删除 \(selectedIds.count) 个聊天")
            
        } catch {
            await handleError(error, operation: "删除聊天")
        }
    }
    
    /// 重命名聊天
    func renameChat(id: String, newTitle: String) async {
        logger.info("重命名聊天: \(id) -> \(newTitle)")
        
        guard let chat = dataSource.getChat(id: id) else {
            uiStateManager.showErrorToast("聊天不存在")
            return
        }
        
        var updatedChat = chat
        updatedChat.title = newTitle
        
        do {
            try await dataSource.updateChat(updatedChat)
            uiStateManager.finishRenaming()
            uiStateManager.showSuccessToast("重命名成功")
            
        } catch {
            await handleError(error, operation: "重命名聊天")
        }
    }
    
    /// 重试失败的操作
    func retryFailedOperation() async {
        guard uiStateManager.canRetry() else {
            uiStateManager.showErrorToast("已达到最大重试次数")
            return
        }
        
        uiStateManager.incrementRetryCount()
        uiStateManager.clearError()
        
        // 根据当前状态决定重试什么操作
        if !uiStateManager.isInitialLoadCompleted {
            await performInitialLoad()
        } else {
            await refreshData()
        }
    }
    
    /// 清空搜索
    func clearSearch() {
        uiStateManager.clearSearchResults()
        
        // 如果当前显示的是搜索结果，重新加载原始数据
        if uiStateManager.showSearchResults {
            Task {
                await refreshData()
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // 监听数据源事件
        dataSource.dataEventPublisher
            .receive(on: DispatchQueue.main)
            .sink { [weak self] event in
                self?.handleDataEvent(event)
            }
            .store(in: &cancellables)
        
        // 监听搜索文本变化
        uiStateManager.$searchText
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] searchText in
                if searchText.isEmpty {
                    self?.clearSearch()
                } else {
                    self?.searchChats(keyword: searchText)
                }
            }
            .store(in: &cancellables)
    }
    
    private func setupNotificationObservers() {
        // 监听数据库设置完成通知
        NotificationCenter.default.publisher(for: .databaseSetupCompleted)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                Task {
                    await self?.performInitialLoad()
                }
            }
            .store(in: &cancellables)
        
        // 监听登出通知
        NotificationCenter.default.publisher(for: .logout)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.handleLogout()
            }
            .store(in: &cancellables)
    }
    
    private func handleDataEvent(_ event: ChatListDataEvent) {
        uiStateManager.trackUpdate()
        
        switch event {
        case .dataLoaded(let chats):
            logger.debug("数据加载事件: \(chats.count) 条")
            
        case .chatAdded(let chat):
            logger.debug("聊天添加事件: \(chat.id)")
            
        case .chatUpdated(let chat):
            logger.debug("聊天更新事件: \(chat.id)")
            
        case .chatRemoved(let id):
            logger.debug("聊天删除事件: \(id)")
            
        case .dataCleared:
            logger.debug("数据清空事件")
            
        case .loadMoreCompleted(let chats):
            logger.debug("加载更多完成事件: \(chats.count) 条")
            
        case .searchCompleted(let results):
            logger.debug("搜索完成事件: \(results.count) 条结果")
            uiStateManager.finishSearching(results: results)
        }
    }
    
    private func performSearch(keyword: String) async {
        guard !keyword.isEmpty else {
            clearSearch()
            return
        }
        
        logger.info("执行搜索: \(keyword)")
        operationStartTime = Date()
        
        uiStateManager.startSearching()
        
        do {
            try await dataSource.searchChats(keyword: keyword)
            
            let searchTime = Date().timeIntervalSince(operationStartTime)
            logger.info("搜索完成，耗时: \(searchTime * 1000)ms")
            
        } catch {
            await handleError(error, operation: "搜索", errorType: .searchError)
        }
    }
    
    private func handleError(_ error: Error, operation: String, errorType: ChatListErrorType = .unknownError) async {
        logger.error("\(operation)失败: \(error.localizedDescription)")
        
        // 使用错误恢复管理器处理错误
        let recoveryAction = await errorRecoveryManager.handleNetworkError(error)
        
        do {
            try await errorRecoveryManager.executeRecoveryAction(recoveryAction)
            
            switch recoveryAction {
            case .retry, .retryWithDelay:
                if uiStateManager.canRetry() {
                    await retryFailedOperation()
                } else {
                    uiStateManager.handleError(error, type: errorType)
                }
                
            case .showError(let message):
                uiStateManager.handleError(
                    NSError(domain: "ChatListCoordinator", code: -1, userInfo: [NSLocalizedDescriptionKey: message]),
                    type: errorType
                )
                
            default:
                uiStateManager.handleError(error, type: errorType)
            }
            
        } catch {
            uiStateManager.handleError(error, type: errorType)
        }
        
        operationCount += 1
    }
    
    private func handleLogout() {
        dataSource.clearAllData()
        uiStateManager.resetAllStates()
        logger.info("处理登出事件，清空所有数据")
    }
    
    // MARK: - Performance Monitoring
    
    func getPerformanceStats() -> (operationCount: Int, dataSourceStats: (loadCount: Int, averageLoadTime: TimeInterval, totalChats: Int), uiStats: (updateCount: Int, lastUpdateTime: Date)) {
        return (
            operationCount: operationCount,
            dataSourceStats: dataSource.getPerformanceStats(),
            uiStats: uiStateManager.getPerformanceStats()
        )
    }
    
    deinit {
        searchDebounceTimer?.invalidate()
        cancellables.forEach { $0.cancel() }
    }
}
