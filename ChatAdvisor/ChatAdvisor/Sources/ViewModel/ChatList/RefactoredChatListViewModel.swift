//
//  RefactoredChatListViewModel.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Combine
import Foundation
import OSLog
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "RefactoredChatListViewModel")

/// 重构后的ChatListViewModel - 简化职责，委托给专门的组件
@MainActor
class RefactoredChatListViewModel: ObservableObject {
    
    // MARK: - 委托给专门组件的属性
    var chats: [Chat] { dataSource.chats }
    var groupedChats: [Date: [Chat]] { dataSource.groupedChats }
    var hasMoreData: Bool { dataSource.hasMoreData }
    var totalCount: Int { dataSource.totalCount }
    
    // UI状态委托
    var searchText: String {
        get { uiStateManager.searchText }
        set { uiStateManager.updateSearchText(newValue) }
    }
    var isLoading: Bool { uiStateManager.loadingState != .idle }
    var isLoadingMore: Bool { uiStateManager.loadingState == .loadingMore }
    var hasError: Bool { uiStateManager.hasError }
    var errorMessage: String { uiStateManager.errorMessage }
    var isInitialLoadCompleted: Bool { uiStateManager.isInitialLoadCompleted }
    var showNoMoreDataMessage: Bool { uiStateManager.showNoMoreDataMessage }
    var showLoadingIndicator: Bool { uiStateManager.showLoadingIndicator }
    var showRefreshIndicator: Bool { uiStateManager.showRefreshIndicator }
    
    // 搜索状态委托
    var isSearching: Bool { uiStateManager.isSearching }
    var searchResults: [Chat] { uiStateManager.searchResults }
    var showSearchResults: Bool { uiStateManager.showSearchResults }
    
    // 选择状态委托
    var selectedChatIds: Set<String> { uiStateManager.selectedChatIds }
    var isSelectionMode: Bool { uiStateManager.isSelectionMode }
    var showDeleteConfirmation: Bool { uiStateManager.showDeleteConfirmation }
    
    // 重命名状态委托
    var newChatTitle: String {
        get { uiStateManager.newChatTitle }
        set { uiStateManager.newChatTitle = newValue }
    }
    var renamingChatId: String { uiStateManager.renamingChatId }
    var isRenaming: Bool { uiStateManager.isRenaming }
    var showRenameDialog: Bool { uiStateManager.showRenameDialog }
    
    // Toast状态委托
    var showToast: Bool { uiStateManager.showToast }
    var toastMessage: String { uiStateManager.toastMessage }
    var toastType: ToastType { uiStateManager.toastType }
    
    // MARK: - 核心组件
    private let dataSource: ChatListDataSource
    private let uiStateManager: ChatListUIStateManager
    private let coordinator: ChatListCoordinator
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    private let isArchived: Bool
    
    // MARK: - Initialization
    init(isArchived: Bool = false) {
        self.isArchived = isArchived
        
        // 初始化Repository
        let database = AdvisorDatabaseManager.shared.database!
        let databaseQueue = AdvisorDatabaseManager.shared.databaseQueue
        let chatCacheManager = try! CacheManager<Chat>(name: "chatlist_chats")
        let messageCacheManager = try! CacheManager<ChatMessage>(name: "chatlist_messages")
        
        let chatRepository = ChatListRepository(
            database: database,
            databaseQueue: databaseQueue,
            cacheManager: chatCacheManager
        )
        
        let messageRepository = MessageRepository(
            database: database,
            databaseQueue: databaseQueue,
            cacheManager: messageCacheManager
        )
        
        // 初始化组件
        self.dataSource = ChatListDataSource(
            chatRepository: chatRepository,
            messageRepository: messageRepository,
            isArchived: isArchived
        )
        
        self.uiStateManager = ChatListUIStateManager()
        
        self.coordinator = ChatListCoordinator(
            dataSource: dataSource,
            uiStateManager: uiStateManager
        )
        
        setupBindings()
        setupNotificationObservers()
    }
    
    // MARK: - Public Methods
    
    /// 执行初始加载
    func performInitialLoad() {
        Task {
            await coordinator.performInitialLoad()
        }
    }
    
    /// 刷新数据
    func refreshChats() {
        Task {
            await coordinator.refreshData()
        }
    }
    
    /// 加载更多数据
    func loadMoreChats() {
        Task {
            await coordinator.loadMoreData()
        }
    }
    
    /// 搜索聊天
    func searchChats() {
        coordinator.searchChats(keyword: searchText)
    }
    
    /// 清空搜索
    func clearSearch() {
        coordinator.clearSearch()
    }
    
    /// 更新内存中的聊天
    func updateMemoryChat(newChat: Chat) {
        Task {
            await coordinator.updateChat(newChat)
        }
    }
    
    /// 移除内存中的聊天
    func removeMemoryChat(id: String) {
        Task {
            try? await dataSource.removeChat(id: id)
        }
    }
    
    /// 进入选择模式
    func enterSelectionMode() {
        uiStateManager.enterSelectionMode()
    }
    
    /// 退出选择模式
    func exitSelectionMode() {
        uiStateManager.exitSelectionMode()
    }
    
    /// 切换聊天选择状态
    func toggleChatSelection(_ chatId: String) {
        uiStateManager.toggleChatSelection(chatId)
    }
    
    /// 选择所有聊天
    func selectAllChats() {
        let allChatIds = chats.map { $0.id }
        uiStateManager.selectAllChats(allChatIds)
    }
    
    /// 删除选中的聊天
    func deleteSelectedChats() {
        uiStateManager.showDeleteConfirmationDialog()
    }
    
    /// 确认删除选中的聊天
    func confirmDeleteSelectedChats() {
        Task {
            await coordinator.deleteSelectedChats()
        }
        uiStateManager.hideDeleteConfirmationDialog()
    }
    
    /// 取消删除
    func cancelDelete() {
        uiStateManager.hideDeleteConfirmationDialog()
    }
    
    /// 开始重命名聊天
    func startRenaming(chatId: String) {
        guard let chat = dataSource.getChat(id: chatId) else { return }
        uiStateManager.startRenaming(chatId: chatId, currentTitle: chat.title)
    }
    
    /// 完成重命名
    func finishRenaming() {
        guard !renamingChatId.isEmpty && !newChatTitle.isEmpty else {
            uiStateManager.cancelRenaming()
            return
        }
        
        Task {
            await coordinator.renameChat(id: renamingChatId, newTitle: newChatTitle)
        }
    }
    
    /// 取消重命名
    func cancelRenaming() {
        uiStateManager.cancelRenaming()
    }
    
    /// 重试失败的操作
    func retryRefresh() {
        Task {
            await coordinator.retryFailedOperation()
        }
    }
    
    /// 获取性能统计
    func getPerformanceStats() -> (operationCount: Int, dataSourceStats: (loadCount: Int, averageLoadTime: TimeInterval, totalChats: Int), uiStats: (updateCount: Int, lastUpdateTime: Date)) {
        return coordinator.getPerformanceStats()
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        // 绑定数据源变化
        dataSource.objectWillChange
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
        
        // 绑定UI状态变化
        uiStateManager.objectWillChange
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
        
        // 绑定协调器变化
        coordinator.objectWillChange
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
    }
    
    private func setupNotificationObservers() {
        // 监听登出通知
        NotificationCenter.default.publisher(for: .logout)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.handleLogout()
            }
            .store(in: &cancellables)
        
        // 监听数据库设置完成通知
        NotificationCenter.default.publisher(for: .databaseSetupCompleted)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.performInitialLoad()
            }
            .store(in: &cancellables)
    }
    
    private func handleLogout() {
        dataSource.clearAllData()
        uiStateManager.resetAllStates()
        logger.info("处理登出事件，清空所有数据")
    }
    
    deinit {
        cancellables.forEach { $0.cancel() }
    }
}

// MARK: - 向后兼容的方法（逐步移除）
extension RefactoredChatListViewModel {
    
    /// 向后兼容：loadMore属性
    var loadMore: Bool {
        get { hasMoreData && !isLoadingMore }
        set { 
            if newValue && hasMoreData && !isLoadingMore {
                loadMoreChats()
            }
        }
    }
    
    /// 向后兼容：清理空聊天
    func cleanupEmptyChats() {
        // 这个功能现在由Repository层处理
        logger.debug("cleanupEmptyChats调用已委托给Repository层")
    }
    
    /// 向后兼容：添加新聊天到分组
    func addNewChats(_ newChats: [Chat]) {
        // 这个功能现在由DataSource自动处理
        logger.debug("addNewChats调用已委托给DataSource")
    }
    
    /// 向后兼容：更新分组聊天中的聊天
    func updateChatInGroupedChats(newChat: Chat) {
        // 这个功能现在由DataSource自动处理
        logger.debug("updateChatInGroupedChats调用已委托给DataSource")
    }
}
