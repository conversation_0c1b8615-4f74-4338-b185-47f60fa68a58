//
//  PricingViewModel.swift
//  JunShi
//
//  Created by zweiteng on 2024/5/14.
//

import Foundation
import OSLog
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "VerificationViewModel")
class PricingViewModel: ObservableObject {
    @Published var toastMessage = ""
    @Published var isLoading = false
    @Published var verifyButtonDisable = true
    @Published var showToast = false
    @Published var isRequestError = false

    func getPricing() {
        guard !isLoading else { return }
        isLoading = true
        NetworkService.shared.requestMulti(PricingTarget.getPricing) { [weak self] (result: Result<NetworkResponse<[ChatsModel]>, NetworkError>) in
            guard let self else { return }
            switch result {
            case let .success(response):
                if response.isSuccess {
                    //
                } else {
                    //                    self.failedToast(response.message ?? "验证失败")
                }
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isLoading = false
                    verifyButtonDisable = false
                }
                logger.info("getPricing successful: \(response.localizedDescription)")
            case let .failure(error):
//                self.failedToast(error.errorMessage ?? "验证失败")
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isLoading = false
                }
                logger.error("getPricing failed: \(error.localizedDescription)")
            }
        }
    }

    func failedToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = true
            toastMessage = message
            showToast = true
        }
    }

    func successToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = false
            toastMessage = message
            showToast = true
        }
    }
}
