//
//  RegisterViewModel.swift
//  JunShi
//
//  Created by md on 2024/4/19.
//

import Foundation
import OSLog
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "RegisterViewModel")

class RegisterViewModel: ObservableObject {
    @Published var email = ""
    @Published var password = ""
    @Published var toastMessage = ""
    @Published var isTimerActive = false
    @Published var isLoading = false
    @Published var showingVerificationView = false
    @Published var showingVerificationButton = false
    @Published var showToast = false
    @Published var isRequestError = false
    @Published var sendButtonDisable = true
    @Published var isEmailValid: Bool = true
    @Published var isPasswordValid: Bool = true
    @Published var isEditingDisabled = false

    let inputHeight: CGFloat = 50 // 设置输入框高度

    func sendVerificationCode() {
        guard !isLoading else { return }
        guard !isTimerActive else { return }
        guard email.isValidEmail else {
            failedToast("请输入正确的邮箱")
            return
        }
        guard password.count >= 6 else {
            failedToast("密码长度至少为6位")
            return
        }
        sendEmailCode(email: email)
    }

    // 邮箱验证码
    func sendEmailCode(email: String) {
        isLoading = true
        NetworkService.shared.requestMulti(AuthTarget.sendEmailCode(email: email)) { [weak self] (result: Result<NetworkResponse<String>, NetworkError>) in
            guard let self else { return }
            switch result {
            case let .success(response):
                if response.isSuccess {
                    successToast(response.message ?? "验证码发送成功")
                    DispatchQueue.main.async { [weak self] in
                        guard let self else { return }
                        isTimerActive = true
                        showingVerificationView = true
                        showingVerificationButton = true
                        isLoading = false
                    }
                } else {
                    failedToast(response.message ?? "验证码发送失败")
                    DispatchQueue.main.async { [weak self] in
                        guard let self else { return }
                        isLoading = false
                        showingVerificationButton = false
                    }
                    logger.error("sendEmailCode failed: \(response.message ?? "")")
                }
                logger.info("sendEmailCode successful: \(response.localizedDescription)")
            case let .failure(error):
                failedToast(error.errorMessage ?? "验证码发送失败")
                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    isLoading = false
                    showingVerificationButton = false
                }
                logger.error("sendEmailCode failed: \(error.localizedDescription)")
            }
        }
    }

    // 注册功能
    func register(credentials: UserCredentials) {
        NetworkService.shared.requestMulti(AuthTarget.register(credentials: credentials)) { [weak self] (result: Result<NetworkResponse<String>, NetworkError>) in
            guard let self else { return }
            switch result {
            case let .success(response):
                // 成功接收到响应
                logger.info("register successful: \(response.localizedDescription)")
            case let .failure(error):
                // 处理错误情况
                logger.error("register failed: \(error.localizedDescription)")
            }
        }
    }

    func failedToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = true
            toastMessage = message
            showToast = true
        }
    }

    func successToast(_ message: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self else { return }
            isRequestError = false
            toastMessage = message
            showToast = true
        }
    }
}
