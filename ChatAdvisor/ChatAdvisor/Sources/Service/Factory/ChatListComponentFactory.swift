//
//  ChatListComponentFactory.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import WCDBSwift
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatListComponentFactory")

/// 聊天列表组件配置
struct ChatListConfiguration {
    let pageSize: Int
    let cacheExpiration: TimeInterval
    let maxCacheItems: Int
    let enableDataFlowDebugging: Bool
    let enableRepository: Bool
    
    static let `default` = ChatListConfiguration(
        pageSize: 20,
        cacheExpiration: 300, // 5分钟
        maxCacheItems: 100,
        enableDataFlowDebugging: false,
        enableRepository: true
    )
    
    static let debug = ChatListConfiguration(
        pageSize: 10,
        cacheExpiration: 60, // 1分钟
        maxCacheItems: 50,
        enableDataFlowDebugging: true,
        enableRepository: true
    )
    
    static let production = ChatListConfiguration(
        pageSize: 30,
        cacheExpiration: 600, // 10分钟
        maxCacheItems: 200,
        enableDataFlowDebugging: false,
        enableRepository: true
    )
}

/// 聊天列表组件工厂 - 负责创建和配置所有相关组件
class ChatListComponentFactory {
    
    // MARK: - Singleton
    static let shared = ChatListComponentFactory()
    
    // MARK: - Private Properties
    private var configuration: ChatListConfiguration
    private var cachedComponents: [String: Any] = [:]
    private let queue = DispatchQueue(label: "com.sanva.chatadvisor.factory", qos: .utility)
    
    // MARK: - Initialization
    private init(configuration: ChatListConfiguration = .default) {
        self.configuration = configuration
        setupGlobalConfiguration()
    }
    
    // MARK: - Configuration
    
    /// 更新配置
    func updateConfiguration(_ newConfiguration: ChatListConfiguration) {
        queue.async { [weak self] in
            self?.configuration = newConfiguration
            self?.cachedComponents.removeAll() // 清除缓存，强制重新创建
            self?.setupGlobalConfiguration()
        }
        
        logger.info("聊天列表组件配置已更新")
    }
    
    /// 获取当前配置
    func getCurrentConfiguration() -> ChatListConfiguration {
        return queue.sync {
            return configuration
        }
    }
    
    // MARK: - Component Creation
    
    /// 创建聊天Repository
    func createChatRepository() -> ChatRepositoryProtocol {
        return queue.sync {
            let cacheKey = "chatRepository"
            
            if let cached = cachedComponents[cacheKey] as? ChatRepositoryProtocol {
                return cached
            }
            
            let repository: ChatRepositoryProtocol
            
            if configuration.enableRepository {
                // 使用优化的Repository
                let database = AdvisorDatabaseManager.shared.database!
                let databaseQueue = AdvisorDatabaseManager.shared.databaseQueue
                let cacheManager = createCacheManager(name: "chats", type: Chat.self)
                
                repository = ChatListRepository(
                    database: database,
                    databaseQueue: databaseQueue,
                    cacheManager: cacheManager
                )
            } else {
                // 使用标准Repository
                let database = AdvisorDatabaseManager.shared.database!
                let databaseQueue = AdvisorDatabaseManager.shared.databaseQueue
                let cacheManager = createCacheManager(name: "chats_standard", type: Chat.self)
                
                repository = ChatRepository(
                    database: database,
                    databaseQueue: databaseQueue,
                    cacheManager: cacheManager
                )
            }
            
            cachedComponents[cacheKey] = repository
            return repository
        }
    }
    
    /// 创建消息Repository
    func createMessageRepository() -> MessageRepositoryProtocol {
        return queue.sync {
            let cacheKey = "messageRepository"
            
            if let cached = cachedComponents[cacheKey] as? MessageRepositoryProtocol {
                return cached
            }
            
            let database = AdvisorDatabaseManager.shared.database!
            let databaseQueue = AdvisorDatabaseManager.shared.databaseQueue
            let cacheManager = createCacheManager(name: "messages", type: ChatMessage.self)
            
            let repository = MessageRepository(
                database: database,
                databaseQueue: databaseQueue,
                cacheManager: cacheManager
            )
            
            cachedComponents[cacheKey] = repository
            return repository
        }
    }
    
    /// 创建数据源
    @MainActor
    func createDataSource(isArchived: Bool = false) -> ChatListDataSource {
        let chatRepository = createChatRepository()
        let messageRepository = createMessageRepository()
        
        return ChatListDataSource(
            chatRepository: chatRepository,
            messageRepository: messageRepository,
            isArchived: isArchived
        )
    }
    
    /// 创建UI状态管理器
    @MainActor
    func createUIStateManager() -> ChatListUIStateManager {
        return ChatListUIStateManager()
    }

    /// 创建协调器
    @MainActor
    func createCoordinator(dataSource: ChatListDataSource, uiStateManager: ChatListUIStateManager) -> ChatListCoordinator {
        let errorRecoveryManager = createErrorRecoveryManager()
        
        return ChatListCoordinator(
            dataSource: dataSource,
            uiStateManager: uiStateManager,
            errorRecoveryManager: errorRecoveryManager
        )
    }
    
    /// 创建完整的ViewModel
    @MainActor
    func createViewModel(isArchived: Bool = false) -> RefactoredChatListViewModel {
        return RefactoredChatListViewModel(isArchived: isArchived)
    }
    
    // MARK: - Support Components
    
    /// 创建缓存管理器
    private func createCacheManager<T: Codable & Identifiable>(name: String, type: T.Type) -> CacheManager<T> {
        do {
            return try CacheManager<T>(
                name: name,
                maxMemoryItems: configuration.maxCacheItems,
                defaultExpiration: configuration.cacheExpiration
            )
        } catch {
            logger.error("创建缓存管理器失败: \(error.localizedDescription)")
            // 返回一个默认配置的缓存管理器
            return try! CacheManager<T>(name: "\(name)_fallback")
        }
    }
    
    /// 创建错误恢复管理器
    private func createErrorRecoveryManager() -> ErrorRecoveryManager {
        return queue.sync {
            let cacheKey = "errorRecoveryManager"
            
            if let cached = cachedComponents[cacheKey] as? ErrorRecoveryManager {
                return cached
            }
            
            let manager = ErrorRecoveryManager()
            cachedComponents[cacheKey] = manager
            return manager
        }
    }
    
    // MARK: - Global Setup
    
    private func setupGlobalConfiguration() {
        // 性能监控已移除
        
        // 配置数据流调试
        if configuration.enableDataFlowDebugging {
            DataFlowDebugger.shared.enable()
            logger.debug("数据流调试已启用")
        } else {
            DataFlowDebugger.shared.disable()
        }
        
        // 配置数据变更通知
        // DataChangeNotificationCenter已经是单例，无需特殊配置
    }
    
    // MARK: - Cache Management
    
    /// 清除组件缓存
    func clearComponentCache() {
        queue.async { [weak self] in
            self?.cachedComponents.removeAll()
        }
        
        logger.info("组件缓存已清除")
    }
    
    /// 预热组件缓存
    func warmupComponents() {
        queue.async { [weak self] in
            guard let self = self else { return }
            
            // 预创建常用组件
            _ = self.createChatRepository()
            _ = self.createMessageRepository()
            _ = self.createErrorRecoveryManager()
        }
        
        logger.info("组件缓存预热完成")
    }
    
    // MARK: - Diagnostics
    
    /// 获取工厂诊断信息
    func getDiagnostics() -> [String: Any] {
        return queue.sync {
            return [
                "configuration": [
                    "pageSize": configuration.pageSize,
                    "cacheExpiration": configuration.cacheExpiration,
                    "maxCacheItems": configuration.maxCacheItems,
                    // enablePerformanceMonitoring已移除
                    "enableDataFlowDebugging": configuration.enableDataFlowDebugging,
                    "enableRepository": configuration.enableRepository
                ],
                "cachedComponents": Array(cachedComponents.keys),
                "componentCount": cachedComponents.count
            ]
        }
    }

}

// MARK: - 便利扩展
extension ChatListComponentFactory {
    
    /// 快速创建用于调试的组件
    @MainActor
    static func createDebugComponents(isArchived: Bool = false) -> (dataSource: ChatListDataSource, uiStateManager: ChatListUIStateManager, coordinator: ChatListCoordinator) {
        let factory = ChatListComponentFactory.shared
        factory.updateConfiguration(.debug)
        
        let dataSource = factory.createDataSource(isArchived: isArchived)
        let uiStateManager = factory.createUIStateManager()
        let coordinator = factory.createCoordinator(dataSource: dataSource, uiStateManager: uiStateManager)
        
        return (dataSource, uiStateManager, coordinator)
    }
    
    /// 快速创建用于生产的组件
    @MainActor
    static func createProductionComponents(isArchived: Bool = false) -> RefactoredChatListViewModel {
        let factory = ChatListComponentFactory.shared
        factory.updateConfiguration(.production)
        
        return factory.createViewModel(isArchived: isArchived)
    }
}

// MARK: - String扩展
private extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}
