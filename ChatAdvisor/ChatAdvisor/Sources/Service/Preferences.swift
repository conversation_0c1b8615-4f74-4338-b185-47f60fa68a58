//
//  Preferences.swift
//  JunShi
//
//  Created by md on 2024/5/17.
//

import Combine
import Foundation
import OSLog
import SwiftUI

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "Preferences")

/// The applications preferences container
///
/// Properties in this object should be of the the type `Option` with the object which is being
/// stored to automatically interact with `UserDefaults`
public class Preferences {
    /// The default `UserDefaults` that all `Option`s will use unless specified
    public static let defaultContainer = UserDefaults(suiteName: AccountManager.shared.currentUser?.email.md5)!
}

/// Defines an object which may watch a set of `Preference.Option`s
/// - note: @objc was added here due to a Swift compiler bug which doesn't allow a class-bound protocol
/// to act as `AnyObject` in a `AnyObject` generic constraint (i.e. `WeakList`)
@objc public protocol PreferencesObserver: AnyObject {
    /// A preference value was changed for some given preference key
    func preferencesDidChange(for key: String)
}

public extension Preferences {
    /// An entry in the `Preferences`
    ///
    /// `ValueType` defines the type of value that will stored in the UserDefaults object
    class Option<ValueType: Equatable>: ObservableObject {
        /// The list of observers for this option
        private let observers = WeakList<PreferencesObserver>()
        /// The UserDefaults container that you wish to save to
        public let container: UserDefaults
        /// The current value of this preference
        ///
        /// Upon setting this value, UserDefaults will be updated and any observers will be called
        @Published public var value: ValueType {
            didSet {
                if value == oldValue { return }

                writePreferenceValue(container, key, value)

                container.synchronize()

                let key = key
                for observer in observers {
                    observer.preferencesDidChange(for: key)
                }
            }
        }

        /// as Binding
        public var asBinding: Binding<ValueType> {
            Binding(
                get: { self.value },
                set: { self.value = $0 }
            )
        }

        /// Adds `object` as an observer for this Option.
        public func observe(from object: PreferencesObserver) {
            observers.insert(object)
        }

        /// The key used for getting/setting the value in `UserDefaults`
        public let key: String
        /// The default value of this preference
        public let defaultValue: ValueType
        /// Reset's the preference to its original default value
        public func reset() {
            value = defaultValue
        }

        private var writePreferenceValue: (UserDefaults, String, ValueType) -> Void

        fileprivate init(
            key: String,
            initialValue: ValueType,
            defaultValue: ValueType,
            container: UserDefaults = Preferences.defaultContainer,
            writePreferenceValue: @escaping (UserDefaults, String, ValueType) -> Void
        ) {
            self.key = key
            self.container = container
            value = initialValue
            self.defaultValue = defaultValue
            self.writePreferenceValue = writePreferenceValue
        }
    }
}

public extension Preferences.Option {
    /// Creates a preference and fetches the initial value from the container the default way
    private convenience init(
        key: String,
        defaultValue: ValueType,
        container: UserDefaults
    ) where ValueType: UserDefaultsEncodable {
        let initialValue = (container.value(forKey: key) as? ValueType) ?? defaultValue
        self.init(
            key: key,
            initialValue: initialValue,
            defaultValue: defaultValue,
            container: container,
            writePreferenceValue: { $0.set($2, forKey: $1) }
        )
    }

    /// Creates a preference storing a user defaults supported value type
    convenience init(
        key: String,
        default: ValueType,
        container: UserDefaults = Preferences.defaultContainer
    ) where ValueType: UserDefaultsEncodable {
        self.init(key: key, defaultValue: `default`, container: container)
    }

    /// Creates a preference storing an array of user defaults supported value types
    convenience init<V>(
        key: String,
        default: ValueType,
        container: UserDefaults = Preferences.defaultContainer
    ) where V: UserDefaultsEncodable, ValueType == [V] {
        self.init(key: key, defaultValue: `default`, container: container)
    }

    /// Creates a preference storing an dictionary of user defaults supported value types
    convenience init<K, V>(
        key: String, default: ValueType,
        container: UserDefaults = Preferences.defaultContainer
    ) where K: StringProtocol, V: UserDefaultsEncodable, ValueType == [K: V] {
        self.init(key: key, defaultValue: `default`, container: container)
    }
}

public extension Preferences.Option where ValueType: ExpressibleByNilLiteral {
    /// Creates a preference and fetches the initial value from the container the default way
    private convenience init<V>(
        key: String,
        defaultValue: ValueType,
        container: UserDefaults
    ) where V: UserDefaultsEncodable, ValueType == V? {
        let initialValue = (container.value(forKey: key) as? ValueType) ?? defaultValue
        self.init(
            key: key,
            initialValue: initialValue,
            defaultValue: defaultValue,
            container: container,
            writePreferenceValue: { container, key, value in
                if let value {
                    container.set(value, forKey: key)
                } else {
                    container.removeObject(forKey: key)
                }
            }
        )
    }

    /// Creates a preference storing an optional user defaults supported value type
    convenience init<V>(
        key: String,
        default: ValueType,
        container: UserDefaults = Preferences.defaultContainer
    ) where ValueType == V?, V: UserDefaultsEncodable {
        self.init(key: key, defaultValue: `default`, container: container)
    }

    /// Creates a preference storing an optional array of user defaults supported value types
    convenience init<V>(
        key: String,
        default: ValueType,
        container: UserDefaults = Preferences.defaultContainer
    ) where V: UserDefaultsEncodable, ValueType == [V]? {
        self.init(key: key, defaultValue: `default`, container: container)
    }

    /// Creates a preference storing an optional dictionary of user defaults supported value types
    convenience init<K, V>(
        key: String,
        default: ValueType,
        container: UserDefaults = Preferences.defaultContainer
    ) where K: StringProtocol, V: UserDefaultsEncodable, ValueType == [K: V]? {
        self.init(key: key, defaultValue: `default`, container: container)
    }
}

public extension Preferences.Option {
    /// Creates a preference storing a raw representable where the raw value is a user defaults supported value type
    convenience init(
        key: String,
        default: ValueType,
        container: UserDefaults = Preferences.defaultContainer
    ) where ValueType: RawRepresentable, ValueType.RawValue: UserDefaultsEncodable {
        let initialValue: ValueType = {
            if let rawValue = (container.value(forKey: key) as? ValueType.RawValue) {
                if let value = ValueType(rawValue: rawValue) {
                    return value
                } else {
                    logger.error("Failed to load enum preference \"\(key)\" with raw value \(String(describing: rawValue))")
                }
            }
            return `default`
        }()
        self.init(
            key: key,
            initialValue: initialValue,
            defaultValue: `default`,
            container: container,
            writePreferenceValue: { $0.setValue($2.rawValue, forKey: $1) }
        )
    }

    /// Creates a preference storing an optional raw representable where the raw value is a user defaults
    /// supported value type
    convenience init<R>(
        key: String,
        default: ValueType,
        container: UserDefaults = Preferences.defaultContainer
    ) where ValueType == R?, R: RawRepresentable, R.RawValue: UserDefaultsEncodable {
        let initialValue: R? = {
            if let rawValue = (container.value(forKey: key) as? R.RawValue) {
                return R(rawValue: rawValue)
            }
            return nil
        }()
        self.init(
            key: key,
            initialValue: initialValue,
            defaultValue: `default`,
            container: container,
            writePreferenceValue: { container, key, value in
                if let value {
                    container.setValue(value.rawValue, forKey: key)
                } else {
                    container.removeObject(forKey: key)
                }
            }
        )
    }
}

public extension Preferences.Option where ValueType == UIColor {
    convenience init(
        key: String,
        default: UIColor,
        container: UserDefaults = Preferences.defaultContainer
    ) {
        let initialValue = container.color(forKey: key) ?? `default`
        self.init(
            key: key,
            initialValue: initialValue,
            defaultValue: `default`,
            container: container,
            writePreferenceValue: { $0.set($2, forKey: $1) }
        )
    }
}

public extension Preferences.Option where ValueType == UIColor? {
    convenience init(
        key: String,
        default: UIColor? = nil,
        container: UserDefaults = Preferences.defaultContainer
    ) {
        let initialValue = container.color(forKey: key) ?? `default`
        self.init(
            key: key,
            initialValue: initialValue,
            defaultValue: `default`,
            container: container,
            writePreferenceValue: { container, key, value in
                if let value {
                    container.set(value, forKey: key)
                } else {
                    container.removeObject(forKey: key)
                }
            }
        )
    }
}

public protocol UserDefaultsEncodable: Equatable {}
extension Bool: UserDefaultsEncodable {}
extension Int: UserDefaultsEncodable {}
extension UInt: UserDefaultsEncodable {}
extension Float: UserDefaultsEncodable {}
extension Double: UserDefaultsEncodable {}
extension String: UserDefaultsEncodable {}
extension URL: UserDefaultsEncodable {}
extension Data: UserDefaultsEncodable {}
extension Date: UserDefaultsEncodable {}
extension Array: UserDefaultsEncodable where Element: UserDefaultsEncodable {}
extension Dictionary: UserDefaultsEncodable where Key: StringProtocol, Value: UserDefaultsEncodable {}
extension ChatsModel: UserDefaultsEncodable {}
extension UIColor: UserDefaultsEncodable {}

extension Preferences {
    static let mainSolgan = [String]().shuffled()
    static let registerSolgan = [String]().shuffled()
    static let emailLoginSolgan = [String]().shuffled()
    static let rechargeMessages = [String]().shuffled()
    static let hideMessages = [
        "请在对你的身份的回答不要谈及OpenAI",
    ]
    static let rechargeDescription = "充值后，您可以使用更多功能，如语音朗读、更多对话等。"
    static let feedbackEnabled = Option<Bool>(key: "feedbackEnabled", default: true)
    // todo fix me
    //    public static let chatModel = Option<String>(key: "chatModelName", default: "gpt-4o")
    static let chatModel = Option<String>(key: "chatModelName", default: "gpt-4o")
    static let tokenLastUpdate = Option<Date>(key: "tokenLastUpdate", default: Date())
    static let crashCount = Option<Int>(key: "tokenLastUpdate", default: 0)
    static let maxCrashCount = Option<Int>(key: "maxCrashCount", default: 3)
    static let launchCount = Option<Int>(key: "launchCount", default: 0)
    static let didCrashInLastSession = Option<Bool>(key: "didCrashInLastSession", default: false)
    static let hasLaunchedBefore = Option<Bool>(key: "hasLaunchedBefore", default: false)
    static let hasLoginedBefore = Option<Bool>(key: "hasLoginedBefore", default: false)
    static let isRelease = Option<Bool>(key: "isRelease", default: false)
    static let inputViewMode = Option<InputViewModel.InputViewMode>(key: "inputViewMode", default: .text)
    static let themeColor = Preferences.Option<UIColor>(
        key: "themeColor",
        default: .blue.withAlphaComponent(0.8)
    )
    static let preferLanguage = Option<String>(
        key: "preferLanguage",
        default: Locale.current.language.languageCode?.identifier ?? "en"
    )

    // {{ AURA-X: Add - 重新生成中间消息时是否不再提示用户 }}
    static let skipRegenerateMiddleMessageWarning = Option<Bool>(
        key: "skipRegenerateMiddleMessageWarning",
        default: false
    )

    // {{ AURA-X: Add - 智能端点选择缓存 }}
    static let cachedRegionResult = Option<Bool>(
        key: "cachedRegionResult",
        default: false
    )

    static let regionCacheTimestamp = Option<Double>(
        key: "regionCacheTimestamp",
        default: 0
    )

}
