//
//  UserSettings.swift
//  JunShi
//
//  Created by md on 2024/4/19.
//

import Foundation

@propertyWrapper
struct UserDefaultBase<Value: Codable> {
    private let key: String
    private let defaultValue: Value

    init(key: String, defaultValue: Value) {
        self.key = key
        self.defaultValue = defaultValue
    }

    var wrappedValue: Value {
        get {
            // 从 UserDefaults 中获取值，如果不存在则返回默认值
            guard let data = UserDefaults.standard.data(forKey: key) else {
                return defaultValue
            }
            let decoder = JSONDecoder()
            if let decodedValue = try? decoder.decode(Value.self, from: data) {
                return decodedValue
            }
            return defaultValue
        }
        set {
            // 将新值编码为 JSON 数据并存储在 UserDefaults 中
            let encoder = JSONEncoder()
            if let encodedValue = try? encoder.encode(newValue) {
                UserDefaults.standard.set(encodedValue, forKey: key)
            }
        }
    }
}

@propertyWrapper
struct UserDefault<T: Codable> {
    let key: String
    let defaultValue: T?

    init(key: String, defaultValue: T?) {
        self.key = key
        self.defaultValue = defaultValue
    }

    var wrappedValue: T? {
        get {
            if let data = UserDefaults.standard.object(forKey: key) as? Data {
                if let decoded = try? JSONDecoder().decode(T.self, from: data) {
                    return decoded
                }
            }
            return nil
        }
        set {
            if let encoded = try? JSONEncoder().encode(newValue) {
                UserDefaults.standard.set(encoded, forKey: key)
            }
        }
    }
}

enum UserSettings {
    @UserDefault(key: "user", defaultValue: nil)
    static var user: User?
}
