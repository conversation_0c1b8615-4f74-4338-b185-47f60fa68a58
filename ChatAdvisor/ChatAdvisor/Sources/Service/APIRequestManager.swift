//
//  APIRequestManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//  {{ AURA-X: Add - 创建API请求状态管理器，防止重复请求. Approval: mcp-feedback-enhanced(ID:20250129001). }}
//

import Foundation
import OSLog
import Combine

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "APIRequestManager")

/// API请求管理器，防止短时间内的重复请求
class APIRequestManager: ObservableObject {
    static let shared = APIRequestManager()
    
    // 请求状态枚举
    enum RequestState {
        case idle
        case loading
        case completed(Date)
        case failed(Date, Error)
    }
    
    // 请求配置
    struct RequestConfig {
        let endpoint: String
        let cacheDuration: TimeInterval // 缓存持续时间，秒
        let allowConcurrent: Bool // 是否允许并发请求
        
        static let `default` = RequestConfig(
            endpoint: "",
            cacheDuration: 30.0, // 默认30秒内不重复请求
            allowConcurrent: false
        )
    }
    
    // 存储请求状态
    private var requestStates: [String: RequestState] = [:]
    private let queue = DispatchQueue(label: "com.sanva.chatadvisor.requestManager", attributes: .concurrent)
    
    // 预定义的API配置
    private let apiConfigs: [String: RequestConfig] = [
        "/getConfig": RequestConfig(endpoint: "/getConfig", cacheDuration: 60.0, allowConcurrent: false),
        "/getPricing": RequestConfig(endpoint: "/getPricing", cacheDuration: 30.0, allowConcurrent: false),
        "/getProducts": RequestConfig(endpoint: "/getProducts", cacheDuration: 30.0, allowConcurrent: false),
        "/refreshToken": RequestConfig(endpoint: "/refreshToken", cacheDuration: 10.0, allowConcurrent: false)
    ]
    
    private init() {}
    
    /// 检查是否应该执行请求
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - forceRefresh: 是否强制刷新
    /// - Returns: 是否应该执行请求
    func shouldExecuteRequest(for endpoint: String, forceRefresh: Bool = false) -> Bool {
        return queue.sync {
            let config = apiConfigs[endpoint] ?? .default
            let currentState = requestStates[endpoint] ?? .idle
            
            // 如果强制刷新，直接允许
            if forceRefresh {
                logger.info("强制刷新请求: \(endpoint)")
                return true
            }
            
            switch currentState {
            case .idle:
                // 空闲状态，允许请求
                return true
                
            case .loading:
                // 正在加载中
                if config.allowConcurrent {
                    logger.info("允许并发请求: \(endpoint)")
                    return true
                } else {
                    logger.info("请求正在进行中，跳过重复请求: \(endpoint)")
                    return false
                }
                
            case .completed(let date):
                // 检查缓存是否过期
                let elapsed = Date().timeIntervalSince(date)
                if elapsed < config.cacheDuration {
                    logger.info("请求在缓存时间内，跳过重复请求: \(endpoint), 剩余: \(config.cacheDuration - elapsed)秒")
                    return false
                } else {
                    logger.info("缓存已过期，允许新请求: \(endpoint)")
                    return true
                }
                
            case .failed(let date, _):
                // 失败后等待一定时间再重试
                let retryInterval: TimeInterval = 5.0
                let elapsed = Date().timeIntervalSince(date)
                if elapsed < retryInterval {
                    logger.info("请求失败后重试间隔未到，跳过: \(endpoint), 剩余: \(retryInterval - elapsed)秒")
                    return false
                } else {
                    logger.info("失败重试间隔已到，允许重试: \(endpoint)")
                    return true
                }
            }
        }
    }
    
    /// 标记请求开始
    /// - Parameter endpoint: API端点
    func markRequestStarted(for endpoint: String) {
        queue.async(flags: .barrier) {
            self.requestStates[endpoint] = .loading
            logger.info("标记请求开始: \(endpoint)")
        }
    }
    
    /// 标记请求成功完成
    /// - Parameter endpoint: API端点
    func markRequestCompleted(for endpoint: String) {
        queue.async(flags: .barrier) {
            self.requestStates[endpoint] = .completed(Date())
            logger.info("标记请求完成: \(endpoint)")
        }
    }
    
    /// 标记请求失败
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - error: 错误信息
    func markRequestFailed(for endpoint: String, error: Error) {
        queue.async(flags: .barrier) {
            self.requestStates[endpoint] = .failed(Date(), error)
            logger.error("标记请求失败: \(endpoint), 错误: \(error.localizedDescription)")
        }
    }
    
    /// 清除特定端点的状态
    /// - Parameter endpoint: API端点
    func clearRequestState(for endpoint: String) {
        queue.async(flags: .barrier) {
            self.requestStates.removeValue(forKey: endpoint)
            logger.info("清除请求状态: \(endpoint)")
        }
    }
    
    /// 清除所有状态
    func clearAllStates() {
        queue.async(flags: .barrier) {
            self.requestStates.removeAll()
            logger.info("清除所有请求状态")
        }
    }
    
    /// 获取当前请求状态（用于调试）
    func getCurrentState(for endpoint: String) -> RequestState {
        return queue.sync {
            return requestStates[endpoint] ?? .idle
        }
    }
    
    /// 获取所有请求状态（用于调试）
    func getAllStates() -> [String: RequestState] {
        return queue.sync {
            return requestStates
        }
    }
}

// MARK: - 便利方法
extension APIRequestManager {
    
    /// 执行带有去重保护的请求
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - forceRefresh: 是否强制刷新
    ///   - request: 实际的请求闭包
    func executeRequestIfNeeded(
        endpoint: String,
        forceRefresh: Bool = false,
        request: @escaping () -> Void
    ) {
        guard shouldExecuteRequest(for: endpoint, forceRefresh: forceRefresh) else {
            return
        }
        
        markRequestStarted(for: endpoint)
        request()
    }
    
    /// 包装网络请求结果处理
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - result: 网络请求结果
    ///   - onSuccess: 成功回调
    ///   - onFailure: 失败回调
    func handleRequestResult<T, E: Error>(
        endpoint: String,
        result: Result<T, E>,
        onSuccess: @escaping (T) -> Void,
        onFailure: @escaping (E) -> Void
    ) {
        switch result {
        case .success(let data):
            markRequestCompleted(for: endpoint)
            onSuccess(data)
        case .failure(let error):
            markRequestFailed(for: endpoint, error: error)
            onFailure(error)
        }
    }
}