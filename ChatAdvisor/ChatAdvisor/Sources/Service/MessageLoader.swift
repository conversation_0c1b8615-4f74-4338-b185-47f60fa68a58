//
//  MessageLoader.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import Foundation
import Combine
import OSLog
import WCDBSwift

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "MessageLoader")
/// 消息加载状态
enum MessageLoadingState: Equatable {
    case idle
    case loading
    case loaded
    case error(String)
    case preloading

    static func == (lhs: MessageLoadingState, rhs: MessageLoadingState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.loading, .loading), (.loaded, .loaded), (.preloading, .preloading):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

/// 智能消息加载器 - 优化消息加载性能和用户体验
@MainActor
class MessageLoader: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published private(set) var loadingState: MessageLoadingState = .idle
    @Published private(set) var loadedMessages: [ChatMessage] = []
    @Published private(set) var hasMoreMessages: Bool = true
    @Published private(set) var isPreloadingOlder: Bool = false

    // 计算属性：检查是否正在加载
    private var isLoading: Bool {
        if case .loading = loadingState {
            return true
        }
        return false
    }
    
    // MARK: - Private Properties
    
    private let chatId: String
    private let pageSize: Int
    private var currentOffset: Int = 0
    private var messageCache: [String: [ChatMessage]] = [:]
    private var preloadedRanges: Set<Range<Int>> = []
    private let maxCacheSize = 1000 // 最大缓存消息数
    
    // MARK: - Initialization
    
    init(chatId: String, pageSize: Int = 50) {
        self.chatId = chatId
        self.pageSize = pageSize
    }
    
    // MARK: - Public Methods
    
    /// 加载初始消息
    func loadInitialMessages() async throws {
        guard !isLoading else { return }
        
        loadingState = .loading
        currentOffset = 0
        
        do {
            let messages = try await fetchMessages(offset: 0, limit: pageSize)
            
            loadedMessages = messages
            hasMoreMessages = messages.count >= pageSize
            currentOffset = messages.count
            
            // 缓存消息
            cacheMessages(messages, for: "initial")
            
            loadingState = .loaded
            
            // 预加载下一页
            if hasMoreMessages {
                Task {
                    await preloadNextPage()
                }
            }
            
        } catch {
            loadingState = .error(error.localizedDescription)
            throw error
        }
    }
    
    /// 加载更多历史消息
    func loadMoreMessages() async throws {
        guard !isLoading && hasMoreMessages else { return }
        
        loadingState = .loading
        
        do {
            let newMessages = try await fetchMessages(offset: currentOffset, limit: pageSize)
            
            // 插入到消息列表开头（历史消息）
            loadedMessages.insert(contentsOf: newMessages, at: 0)
            hasMoreMessages = newMessages.count >= pageSize
            currentOffset += newMessages.count
            
            // 缓存消息
            cacheMessages(newMessages, for: "page_\(currentOffset / pageSize)")
            
            loadingState = .loaded
            
            // 管理缓存大小
            manageCacheSize()
            
        } catch {
            loadingState = .error(error.localizedDescription)
            throw error
        }
    }
    
    /// 添加新消息（用户发送或接收）
    func addMessage(_ message: ChatMessage) {
        loadedMessages.append(message)
        
        // 异步保存到数据库
        Task {
            await AdvisorDatabaseManager.shared.update(message: message)
        }
    }
    
    /// 更新消息（用于流式响应）
    func updateMessage(_ message: ChatMessage) {
        if let index = loadedMessages.firstIndex(where: { $0.id == message.id }) {
            loadedMessages[index] = message
            
            // 异步更新数据库
            Task {
                await AdvisorDatabaseManager.shared.update(message: message)
            }
        }
    }
    
    /// 预加载指定范围的消息
    func preloadMessages(in range: Range<Int>) async {
        guard !preloadedRanges.contains(range) else { return }
        
        isPreloadingOlder = true
        defer { isPreloadingOlder = false }
        
        do {
            let messages = try await fetchMessages(offset: range.lowerBound, limit: range.count)
            cacheMessages(messages, for: "preload_\(range.lowerBound)")
            preloadedRanges.insert(range)
            
            logger.info("预加载消息成功: \(range)")
            
        } catch {
            logger.error("预加载消息失败: \(error)")
        }
    }
    
    /// 清理缓存
    func clearCache() {
        messageCache.removeAll()
        preloadedRanges.removeAll()
        loadedMessages.removeAll()
        currentOffset = 0
        hasMoreMessages = true
        loadingState = .idle
    }
    
    /// 获取缓存统计信息
    func getCacheStats() -> MessageCacheStats {
        let totalCachedMessages = messageCache.values.flatMap { $0 }.count
        let cacheKeys = Array(messageCache.keys)
        
        return MessageCacheStats(
            totalCachedMessages: totalCachedMessages,
            cacheKeys: cacheKeys,
            preloadedRanges: Array(preloadedRanges),
            currentOffset: currentOffset
        )
    }
    
    // MARK: - Private Methods
    
    private func fetchMessages(offset: Int, limit: Int) async throws -> [ChatMessage] {
        // 首先检查缓存
        let cacheKey = "offset_\(offset)_limit_\(limit)"
        if let cachedMessages = messageCache[cacheKey] {
            return cachedMessages
        }
        
        // 从数据库获取
        let messages = await AdvisorDatabaseManager.shared.fetchMessagesPaginated(
            chatId: chatId,
            beforeTimestamp: nil,
            limit: limit,
            offset: offset
        )
        
        // 缓存结果
        messageCache[cacheKey] = messages
        
        return messages
    }
    
    private func cacheMessages(_ messages: [ChatMessage], for key: String) {
        messageCache[key] = messages
        
        // 检查缓存大小
        let totalCached = messageCache.values.flatMap { $0 }.count
        if totalCached > maxCacheSize {
            manageCacheSize()
        }
    }
    
    private func manageCacheSize() {
        let totalCached = messageCache.values.flatMap { $0 }.count
        
        guard totalCached > maxCacheSize else { return }
        
        // 移除最旧的缓存项
        let sortedKeys = messageCache.keys.sorted()
        let keysToRemove = sortedKeys.prefix(sortedKeys.count / 3) // 移除1/3的缓存
        
        for key in keysToRemove {
            messageCache.removeValue(forKey: key)
        }

        logger.info("消息缓存清理完成，剩余: \(self.messageCache.count) 个缓存项")
    }
    
    private func preloadNextPage() async {
        guard hasMoreMessages else { return }
        
        let nextOffset = currentOffset
        let nextRange = nextOffset..<(nextOffset + pageSize)
        
        await preloadMessages(in: nextRange)
    }
}

// MARK: - Supporting Types

/// 消息缓存统计信息
struct MessageCacheStats {
    let totalCachedMessages: Int
    let cacheKeys: [String]
    let preloadedRanges: [Range<Int>]
    let currentOffset: Int
    
    var description: String {
        return """
        消息缓存统计:
        - 总缓存消息数: \(totalCachedMessages)
        - 缓存键数量: \(cacheKeys.count)
        - 预加载范围: \(preloadedRanges.count) 个
        - 当前偏移: \(currentOffset)
        """
    }
}

/// 智能消息加载策略
enum MessageLoadingStrategy {
    case lazy // 懒加载，按需加载
    case aggressive // 积极加载，预加载更多内容
    case balanced // 平衡模式，适中的预加载
    
    var pageSize: Int {
        switch self {
        case .lazy: return 20
        case .aggressive: return 100
        case .balanced: return 50
        }
    }
    
    var preloadThreshold: Int {
        switch self {
        case .lazy: return 5
        case .aggressive: return 20
        case .balanced: return 10
        }
    }
}

// MARK: - AdvisorDatabaseManager Extension

extension AdvisorDatabaseManager {

    /// 分页获取消息（优化版本）
    func fetchMessagesPaginated(
        chatId: String,
        beforeTimestamp: Int64? = nil,
        limit: Int = 50,
        offset: Int = 0
    ) async -> [ChatMessage] {
        guard let database else { return [] }

        do {
            return try await AdvisorDatabaseManager.shared.executeInDatabaseQueue { [weak self] in
                guard let self, let database = self.database else { return [] }

                var messages: [ChatMessage]

                if let beforeTimestamp = beforeTimestamp {
                    // 使用时间戳过滤
                    messages = try database.getObjects(
                        fromTable: "chatMessages",
                        where: Column(named: "chatId") == chatId && Column(named: "createdTime") < beforeTimestamp,
                        orderBy: [Column(named: "createdTime").order(.descending)],
                        limit: limit
                    )
                } else {
                    // 使用偏移量分页
                    messages = try database.getObjects(
                        fromTable: "chatMessages",
                        where: Column(named: "chatId") == chatId,
                        orderBy: [Column(named: "createdTime").order(.descending)],
                        limit: limit,
                        offset: offset
                    )
                }

                // 按时间正序排列（最旧的在前）
                let sortedMessages = messages.sorted { $0.createdTime < $1.createdTime }
                return sortedMessages
            }
        } catch {
            logger.error("分页获取消息失败: \(error)")
            return []
        }
    }
}
