//
//  BootsManager.swift
//  JunShi
//
//  Created by md on 2024/5/16.
//

import Foundation
import Moya
import Network
import OSLog
import StoreKit


private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "BootConfigManager")

class BootManager: NSObject, ObservableObject {
    static let shared = BootManager()
    @Published var isLoading = false
    @Published var isChina: Bool = false
    @Published var config: Configs = .default
    @Published var questions: [Question] = []
    @Published var allChatModels: [ChatsModel] = []

    override init() {
        super.init()
        // {{ AURA-X: Modify - 使用新的综合地区检测逻辑替代单一的 Storefront 检测 }}
        initializeRegionDetection()
        SKPaymentQueue.default().add(self)
    }

    /// 初始化智能端点选择
    private func initializeRegionDetection() {
        // 先使用快速的 Storefront 检测作为初始值
        isChina = SKPaymentQueue.default().storefront?.countryCode == "CHN"

        // 异步执行智能端点选择（地理位置 + 网络性能）
       
         _Concurrency.Task { [weak self] in
            let result = await RegionDetector.shared.detectOptimalEndpoint()
            await MainActor.run {
                self?.isChina = result.isChina
                logger.info("智能端点选择完成: \(result.selectedEndpoint), 置信度: \(result.confidence), 来源: \(result.sources.joined(separator: ", "))")
            }
        }
    }

    /// 手动触发端点重新选择
    /// - Parameter forceRefresh: 是否强制刷新，忽略缓存
    func refreshRegionDetection(forceRefresh: Bool = false) {
         _Concurrency.Task { [weak self] in
            let result = await RegionDetector.shared.detectOptimalEndpoint(forceRefresh: forceRefresh)
            await MainActor.run {
                self?.isChina = result.isChina
                logger.info("手动端点重新选择完成: \(result.selectedEndpoint), 置信度: \(result.confidence)")
            }
        }
    }

    func getConfig(forceRefresh: Bool = false) {
        // {{ AURA-X: Modify - 添加重复请求防护机制. Approval: mcp-feedback-enhanced(ID:20250129001). }}
        APIRequestManager.shared.executeRequestIfNeeded(
            endpoint: "/getConfig",
            forceRefresh: forceRefresh
        ) { [weak self] in
            guard let self else { return }
            
            executeOnMainThread {
                self.isLoading = true
            }
            
            NetworkService.shared.requestMulti(ConfigTarget.getConfig) { [weak self] (result: Result<NetworkResponse<Configs>, NetworkError>) in
                guard let self else { return }
                
                // 使用APIRequestManager处理结果
                APIRequestManager.shared.handleRequestResult(
                    endpoint: "/getConfig",
                    result: result,
                    onSuccess: { [weak self] response in
                        guard let self else { return }
                        DispatchQueue.main.async { [weak self] in
                            guard let self else { return }
                            if response.isSuccess {
                                self.config = response.data ?? Configs.default
                                logger.info("配置获取成功")
                            } else if self.config == nil {
                                self.config = Configs.default
                            }
                            self.isLoading = false
                        }
                    },
                    onFailure: { [weak self] error in
                        guard let self else { return }
                        DispatchQueue.main.async { [weak self] in
                            guard let self else { return }
                            logger.error("getConfig error: \(error)")
                            // 如果网络请求失败且config为nil，设置默认配置
                            if self.config == nil {
                                self.config = Configs.default
                            }
                            self.isLoading = false
                        }
                    }
                )
            }
        }
    }



    /// 主动检查版本更新
    /// - Parameter force: 是否强制检查
    func checkForUpdates(force: Bool = false) {
        // {{ AURA-X: Modify - 版本检测已迁移到闪屏页，这里保留接口但添加说明. Approval: mcp-feedback-enhanced(ID:20250129013). }}
        logger.info("主动检查版本更新... (注意：主要版本检测已在闪屏页完成)")
        VersionUpdateManager.shared.checkForUpdates(force: force)
    }

    func getPricing(forceRefresh: Bool = false) {
        // {{ AURA-X: Modify - 添加重复请求防护机制. Approval: mcp-feedback-enhanced(ID:20250129001). }}
        APIRequestManager.shared.executeRequestIfNeeded(
            endpoint: "/getPricing",
            forceRefresh: forceRefresh
        ) { [weak self] in
            guard let self else { return }
            
            NetworkService.shared.requestMulti(PricingTarget.getPricing) { [weak self] (result: Result<NetworkResponse<[ChatsModel]>, NetworkError>) in
                guard let self else { return }
                
                // 使用APIRequestManager处理结果
                APIRequestManager.shared.handleRequestResult(
                    endpoint: "/getPricing",
                    result: result,
                    onSuccess: { [weak self] response in
                        guard let self else { return }
                        DispatchQueue.main.async { [weak self] in
                            guard let self else { return }
                            if response.isSuccess {
                                self.allChatModels = response.data ?? []
                                ChatViewModel.allModels = response.data ?? []
                            } else {
                                self.allChatModels = []
                            }
                        }
                    },
                    onFailure: { [weak self] error in
                        guard let self else { return }
                        logger.error("getPricing failed: \(error.localizedDescription)")
                    }
                )
            }
        }
    }
}

extension BootManager: SKPaymentTransactionObserver {
    func paymentQueue(_: SKPaymentQueue, updatedTransactions _: [SKPaymentTransaction]) {}

    func paymentQueueDidChangeStorefront(_ queue: SKPaymentQueue) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            // {{ AURA-X: Modify - Storefront 变化时触发智能端点重新选择 }}
            logger.info("App Store Storefront 发生变化，触发智能端点重新选择")

            // 立即更新为 Storefront 的结果作为临时值
            isChina = queue.storefront?.countryCode == "CHN"

            // 异步执行完整的智能端点选择
             _Concurrency.Task { [weak self] in
                let result = await RegionDetector.shared.detectOptimalEndpoint(forceRefresh: true)
                await MainActor.run {
                    self?.isChina = result.isChina
                    logger.info("Storefront 变化后智能端点重新选择完成: \(result.selectedEndpoint)")
                }
            }
        }
    }

    func executeOnMainThread(_ block: @escaping () -> Void) {
        if Thread.isMainThread {
            block()
        } else {
            DispatchQueue.main.async {
                block()
            }
        }
    }
}
