//
//  PurchaseTarget.swift
//  JunShi
//
//  Created by md on 2024/5/27.
//

import Foundation
import <PERSON>ya

enum PurchaseTarget {
    case getProducts
    case verifyPurchase(userId: String, productIdentifier: String, receipt: String)
}

extension PurchaseTarget: TargetType {
    var baseURL: URL {
        NetworkURL.current
    }

    var path: String {
        switch self {
        case .verifyPurchase:
            "/verifyPurchase"
        case .getProducts:
            "/getProducts"
        }
    }

    var method: Moya.Method {
        switch self {
        case .verifyPurchase:
            .post
        case .getProducts:
            .get
        }
    }

    var task: Task {
        switch self {
        case let .verifyPurchase(userId, productIdentifier, receipt):
            .requestParameters(
                parameters: ["userId": userId, "productIdentifier": productIdentifier, "receipt": receipt],
                encoding: JSONEncoding.default
            )
        case .getProducts:
            .requestPlain
        }
    }

    var headers: [String: String]? {
        APIBase.commonHeaders()
    }
}
