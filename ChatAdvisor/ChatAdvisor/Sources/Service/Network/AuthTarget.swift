import Foundation
import Moya

struct UserCredentials: Codable {
    var email: String
    var password: String
}

enum AuthTarget {
    case register(credentials: UserCredentials)
    case login(credentials: UserCredentials)
    case loginWithApple(idToken: String)
    case loginWithGoogle(idToken: String)
    case loginWithTwitter(token: String, secret: String)
    case sendEmailCode(email: String)
    case verifyEmailCode(email: String, code: String, password: String)
    case refreshToken
    case deleteAccount(password: String?)
}

extension AuthTarget: TargetType {
    var baseURL: URL {
        NetworkURL.current
    }

    var path: String {
        switch self {
        case .register:
            "/register"
        case .login:
            "/login"
        case .loginWithApple:
            "/appleLogin"
        case .loginWithGoogle:
            "/googleLogin"
        case .loginWithTwitter:
            "/twitterLogin"
        case .sendEmailCode:
            "/sendEmailCode"
        case .verifyEmailCode:
            "/verifyEmailCode"
        case .refreshToken:
            "/refreshToken"
        case .deleteAccount:
            "/deleteAccount"
        }
    }

    var method: Moya.Method {
        switch self {
        case .register, .login, .sendEmailCode, .verifyEmailCode, .loginWithApple, .loginWithGoogle, .loginWithTwitter, .refreshToken:
            .post
        case .deleteAccount:
            .delete
        }
    }

    var task: Task {
        switch self {
        case let .register(credentials):
            return .requestJSONEncodable(credentials)
        case let .login(credentials):
            let params = ["email": credentials.email, "password": CryptoFunction.hashPassword(credentials.password)]
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case let .loginWithApple(idToken):
            let params = ["idToken": idToken, "appId": Bundle.main.bundleIdentifier ?? ""]
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case let .loginWithGoogle(idToken):
            let params = ["idToken": idToken, "appId": Bundle.main.bundleIdentifier ?? ""]
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case let .loginWithTwitter(token, secret):
            let params = ["token": token, "secret": secret]
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case let .sendEmailCode(email):
            let params = ["email": email]
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case let .verifyEmailCode(email, code, password):
            let params = ["email": email, "code": code, "password": CryptoFunction.hashPassword(password)]
            return .requestParameters(parameters: params, encoding: JSONEncoding.default)
        case let .deleteAccount(password):
            if let password {
                let params = ["password": CryptoFunction.hashPassword(password)]
                return .requestParameters(parameters: params, encoding: JSONEncoding.default)
            }
            return .requestPlain
        case .refreshToken:
            return .requestPlain
        }
    }

    var headers: [String: String]? {
        APIBase.commonHeaders()
    }
}
