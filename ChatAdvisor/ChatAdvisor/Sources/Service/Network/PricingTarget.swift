//
//  PricingTarget.swift
//  JunShi
//
//  Created by zweiteng on 2024/5/14.
//

import Foundation
import <PERSON>ya

enum PricingTarget {
    case getPricing
    case balance
}

extension PricingTarget: TargetType {
    var baseURL: URL {
        NetworkURL.current
    }

    var path: String {
        switch self {
        case .getPricing:
            "/getPricing"
        case .balance:
            "/userBalance"
        }
    }

    var method: Moya.Method {
        switch self {
        case .getPricing, .balance:
            .get
        }
    }

    var task: Task {
        .requestPlain
    }

    var headers: [String: String]? {
        APIBase.commonHeaders()
    }
}
