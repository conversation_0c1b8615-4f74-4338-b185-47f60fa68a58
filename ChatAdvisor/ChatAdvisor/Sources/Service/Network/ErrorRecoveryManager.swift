//
//  ErrorRecoveryManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog

private let errorRecoveryLogger = Logger(subsystem: "com.sanva.chatadvisor", category: "ErrorRecoveryManager")

/// 恢复动作类型
enum RecoveryAction {
    case retry
    case retryWithDelay(TimeInterval)
    case reconnect
    case fallbackToCache
    case showError(String)
    case none
}

/// 错误分类
enum ErrorCategory {
    case network
    case server
    case client
    case timeout
    case authentication
    case rateLimit
    case unknown
}

/// 错误恢复管理器
class ErrorRecoveryManager: @unchecked Sendable {
    private let maxRetryAttempts: Int
    private let retryDelays: [TimeInterval]
    private var retryCounters: [String: Int] = [:]
    private let queue = DispatchQueue(label: "com.sanva.chatadvisor.errorrecovery", qos: .utility)
    
    init(maxRetryAttempts: Int = 3, retryDelays: [TimeInterval] = [1.0, 2.0, 5.0]) {
        self.maxRetryAttempts = maxRetryAttempts
        self.retryDelays = retryDelays
    }
    
    // MARK: - 公开方法
    
    /// 处理网络错误
    func handleNetworkError(_ error: Error) async -> RecoveryAction {
        let category = categorizeError(error)
        let errorKey = generateErrorKey(error)
        
//        errorRecoveryLogger.info("处理错误: \(error.localizedDescription), 类别: \(category)")
        
        return await determineRecoveryAction(for: category, errorKey: errorKey, error: error)
    }
    
    /// 执行恢复动作
    func executeRecoveryAction(_ action: RecoveryAction) async throws {
        switch action {
        case .retry:
            errorRecoveryLogger.info("执行重试")
            // 立即重试，由调用方处理

        case .retryWithDelay(let delay):
            errorRecoveryLogger.info("延迟 \(delay) 秒后重试")
            try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))

        case .reconnect:
            errorRecoveryLogger.info("执行重连")
            // 重连逻辑由调用方处理

        case .fallbackToCache:
            errorRecoveryLogger.info("回退到缓存数据")
            // 缓存回退逻辑由调用方处理

        case .showError(let message):
            errorRecoveryLogger.warning("显示错误信息: \(message)")
            // 错误显示由调用方处理

        case .none:
            errorRecoveryLogger.info("无需恢复动作")
        }
    }
    
    /// 判断是否应该重试请求
    func shouldRetryRequest(for error: Error, attemptCount: Int) -> Bool {
        let category = categorizeError(error)
        
        // 超过最大重试次数
        guard attemptCount < maxRetryAttempts else {
            errorRecoveryLogger.warning("达到最大重试次数: \(attemptCount)")
            return false
        }
        
        // 根据错误类型决定是否重试
        switch category {
        case .network, .timeout:
            return true
        case .server:
            // 5xx错误可以重试
            if let networkError = error as? NetworkError,
               case .hostError = networkError {
                return true
            }
            return false
        case .rateLimit:
            return true // 限流错误可以重试
        case .authentication, .client:
            return false // 认证和客户端错误不重试
        case .unknown:
            return attemptCount < 2 // 未知错误最多重试一次
        }
    }
    
    /// 获取重试延迟时间
    func getRetryDelay(for attempt: Int) -> TimeInterval {
        let index = min(attempt - 1, retryDelays.count - 1)
        return retryDelays[index]
    }
    
    /// 重置错误计数器
    func resetErrorCounter(for errorKey: String) {
        queue.async { [weak self] in
            self?.retryCounters.removeValue(forKey: errorKey)
        }
    }
    
    /// 清理所有错误计数器
    func clearAllErrorCounters() {
        queue.async { [weak self] in
            self?.retryCounters.removeAll()
        }
    }
    
    // MARK: - 私有方法
    
    private func categorizeError(_ error: Error) -> ErrorCategory {
        if let networkError = error as? NetworkError {
            switch networkError {
            case .lostConnect, .timeout:
                return .network
            case .hostError:
                return .server
            case .badRequest:
                return .client
            case .response(let code, _):
                if code == 401 || code == 403 {
                    return .authentication
                } else if code == 429 {
                    return .rateLimit
                } else if code >= 500 {
                    return .server
                } else {
                    return .client
                }
            case .unknown, .fileNotFound, .cancelled:
                return .unknown
            }
        }
        
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet, .networkConnectionLost:
                return .network
            case .timedOut:
                return .timeout
            case .userAuthenticationRequired:
                return .authentication
            default:
                return .unknown
            }
        }
        
        return .unknown
    }
    
    private func generateErrorKey(_ error: Error) -> String {
        if let networkError = error as? NetworkError {
            switch networkError {
            case .response(let code, _):
                return "response_\(code)"
            case .hostError:
                return "host_error"
            case .badRequest:
                return "bad_request"
            default:
                return String(describing: networkError)
            }
        }
        
        if let urlError = error as? URLError {
            return "url_\(urlError.code.rawValue)"
        }
        
        return String(describing: type(of: error))
    }
    
    private func determineRecoveryAction(for category: ErrorCategory, errorKey: String, error: Error) async -> RecoveryAction {
        return await withCheckedContinuation { continuation in
            queue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(returning: .none)
                    return
                }
                
                let currentCount = self.retryCounters[errorKey, default: 0]
                self.retryCounters[errorKey] = currentCount + 1
                
                let action: RecoveryAction
                
                switch category {
                case .network:
                    if currentCount < self.maxRetryAttempts {
                        let delay = self.getRetryDelay(for: currentCount + 1)
                        action = .retryWithDelay(delay)
                    } else {
                        action = .showError("网络连接失败，请检查网络设置")
                    }
                    
                case .timeout:
                    if currentCount < self.maxRetryAttempts {
                        action = .retryWithDelay(2.0)
                    } else {
                        action = .showError("请求超时，请稍后重试")
                    }
                    
                case .server:
                    if currentCount < 2 {
                        action = .retryWithDelay(5.0)
                    } else {
                        action = .showError("服务器暂时不可用，请稍后重试")
                    }
                    
                case .rateLimit:
                    action = .retryWithDelay(10.0) // 限流错误等待更长时间
                    
                case .authentication:
                    action = .showError("认证失败，请重新登录")
                    
                case .client:
                    action = .showError("请求格式错误")
                    
                case .unknown:
                    if currentCount < 2 {
                        action = .retry
                    } else {
                        action = .showError("发生未知错误，请稍后重试")
                    }
                }
                
                continuation.resume(returning: action)
            }
        }
    }
}

/// 错误恢复策略配置
struct ErrorRecoveryConfig {
    let maxRetryAttempts: Int
    let retryDelays: [TimeInterval]
    let enableFallbackToCache: Bool
    let enableAutoReconnect: Bool
    
    static let `default` = ErrorRecoveryConfig(
        maxRetryAttempts: 3,
        retryDelays: [1.0, 2.0, 5.0],
        enableFallbackToCache: true,
        enableAutoReconnect: true
    )
    
    static let aggressive = ErrorRecoveryConfig(
        maxRetryAttempts: 5,
        retryDelays: [0.5, 1.0, 2.0, 4.0, 8.0],
        enableFallbackToCache: true,
        enableAutoReconnect: true
    )
    
    static let conservative = ErrorRecoveryConfig(
        maxRetryAttempts: 2,
        retryDelays: [2.0, 5.0],
        enableFallbackToCache: false,
        enableAutoReconnect: false
    )
}
