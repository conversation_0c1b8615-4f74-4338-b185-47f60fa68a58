//
//  ConnectionManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import Network
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ConnectionManager")

/// 连接状态
enum ConnectionState {
    case disconnected
    case connecting
    case connected
    case reconnecting
    case failed(Error)
}

/// 重连策略
struct ReconnectionStrategy {
    let maxAttempts: Int
    let baseDelay: TimeInterval
    let maxDelay: TimeInterval
    let backoffMultiplier: Double
    
    static let `default` = ReconnectionStrategy(
        maxAttempts: 5,
        baseDelay: 1.0,
        maxDelay: 30.0,
        backoffMultiplier: 2.0
    )
    
    func delay(for attempt: Int) -> TimeInterval {
        let delay = baseDelay * pow(backoffMultiplier, Double(attempt - 1))
        return min(delay, maxDelay)
    }
}

/// 连接管理器
class ConnectionManager: ObservableObject {
    @Published var connectionState: ConnectionState = .disconnected
    @Published var isNetworkAvailable: Bool = true
    
    private let networkMonitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "com.sanva.chatadvisor.networkmonitor")
    private let reconnectionStrategy: ReconnectionStrategy
    private var reconnectionTimer: Timer?
    private var currentAttempt: Int = 0
    
    // 连接回调
    var onConnectionStateChanged: ((ConnectionState) -> Void)?
    var onNetworkStatusChanged: ((Bool) -> Void)?
    
    init(reconnectionStrategy: ReconnectionStrategy = .default) {
        self.reconnectionStrategy = reconnectionStrategy
        setupNetworkMonitoring()
    }
    
    // MARK: - 网络监控
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                let isAvailable = path.status == .satisfied
                self?.isNetworkAvailable = isAvailable
                self?.onNetworkStatusChanged?(isAvailable)
                
                if isAvailable {
                    logger.info("网络连接恢复")
                    // 网络恢复时，如果之前连接失败，尝试重连
                    if case .failed = self?.connectionState {
                        self?.attemptReconnection()
                    }
                } else {
                    logger.warning("网络连接断开")
                    self?.updateConnectionState(.failed(ConnectionError.noConnection))
                }
            }
        }
        
        networkMonitor.start(queue: monitorQueue)
    }
    
    // MARK: - 连接管理
    
    /// 建立连接
    func establishConnection(url: URL, headers: [String: String] = [:]) async throws {
        guard isNetworkAvailable else {
            throw ConnectionError.noConnection
        }
        
        updateConnectionState(.connecting)
        currentAttempt = 0
        
        do {
            // 这里可以添加具体的连接逻辑
            // 例如：WebSocket连接、HTTP长连接等
            logger.info("建立连接: \(url.absoluteString)")
            
            // 模拟连接过程
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            
            updateConnectionState(.connected)
            logger.info("连接建立成功")
            
        } catch {
            logger.error("连接建立失败: \(error.localizedDescription)")
            updateConnectionState(.failed(error))
            throw error
        }
    }
    
    /// 断开连接
    func disconnect() {
        stopReconnectionTimer()
        updateConnectionState(.disconnected)
        logger.info("连接已断开")
    }
    
    /// 处理连接失败
    func handleConnectionFailure(_ error: Error) async {
        logger.error("连接失败: \(error.localizedDescription)")
        updateConnectionState(.failed(error))
        
        if shouldAttemptReconnection() {
            await scheduleReconnection()
        }
    }
    
    /// 尝试重连
    func attemptReconnection() {
        guard shouldAttemptReconnection() else {
            logger.warning("达到最大重连次数，停止重连")
            return
        }
        
        Task {
            await scheduleReconnection()
        }
    }
    
    // MARK: - 私有方法
    
    private func updateConnectionState(_ newState: ConnectionState) {
        DispatchQueue.main.async { [weak self] in
            self?.connectionState = newState
            self?.onConnectionStateChanged?(newState)
        }
    }
    
    private func shouldAttemptReconnection() -> Bool {
        return isNetworkAvailable && currentAttempt < reconnectionStrategy.maxAttempts
    }
    
    private func scheduleReconnection() async {
        guard shouldAttemptReconnection() else { return }
        
        currentAttempt += 1
        let delay = reconnectionStrategy.delay(for: currentAttempt)
        
        logger.info("计划重连 (第 \(self.currentAttempt) 次)，延迟 \(delay) 秒")
        updateConnectionState(.reconnecting)
        
        try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
        
        guard shouldAttemptReconnection() else { return }
        
        // 这里应该调用具体的重连逻辑
        // 例如：重新建立WebSocket连接
        logger.info("执行重连 (第 \(self.currentAttempt) 次)")
    }
    
    private func stopReconnectionTimer() {
        reconnectionTimer?.invalidate()
        reconnectionTimer = nil
        currentAttempt = 0
    }
    
    deinit {
        networkMonitor.cancel()
        stopReconnectionTimer()
    }
}

/// 连接错误类型
enum ConnectionError: Error, LocalizedError {
    case noConnection
    case timeout
    case invalidURL
    case invalidResponse
    case serverError(Int)
    case unknown(Error)
    
    var errorDescription: String? {
        switch self {
        case .noConnection:
            return "网络连接不可用"
        case .timeout:
            return "连接超时"
        case .invalidURL:
            return "无效的URL"
        case .invalidResponse:
            return "无效的响应"
        case .serverError(let code):
            return "服务器错误: \(code)"
        case .unknown(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }
}
