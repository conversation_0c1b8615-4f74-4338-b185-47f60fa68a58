import Alamofire
import Foundation
import Moya

class NetworkService {
    typealias ProgressCallback = (TargetType, Double) -> Void

    static let shared = NetworkService()
    /// 网络请求队列
    private let requestQueue: DispatchQueue = .init(label: "NetworkRequestQueue")
    /// 网络请求回调队列
    private let queue: DispatchQueue = .init(label: "NetworkQueue", attributes: .concurrent)

    private init() {}

    // 使用泛型和Result类型简化回调定义
    typealias ResponseCallback<T: Codable> = (Result<NetworkResponse<T>, NetworkError>) -> Void

    // 异步方法支持
    @discardableResult
    func requestMulti<T: Codable>(_ api: TargetType,
                                  params _: [String: Any]? = nil,
                                  progress: ProgressCallback? = nil,
                                  completion: @escaping ResponseCallback<T>) -> Cancellable
    {
        let provider = createProvider()

        return provider.request(MultiTarget(api), callbackQueue: queue) { progressResult in
            progress?(api, progressResult.progress)
        } completion: { result in
            switch result {
            case let .success(res):
                do {
                    if res.statusCode == 401 || res.statusCode == 403 {
                        completion(.failure(.init(error: NSError(domain: "", code: res.statusCode, userInfo: [NSLocalizedDescriptionKey: "Unauthorized"]))))
                        NotificationCenter.default.post(name: .tokenExpired, object: nil)
                    } else {
                        let decodedResponse = try JSONDecoder().decode(NetworkResponse<T>.self, from: res.data)
                        if decodedResponse.code == 401 || decodedResponse.code == 403 {
                            completion(.failure(.init(error: NSError(domain: "", code: res.statusCode, userInfo: [NSLocalizedDescriptionKey: "Unauthorized"]))))
                            NotificationCenter.default.post(name: .tokenExpired, object: nil)
                        }
                        completion(.success(decodedResponse))
                    }
                    FirebaseManager.shared.logEvent("请求成功_\(api.path.replacingOccurrences(of: "/", with: ""))")
                } catch {
                    FirebaseManager.shared.logEvent("请求结果错误_\(api.path.replacingOccurrences(of: "/", with: ""))_\((error as NSError).userInfo)", parameters: ["error": error.localizedDescription])
                    completion(.failure(.init(error: error as NSError)))
                }
            case let .failure(err):
                FirebaseManager.shared.logEvent("网络错误_\(api.path.replacingOccurrences(of: "/", with: ""))_\((err as NSError).userInfo)", parameters: ["error": err.localizedDescription])
                let networkError = NetworkError(with: err)
                completion(.failure(networkError))
            }
        }
    }

    private func createProvider() -> MoyaProvider<MultiTarget> {
        let endPointClosure = { (target: MultiTarget) -> Endpoint in
            MoyaProvider.defaultEndpointMapping(for: target)
        }

        let requestClosure = { (endpoint: Endpoint, done: MoyaProvider.RequestResultClosure) in
            do {
                var request: URLRequest = try endpoint.urlRequest()
                request.timeoutInterval = 15
                // 支持请求体签名
                if let body = request.httpBody {
                    if let signature = CryptoFunction.createSignature(data: body) {
                        request.setValue(signature, forHTTPHeaderField: "signature")
                    }
                }
                done(.success(request))
            } catch {
                done(.failure(MoyaError.requestMapping(endpoint.url)))
            }
        }

        return MoyaProvider<MultiTarget>(endpointClosure: endPointClosure, requestClosure: requestClosure, plugins: [])
    }

    private func createProvider<T: Codable>(onData _: @escaping (T) -> Void, onError _: @escaping (Error) -> Void) -> MoyaProvider<MultiTarget> {
        let endPointClosure = { (target: MultiTarget) -> Endpoint in
            MoyaProvider.defaultEndpointMapping(for: target)
        }

        let requestClosure = { (endpoint: Endpoint, done: MoyaProvider.RequestResultClosure) in
            do {
                var request = try endpoint.urlRequest()
                request.timeoutInterval = 15
                done(.success(request))
            } catch {
                done(.failure(MoyaError.requestMapping(endpoint.url)))
            }
        }

        return MoyaProvider<MultiTarget>(endpointClosure: endPointClosure, requestClosure: requestClosure, plugins: [NetworkLoggerPlugin(configuration: .init(logOptions: .verbose))])
    }
}
