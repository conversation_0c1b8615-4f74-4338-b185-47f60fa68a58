//
//  ConfigTarget.swift
//  帮聊
//
//  Created by zweiten<PERSON> on 2024/6/2.
//

import Foundation
import Moya

enum ConfigTarget {
    case getConfig
}

extension ConfigTarget: TargetType {
    var baseURL: URL {
        NetworkURL.current
    }

    var path: String {
        switch self {
        case .getConfig:
            "/getConfig"
        }
    }

    var method: Moya.Method {
        switch self {
        case .getConfig:
            .get
        }
    }

    var task: Task {
        switch self {
        case .getConfig:
            .requestPlain
        }
    }

    var headers: [String: String]? {
        APIBase.commonHeaders()
    }
}
