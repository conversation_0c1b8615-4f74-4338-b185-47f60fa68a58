import Foundation
import Moya
import UniformTypeIdentifiers

enum ChatTarget {
    case sendMessage(chats: [ChatMessage])
    case generateQuestion
    case uploadAudio(assetURL: URL)
}

extension ChatTarget: TargetType {
    var baseURL: URL {
        NetworkURL.current
    }

    var path: String {
        switch self {
        case .sendMessage:
            "/chat"
        case .generateQuestion:
            "/generateQuestion"
        case .uploadAudio:
            "/uploadAudio"
        }
    }

    var method: Moya.Method {
        switch self {
        case .sendMessage, .generateQuestion, .uploadAudio:
            .post
        }
    }

    var task: Task {
        switch self {
        case let .sendMessage(chats):
            // 包装消息数组为正确的请求格式，包含 messages 字段
            let messages = chats.map { ["role": $0.role.rawValue, "content": $0.content] }
            let requestBody = ["messages": messages]
            return .requestCustomJSONEncodable(requestBody, encoder: JSONEncoder())
        case .generateQuestion:
            return .requestPlain
        case let .uploadAudio(assetURL):
            do {
                let fileData = try Data(contentsOf: assetURL)
                let fileName = assetURL.lastPathComponent
                let mimeType = mimeType(for: assetURL) ?? "application/octet-stream"
                let formData = MultipartFormData(provider: .data(fileData), name: "audio", fileName: fileName, mimeType: mimeType)
                return .uploadMultipart([formData])
            } catch {
                print("Failed to read file data: \(error)")
                return .requestPlain
            }
        }
    }

    var headers: [String: String]? {
        var header = APIBase.commonHeaders()
        switch self {
        case .sendMessage:
            header["Accept"] = "text/event-stream"
        case .generateQuestion:
            break
        case let .uploadAudio(assetURL):
            header["Content-Type"] = mimeType(for: assetURL) ?? "multipart/form-data"
//            header["prefer-language"] = Locale.preferredLanguages.first
        }
        return header
    }

    private func mimeType(for url: URL) -> String? {
        let pathExtension = url.pathExtension
        guard let utType = UTType(filenameExtension: pathExtension) else {
            return nil
        }
        return utType.preferredMIMEType
    }
}
