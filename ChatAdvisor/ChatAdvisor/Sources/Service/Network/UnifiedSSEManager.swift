//
//  UnifiedSSEManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2024/12/26.
//  Updated by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "UnifiedSSEManager")

/// 统一的SSE管理器协议
protocol UnifiedSSEManagerProtocol {
    associatedtype EventType: Codable
    
    var onEventReceived: ((EventType) -> Void)? { get set }
    var onError: ((Error) -> Void)? { get set }
    var onConnectionOpened: (() -> Void)? { get set }
    var onConnectionClosed: (() -> Void)? { get set }
    
    func connect(url: URL, method: String, httpBody: Data?, headers: [String: String])
    func disconnect()
    var isConnected: Bool { get }
}

/// SSE连接状态
enum SSEConnectionState: Equatable {
    case disconnected
    case connecting
    case connected
    case error(Error)

    static func == (lhs: SSEConnectionState, rhs: SSEConnectionState) -> <PERSON><PERSON> {
        switch (lhs, rhs) {
        case (.disconnected, .disconnected),
             (.connecting, .connecting),
             (.connected, .connected):
            return true
        case (.error, .error):
            // 对于错误状态，我们只比较状态类型，不比较具体的错误内容
            return true
        default:
            return false
        }
    }
}

/// SSE错误类型
enum SSEError: Error, LocalizedError {
    case invalidURL
    case connectionFailed(Error)
    case dataConversionFailed
    case jsonDecodingFailed(Error)
    case connectionClosed
    case timeout

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .connectionFailed(let error):
            return "连接失败: \(error.localizedDescription)"
        case .dataConversionFailed:
            return "数据转换失败"
        case .jsonDecodingFailed(let error):
            return "JSON解析失败: \(error.localizedDescription)"
        case .connectionClosed:
            return "连接已关闭"
        case .timeout:
            return "连接超时"
        }
    }
}

/// 数据格式类型
enum DataFormat {
    case standardSSE    // 标准SSE格式 (data: {...})
    case directJSON     // 直接JSON格式 ({...})
}

/// 统一的SSE管理器实现
final class UnifiedSSEManager<T: Codable>: NSObject, UnifiedSSEManagerProtocol, URLSessionDataDelegate {
    typealias EventType = T

    // MARK: - 公开属性
    var onEventReceived: ((T) -> Void)?
    var onError: ((Error) -> Void)?
    var onConnectionOpened: (() -> Void)?
    var onConnectionClosed: (() -> Void)?

    var isConnected: Bool {
        return connectionState == .connected
    }

    // MARK: - 私有属性
    private var urlSession: URLSession!
    private var dataTask: URLSessionDataTask?
    private var connectionState: SSEConnectionState = .disconnected
    private var dataBuffer = ""
    private var jsonBuffer = "" // JSON数据缓冲区
    private let processQueue = DispatchQueue(label: "com.sanva.UnifiedSSEManager.process", qos: .userInitiated)
    private let callbackQueue = DispatchQueue.main
    private let bufferLock = NSLock() // 缓冲区锁

    // 连接超时管理
    private var connectionTimeoutTimer: Timer?
    private let connectionTimeout: TimeInterval = 30.0

    // 新增：集成的管理器
    private let connectionManager: ConnectionManager
    private let errorRecoveryManager: ErrorRecoveryManager
    private let networkStatusManager = NetworkStatusManager.shared

    // 重连相关属性
    private var lastRequest: URLRequest?
    private var shouldReconnect: Bool = false
    private var reconnectAttempts: Int = 0
    private let maxReconnectAttempts: Int = 3
    private var reconnectDelay: TimeInterval = 1.0
    private let maxReconnectDelay: TimeInterval = 30.0

    // SSE常量
    private let sseDataPrefix = "data:"
    private let sseEventPrefix = "event:"
    private let sseIdPrefix = "id:"
    private let sseRetryPrefix = "retry:"
    private let sseCommentPrefix = ":"
    private let streamEndMarker = "[DONE]"

    // MARK: - 初始化
    init(connectionManager: ConnectionManager? = nil, errorRecoveryManager: ErrorRecoveryManager? = nil) {
        self.connectionManager = connectionManager ?? ConnectionManager()
        self.errorRecoveryManager = errorRecoveryManager ?? ErrorRecoveryManager()
        super.init()
        setupURLSession()
    }

    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 300
        config.waitsForConnectivity = true
        urlSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }
    
    // MARK: - 公开方法
    func connect(url: URL, method: String = "POST", httpBody: Data?, headers: [String: String]) {
        logger.info("请求建立SSE连接: \(url.absoluteString)")

        // 确保之前的连接已完全关闭
        disconnect()

        // 使用异步方式立即开始新连接，无需等待
        Task { [weak self] in
            guard let self = self else { return }

            // 短暂延迟确保disconnect操作完成，但不阻塞主线程
            try? await Task.sleep(nanoseconds: 50_000_000) // 50ms

            await self.performConnection(url: url, method: method, httpBody: httpBody, headers: headers)
        }
    }

    private func performConnection(url: URL, method: String, httpBody: Data?, headers: [String: String]) async {
        // 检查是否已经在连接或已连接状态
        guard connectionState == .disconnected else {
            logger.warning("连接已存在或正在连接中，跳过新连接请求")
            return
        }

        do {
            // 使用ConnectionManager检查网络状态
            try await connectionManager.establishConnection(url: url, headers: headers)

            // 再次检查状态，确保在网络检查期间没有被取消
            guard connectionState == .disconnected else {
                logger.warning("连接在网络检查期间被取消")
                return
            }

            connectionState = .connecting
            shouldReconnect = true

            // 在后台队列清理缓冲区，避免阻塞
            processQueue.async { [weak self] in
                guard let self = self else { return }
                self.bufferLock.lock()
                self.dataBuffer = ""
                self.bufferLock.unlock()
            }

            var request = URLRequest(url: url)
            request.httpMethod = method
            request.allHTTPHeaderFields = headers
            request.httpBody = httpBody
            request.timeoutInterval = 30 // 添加请求超时

            // 确保设置了正确的SSE头部
            request.setValue("text/event-stream", forHTTPHeaderField: "Accept")
            request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")

            // 保存请求以便重连使用
            lastRequest = request

            logger.info("开始建立SSE连接: \(url.absoluteString)")
            networkStatusManager.setConnectionStatus(.connecting, isUserInitiated: false)

            // 在主线程启动连接任务
            await MainActor.run { [weak self] in
                guard let self = self else { return }
                guard self.connectionState == .connecting else {
                    logger.warning("连接状态已改变，取消连接任务")
                    return
                }

                // 启动连接超时定时器
                self.startConnectionTimeout()

                self.dataTask = self.urlSession.dataTask(with: request)
                self.dataTask?.resume()
                logger.info("SSE连接任务已启动")
            }

        } catch {
            logger.error("连接建立失败: \(error.localizedDescription)")
            await handleConnectionError(error)
        }
    }
    
    func disconnect() {
        logger.info("开始断开SSE连接")

        shouldReconnect = false

        // 停止连接超时定时器
        stopConnectionTimeout()

        // 记录当前状态用于后续判断
        let wasConnectedOrConnecting = connectionState == .connected || connectionState == .connecting

        // 立即更新连接状态，防止新的连接尝试
        connectionState = .disconnected

        // 取消当前任务（在主线程执行以确保线程安全）
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.dataTask?.cancel()
            self.dataTask = nil
            self.lastRequest = nil
        }

        // 在后台队列清理数据缓冲区，避免阻塞主线程
        processQueue.async { [weak self] in
            guard let self = self else { return }

            self.bufferLock.lock()
            self.dataBuffer = ""
            self.jsonBuffer = ""
            self.bufferLock.unlock()

            // 重置重连状态
            self.reconnectAttempts = 0

            // 在主线程更新网络状态和通知回调
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }

                self.networkStatusManager.setConnectionStatus(.disconnected)

                if wasConnectedOrConnecting {
                    self.onConnectionClosed?()
                }

                logger.info("SSE连接断开完成")
            }
        }
    }

    // MARK: - 错误处理方法

    private func handleConnectionError(_ error: Error) async {
        logger.error("连接错误: \(error.localizedDescription)")

        let recoveryAction = await errorRecoveryManager.handleNetworkError(error)

        do {
            try await errorRecoveryManager.executeRecoveryAction(recoveryAction)

            switch recoveryAction {
            case .retry, .retryWithDelay:
                if shouldReconnect && reconnectAttempts < maxReconnectAttempts {
                    await attemptReconnectionWithRecovery()
                } else {
                    await notifyConnectionError(error)
                }
            case .reconnect:
                await attemptReconnectionWithRecovery()
            case .showError(let message):
                await notifyConnectionError(NSError(domain: "UnifiedSSEManager", code: -1, userInfo: [NSLocalizedDescriptionKey: message]))
            case .fallbackToCache, .none:
                await notifyConnectionError(error)
            }
        } catch {
            await notifyConnectionError(error)
        }
    }

    private func attemptReconnectionWithRecovery() async {
        guard let lastRequest = lastRequest,
              shouldReconnect,
              reconnectAttempts < maxReconnectAttempts else {
            logger.warning("无法重连：缺少必要条件")
            return
        }

        reconnectAttempts += 1
        logger.info("尝试重连 (第 \(self.reconnectAttempts) 次)")

        let delay = errorRecoveryManager.getRetryDelay(for: reconnectAttempts)
        try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.dataTask = self.urlSession.dataTask(with: lastRequest)
            self.dataTask?.resume()
        }
    }

    private func notifyConnectionError(_ error: Error) async {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.connectionState = .error(error)
            self.networkStatusManager.setConnectionStatus(.disconnected)
            self.onError?(error)
        }
    }

    // MARK: - 连接超时管理

    private func startConnectionTimeout() {
        stopConnectionTimeout() // 清理之前的定时器

        connectionTimeoutTimer = Timer.scheduledTimer(withTimeInterval: connectionTimeout, repeats: false) { [weak self] _ in
            guard let self = self else { return }

            logger.warning("连接超时，自动断开连接")

            // 超时处理
            let timeoutError = SSEError.connectionFailed(
                NSError(domain: "UnifiedSSEManager",
                       code: -1001,
                       userInfo: [NSLocalizedDescriptionKey: "连接超时"])
            )

            self.handleError(timeoutError)
            self.disconnect()
        }
    }

    private func stopConnectionTimeout() {
        connectionTimeoutTimer?.invalidate()
        connectionTimeoutTimer = nil
    }

    deinit {
        stopConnectionTimeout()
        disconnect()
    }

    // MARK: - URLSessionDataDelegate
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {

        guard let httpResponse = response as? HTTPURLResponse else {
            let error = SSEError.connectionFailed(NSError(domain: "UnifiedSSEManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的HTTP响应"]))
            handleError(error)
            completionHandler(.cancel)
            return
        }

        logger.info("收到HTTP响应，状态码: \(httpResponse.statusCode)")

        if httpResponse.statusCode == 200 {
            connectionState = .connected
            stopConnectionTimeout() // 连接成功，停止超时定时器
            networkStatusManager.setConnectionStatus(.connected, isUserInitiated: false)
            networkStatusManager.resetRetryCount()
            callbackQueue.async { [weak self] in
                self?.onConnectionOpened?()
            }
            completionHandler(.allow)
        } else {
            let error = SSEError.connectionFailed(NSError(domain: "UnifiedSSEManager", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "HTTP错误: \(httpResponse.statusCode)"]))
            handleError(error)
            completionHandler(.cancel)
        }
    }

    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
        // 检查连接状态，确保仍在有效连接中
        guard connectionState == .connected || connectionState == .connecting else {
            return
        }

        guard let stringData = String(data: data, encoding: .utf8) else {
            logger.error("数据转换失败: 无法将接收到的数据转换为UTF-8字符串")
            handleError(SSEError.dataConversionFailed)
            return
        }

        logger.debug("接收到原始数据 (\(data.count) 字节): \(stringData.prefix(200))...")

        // 使用弱引用避免循环引用，并在处理队列中异步处理
        processQueue.async { [weak self] in
            guard let self = self else { return }
            // 再次检查状态，确保在队列等待期间连接没有被断开
            guard self.connectionState == .connected || self.connectionState == .connecting else {
                logger.debug("数据处理时连接已断开，跳过处理")
                return
            }
            self.processReceivedData(stringData)
        }
    }

    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            if (error as NSError).code == NSURLErrorCancelled {
                logger.info("SSE连接被取消")
                return
            }

            // 如果需要重连且网络可用，尝试自动重连
            if shouldReconnect && networkStatusManager.isNetworkAvailable {
                Task {
                    await handleConnectionError(error)
                }
            } else {
                handleError(SSEError.connectionFailed(error))
            }
        } else {
            logger.info("SSE连接正常结束")
            connectionState = .disconnected
            // 正常结束的连接不显示断开状态
            networkStatusManager.setConnectionStatus(.disconnected, isUserInitiated: false)
            callbackQueue.async { [weak self] in
                self?.onConnectionClosed?()
            }
        }
    }

    // MARK: - 私有方法

    /// 尝试自动重连
    private func attemptReconnection() {
        guard let request = lastRequest, shouldReconnect else { return }

        // 检查重连次数限制
        guard reconnectAttempts < maxReconnectAttempts else {
            logger.error("达到最大重连次数限制，停止重连")
            handleError(SSEError.connectionFailed(NSError(domain: "UnifiedSSEManager", code: -2, userInfo: [NSLocalizedDescriptionKey: "连接失败，已达到最大重试次数"])))
            return
        }

        reconnectAttempts += 1
        logger.info("尝试自动重连SSE (第 \(self.reconnectAttempts) 次)")

        // 使用指数退避策略
        DispatchQueue.main.asyncAfter(deadline: .now() + reconnectDelay) { [weak self] in
            guard let self = self, self.shouldReconnect else { return }

            self.networkStatusManager.attemptReconnection { [weak self] success in
                guard let self = self, success, self.shouldReconnect else {
                    // 重连失败，增加延迟时间
                    self?.reconnectDelay = min(self?.reconnectDelay ?? 1.0 * 2, self?.maxReconnectDelay ?? 30.0)
                    // 继续尝试重连
                    self?.attemptReconnection()
                    return
                }

                DispatchQueue.main.async {
                    self.connectionState = .connecting
                    self.bufferLock.lock()
                    self.dataBuffer = ""
                    self.jsonBuffer = ""
                    self.bufferLock.unlock()

                    self.dataTask = self.urlSession.dataTask(with: request)
                    self.dataTask?.resume()

                    logger.info("SSE重连成功")
                    // 重连成功，重置延迟时间
                    self.reconnectDelay = 1.0
                }
            }
        }
    }

    // MARK: - 数据处理
    private func processReceivedData(_ data: String) {
        logger.debug("处理接收数据: '\(data)' (长度: \(data.count))")

        // 使用更短的锁持有时间
        var currentBuffer: String
        bufferLock.lock()
        dataBuffer += data
        currentBuffer = dataBuffer
        bufferLock.unlock()

        logger.debug("当前缓冲区内容: '\(currentBuffer)' (总长度: \(currentBuffer.count))")

        // 检测数据格式并相应处理
        if detectDataFormat(currentBuffer) == .directJSON {
            logger.debug("检测到直接JSON格式，尝试直接解析")
            processDirectJSON()
        } else {
            logger.debug("使用标准SSE格式处理")
            processSSEFormat()
        }
    }

    /// 检测数据格式
    private func detectDataFormat(_ data: String) -> DataFormat {
        let trimmed = data.trimmingCharacters(in: .whitespacesAndNewlines)

        // 如果数据以"data:"开头，认为是标准SSE格式
        if trimmed.hasPrefix("data:") {
            return .standardSSE
        }

        // 如果数据看起来像JSON对象或数组，认为是直接JSON格式
        if (trimmed.hasPrefix("{") && trimmed.contains("}")) ||
           (trimmed.hasPrefix("[") && trimmed.contains("]")) {
            return .directJSON
        }

        // 默认使用标准SSE格式
        return .standardSSE
    }

    /// 处理直接JSON格式的数据
    private func processDirectJSON() {
        // 尝试直接解析缓冲区中的JSON数据
        let trimmed = self.dataBuffer.trimmingCharacters(in: .whitespacesAndNewlines)

        // 检查是否是完整的JSON
        if isCompleteJSON(trimmed) {
            logger.debug("检测到完整的JSON数据，开始解析")
            processEventData(trimmed, eventInfo: [:])
            self.dataBuffer = "" // 清空缓冲区
        } else {
            logger.debug("JSON数据不完整，继续缓冲")
        }
    }

    /// 处理标准SSE格式的数据
    private func processSSEFormat() {
        // 按行处理数据，使用更精确的行分割
        let lines = self.dataBuffer.components(separatedBy: CharacterSet.newlines)
        logger.debug("分割后行数: \(lines.count)")

        // 保留最后一行（可能不完整）
        if lines.count > 1 {
            self.dataBuffer = lines.last ?? ""
            logger.debug("保留最后一行: '\(self.dataBuffer)'")

            // 处理完整的行，过滤空行
            let completeLines = Array(lines.dropLast()).filter { !$0.trimmingCharacters(in: .whitespaces).isEmpty }
            logger.debug("完整行数: \(completeLines.count)")

            if !completeLines.isEmpty {
                logger.debug("开始处理完整行: \(completeLines)")
                processSSELines(completeLines)
            } else {
                logger.debug("没有完整行需要处理")
            }
        } else {
            logger.debug("数据不包含换行符，继续缓冲")
        }
    }
    
    private func processSSELines(_ lines: [String]) {
        var eventData: [String: String] = [:]
        var dataLines: [String] = []

        logger.debug("处理SSE行，总行数: \(lines.count)")

        for (index, line) in lines.enumerated() {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)
            logger.debug("处理第\(index + 1)行: '\(trimmedLine)'")

            // 空行表示事件结束
            if trimmedLine.isEmpty {
                logger.debug("遇到空行，事件结束")
                if !dataLines.isEmpty {
                    let jsonString = dataLines.joined(separator: "\n")
                    logger.debug("处理事件数据: '\(jsonString)'")
                    processEventData(jsonString, eventInfo: eventData)
                }
                // 重置状态
                eventData.removeAll()
                dataLines.removeAll()
                continue
            }

            // 注释行，忽略
            if trimmedLine.hasPrefix(sseCommentPrefix) {
                logger.debug("忽略注释行")
                continue
            }

            // 解析SSE字段
            if trimmedLine.hasPrefix(sseDataPrefix) {
                let dataContent = String(trimmedLine.dropFirst(sseDataPrefix.count)).trimmingCharacters(in: .whitespaces)
                logger.debug("解析到data字段: '\(dataContent)'")
                if dataContent == streamEndMarker {
                    logger.info("收到流结束标记")
                    continue
                }

                // 检查是否是完整的JSON对象
                if isCompleteJSON(dataContent) {
                    // 如果是完整的JSON，立即处理
                    logger.debug("检测到完整JSON，立即处理: '\(dataContent)'")
                    processEventData(dataContent, eventInfo: eventData)
                    // 处理完后清空eventData，为下一个事件做准备
                    eventData.removeAll()
                } else {
                    // 如果不是完整的JSON，添加到dataLines等待更多数据
                    logger.debug("JSON不完整，添加到缓冲区: '\(dataContent)'")
                    dataLines.append(dataContent)
                }
            } else if trimmedLine.hasPrefix(sseEventPrefix) {
                let eventType = String(trimmedLine.dropFirst(sseEventPrefix.count)).trimmingCharacters(in: .whitespaces)
                logger.debug("解析到event字段: '\(eventType)'")
                eventData["event"] = eventType
            } else if trimmedLine.hasPrefix(sseIdPrefix) {
                let eventId = String(trimmedLine.dropFirst(sseIdPrefix.count)).trimmingCharacters(in: .whitespaces)
                logger.debug("解析到id字段: '\(eventId)'")
                eventData["id"] = eventId
            } else if trimmedLine.hasPrefix(sseRetryPrefix) {
                let retryTime = String(trimmedLine.dropFirst(sseRetryPrefix.count)).trimmingCharacters(in: .whitespaces)
                logger.debug("解析到retry字段: '\(retryTime)'")
                eventData["retry"] = retryTime
            } else {
                // 非标准SSE格式，可能是直接的JSON数据
                logger.debug("非标准SSE格式，尝试作为JSON数据处理: '\(trimmedLine)'")
                processEventData(trimmedLine, eventInfo: eventData)
            }
        }

        // 处理最后一个事件（如果没有以空行结束）
        if !dataLines.isEmpty {
            // 检查是否应该连接多个data行
            if dataLines.count == 1 {
                // 单个data行，直接处理
                let dataLine = dataLines[0]
                if !dataLine.isEmpty {
                    logger.debug("处理剩余单个事件数据: '\(dataLine)'")
                    processEventData(dataLine, eventInfo: eventData)
                }
            } else {
                // 多个data行，检查是否应该连接
                let combinedData = dataLines.joined(separator: "\n")
                if isCompleteJSON(combinedData) {
                    // 连接后是完整JSON，作为一个事件处理
                    logger.debug("处理连接后的完整事件数据: '\(combinedData)'")
                    processEventData(combinedData, eventInfo: eventData)
                } else {
                    // 分别处理每个数据行
                    for dataLine in dataLines {
                        if !dataLine.isEmpty && isCompleteJSON(dataLine) {
                            logger.debug("处理剩余独立事件数据: '\(dataLine)'")
                            processEventData(dataLine, eventInfo: eventData)
                        }
                    }
                }
            }
        }
    }
    
    private func processEventData(_ jsonString: String, eventInfo: [String: String]) {
        guard !jsonString.isEmpty else {
            logger.debug("跳过空的事件数据")
            return
        }

        logger.debug("开始处理事件数据: '\(jsonString)' (长度: \(jsonString.count))")

        // 尝试解析JSON，如果失败则缓存数据等待更多内容
        let fullJsonString = self.jsonBuffer + jsonString
        logger.debug("完整JSON字符串: '\(fullJsonString)' (缓冲区长度: \(self.jsonBuffer.count))")

        guard let jsonData = fullJsonString.data(using: .utf8) else {
            logger.error("JSON字符串转换为Data失败: '\(fullJsonString)'")
            handleErrorWithRetry(SSEError.dataConversionFailed)
            return
        }

        do {
            logger.debug("尝试解析JSON数据...")
            let decodedObject = try JSONDecoder().decode(T.self, from: jsonData)
            // 解析成功，清空缓冲区
            self.jsonBuffer = ""
            logger.info("JSON解析成功，触发onEventReceived回调")
            callbackQueue.async { [weak self] in
                self?.onEventReceived?(decodedObject)
            }
        } catch {
            logger.debug("JSON解析失败: \(error.localizedDescription)")
            // JSON解析失败，可能是数据不完整
            if isLikelyIncompleteJSON(fullJsonString) {
                // 缓存数据等待更多内容
                self.jsonBuffer = fullJsonString
                logger.debug("JSON数据可能不完整，缓存等待更多数据。当前缓冲区: '\(self.jsonBuffer)'")
            } else {
                // 确实是解析错误，使用增强的错误处理
                logger.error("JSON解析确实失败。错误: \(error.localizedDescription)")
                logger.error("失败的JSON数据: '\(fullJsonString)'")
                handleErrorWithRetry(SSEError.jsonDecodingFailed(error))
            }
        }
    }

    /// 检查JSON字符串是否可能不完整
    private func isLikelyIncompleteJSON(_ jsonString: String) -> Bool {
        let trimmed = jsonString.trimmingCharacters(in: .whitespacesAndNewlines)

        // 基本的JSON完整性检查
        if trimmed.hasPrefix("{") && !trimmed.hasSuffix("}") {
            return true
        }
        if trimmed.hasPrefix("[") && !trimmed.hasSuffix("]") {
            return true
        }

        // 检查括号匹配
        let openBraces = trimmed.filter { $0 == "{" }.count
        let closeBraces = trimmed.filter { $0 == "}" }.count
        let openBrackets = trimmed.filter { $0 == "[" }.count
        let closeBrackets = trimmed.filter { $0 == "]" }.count

        return openBraces != closeBraces || openBrackets != closeBrackets
    }

    /// 检查JSON字符串是否完整
    private func isCompleteJSON(_ jsonString: String) -> Bool {
        let trimmed = jsonString.trimmingCharacters(in: .whitespacesAndNewlines)

        // 空字符串不是完整的JSON
        guard !trimmed.isEmpty else { return false }

        // 检查基本的JSON结构
        if trimmed.hasPrefix("{") && trimmed.hasSuffix("}") {
            // 检查括号匹配
            let openBraces = trimmed.filter { $0 == "{" }.count
            let closeBraces = trimmed.filter { $0 == "}" }.count
            return openBraces == closeBraces
        }

        if trimmed.hasPrefix("[") && trimmed.hasSuffix("]") {
            // 检查括号匹配
            let openBrackets = trimmed.filter { $0 == "[" }.count
            let closeBrackets = trimmed.filter { $0 == "]" }.count
            return openBrackets == closeBrackets
        }

        // 简单值（字符串、数字、布尔值、null）
        if trimmed.hasPrefix("\"") && trimmed.hasSuffix("\"") {
            return true
        }

        if trimmed == "true" || trimmed == "false" || trimmed == "null" {
            return true
        }

        // 数字
        if Double(trimmed) != nil {
            return true
        }

        return false
    }
    
    private func handleError(_ error: SSEError) {
        logger.error("SSE错误: \(error.localizedDescription)")

        // 记录详细的错误上下文
        switch error {
        case .jsonDecodingFailed(let underlyingError):
            logger.error("JSON解析错误详情: \(underlyingError)")
            logger.error("当前JSON缓冲区: '\(self.jsonBuffer)'")
            logger.error("当前数据缓冲区: '\(self.dataBuffer)'")
        case .dataConversionFailed:
            logger.error("数据转换失败，当前缓冲区: '\(self.dataBuffer)'")
        case .connectionFailed(let underlyingError):
            logger.error("连接失败详情: \(underlyingError)")
        default:
            break
        }

        connectionState = .error(error)

        callbackQueue.async { [weak self] in
            self?.onError?(error)
        }
    }

    /// 增强的错误处理，包含重试逻辑
    private func handleErrorWithRetry(_ error: SSEError) {
        logger.error("处理错误并考虑重试: \(error.localizedDescription)")

        switch error {
        case .jsonDecodingFailed:
            // JSON解析失败时，尝试清理缓冲区并重新开始
            logger.warning("JSON解析失败，清理缓冲区并重新开始")
            bufferLock.lock()
            self.jsonBuffer = ""
            self.dataBuffer = ""
            bufferLock.unlock()

            // 不立即报告错误，给下一个数据包一个机会
            return

        case .dataConversionFailed:
            // 数据转换失败，清理缓冲区
            logger.warning("数据转换失败，清理缓冲区")
            bufferLock.lock()
            self.dataBuffer = ""
            bufferLock.unlock()

        default:
            break
        }

        // 对于其他错误，使用标准错误处理
        handleError(error)
    }
}
