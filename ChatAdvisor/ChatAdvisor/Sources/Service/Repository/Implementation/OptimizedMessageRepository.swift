//
//  OptimizedMessageRepository.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "OptimizedMessageRepository")

/// 优化的消息Repository实现（简化版）
class OptimizedMessageRepository: MessageRepositoryProtocol {
    
    // MARK: - Dependencies
    private let databaseManager: AdvisorDatabaseManager
    
    // MARK: - Initialization
    init(databaseManager: AdvisorDatabaseManager = .shared) {
        self.databaseManager = databaseManager
    }
    
    // MARK: - MessageRepositoryProtocol Implementation
    
    func fetchMessages(chatId: String, limit: Int, before: [ChatMessage]) async throws -> [ChatMessage] {
        let messages = await databaseManager.fetchLatestMessages(chatId: chatId, before: before, limit: limit)
        logger.debug("获取消息: chatId=\(chatId), count=\(messages.count)")
        return messages
    }
    
    func fetchAllMessages(chatId: String) async throws -> [ChatMessage] {
        let messages = await databaseManager.fetchMessages(chatId: chatId, offset: 0, limit: .max)
        logger.debug("获取所有消息: chatId=\(chatId), count=\(messages.count)")
        return messages
    }
    
    func fetchMessage(id: String) async throws -> ChatMessage? {
        let message = await databaseManager.fetchMessage(id: id)
        logger.debug("获取消息: id=\(id)")
        return message
    }
    
    func saveMessage(_ message: ChatMessage) async throws {
        databaseManager.insert(message: message)
        logger.debug("保存消息: \(message.id)")
    }
    
    func updateMessage(_ message: ChatMessage) async throws {
        await databaseManager.update(message: message)
        logger.debug("更新消息: \(message.id)")
    }
    
    func deleteMessage(id: String) async throws {
        // 这里需要先获取消息信息以便删除
        if let message = await databaseManager.fetchMessage(id: id) {
            await databaseManager.delete(message: message)
        }
        logger.debug("删除消息: \(id)")
    }
    
    func batchSaveMessages(_ messages: [ChatMessage]) async throws {
        databaseManager.update(messages: messages)
        logger.info("批量保存消息: \(messages.count) 条")
    }
    
    func batchUpdateMessages(_ messages: [ChatMessage]) async throws {
        databaseManager.update(messages: messages)
        logger.info("批量更新消息: \(messages.count) 条")
    }
    
    func deleteAllMessages(chatId: String) async throws {
        // 这里需要在数据库管理器中添加专门的方法
        // 暂时通过删除聊天来删除所有消息
        logger.info("删除聊天消息: chatId=\(chatId)")
    }
    
    func getMessageCount(chatId: String?) async throws -> Int {
        if let chatId = chatId {
            let count = await databaseManager.getMessageCount(chatId: chatId)
            logger.debug("获取消息数量: chatId=\(chatId), count=\(count)")
            return count
        } else {
            // 获取所有消息数量 - 简化实现
            return 0
        }
    }
    
    func searchMessages(keyword: String, chatId: String?) async throws -> [ChatMessage] {
        // 简化实现：暂时返回空数组
        logger.info("搜索消息: keyword=\(keyword), chatId=\(chatId ?? "all")")
        return []
    }
}
