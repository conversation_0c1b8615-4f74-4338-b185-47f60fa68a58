//
//  OptimizedChatRepository.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "OptimizedChatRepository")

/// 优化的聊天Repository实现（简化版）
@MainActor
class OptimizedChatRepository: ChatRepositoryProtocol {
    
    // MARK: - Dependencies
    private let databaseManager: AdvisorDatabaseManager
    
    // MARK: - Simple Cache
    private var chatCache: [String: Chat] = [:]
    private let maxCacheSize = 50 // 简化缓存：最多50个聊天
    
    // MARK: - Initialization
    init(databaseManager: AdvisorDatabaseManager = .shared) {
        self.databaseManager = databaseManager
    }
    
    // MARK: - ChatRepositoryProtocol Implementation
    
    func fetchChats(limit: Int, offset: Int, isArchived: Bool) async throws -> [Chat] {
        // 直接从数据库获取，保持简单
        let chats = try await databaseManager.fetchChats(offset: offset, isArchived: isArchived)
        
        // 简单缓存：只缓存单个聊天
        for chat in chats {
            cacheChat(chat)
        }
        
        logger.info("获取聊天列表，数量: \(chats.count)")
        return chats
    }
    
    func fetchChat(id: String) async throws -> Chat? {
        // 优先从简单缓存获取
        if let cachedChat = chatCache[id] {
            logger.debug("从缓存返回聊天: \(id)")
            return cachedChat
        }
        
        // 从数据库获取
        let chat = await databaseManager.fetchChat(id: id)
        
        // 缓存结果
        if let chat = chat {
            cacheChat(chat)
        }
        
        logger.debug("从数据库获取聊天: \(id)")
        return chat
    }
    
    func saveChat(_ chat: Chat) async throws {
        // 保存到数据库
        databaseManager.insert(chat: chat)
        
        // 更新缓存
        cacheChat(chat)
        
        logger.info("保存聊天: \(chat.id)")
    }
    
    func updateChat(_ chat: Chat) async throws {
        // 更新数据库
        databaseManager.update(chat: chat)
        
        // 更新缓存
        cacheChat(chat)
        
        logger.info("更新聊天: \(chat.id)")
    }
    
    func deleteChat(id: String) async throws {
        // 从数据库删除
        databaseManager.deleteChat(id: id)
        
        // 从缓存删除
        chatCache.removeValue(forKey: id)
        
        logger.info("删除聊天: \(id)")
    }
    
    func batchDeleteChats(ids: [String]) async throws {
        // 批量删除操作
        for id in ids {
            databaseManager.deleteChat(id: id)
            chatCache.removeValue(forKey: id)
        }
        
        logger.info("批量删除聊天: \(ids.count) 个")
    }
    
    func searchChats(keyword: String, isArchived: Bool) async throws -> [Chat] {
        // 搜索结果不缓存，直接从数据库获取
        let searchResults = await databaseManager.searchChats(keyword: keyword, archive: isArchived)
        
        logger.info("搜索聊天结果: \(searchResults.count) 个，关键词: \(keyword)")
        return searchResults
    }
    
    func getChatCount(isArchived: Bool) async throws -> Int {
        // 简单实现：获取所有聊天然后计数
        let allChats = try await databaseManager.fetchChats(offset: 0, isArchived: isArchived)
        return allChats.count
    }
    
    func archiveChat(id: String, isArchived: Bool) async throws {
        // 更新数据库
        if isArchived {
            databaseManager.archiveChat(id: id)
        } else {
            databaseManager.unarchiveChat(id: id)
        }
        
        // 从缓存移除（因为归档状态改变了）
        chatCache.removeValue(forKey: id)
        
        logger.info("\(isArchived ? "归档" : "取消归档")聊天: \(id)")
    }
    
    func cleanupEmptyChats() async throws {
        databaseManager.cleanupEmptyChats()
        
        // 清除所有缓存，因为数据可能已改变
        chatCache.removeAll()
        
        logger.info("清理空聊天完成")
    }
    
    // MARK: - Private Methods
    
    private func cacheChat(_ chat: Chat) {
        // 限制缓存大小
        if chatCache.count >= maxCacheSize {
            // 简单策略：移除第一个（最旧的）
            if let firstKey = chatCache.keys.first {
                chatCache.removeValue(forKey: firstKey)
            }
        }
        
        chatCache[chat.id] = chat
    }
}
