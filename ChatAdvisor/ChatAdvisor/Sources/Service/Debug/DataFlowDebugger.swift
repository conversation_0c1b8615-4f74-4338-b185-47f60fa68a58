//
//  DataFlowDebugger.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "DataFlowDebugger")

/// 数据流事件类型
enum DataFlowEventType {
    case dataSourceUpdate
    case repositoryOperation
    case uiStateChange
    case cacheOperation
    case networkRequest
    case userAction
}

/// 数据流事件
struct DataFlowEvent {
    let id: String
    let type: DataFlowEventType
    let source: String
    let target: String?
    let operation: String
    let data: [String: Any]
    let timestamp: Date
    let duration: TimeInterval?
    
    init(type: DataFlowEventType, source: String, target: String? = nil, operation: String, data: [String: Any] = [:], duration: TimeInterval? = nil) {
        self.id = UUID().uuidString
        self.type = type
        self.source = source
        self.target = target
        self.operation = operation
        self.data = data
        self.timestamp = Date()
        self.duration = duration
    }
}

/// 数据流调试器 - 用于追踪和调试数据流
class DataFlowDebugger {
    static let shared = DataFlowDebugger()
    
    // MARK: - Private Properties
    private var events: [DataFlowEvent] = []
    private let queue = DispatchQueue(label: "com.sanva.chatadvisor.dataflow.debug", qos: .utility)
    private let maxEventsCount = 500
    private var isEnabled = false
    
    // 数据流路径追踪
    private var activeFlows: [String: [DataFlowEvent]] = [:]
    
    private init() {
        #if DEBUG
        isEnabled = true
        #endif
    }
    
    // MARK: - Public Methods
    
    /// 启用调试
    func enable() {
        isEnabled = true
        logger.info("数据流调试器已启用")
    }
    
    /// 禁用调试
    func disable() {
        isEnabled = false
        logger.info("数据流调试器已禁用")
    }
    
    /// 记录数据流事件
    func logEvent(type: DataFlowEventType, source: String, target: String? = nil, operation: String, data: [String: Any] = [:], duration: TimeInterval? = nil) {
        guard isEnabled else { return }
        
        let event = DataFlowEvent(
            type: type,
            source: source,
            target: target,
            operation: operation,
            data: data,
            duration: duration
        )
        
        queue.async { [weak self] in
            self?.addEvent(event)
        }
        
        logger.debug("数据流事件: \(source) -> \(target ?? "N/A") | \(operation)")
    }
    
    /// 开始追踪数据流
    func startFlow(_ flowId: String, initialEvent: DataFlowEvent) {
        guard isEnabled else { return }
        
        queue.async { [weak self] in
            self?.activeFlows[flowId] = [initialEvent]
        }
        
        logger.debug("开始追踪数据流: \(flowId)")
    }
    
    /// 添加事件到数据流
    func addToFlow(_ flowId: String, event: DataFlowEvent) {
        guard isEnabled else { return }
        
        queue.async { [weak self] in
            self?.activeFlows[flowId]?.append(event)
        }
    }
    
    /// 结束数据流追踪
    func endFlow(_ flowId: String) {
        guard isEnabled else { return }
        
        queue.async { [weak self] in
            if let flow = self?.activeFlows.removeValue(forKey: flowId) {
                logger.info("数据流完成: \(flowId), 事件数: \(flow.count)")
            }
        }
    }
    
    /// 获取所有事件
    func getAllEvents() -> [DataFlowEvent] {
        return queue.sync {
            return events
        }
    }
    
    /// 获取指定类型的事件
    func getEvents(ofType type: DataFlowEventType) -> [DataFlowEvent] {
        return queue.sync {
            return events.filter { $0.type == type }
        }
    }
    
    /// 获取指定源的事件
    func getEvents(fromSource source: String) -> [DataFlowEvent] {
        return queue.sync {
            return events.filter { $0.source == source }
        }
    }
    
    /// 获取数据流路径
    func getFlowPath(_ flowId: String) -> [DataFlowEvent]? {
        return queue.sync {
            return activeFlows[flowId]
        }
    }
    
    /// 生成数据流报告
    func generateFlowReport() -> String {
        let allEvents = getAllEvents()
        
        var report = "🔍 数据流调试报告\n"
        report += "=" * 50 + "\n\n"
        
        if allEvents.isEmpty {
            report += "暂无数据流事件\n"
            return report
        }
        
        // 统计信息
        let eventsByType = Dictionary(grouping: allEvents) { $0.type }
        let eventsBySource = Dictionary(grouping: allEvents) { $0.source }
        
        report += "📊 统计信息:\n"
        report += "  • 总事件数: \(allEvents.count)\n"
        report += "  • 活跃数据流: \(activeFlows.count)\n"
        report += "  • 事件类型: \(eventsByType.count) 种\n"
        report += "  • 数据源: \(eventsBySource.count) 个\n\n"
        
        // 按类型统计
        report += "📋 事件类型统计:\n"
        for (type, events) in eventsByType {
            let avgDuration = events.compactMap { $0.duration }.reduce(0, +) / Double(max(1, events.compactMap { $0.duration }.count))
            report += "  • \(type): \(events.count) 次"
            if avgDuration > 0 {
                report += " (平均耗时: \(String(format: "%.3f", avgDuration))s)"
            }
            report += "\n"
        }
        report += "\n"
        
        // 按源统计
        report += "📋 数据源统计:\n"
        for (source, events) in eventsBySource.sorted(by: { $0.value.count > $1.value.count }) {
            report += "  • \(source): \(events.count) 次\n"
        }
        report += "\n"
        
        // 最近的事件
        let recentEvents = Array(allEvents.suffix(10))
        report += "🕐 最近事件 (最新10条):\n"
        for event in recentEvents.reversed() {
            let timeStr = DateFormatter.debugTimeFormatter.string(from: event.timestamp)
            let targetStr = event.target.map { " -> \($0)" } ?? ""
            let durationStr = event.duration.map { " (\(String(format: "%.3f", $0))s)" } ?? ""
            report += "  [\(timeStr)] \(event.source)\(targetStr) | \(event.operation)\(durationStr)\n"
        }
        
        // 性能警告
        let slowEvents = allEvents.filter { ($0.duration ?? 0) > 1.0 }
        if !slowEvents.isEmpty {
            report += "\n⚠️ 性能警告 (耗时>1s):\n"
            for event in slowEvents.suffix(5) {
                let timeStr = DateFormatter.debugTimeFormatter.string(from: event.timestamp)
                let durationStr = String(format: "%.3f", event.duration ?? 0)
                report += "  [\(timeStr)] \(event.source) | \(event.operation) (\(durationStr)s)\n"
            }
        }
        
        return report
    }
    
    /// 生成数据流图
    func generateFlowDiagram() -> String {
        let allEvents = getAllEvents()
        
        var diagram = "📊 数据流图\n"
        diagram += "=" * 30 + "\n\n"
        
        // 构建数据流图
        var connections: [String: Set<String>] = [:]
        
        for event in allEvents {
            if let target = event.target {
                if connections[event.source] == nil {
                    connections[event.source] = Set<String>()
                }
                connections[event.source]?.insert(target)
            }
        }
        
        // 生成图形表示
        for (source, targets) in connections.sorted(by: { $0.key < $1.key }) {
            diagram += "\(source)\n"
            for target in targets.sorted() {
                diagram += "  └─> \(target)\n"
            }
            diagram += "\n"
        }
        
        return diagram
    }
    
    /// 清理旧事件
    func cleanup() {
        queue.async { [weak self] in
            guard let self = self else { return }
            
            if self.events.count > self.maxEventsCount {
                let removeCount = self.events.count - self.maxEventsCount
                self.events.removeFirst(removeCount)
            }
            
            // 清理超过1小时的活跃流
            let oneHourAgo = Date().addingTimeInterval(-3600)
            self.activeFlows = self.activeFlows.filter { _, events in
                events.last?.timestamp ?? Date.distantPast > oneHourAgo
            }
            
            logger.info("数据流调试器清理完成")
        }
    }
    
    /// 重置所有数据
    func reset() {
        queue.async { [weak self] in
            self?.events.removeAll()
            self?.activeFlows.removeAll()
            logger.info("数据流调试器已重置")
        }
    }
    
    /// 导出调试数据
    func exportDebugData() -> [String: Any] {
        return queue.sync {
            return [
                "events": events.map { event in
                    [
                        "id": event.id,
                        "type": String(describing: event.type),
                        "source": event.source,
                        "target": event.target as Any,
                        "operation": event.operation,
                        "data": event.data,
                        "timestamp": event.timestamp.timeIntervalSince1970,
                        "duration": event.duration as Any
                    ]
                },
                "activeFlows": activeFlows.mapValues { flow in
                    flow.map { event in
                        [
                            "id": event.id,
                            "operation": event.operation,
                            "timestamp": event.timestamp.timeIntervalSince1970
                        ]
                    }
                }
            ]
        }
    }
    
    // MARK: - Private Methods
    
    private func addEvent(_ event: DataFlowEvent) {
        events.append(event)
        
        // 自动清理
        if events.count > maxEventsCount {
            events.removeFirst()
        }
    }
}

// MARK: - 便利扩展
extension DataFlowDebugger {
    
    /// 记录数据源更新
    func logDataSourceUpdate(source: String, operation: String, data: [String: Any] = [:]) {
        logEvent(type: .dataSourceUpdate, source: source, operation: operation, data: data)
    }
    
    /// 记录Repository操作
    func logRepositoryOperation(source: String, operation: String, data: [String: Any] = [:], duration: TimeInterval? = nil) {
        logEvent(type: .repositoryOperation, source: source, operation: operation, data: data, duration: duration)
    }
    
    /// 记录UI状态变化
    func logUIStateChange(source: String, operation: String, data: [String: Any] = [:]) {
        logEvent(type: .uiStateChange, source: source, operation: operation, data: data)
    }
    
    /// 记录缓存操作
    func logCacheOperation(source: String, operation: String, data: [String: Any] = [:]) {
        logEvent(type: .cacheOperation, source: source, operation: operation, data: data)
    }
    
    /// 记录用户操作
    func logUserAction(source: String, operation: String, data: [String: Any] = [:]) {
        logEvent(type: .userAction, source: source, operation: operation, data: data)
    }
}

// MARK: - DateFormatter扩展
private extension DateFormatter {
    static let debugTimeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter
    }()
}

// MARK: - String扩展
private extension String {
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
}
