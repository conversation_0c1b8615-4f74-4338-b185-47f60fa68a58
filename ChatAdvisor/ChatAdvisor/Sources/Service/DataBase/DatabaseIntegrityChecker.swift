//
//  DatabaseIntegrityChecker.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/06/27.
//

import Foundation
import WCDBSwift

/// 数据库完整性检查器
class DatabaseIntegrityChecker {
    static let shared = DatabaseIntegrityChecker()
    
    private let databaseQueue = DispatchQueue(label: "com.sanva.chatadvisor.database.integrity", qos: .background)
    
    private init() {}
    
    /// 完整性检查结果
    struct IntegrityCheckResult {
        let isValid: Bool
        let issues: [IntegrityIssue]
        let fixedIssues: [IntegrityIssue]
        let checkTime: Date
        
        var hasIssues: Bool {
            return !issues.isEmpty
        }
    }
    
    /// 完整性问题类型
    enum IntegrityIssue {
        case orphanedMessages(chatId: String, messageCount: Int)
        case emptyChats(chatIds: [String])
        case missingSystemMessages(chatIds: [String])
        case duplicateMessages(messageIds: [String])
        case invalidTimestamps(messageIds: [String])
        case corruptedData(tableName: String, description: String)
        
        var description: String {
            switch self {
            case .orphanedMessages(let chatId, let count):
                return "孤立消息: 会话 \(chatId) 有 \(count) 条消息但会话不存在"
            case .emptyChats(let chatIds):
                return "空会话: \(chatIds.count) 个会话没有任何消息"
            case .missingSystemMessages(let chatIds):
                return "缺少系统消息: \(chatIds.count) 个会话缺少系统提示消息"
            case .duplicateMessages(let messageIds):
                return "重复消息: \(messageIds.count) 条消息存在重复"
            case .invalidTimestamps(let messageIds):
                return "无效时间戳: \(messageIds.count) 条消息的时间戳无效"
            case .corruptedData(let tableName, let description):
                return "数据损坏: 表 \(tableName) - \(description)"
            }
        }
        
        var severity: Severity {
            switch self {
            case .orphanedMessages, .corruptedData:
                return .high
            case .emptyChats, .duplicateMessages:
                return .medium
            case .missingSystemMessages, .invalidTimestamps:
                return .low
            }
        }
        
        enum Severity {
            case low, medium, high
        }
    }
    
    /// 执行完整的数据库完整性检查
    func performIntegrityCheck(database: Database, autoFix: Bool = true) async -> IntegrityCheckResult {
        return await withCheckedContinuation { continuation in
            databaseQueue.async {
                var issues: [IntegrityIssue] = []
                var fixedIssues: [IntegrityIssue] = []
                
                do {
                    // 1. 检查孤立消息
                    let orphanedIssues = try self.checkOrphanedMessages(database: database)
                    issues.append(contentsOf: orphanedIssues)
                    
                    // 2. 检查空会话
                    let emptyChatsIssues = try self.checkEmptyChats(database: database)
                    issues.append(contentsOf: emptyChatsIssues)
                    
                    // 3. 检查重复消息
                    let duplicateIssues = try self.checkDuplicateMessages(database: database)
                    issues.append(contentsOf: duplicateIssues)
                    
                    // 4. 检查无效时间戳
                    let timestampIssues = try self.checkInvalidTimestamps(database: database)
                    issues.append(contentsOf: timestampIssues)
                    
                    // 5. 检查数据库结构完整性
                    let structureIssues = try self.checkDatabaseStructure(database: database)
                    issues.append(contentsOf: structureIssues)
                    
                    // 自动修复问题
                    if autoFix {
                        fixedIssues = try self.autoFixIssues(issues, database: database)
                        // 移除已修复的问题
                        issues.removeAll { issue in
                            fixedIssues.contains { $0.description == issue.description }
                        }
                    }
                    
                    let result = IntegrityCheckResult(
                        isValid: issues.isEmpty,
                        issues: issues,
                        fixedIssues: fixedIssues,
                        checkTime: Date()
                    )
                    
                    continuation.resume(returning: result)

                } catch {
                    let result = IntegrityCheckResult(
                        isValid: false,
                        issues: [.corruptedData(tableName: "unknown", description: error.localizedDescription)],
                        fixedIssues: [],
                        checkTime: Date()
                    )
                    continuation.resume(returning: result)
                }
            }
        }
    }
    
    /// 检查孤立消息
    private func checkOrphanedMessages(database: Database) throws -> [IntegrityIssue] {
        var issues: [IntegrityIssue] = []

        // 获取所有存在的聊天ID
        let existingChats: [Chat] = try database.getObjects(fromTable: "chats")
        let existingChatIds = Set(existingChats.map { $0.id })

        // 获取所有消息
        let allMessages: [ChatMessage] = try database.getObjects(fromTable: "chatMessages")

        // 按chatId分组统计孤立消息
        let messagesByChatId = Dictionary(grouping: allMessages) { $0.chatId }

        for (chatId, messages) in messagesByChatId {
            if !existingChatIds.contains(chatId) {
                issues.append(.orphanedMessages(chatId: chatId, messageCount: messages.count))
            }
        }

        return issues
    }
    
    /// 检查空会话
    private func checkEmptyChats(database: Database) throws -> [IntegrityIssue] {
        // 获取所有聊天
        let allChats: [Chat] = try database.getObjects(fromTable: "chats")

        // 获取有用户消息的聊天ID
        let userMessages: [ChatMessage] = try database.getObjects(
            fromTable: "chatMessages",
            where: ChatMessage.Properties.role == "user"
        )
        let chatsWithUserMessages = Set(userMessages.map { $0.chatId })

        // 找出没有用户消息的聊天
        let emptyChatIds = allChats.compactMap { chat in
            chatsWithUserMessages.contains(chat.id) ? nil : chat.id
        }

        return emptyChatIds.isEmpty ? [] : [.emptyChats(chatIds: emptyChatIds)]
    }
    
    /// 检查重复消息
    private func checkDuplicateMessages(database: Database) throws -> [IntegrityIssue] {
        // 获取所有消息
        let allMessages: [ChatMessage] = try database.getObjects(fromTable: "chatMessages")

        // 按ID分组找出重复的消息
        let messagesById = Dictionary(grouping: allMessages) { $0.id }
        let duplicateIds = messagesById.compactMap { (id, messages) in
            messages.count > 1 ? id : nil
        }

        return duplicateIds.isEmpty ? [] : [.duplicateMessages(messageIds: duplicateIds)]
    }
    
    /// 检查无效时间戳
    private func checkInvalidTimestamps(database: Database) throws -> [IntegrityIssue] {
        let currentTime = Int64(Date().timeIntervalSince1970)

        // 获取时间戳无效的消息
        let invalidMessages: [ChatMessage] = try database.getObjects(
            fromTable: "chatMessages",
            where: ChatMessage.Properties.createdTime <= 0 || ChatMessage.Properties.createdTime > currentTime
        )

        let invalidIds = invalidMessages.map { $0.id }
        return invalidIds.isEmpty ? [] : [.invalidTimestamps(messageIds: invalidIds)]
    }
    
    /// 检查数据库结构完整性
    private func checkDatabaseStructure(database: Database) throws -> [IntegrityIssue] {
        var issues: [IntegrityIssue] = []

        // 检查必要的表是否存在
        let requiredTables = ["chats", "chatMessages", "database_version"]
        for tableName in requiredTables {
            if !(try database.isTableExists(tableName)) {
                issues.append(.corruptedData(tableName: tableName, description: "表不存在"))
            }
        }

        return issues
    }
    
    /// 自动修复问题
    private func autoFixIssues(_ issues: [IntegrityIssue], database: Database) throws -> [IntegrityIssue] {
        var fixedIssues: [IntegrityIssue] = []

        for issue in issues {
            switch issue {
            case .orphanedMessages(let chatId, _):
                // 删除孤立消息
                try database.delete(fromTable: "chatMessages", where: ChatMessage.Properties.chatId == chatId)
                fixedIssues.append(issue)

            case .emptyChats(let chatIds):
                // 删除空会话
                for chatId in chatIds {
                    try database.delete(fromTable: "chats", where: Chat.Properties.id == chatId)
                }
                fixedIssues.append(issue)

            case .duplicateMessages(let messageIds):
                // 删除重复消息（保留最新的）
                for messageId in messageIds {
                    // 获取该ID的所有消息，按rowid排序
                    let duplicateMessages: [ChatMessage] = try database.getObjects(
                        fromTable: "chatMessages",
                        where: ChatMessage.Properties.id == messageId,
                        orderBy: [ChatMessage.Properties.createdTime.order(.descending)]
                    )

                    // 保留第一个（最新的），删除其余的
                    if duplicateMessages.count > 1 {
                        for i in 1..<duplicateMessages.count {
                            try database.delete(
                                fromTable: "chatMessages",
                                where: ChatMessage.Properties.id == messageId &&
                                       ChatMessage.Properties.createdTime == duplicateMessages[i].createdTime
                            )
                        }
                    }
                }
                fixedIssues.append(issue)

            case .invalidTimestamps(let messageIds):
                // 修复无效时间戳
                let currentTime = Int64(Date().timeIntervalSince1970)
                for messageId in messageIds {
                    try database.update(
                        table: "chatMessages",
                        on: [ChatMessage.Properties.createdTime],
                        with: currentTime,
                        where: ChatMessage.Properties.id == messageId
                    )
                }
                fixedIssues.append(issue)

            default:
                // 其他问题需要手动处理
                break
            }
        }

        return fixedIssues
    }
}
