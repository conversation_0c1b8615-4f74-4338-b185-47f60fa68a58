//
//  DataChangeNotificationCenter.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import Combine
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "DataChangeNotificationCenter")

/// 数据变更类型
enum DataChangeType {
    case created
    case updated
    case deleted
    case batchUpdated
    case batchDeleted
}

/// 数据变更事件
struct DataChangeEvent {
    let entityType: String
    let entityId: String
    let changeType: DataChangeType
    let timestamp: Date
    let metadata: [String: Any]?
    
    init(entityType: String, entityId: String, changeType: DataChangeType, metadata: [String: Any]? = nil) {
        self.entityType = entityType
        self.entityId = entityId
        self.changeType = changeType
        self.timestamp = Date()
        self.metadata = metadata
    }
}

/// 数据变更通知中心 - 统一的数据变更通知系统
class DataChangeNotificationCenter {
    static let shared = DataChangeNotificationCenter()
    
    // MARK: - Publishers
    private let chatChangeSubject = PassthroughSubject<DataChangeEvent, Never>()
    private let messageChangeSubject = PassthroughSubject<DataChangeEvent, Never>()
    private let allChangesSubject = PassthroughSubject<DataChangeEvent, Never>()
    
    // MARK: - Public Publishers
    var chatChanges: AnyPublisher<DataChangeEvent, Never> {
        chatChangeSubject.eraseToAnyPublisher()
    }
    
    var messageChanges: AnyPublisher<DataChangeEvent, Never> {
        messageChangeSubject.eraseToAnyPublisher()
    }
    
    var allChanges: AnyPublisher<DataChangeEvent, Never> {
        allChangesSubject.eraseToAnyPublisher()
    }
    
    // MARK: - Private Properties
    private var subscribers: [String: AnyCancellable] = [:]
    private let queue = DispatchQueue(label: "com.sanva.chatadvisor.datachange", qos: .utility)
    
    // 性能监控
    private var eventCount: Int = 0
    private var lastEventTime: Date = Date()

    // MARK: - Public Methods
    
    /// 发布聊天变更事件
    func publishChatChange(chatId: String, changeType: DataChangeType, metadata: [String: Any]? = nil) {
        let event = DataChangeEvent(
            entityType: "Chat",
            entityId: chatId,
            changeType: changeType,
            metadata: metadata
        )
        
        queue.async { [weak self] in
            self?.eventCount += 1
            self?.lastEventTime = Date()
            
            DispatchQueue.main.async {
                self?.chatChangeSubject.send(event)
                self?.allChangesSubject.send(event)
            }
        }
        
    }
    
    /// 发布消息变更事件
    func publishMessageChange(messageId: String, changeType: DataChangeType, metadata: [String: Any]? = nil) {
        let event = DataChangeEvent(
            entityType: "ChatMessage",
            entityId: messageId,
            changeType: changeType,
            metadata: metadata
        )
        
        queue.async { [weak self] in
            self?.eventCount += 1
            self?.lastEventTime = Date()
            
            DispatchQueue.main.async {
                self?.messageChangeSubject.send(event)
                self?.allChangesSubject.send(event)
            }
        }
        
    }
    
    /// 批量发布聊天变更事件
    func publishBatchChatChanges(chatIds: [String], changeType: DataChangeType, metadata: [String: Any]? = nil) {
        for chatId in chatIds {
            publishChatChange(chatId: chatId, changeType: changeType, metadata: metadata)
        }
        
    }
    
    /// 批量发布消息变更事件
    func publishBatchMessageChanges(messageIds: [String], changeType: DataChangeType, metadata: [String: Any]? = nil) {
        for messageId in messageIds {
            publishMessageChange(messageId: messageId, changeType: changeType, metadata: metadata)
        }
        
    }
    
    /// 订阅聊天变更
    func subscribeToChatChanges(subscriber: String, handler: @escaping (DataChangeEvent) -> Void) {
        let cancellable = chatChanges
            .receive(on: DispatchQueue.main)
            .sink(receiveValue: handler)
        
        subscribers["\(subscriber)_chat"] = cancellable
        logger.debug("订阅聊天变更: \(subscriber)")
    }
    
    /// 订阅消息变更
    func subscribeToMessageChanges(subscriber: String, handler: @escaping (DataChangeEvent) -> Void) {
        let cancellable = messageChanges
            .receive(on: DispatchQueue.main)
            .sink(receiveValue: handler)
        
        subscribers["\(subscriber)_message"] = cancellable
        logger.debug("订阅消息变更: \(subscriber)")
    }
    
    /// 订阅所有变更
    func subscribeToAllChanges(subscriber: String, handler: @escaping (DataChangeEvent) -> Void) {
        let cancellable = allChanges
            .receive(on: DispatchQueue.main)
            .sink(receiveValue: handler)
        
        subscribers["\(subscriber)_all"] = cancellable
        logger.debug("订阅所有变更: \(subscriber)")
    }
    
    /// 取消订阅
    func unsubscribe(subscriber: String) {
        let keysToRemove = subscribers.keys.filter { $0.hasPrefix(subscriber) }
        for key in keysToRemove {
            subscribers.removeValue(forKey: key)?.cancel()
        }
        
        logger.debug("取消订阅: \(subscriber)")
    }
    
    /// 取消所有订阅
    func unsubscribeAll() {
        subscribers.values.forEach { $0.cancel() }
        subscribers.removeAll()
        
        logger.info("取消所有订阅")
    }
    
    /// 获取性能统计
    func getPerformanceStats() -> (eventCount: Int, lastEventTime: Date, subscriberCount: Int) {
        return (
            eventCount: eventCount,
            lastEventTime: lastEventTime,
            subscriberCount: subscribers.count
        )
    }
    
    deinit {
        unsubscribeAll()
    }
}

// MARK: - 便利扩展
extension DataChangeNotificationCenter {
    
    /// 便利方法：聊天创建
    func chatCreated(_ chatId: String, metadata: [String: Any]? = nil) {
        publishChatChange(chatId: chatId, changeType: .created, metadata: metadata)
    }
    
    /// 便利方法：聊天更新
    func chatUpdated(_ chatId: String, metadata: [String: Any]? = nil) {
        publishChatChange(chatId: chatId, changeType: .updated, metadata: metadata)
    }
    
    /// 便利方法：聊天删除
    func chatDeleted(_ chatId: String, metadata: [String: Any]? = nil) {
        publishChatChange(chatId: chatId, changeType: .deleted, metadata: metadata)
    }
    
    /// 便利方法：消息创建
    func messageCreated(_ messageId: String, chatId: String? = nil) {
        var metadata: [String: Any]? = nil
        if let chatId = chatId {
            metadata = ["chatId": chatId]
        }
        publishMessageChange(messageId: messageId, changeType: .created, metadata: metadata)
    }
    
    /// 便利方法：消息更新
    func messageUpdated(_ messageId: String, chatId: String? = nil) {
        var metadata: [String: Any]? = nil
        if let chatId = chatId {
            metadata = ["chatId": chatId]
        }
        publishMessageChange(messageId: messageId, changeType: .updated, metadata: metadata)
    }
    
    /// 便利方法：消息删除
    func messageDeleted(_ messageId: String, chatId: String? = nil) {
        var metadata: [String: Any]? = nil
        if let chatId = chatId {
            metadata = ["chatId": chatId]
        }
        publishMessageChange(messageId: messageId, changeType: .deleted, metadata: metadata)
    }
}

// MARK: - AnyCancellable存储扩展
private extension Dictionary where Key == String, Value == AnyCancellable {
    mutating func store(_ cancellable: AnyCancellable, key: String) {
        self[key] = cancellable
    }
}


