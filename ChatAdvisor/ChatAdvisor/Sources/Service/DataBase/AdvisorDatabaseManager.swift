//
//  AdvisorDatabaseManager.swift
//  JunShi
//
//  Created by md on 2024/4/30.
//

import Foundation
import OSLog
import SwifterSwift
import WCDBSwift

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatDatabaseManager")

class AdvisorDatabaseManager {
    static let shared = AdvisorDatabaseManager()
    var database: Database?
    var limit = 20
    let databaseQueue = DispatchQueue(label: "com.sanva.chatadvisor.database", qos: .background)

    // 新增：Repository系统支持
    private var isRepositoryEnabled: Bool = false // 默认禁用

    func setupDatabase() {
        logger.info("开始设置数据库...")
        databaseQueue.async { [weak self] in
            guard let self else { return }
            let path = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first!
            guard let emailHash = AccountManager.shared.currentUser?.email.md5 else {
                logger.error("用户邮箱为空，无法创建数据库")
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: .databaseSetupFailed, object: nil)
                }
                return
            }

            let databasePath = path + "/ChatDatabase_\(emailHash).db"
            logger.info("创建数据库连接: \(databasePath)")
            database = Database(at: databasePath)

            do {
                // 创建表和索引
                logger.info("开始创建数据库表和索引...")
                try createTablesIfNeeded()

                logger.info("数据库设置完成，发送完成通知")

                // 数据库设置完成后，发送通知
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: .databaseSetupCompleted, object: nil)
                }

                // 数据库设置完成后，清理空会话
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
                    logger.info("开始清理空会话...")
                    self?.cleanupEmptyChats()
                }
            } catch {
                logger.error("数据库设置失败: \(error)")
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: .databaseSetupFailed, object: nil)
                }
            }
        }
    }

    func closeDatabase() {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            database?.close()
            database = nil
        }
    }

    func cleanDataBase() {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            guard let path = database?.path else { return }
            do {
                try database?.close(onClosed: {
                    try FileManager.default.removeItem(atPath: path)
//                    self.setupDatabase()
                })
            } catch {
                logger.error("Clean Database Error: \(error)")
            }
        }
    }

    private func createTablesIfNeeded() throws {
        guard let database = database else {
            logger.error("数据库未初始化")
            return
        }

        // 创建表
        try database.create(table: "chats", of: Chat.self)
        try database.create(table: "chatMessages", of: ChatMessage.self)

        // 创建版本管理表
        try createVersionTableIfNeeded()

        // 创建索引以优化查询性能
        try createIndexesIfNeeded()

        // 执行数据库完整性检查
        performBasicIntegrityCheck()

        logger.info("Database tables created successfully.")
    }

    /// 创建版本管理表
    private func createVersionTableIfNeeded() throws {
        guard let database = database else { return }

        // 使用简单的方法创建版本管理，不依赖复杂的SQL执行
        // 这里我们简化处理，只记录在UserDefaults中
        let versionKey = "database_version_\(database.path.md5)"
        let currentVersion = UserDefaults.standard.integer(forKey: versionKey)

        if currentVersion == 0 {
            // 首次创建，设置版本为1
            UserDefaults.standard.set(1, forKey: versionKey)
            UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "\(versionKey)_timestamp")
            logger.info("创建初始数据库版本记录: v1")
        }
    }

    /// 执行基本的数据库完整性检查
    private func performBasicIntegrityCheck() {
        guard let database = database else { return }

        Task {
            do {
                // 使用WCDB的ORM方法检查孤立消息
                let allMessages: [ChatMessage] = try database.getObjects(fromTable: "chatMessages")
                let allChats: [Chat] = try database.getObjects(fromTable: "chats")
                let chatIds = Set(allChats.map { $0.id })

                let orphanedMessages = allMessages.filter { !chatIds.contains($0.chatId) }

                if !orphanedMessages.isEmpty {
                    logger.warning("发现 \(orphanedMessages.count) 条孤立消息")
                    // 清理孤立消息
                    for message in orphanedMessages {
                        try database.delete(fromTable: "chatMessages", where: ChatMessage.Properties.id == message.id)
                    }
                    logger.info("已清理孤立消息")
                }

                // 检查空会话
                let userMessages: [ChatMessage] = try database.getObjects(fromTable: "chatMessages", where: ChatMessage.Properties.role == "user")
                let chatsWithUserMessages = Set(userMessages.map { $0.chatId })
                let emptyChats = allChats.filter { !chatsWithUserMessages.contains($0.id) }

                if !emptyChats.isEmpty {
                    logger.info("发现 \(emptyChats.count) 个空会话，将在后续清理")
                }

                logger.info("数据库完整性检查完成")
            } catch {
                logger.error("数据库完整性检查失败: \(error)")
            }
        }
    }

    /// 创建数据库索引
    private func createIndexesIfNeeded() throws {
        guard let database = database else { return }

        // WCDB Swift会自动为TableCodable的主键和外键创建索引
        // 对于额外的索引，我们可以在表创建时通过TableBinding指定
        // 这里我们简化处理，依赖WCDB的自动索引优化

        logger.info("Database indexes created successfully.")
    }

    // MARK: - Database Status

    /// 检查数据库是否已准备好
    var isDatabaseReady: Bool {
        return database != nil
    }

    /// 等待数据库准备完成
    func waitForDatabaseReady() async -> Bool {
        // 如果数据库已经准备好，直接返回
        if isDatabaseReady {
            logger.info("数据库已准备就绪")
            return true
        }

        logger.info("数据库尚未准备完成，开始等待...")

        // 等待数据库初始化完成，最多等待10秒（增加等待时间）
        for attempt in 0..<100 {
            if isDatabaseReady {
                logger.info("数据库准备完成，等待了 \(attempt * 100)ms")
                return true
            }
            try? await Task.sleep(nanoseconds: 100_000_000) // 100ms

            // 每秒记录一次等待状态
            if attempt % 10 == 9 {
                logger.info("数据库等待中... 已等待 \((attempt + 1) * 100)ms")
            }
        }

        logger.error("数据库等待超时（10秒），初始化可能失败")
        return false
    }

    // Chat operations
    func getLastChatId() async -> String? {
        return await withCheckedContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self, let database = self.database else {
                    continuation.resume(returning: nil)
                    return
                }
                do {
                    let chat: Chat? = try database.getObject(fromTable: "chats", orderBy: [Chat.Properties.createdTime.order(.descending)], offset: 0)
                    continuation.resume(returning: chat?.id)
                } catch {
                    logger.error("Fetch last chat id failed: \(error)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }

    func insert(chat: Chat) {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.insert(chat, intoTable: "chats")
                // Optionally, you can also insert associated ChatMessages here if they come bundled with the Chat.
            } catch {
                logger.error("Insert Chat Error: \(error)")
            }
        }
    }

    func fetchAllChats(offset: Int = 0) async -> [Chat] {
        guard let database else { return [] }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else { return }
                    do {
                        // 首先获取Chat对象
                        let chats: [Chat] = try database.getObjects(fromTable: "chats", orderBy: [Chat.Properties.createdTime.order(.descending)], limit: limit, offset: offset)

                        // 优化：批量加载所有相关消息，避免N+1查询
                        var chatsWithMessages: [Chat] = []

                        if !chats.isEmpty {
                            // 获取所有Chat的ID
                            let chatIds = chats.map { $0.id }

                            // 批量查询所有相关消息
                            let allMessages: [ChatMessage] = try database.getObjects(
                                fromTable: "chatMessages",
                                where: ChatMessage.Properties.chatId.in(chatIds),
                                orderBy: [ChatMessage.Properties.chatId.order(.ascending),
                                         ChatMessage.Properties.createdTime.order(.ascending)]
                            )

                            // 按chatId分组消息
                            let messagesByChat = Dictionary(grouping: allMessages) { $0.chatId }

                            // 为每���Chat分配对应的消息
                            for var chat in chats {
                                chat.messages = messagesByChat[chat.id] ?? []
                                chatsWithMessages.append(chat)
                            }
                        }

                        continuation.resume(returning: chatsWithMessages)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Fetch all chats with messages failed: \(error)")
            return []
        }
    }

    func fetchLastChat() async -> Chat {
        return await withCheckedContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self, let database = self.database else {
                    continuation.resume(returning: self?.startNewChat() ?? Chat(id: UUID().uuidString, messages: []))
                    return
                }
                do {
                    var chat = try database.getObject(fromTable: "chats", orderBy: [Chat.Properties.createdTime.order(.descending)], offset: 0) ?? self.startNewChat()

                    // 如果找到了Chat，加载其关联的消息
                    if chat.id != self.startNewChat().id { // 不���新创建的空会话
                        let messages: [ChatMessage] = try database.getObjects(fromTable: "chatMessages",
                                                                            where: ChatMessage.Properties.chatId == chat.id,
                                                                            orderBy: [ChatMessage.Properties.createdTime.order(.ascending)])
                        chat.messages = messages
                    }

                    continuation.resume(returning: chat)
                } catch {
                    logger.error("Fetch last chat with messages failed: \(error)")
                    continuation.resume(returning: self.startNewChat())
                }
            }
        }
    }

    func startNewChat() -> Chat {
        let chatId = UUID().uuidString
        let chat = Chat(id: chatId, messages: [])
        return chat
    }

    func update(chat: Chat) {
        Task {
            if await fetchChat(id: chat.id) == nil {
                insert(chat: chat)
            } else {
                databaseQueue.async { [weak self] in
                    do {
                        try self?.database?.update(table: "chats",
                                                 on: [Chat.Properties.title],
                                                 with: chat,
                                                 where: Chat.Properties.id == chat.id)
                    } catch {
                        logger.error("Update Chat Error: \(error)")
                    }
                }
            }
        }
    }

    func updateChatTitle(id: String, title: String) {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.update(table: "chats",
                                     on: [Chat.Properties.title],
                                     with: title,
                                     where: Chat.Properties.id == id)
            } catch {
                logger.error("Update Chat Title Error: \(error)")
            }
        }
    }

    func deleteChat(id: String) {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.delete(fromTable: "chats", where: Chat.Properties.id == id)
                try database?.delete(fromTable: "chatMessages", where: ChatMessage.Properties.chatId == id)
            } catch {
                logger.error("Delete Chat Error: \(error)")
            }
        }
    }

    func archiveChat(id: String) {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.update(table: "chats",
                                     on: [Chat.Properties.isArchived],
                                     with: true,
                                     where: Chat.Properties.id == id)
            } catch {
                logger.error("Archive Chat Error: \(error)")
            }
        }
    }

    func unarchiveChat(id: String) {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.update(table: "chats",
                                     on: [Chat.Properties.isArchived],
                                     with: false,
                                     where: Chat.Properties.id == id)
            } catch {
                logger.error("Archive Chat Error: \(error)")
            }
        }
    }

    func fetchChat(id: String) async -> Chat? {
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else { return }
                    do {
                        if var chat: Chat = try database?.getObject(fromTable: "chats", where: Chat.Properties.id == id) {
                            // 加载该会话的所有消息
                            let messages: [ChatMessage] = try database?.getObjects(fromTable: "chatMessages",
                                                                                 where: ChatMessage.Properties.chatId == chat.id,
                                                                                 orderBy: [ChatMessage.Properties.createdTime.order(.ascending)]) ?? []
                            // 更新Chat对象的messages字段
                            chat.messages = messages
                            continuation.resume(returning: chat)
                        } else {
                            continuation.resume(returning: nil)
                        }
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Fetch chat with messages failed: \(error)")
            return nil
        }
    }

    func fetchChats(offset: Int = 0, isArchived: Bool) async -> [Chat] {
        guard let database else { return [] }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else { return }
                    do {
                        // 首先获取Chat对象
                        let chats: [Chat] = try database.getObjects(fromTable: "chats", where: Chat.Properties.isArchived == isArchived, orderBy: [Chat.Properties.createdTime.order(.descending)], limit: limit, offset: offset)

                        // 优化：批量加载所有相关消息，避免N+1查询
                        var chatsWithMessages: [Chat] = []

                        if !chats.isEmpty {
                            // 获取所有Chat的ID
                            let chatIds = chats.map { $0.id }

                            // 批量查询所有相关消息
                            let allMessages: [ChatMessage] = try database.getObjects(
                                fromTable: "chatMessages",
                                where: ChatMessage.Properties.chatId.in(chatIds),
                                orderBy: [ChatMessage.Properties.chatId.order(.ascending),
                                         ChatMessage.Properties.createdTime.order(.ascending)]
                            )

                            // 按chatId分组消息
                            let messagesByChat = Dictionary(grouping: allMessages) { $0.chatId }

                            // 为每个Chat分配对应的消息
                            for var chat in chats {
                                chat.messages = messagesByChat[chat.id] ?? []
                                chatsWithMessages.append(chat)
                            }
                        }

                        continuation.resume(returning: chatsWithMessages)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Fetch chats with messages failed: \(error)")
            return []
        }
    }

    func fetchUnarchivedChats(offset: Int = 0) async -> [Chat] {
        guard let database else { return [] }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else { return }
                    do {
                        // 首先获取Chat对象
                        let chats: [Chat] = try database.getObjects(fromTable: "chats", where: Chat.Properties.isArchived == false, orderBy: [Chat.Properties.createdTime.order(.descending)], limit: limit, offset: offset)

                        // 优化：批量加载所有相关消息，避免N+1查询
                        var chatsWithMessages: [Chat] = []

                        if !chats.isEmpty {
                            // 获取所有Chat的ID
                            let chatIds = chats.map { $0.id }

                            // 批量查询所有相关消息
                            let allMessages: [ChatMessage] = try database.getObjects(
                                fromTable: "chatMessages",
                                where: ChatMessage.Properties.chatId.in(chatIds),
                                orderBy: [ChatMessage.Properties.chatId.order(.ascending),
                                         ChatMessage.Properties.createdTime.order(.ascending)]
                            )

                            // 按chatId分组消息
                            let messagesByChat = Dictionary(grouping: allMessages) { $0.chatId }

                            // 为每个Chat分配对应的消息
                            for var chat in chats {
                                chat.messages = messagesByChat[chat.id] ?? []
                                chatsWithMessages.append(chat)
                            }
                        }

                        continuation.resume(returning: chatsWithMessages)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Fetch unarchived chats with messages failed: \(error)")
            return []
        }
    }

    func fetchArchivedChats(offset: Int = 0) async -> [Chat] {
        guard let database else { return [] }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else { return }
                    do {
                        // 首先获取Chat对象
                        let chats: [Chat] = try database.getObjects(fromTable: "chats", where: Chat.Properties.isArchived == true, orderBy: [Chat.Properties.createdTime.order(.descending)], limit: limit, offset: offset)

                        // 为每个Chat加载关联的消息
                        var chatsWithMessages: [Chat] = []
                        for var chat in chats {
                            // 加载该会话的所有消息
                            let messages: [ChatMessage] = try database.getObjects(fromTable: "chatMessages",
                                                                                where: ChatMessage.Properties.chatId == chat.id,
                                                                                orderBy: [ChatMessage.Properties.createdTime.order(.ascending)])
                            // 更新Chat对象的messages字段
                            chat.messages = messages
                            chatsWithMessages.append(chat)
                        }

                        continuation.resume(returning: chatsWithMessages)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Fetch archived chats with messages failed: \(error)")
            return []
        }
    }

    // ChatMessage operations
    func fetchMessage(id: String) async -> ChatMessage? {
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else { return }
                    do {
                        let message: ChatMessage? = try database?.getObject(fromTable: "chatMessages", where: ChatMessage.Properties.id == id)
                        continuation.resume(returning: message)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Fetch messages failed: \(error)")
            return nil
        }
    }

    func insert(message: ChatMessage) {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.insert(message, intoTable: "chatMessages")
            } catch {
                logger.error("Insert Message Error: \(error)")
            }
        }
    }

    func update(message: ChatMessage) async {
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else {
                        continuation.resume()
                        return
                    }
                    do {
                        // 检查消息是否存在
                        let existingMessage: ChatMessage? = try database?.getObject(
                            fromTable: "chatMessages",
                            where: ChatMessage.Properties.id == message.id
                        )

                        if existingMessage == nil {
                            // 插入新消息
                            try database?.insert(message, intoTable: "chatMessages")
                        } else {
                            // 更新现有消息，确保所有字段都被更新
                            try database?.update(table: "chatMessages",
                                               on: [ChatMessage.Properties.content,
                                                    ChatMessage.Properties.isComplete,
                                                    ChatMessage.Properties.finishReason],
                                               with: message,
                                               where: ChatMessage.Properties.id == message.id)
                        }
                        continuation.resume()
                    } catch {
                        logger.error("Update Message Error: \(error)")
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Update Message Error: \(error)")
        }
    }

    func update(messages: [ChatMessage]) {
        Task {
            // 优化：使用事务批量更新消息，提高性能和数据一致性
            await updateMessagesInTransaction(messages)
        }
    }

    /// 在事务中批量更新消息，确保数据一致性
    private func updateMessagesInTransaction(_ messages: [ChatMessage]) async {
        guard !messages.isEmpty else { return }

        do {
            try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
                databaseQueue.async { [weak self] in
                    guard let self = self, let database = self.database else {
                        continuation.resume(throwing: NSError(domain: "DatabaseError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Database not available"]))
                        return
                    }

                    do {
                        try database.run(transaction: { _ in
                            for message in messages {
                                // 检查消息是否存在
                                let existingMessage: ChatMessage? = try database.getObject(
                                    fromTable: "chatMessages",
                                    where: ChatMessage.Properties.id == message.id
                                )

                                if existingMessage == nil {
                                    // 插入新消息
                                    try database.insert(message, intoTable: "chatMessages")
                                } else {
                                    // 更新现有消息
                                    try database.update(table: "chatMessages",
                                                       on: [ChatMessage.Properties.content,
                                                            ChatMessage.Properties.isComplete,
                                                            ChatMessage.Properties.finishReason],
                                                       with: message,
                                                       where: ChatMessage.Properties.id == message.id)
                                }
                            }
                        })
                        continuation.resume()
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Batch update messages failed: \(error)")
        }
    }

    func delete(message: ChatMessage) async {
        await withCheckedContinuation { (continuation: CheckedContinuation<Void, Never>) in
            databaseQueue.async { [weak self] in
                guard let self else {
                    continuation.resume()
                    return
                }
                do {
                    try self.database?.delete(fromTable: "chatMessages", where: ChatMessage.Properties.id == message.id)
                } catch {
                    logger.error("Delete Message Error: \(error)")
                }
                continuation.resume()
            }
        }
    }

    func fetchMessages(chatId: String, offset: Int = 0, limit: Int = .max) async -> [ChatMessage] {
        guard let database else { return [] }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else { return }
                    do {
                        let messages: [ChatMessage] = try database.getObjects(fromTable: "chatMessages",
                                                                              where: ChatMessage.Properties.chatId == chatId,
                                                                              orderBy: [ChatMessage.Properties.createdTime.order(.ascending)],
                                                                              limit: limit, offset: offset)
                        continuation.resume(returning: messages)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Fetch messages failed: \(error)")
            return []
        }
    }

    /// 手动触发数据库迁移检查（已废弃，使用setupDatabase中的自动迁移）
    @available(*, deprecated, message: "使用setupDatabase中的自动迁移机制")
    func migrateIfNeeded() {
        // 迁移逻辑已移至setupDatabase方法中
        logger.info("迁移检查已在setupDatabase中完成")
    }

    /// 清理没有用户消息的空会话
    func cleanupEmptyChats() {
        databaseQueue.async { [weak self] in
            guard let self, let database = self.database else { return }
            do {
                // 获取所有会话
                let allChats: [Chat] = try database.getObjects(fromTable: "chats")

                for chat in allChats {
                    // 检查该会话是否有用户消息
                    let userMessages: [ChatMessage] = try database.getObjects(
                        fromTable: "chatMessages",
                        where: ChatMessage.Properties.chatId == chat.id && ChatMessage.Properties.role == Role.user.rawValue
                    )

                    // 如果没有用户消息，删除该会话及其所有消息
                    if userMessages.isEmpty {
                        try database.delete(fromTable: "chats", where: Chat.Properties.id == chat.id)
                        try database.delete(fromTable: "chatMessages", where: ChatMessage.Properties.chatId == chat.id)
                        logger.info("删除空会话: \(chat.id)")
                    }
                }
            } catch {
                logger.error("清理空会话失败: \(error)")
            }
        }
    }
}

extension AdvisorDatabaseManager {
    func searchChats(keyword: String, archive: Bool = false) async -> [Chat] {
        guard let database else { return [] }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else { return }
                    do {
                        // 查询标题匹配且归档状态匹配的聊天记录
                        let chatsWithMatchingTitle: [Chat] = try database.getObjects(fromTable: "chats",
                                                                                     where: Chat.Properties.title.like("%\(keyword)%") && Chat.Properties.isArchived == archive)

                        // 查询内容匹配的聊天消息
                        let messagesWithMatchingContent: [ChatMessage] = try database.getObjects(fromTable: "chatMessages",
                                                                                                 where: ChatMessage.Properties.content.like("%\(keyword)%"))

                        // 获取匹配消息的聊天ID
                        let chatIds = Set(messagesWithMatchingContent.map(\.chatId))

                        // 根据匹配消息的聊天ID查询聊天记录，并匹配归档状态
                        let chatsWithMatchingMessages: [Chat] = try database.getObjects(fromTable: "chats",
                                                                                        where: Chat.Properties.id.in(chatIds.map { Expression(with: $0) }) && Chat.Properties.isArchived == archive)

                        // 使用字典进行去重
                        var allChatsDict: [String: Chat] = [:]

                        for chat in chatsWithMatchingTitle {
                            allChatsDict[chat.id] = chat
                        }

                        for chat in chatsWithMatchingMessages {
                            allChatsDict[chat.id] = chat
                        }

                        // 提取字典中的所有聊天记录
                        let allChats = Array(allChatsDict.values)

                        continuation.resume(returning: allChats)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Search chats failed: \(error)")
            return []
        }
    }

    func deleteAllChat() {
        databaseQueue.async { [weak self] in
            guard let self else { return }
            do {
                try database?.delete(fromTable: "chats")
                try database?.delete(fromTable: "chatMessages")
            } catch {
                logger.error("Delete All Chat Error: \(error)")
            }
        }
    }
}

extension AdvisorDatabaseManager {
    /// 优化的分页消息查询方法
    func fetchLatestMessages(chatId: String, before: [ChatMessage], limit: Int = 20) async -> [ChatMessage] {
        guard let database else { return [] }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async {
                    do {
                        var whereCondition = ChatMessage.Properties.chatId == chatId

                        if !before.isEmpty {
                            // 找到最早的消息时间
                            if let earliestMessage = before.min(by: { $0.createdTime < $1.createdTime }) {
                                whereCondition = whereCondition && ChatMessage.Properties.createdTime < earliestMessage.createdTime
                            }
                        }

                        let messages: [ChatMessage] = try database.getObjects(
                            fromTable: "chatMessages",
                            where: whereCondition,
                            orderBy: [ChatMessage.Properties.createdTime.order(.descending)],
                            limit: limit
                        )
                        let sortedMessages = messages.sorted(by: { $0.createdTime < $1.createdTime })
                        continuation.resume(returning: sortedMessages)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Search ChatMessage failed: \(error)")
            return []
        }
    }

    /// 基于时间戳的高效分页查询
    func fetchMessagesPaginated(chatId: String, beforeTimestamp: Int64? = nil, limit: Int = 20) async -> [ChatMessage] {
        guard let database else { return [] }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async {
                    do {
                        var whereCondition = ChatMessage.Properties.chatId == chatId

                        if let beforeTimestamp = beforeTimestamp {
                            whereCondition = whereCondition && ChatMessage.Properties.createdTime < beforeTimestamp
                        }

                        let messages: [ChatMessage] = try database.getObjects(
                            fromTable: "chatMessages",
                            where: whereCondition,
                            orderBy: [ChatMessage.Properties.createdTime.order(.descending)],
                            limit: limit
                        )
                        // 返回按时间正序排列的消息
                        let sortedMessages = messages.sorted(by: { $0.createdTime < $1.createdTime })
                        continuation.resume(returning: sortedMessages)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Fetch paginated messages failed: \(error)")
            return []
        }
    }

    /// 获取会话的消息总数
    func getMessageCount(chatId: String) async -> Int {
        guard let database else { return 0 }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async {
                    do {
                        let count = try database.getValue(
                            on: ChatMessage.Properties.id.count(),
                            fromTable: "chatMessages",
                            where: ChatMessage.Properties.chatId == chatId
                        ).intValue
                        continuation.resume(returning: count)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Get message count failed: \(error)")
            return 0
        }
    }

    /// 获取会话的最新消息
    func getLatestMessage(chatId: String) async -> ChatMessage? {
        guard let database else { return nil }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async {
                    do {
                        let message: ChatMessage? = try database.getObject(
                            fromTable: "chatMessages",
                            where: ChatMessage.Properties.chatId == chatId,
                            orderBy: [ChatMessage.Properties.createdTime.order(.descending)],
                            offset: 0
                        )
                        continuation.resume(returning: message)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Get latest message failed: \(error)")
            return nil
        }
    }

    func fetchLatestChats(before chatId: String? = nil, limit: Int = 20) async -> [Chat] {
        guard let database else { return [] }
        do {
            return try await withCheckedThrowingContinuation { continuation in
                databaseQueue.async { [weak self] in
                    guard let self else { return }
                    do {
                        var whereCondition: Condition? = nil

                        if let chatId {
                            // 获取具有指定ID的聊天的创建时间
                            if let chat: Chat = try database.getObject(
                                fromTable: "chats",
                                where: Chat.Properties.id == chatId
                            ) {
                                whereCondition = Chat.Properties.createdTime < chat.createdTime
                            }
                        }

                        let chats: [Chat] = try database.getObjects(
                            fromTable: "chats",
                            where: whereCondition,
                            orderBy: [Chat.Properties.createdTime.order(.descending)],
                            limit: limit
                        )
                        continuation.resume(returning: chats)
                    } catch {
                        continuation.resume(throwing: error)
                    }
                }
            }
        } catch {
            logger.error("Search chats failed: \(error)")
            return []
        }
    }

    /// 在数据库队列中执行异步操作
    func executeInDatabaseQueue<T>(_ operation: @escaping () throws -> T) async throws -> T {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async {
                do {
                    let result = try operation()
                    continuation.resume(returning: result)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
}

// MARK: - Repository系统集成扩展
extension AdvisorDatabaseManager {

    /// 启用Repository系统
    func enableRepositorySystem() {
        isRepositoryEnabled = true
        logger.info("Repository系统已启用")
    }

    /// 禁用Repository系统（回退到原有实现）
    func disableRepositorySystem() {
        isRepositoryEnabled = false
        logger.info("Repository系统已禁用，回退到原有实现")
    }

    /// 使用Repository系统更新聊天（如果启用）
    func updateChatWithRepository(_ chat: Chat) {
        // 简化：直接使用本地方法
        update(chat: chat)
    }

    /// 使用Repository系统更新消息（如果启用）
    func updateMessageWithRepository(_ message: ChatMessage) async {
        // 简化：直接使用本地方法
        await update(message: message)
    }

    /// 使用Repository系统获取聊天（如果启用）
    func fetchChatWithRepository(id: String) async -> Chat? {
        // 简化：直接使用本地方法
        return await fetchChat(id: id)
    }

    /// 使用Repository系统获取消息（如果启用）
    func fetchMessagesWithRepository(chatId: String, limit: Int = 20, before: [ChatMessage] = []) async -> [ChatMessage] {
        // 简化：直接使用本地方法
        return await fetchLatestMessages(chatId: chatId, before: before, limit: limit)
    }

    /// 迁移现有数据到Repository系统
    func migrateToRepositorySystem() async {
        // 简化：无需迁移
        logger.info("Repository系统已简化，无需迁移")
    }

    /// 验证Repository系统数据一致性
    func validateRepositoryDataConsistency() async {
        // 简化：无需验证
        logger.info("Repository系统已简化，数据一致性验证跳过")
    }
}
