//
//  DatabaseVersion.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/06/27.
//

import Foundation
import WCDBSwift
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "DatabaseVersion")

/// 数据库版本信息模型
struct DatabaseVersion: Identifiable, Codable, TableCodable {
    let id: String = "database_version" // 固定ID，确保只有一条记录
    var version: Int
    var lastUpdated: Int64
    var migrationHistory: [String] // 存储已执行的迁移记录
    
    init(version: Int = 1, lastUpdated: Int64 = Int64(Date().timeIntervalSince1970), migrationHistory: [String] = []) {
        self.version = version
        self.lastUpdated = lastUpdated
        self.migrationHistory = migrationHistory
    }
    
    enum CodingKeys: String, CodingTableKey {
        typealias Root = DatabaseVersion
        static let objectRelationalMapping = TableBinding(CodingKeys.self) {
            BindColumnConstraint(id, isPrimary: true, isUnique: true)
        }
        
        case id
        case version
        case lastUpdated
        case migrationHistory
    }
}

/// 数据库迁移协议
protocol DatabaseMigration {
    var version: Int { get }
    var description: String { get }
    func migrate(database: Database) throws
    func rollback(database: Database) throws
}

/// 数据库版本管理器
class DatabaseVersionManager {
    static let shared = DatabaseVersionManager()
    
    /// 当前应用支持的最新数据库版本
    static let currentVersion = 1
    
    private let databaseQueue = DispatchQueue(label: "com.sanva.chatadvisor.database.version", qos: .background)
    private var registeredMigrations: [Int: DatabaseMigration] = [:]
    
    private init() {
        registerMigrations()
    }
    
    /// 注册所有迁移
    private func registerMigrations() {
        // 注册所有的数据库迁移
        registeredMigrations[2] = Migration_V1_to_V2()
        registeredMigrations[3] = Migration_V2_to_V3()

        // 未来的迁移可以在这里添加
        // registeredMigrations[4] = Migration_V3_to_V4()
    }
    
    /// 检查并执行数据库迁移
    func migrateIfNeeded(database: Database) throws {
        try createVersionTableIfNeeded(database: database)
        
        let currentDbVersion = try getCurrentDatabaseVersion(database: database)
        let targetVersion = Self.currentVersion
        
        if currentDbVersion < targetVersion {
            logger.info("数据库需要从版本 \(currentDbVersion) 升级到版本 \(targetVersion)")
            try performMigration(database: database, from: currentDbVersion, to: targetVersion)
        } else if currentDbVersion > targetVersion {
            logger.warning("数据库版本 \(currentDbVersion) 高于应用支持的版本 \(targetVersion)")
            // 可以选择降级或者抛出错误
            throw DatabaseVersionError.versionTooHigh(current: currentDbVersion, supported: targetVersion)
        } else {
            logger.info("数据库版本已是最新: \(currentDbVersion)")
        }
    }
    
    /// 创建版本表（如果不存在）
    private func createVersionTableIfNeeded(database: Database) throws {
        try database.create(table: "database_version", of: DatabaseVersion.self)
        
        // 检查是否已有版本记录，如果没有则创建初始记录
        let existingVersion: DatabaseVersion? = try database.getObject(
            fromTable: "database_version",
            where: DatabaseVersion.Properties.id == "database_version"
        )
        
        if existingVersion == nil {
            let initialVersion = DatabaseVersion(version: 1, migrationHistory: ["initial_setup"])
            try database.insert(initialVersion, intoTable: "database_version")
            logger.info("创建初始数据库版本记录: v1")
        }
    }
    
    /// 获取当前数据库版本
    private func getCurrentDatabaseVersion(database: Database) throws -> Int {
        let versionRecord: DatabaseVersion? = try database.getObject(
            fromTable: "database_version",
            where: DatabaseVersion.Properties.id == "database_version"
        )
        return versionRecord?.version ?? 1
    }
    
    /// 执行数据库迁移
    private func performMigration(database: Database, from: Int, to: Int) throws {
        var currentVersion = from
        var migrationHistory: [String] = []
        
        // 获取现有的迁移历史
        if let existingVersion: DatabaseVersion = try database.getObject(
            fromTable: "database_version",
            where: DatabaseVersion.Properties.id == "database_version"
        ) {
            migrationHistory = existingVersion.migrationHistory
        }
        
        // 逐步执行迁移
        while currentVersion < to {
            let nextVersion = currentVersion + 1
            
            guard let migration = registeredMigrations[nextVersion] else {
                throw DatabaseVersionError.migrationNotFound(version: nextVersion)
            }
            
            logger.info("执行数据库迁移: v\(currentVersion) -> v\(nextVersion)")
            
            do {
                try migration.migrate(database: database)
                migrationHistory.append("migration_v\(currentVersion)_to_v\(nextVersion)")
                currentVersion = nextVersion
                logger.info("迁移成功: v\(currentVersion)")
            } catch {
                logger.error("迁移失败: v\(currentVersion) -> v\(nextVersion), 错误: \(error)")
                throw DatabaseVersionError.migrationFailed(from: currentVersion, to: nextVersion, error: error)
            }
        }
        
        // 更新版本记录
        let updatedVersion = DatabaseVersion(
            version: currentVersion,
            lastUpdated: Int64(Date().timeIntervalSince1970),
            migrationHistory: migrationHistory
        )
        
        try database.insertOrReplace(updatedVersion, intoTable: "database_version")
        logger.info("数据库迁移完成，当前版本: v\(currentVersion)")
    }
    
    /// 获取迁移历史
    func getMigrationHistory(database: Database) throws -> [String] {
        let versionRecord: DatabaseVersion? = try database.getObject(
            fromTable: "database_version",
            where: DatabaseVersion.Properties.id == "database_version"
        )
        return versionRecord?.migrationHistory ?? []
    }
    
    /// 验证数据库完整性
    func validateDatabaseIntegrity(database: Database) throws -> Bool {
        // 检查必要的表是否存在
        let requiredTables = ["chats", "chatMessages", "database_version"]
        
        for tableName in requiredTables {
            let exists = try database.isTableExists(tableName)
            if !exists {
                logger.error("必要的表不存在: \(tableName)")
                return false
            }
        }
        
        // 检查版本记录是否存在
        let versionRecord: DatabaseVersion? = try database.getObject(
            fromTable: "database_version",
            where: DatabaseVersion.Properties.id == "database_version"
        )
        
        if versionRecord == nil {
            logger.error("数据库版本记录不存在")
            return false
        }
        
        logger.info("数据库完整性验证通过")
        return true
    }
}

/// 数据库版本错误类型
enum DatabaseVersionError: Error, LocalizedError {
    case versionTooHigh(current: Int, supported: Int)
    case migrationNotFound(version: Int)
    case migrationFailed(from: Int, to: Int, error: Error)
    case integrityCheckFailed(reason: String)
    
    var errorDescription: String? {
        switch self {
        case .versionTooHigh(let current, let supported):
            return "数据库版本过高: 当前版本 \(current), 支持版本 \(supported)"
        case .migrationNotFound(let version):
            return "找不到版本 \(version) 的迁移脚本"
        case .migrationFailed(let from, let to, let error):
            return "数据库迁移失败: v\(from) -> v\(to), 错误: \(error.localizedDescription)"
        case .integrityCheckFailed(let reason):
            return "数据库完整性检查失败: \(reason)"
        }
    }
}
