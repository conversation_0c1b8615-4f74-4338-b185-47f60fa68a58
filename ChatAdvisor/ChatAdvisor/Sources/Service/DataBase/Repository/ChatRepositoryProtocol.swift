//
//  ChatRepositoryProtocol.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation

/// 聊天数据仓库协议
protocol ChatRepositoryProtocol {
    /// 分页获取聊天列表
    /// - Parameters:
    ///   - limit: 每页数量
    ///   - offset: 偏移量
    ///   - isArchived: 是否为归档状态
    /// - Returns: 聊天列表
    func fetchChats(limit: Int, offset: Int, isArchived: Bool) async throws -> [Chat]
    
    /// 根据ID获取单个聊天
    /// - Parameter id: 聊天ID
    /// - Returns: 聊天对象，不存在时返回nil
    func fetchChat(id: String) async throws -> Chat?
    
    /// 保存聊天
    /// - Parameter chat: 聊天对象
    func saveChat(_ chat: Chat) async throws
    
    /// 更新聊天
    /// - Parameter chat: 聊天对象
    func updateChat(_ chat: Chat) async throws
    
    /// 删除聊天
    /// - Parameter id: 聊天ID
    func deleteChat(id: String) async throws
    
    /// 归档/取消归档聊天
    /// - Parameters:
    ///   - id: 聊天ID
    ///   - isArchived: 是否归档
    func archiveChat(id: String, isArchived: Bool) async throws
    
    /// 搜索聊天
    /// - Parameters:
    ///   - keyword: 搜索关键词
    ///   - isArchived: 是否在归档中搜索
    /// - Returns: 匹配的聊天列表
    func searchChats(keyword: String, isArchived: Bool) async throws -> [Chat]
    
    /// 获取聊天总数
    /// - Parameter isArchived: 是否为归档状态
    /// - Returns: 聊天总数
    func getChatCount(isArchived: Bool) async throws -> Int
    
    /// 清理空聊天
    func cleanupEmptyChats() async throws
    
    /// 批量删除聊天
    /// - Parameter ids: 聊天ID列表
    func batchDeleteChats(ids: [String]) async throws
}

/// 数据仓库错误类型
enum RepositoryError: Error, LocalizedError {
    case databaseNotInitialized
    case chatNotFound(String)
    case messageNotFound(String)
    case invalidData
    case operationFailed(Error)
    case concurrencyConflict
    
    var errorDescription: String? {
        switch self {
        case .databaseNotInitialized:
            return "数据库未初始化"
        case .chatNotFound(let id):
            return "聊天不存在: \(id)"
        case .messageNotFound(let id):
            return "消息不存在: \(id)"
        case .invalidData:
            return "数据格式无效"
        case .operationFailed(let error):
            return "操作失败: \(error.localizedDescription)"
        case .concurrencyConflict:
            return "并发操作冲突"
        }
    }
}

/// 数据操作结果
enum RepositoryResult<T> {
    case success(T)
    case failure(RepositoryError)
}
