//
//  MessageRepositoryProtocol.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation

/// 消息数据仓库协议
protocol MessageRepositoryProtocol {
    /// 分页获取消息列表
    /// - Parameters:
    ///   - chatId: 聊天ID
    ///   - limit: 每页数量
    ///   - before: 在这些消息之前的消息
    /// - Returns: 消息列表
    func fetchMessages(chatId: String, limit: Int, before: [ChatMessage]) async throws -> [ChatMessage]
    
    /// 获取聊天的所有消息
    /// - Parameter chatId: 聊天ID
    /// - Returns: 消息列表
    func fetchAllMessages(chatId: String) async throws -> [ChatMessage]
    
    /// 根据ID获取单个消息
    /// - Parameter id: 消息ID
    /// - Returns: 消息对象，不存在时返回nil
    func fetchMessage(id: String) async throws -> ChatMessage?
    
    /// 保存消息
    /// - Parameter message: 消息对象
    func saveMessage(_ message: ChatMessage) async throws
    
    /// 更新消息
    /// - Parameter message: 消息对象
    func updateMessage(_ message: ChatMessage) async throws
    
    /// 删除消息
    /// - Parameter id: 消息ID
    func deleteMessage(id: String) async throws
    
    /// 批量保存消息
    /// - Parameter messages: 消息列表
    func batchSaveMessages(_ messages: [ChatMessage]) async throws
    
    /// 批量更新消息
    /// - Parameter messages: 消息列表
    func batchUpdateMessages(_ messages: [ChatMessage]) async throws
    
    /// 删除聊天的所有消息
    /// - Parameter chatId: 聊天ID
    func deleteAllMessages(chatId: String) async throws
    
    /// 获取消息总数
    /// - Parameter chatId: 聊天ID，nil表示获取所有消息数量
    /// - Returns: 消息总数
    func getMessageCount(chatId: String?) async throws -> Int
    
    /// 搜索消息内容
    /// - Parameters:
    ///   - keyword: 搜索关键词
    ///   - chatId: 聊天ID，nil表示在所有聊天中搜索
    /// - Returns: 匹配的消息列表
    func searchMessages(keyword: String, chatId: String?) async throws -> [ChatMessage]
}