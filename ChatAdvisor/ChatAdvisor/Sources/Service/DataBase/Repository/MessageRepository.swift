//
//  MessageRepository.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import WCDBSwift
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "MessageRepository")

/// 消息数据仓库实现
class MessageRepository: MessageRepositoryProtocol {
    private let database: Database
    private let databaseQueue: DispatchQueue
    private let cacheManager: CacheManager<ChatMessage>
    
    init(database: Database, databaseQueue: DispatchQueue, cacheManager: CacheManager<ChatMessage>) {
        self.database = database
        self.databaseQueue = databaseQueue
        self.cacheManager = cacheManager
    }
    
    // MARK: - MessageRepositoryProtocol Implementation
    
    func fetchMessages(chatId: String, limit: Int, before: [ChatMessage]) async throws -> [ChatMessage] {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    var whereCondition = ChatMessage.Properties.chatId == chatId

                    if !before.isEmpty {
                        if let earliestMessage = before.min(by: { $0.createdTime < $1.createdTime }) {
                            whereCondition = whereCondition && ChatMessage.Properties.createdTime < earliestMessage.createdTime
                        }
                    }

                    let messages: [ChatMessage] = try self.database.getObjects(
                        fromTable: "chatMessages",
                        where: whereCondition,
                        orderBy: [ChatMessage.Properties.createdTime.order(.ascending)],
                        limit: limit
                    )

                    // 缓存获取的消息
                    Task {
                        for message in messages {
                            await self.cacheManager.set(key: message.id, value: message, expiration: 600) // 10分钟缓存
                        }
                    }

                    continuation.resume(returning: messages)
                } catch {
                    logger.error("获取消息列表失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func fetchAllMessages(chatId: String) async throws -> [ChatMessage] {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let messages: [ChatMessage] = try self.database.getObjects(
                        fromTable: "chatMessages",
                        where: ChatMessage.Properties.chatId == chatId,
                        orderBy: [ChatMessage.Properties.createdTime.order(.ascending)]
                    )
                    
                    // 缓存获取的消息
                    Task {
                        for message in messages {
                            await self.cacheManager.set(key: message.id, value: message, expiration: 600)
                        }
                    }
                    
                    continuation.resume(returning: messages)
                } catch {
                    logger.error("获取所有消息失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func fetchMessage(id: String) async throws -> ChatMessage? {
        // 先从缓存获取
        if let cachedMessage = await cacheManager.get(key: id) {
            logger.debug("从缓存获取消息: \(id)")
            return cachedMessage
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let message: ChatMessage? = try self.database.getObject(
                        fromTable: "chatMessages",
                        where: ChatMessage.Properties.id == id
                    )
                    
                    // 缓存获取的消息
                    if let message = message {
                        Task {
                            await self.cacheManager.set(key: message.id, value: message, expiration: 600)
                        }
                    }
                    
                    continuation.resume(returning: message)
                } catch {
                    logger.error("获取消息失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func saveMessage(_ message: ChatMessage) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.insert(message, intoTable: "chatMessages")
                    
                    // 更新缓存
                    Task {
                        await self.cacheManager.set(key: message.id, value: message, expiration: 600)
                    }
                    
                    logger.debug("保存消息成功: \(message.id)")
                    continuation.resume()
                } catch {
                    logger.error("保存消息失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func updateMessage(_ message: ChatMessage) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.update(
                        table: "chatMessages",
                        on: ChatMessage.Properties.all,
                        with: message,
                        where: ChatMessage.Properties.id == message.id
                    )
                    
                    // 更新缓存
                    Task {
                        await self.cacheManager.set(key: message.id, value: message, expiration: 600)
                    }
                    
                    logger.debug("更新消息成功: \(message.id)")
                    continuation.resume()
                } catch {
                    logger.error("更新消息失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func deleteMessage(id: String) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.delete(fromTable: "chatMessages", where: ChatMessage.Properties.id == id)
                    
                    // 从缓存中移除
                    Task {
                        await self.cacheManager.remove(key: id)
                    }
                    
                    logger.debug("删除消息成功: \(id)")
                    continuation.resume()
                } catch {
                    logger.error("删除消息失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func batchSaveMessages(_ messages: [ChatMessage]) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.run(transaction: { _ in
                        for message in messages {
                            try self.database.insert(message, intoTable: "chatMessages")
                        }
                    })
                    
                    // 批量更新缓存
                    Task {
                        for message in messages {
                            await self.cacheManager.set(key: message.id, value: message, expiration: 600)
                        }
                    }
                    
                    logger.debug("批量保存消息成功，保存了 \(messages.count) 条消息")
                    continuation.resume()
                } catch {
                    logger.error("批量保存消息失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func batchUpdateMessages(_ messages: [ChatMessage]) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.run(transaction: { _ in
                        for message in messages {
                            try self.database.update(
                                table: "chatMessages",
                                on: ChatMessage.Properties.all,
                                with: message,
                                where: ChatMessage.Properties.id == message.id
                            )
                        }
                    })
                    
                    // 批量更新缓存
                    Task {
                        for message in messages {
                            await self.cacheManager.set(key: message.id, value: message, expiration: 600)
                        }
                    }
                    
                    logger.debug("批量更新消息成功，更新了 \(messages.count) 条消息")
                    continuation.resume()
                } catch {
                    logger.error("批量更新消息失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func deleteAllMessages(chatId: String) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    // 先获取要删除的消息ID列表，用于清理缓存
                    let messages: [ChatMessage] = try self.database.getObjects(
                        fromTable: "chatMessages",
                        where: ChatMessage.Properties.chatId == chatId
                    )
                    let messageIds = messages.map { $0.id }
                    
                    // 删除消息
                    try self.database.delete(fromTable: "chatMessages", where: ChatMessage.Properties.chatId == chatId)
                    
                    // 从缓存中移除
                    Task {
                        for messageId in messageIds {
                            await self.cacheManager.remove(key: messageId)
                        }
                    }
                    
                    logger.debug("删除聊天所有消息成功: \(chatId)，删除了 \(messageIds.count) 条消息")
                    continuation.resume()
                } catch {
                    logger.error("删除聊天所有消息失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func getMessageCount(chatId: String?) async throws -> Int {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let count: Int32
                    if let chatId = chatId {
                        count = try self.database.getValue(
                            on: ChatMessage.Properties.id.count(),
                            fromTable: "chatMessages",
                            where: ChatMessage.Properties.chatId == chatId
                        ).int32Value
                    } else {
                        count = try self.database.getValue(
                            on: ChatMessage.Properties.id.count(),
                            fromTable: "chatMessages"
                        ).int32Value
                    }
                    
                    continuation.resume(returning: Int(count))
                } catch {
                    logger.error("获取消息数量失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func searchMessages(keyword: String, chatId: String?) async throws -> [ChatMessage] {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let whereCondition: Condition
                    if let chatId = chatId {
                        whereCondition = ChatMessage.Properties.content.like("%\(keyword)%") && 
                                       ChatMessage.Properties.chatId == chatId
                    } else {
                        whereCondition = ChatMessage.Properties.content.like("%\(keyword)%")
                    }
                    
                    let messages: [ChatMessage] = try self.database.getObjects(
                        fromTable: "chatMessages",
                        where: whereCondition,
                        orderBy: [ChatMessage.Properties.createdTime.order(.descending)]
                    )
                    
                    continuation.resume(returning: messages)
                } catch {
                    logger.error("搜索消息失败: \(error.localizedDescription)")
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
}
