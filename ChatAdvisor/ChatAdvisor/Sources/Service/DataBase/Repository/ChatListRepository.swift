//
//  ChatListRepository.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import WCDBSwift

/// 聊天列表专用Repository - 优化聊天列表相关的数据库操作
class ChatListRepository: ChatRepositoryProtocol {

    private let database: Database
    private let databaseQueue: DispatchQueue
    private let cacheManager: CacheManager<Chat>
    private let batchSize: Int = 50

    init(database: Database, databaseQueue: DispatchQueue, cacheManager: CacheManager<Chat>) {
        self.database = database
        self.databaseQueue = databaseQueue
        self.cacheManager = cacheManager
    }
    
    // MARK: - 优化的聊天列表查询
    
    func fetchChats(limit: Int, offset: Int, isArchived: Bool) async throws -> [Chat] {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                let startTime = Date()
                
                do {
                    // 使用预编译查询提升性能
                    let chats: [Chat] = try self.database.getObjects(
                        fromTable: "chats",
                        where: Chat.Properties.isArchived == isArchived,
                        orderBy: [Chat.Properties.createdTime.order(.descending)],
                        limit: limit,
                        offset: offset
                    )
                    
                    // 批量预加载消息以避免N+1查询问题
                    let chatsWithMessages = try self.batchLoadMessages(for: chats)
                    
                    // 批量缓存
                    Task {
                        await self.batchCacheChats(chatsWithMessages)
                    }
                    
                    continuation.resume(returning: chatsWithMessages)

                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func searchChats(keyword: String, isArchived: Bool) async throws -> [Chat] {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                let startTime = Date()
                
                do {
                    // 优化搜索：同时搜索标题和消息内容
                    let titleMatches: [Chat] = try self.database.getObjects(
                        fromTable: "chats",
                        where: Chat.Properties.title.like("%\(keyword)%") && Chat.Properties.isArchived == isArchived,
                        orderBy: [Chat.Properties.createdTime.order(.descending)]
                    )
                    
                    // 搜索消息内容匹配的聊天
                    let messageMatches: [ChatMessage] = try self.database.getObjects(
                        fromTable: "chatMessages",
                        where: ChatMessage.Properties.content.like("%\(keyword)%")
                    )
                    
                    // 获取消息匹配的聊天ID
                    let messageChatIds = Set(messageMatches.map { $0.chatId })
                    let messageMatchedChats: [Chat] = try self.database.getObjects(
                        fromTable: "chats",
                        where: Chat.Properties.id.in(Array(messageChatIds)) && Chat.Properties.isArchived == isArchived,
                        orderBy: [Chat.Properties.createdTime.order(.descending)]
                    )
                    
                    // 合并结果并去重
                    var allMatches = titleMatches
                    for chat in messageMatchedChats {
                        if !allMatches.contains(where: { $0.id == chat.id }) {
                            allMatches.append(chat)
                        }
                    }
                    
                    // 按创建时间重新排序
                    allMatches.sort { $0.createdTime > $1.createdTime }
                    
                    // 批量加载消息
                    let chatsWithMessages = try self.batchLoadMessages(for: allMatches)
                    
                    continuation.resume(returning: chatsWithMessages)

                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func getChatCount(isArchived: Bool) async throws -> Int {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let count = try self.database.getValue(
                        on: Chat.Properties.id.count(),
                        fromTable: "chats",
                        where: Chat.Properties.isArchived == isArchived
                    ).int32Value

                    continuation.resume(returning: Int(count))

                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    // MARK: - 批量操作优化
    
    func batchUpdateChats(_ chats: [Chat]) async throws {
        guard !chats.isEmpty else { return }
        
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                let startTime = Date()
                
                do {
                    try self.database.run(transaction: { database in
                        for chat in chats {
                            try database.update(
                                table: "chats",
                                on: Chat.Properties.all,
                                with: chat,
                                where: Chat.Properties.id == chat.id
                            )
                        }
                    })

                    // 批量更新缓存
                    Task {
                        await self.batchCacheChats(chats)
                    }

                    continuation.resume()

                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func batchDeleteChats(ids: [String]) async throws {
        guard !ids.isEmpty else { return }
        
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                let startTime = Date()
                
                do {
                    try self.database.run(transaction: { database in
                        // 删除聊天
                        for id in ids {
                            try database.delete(fromTable: "chats", where: Chat.Properties.id == id)
                        }

                        // 删除相关消息
                        for id in ids {
                            try database.delete(fromTable: "chatMessages", where: ChatMessage.Properties.chatId == id)
                        }
                    })

                    // 从缓存中批量移除
                    Task {
                        for id in ids {
                            await self.cacheManager.remove(key: id)
                        }
                    }

                    continuation.resume()

                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    // MARK: - 私有优化方法
    
    private func batchLoadMessages(for chats: [Chat]) throws -> [Chat] {
        guard !chats.isEmpty else { return [] }
        
        let chatIds = chats.map { $0.id }
        
        // 批量查询所有相关消息
        let allMessages: [ChatMessage] = try database.getObjects(
            fromTable: "chatMessages",
            where: ChatMessage.Properties.chatId.in(chatIds),
            orderBy: [ChatMessage.Properties.createdTime.order(.ascending)]
        )
        
        // 按chatId分组消息
        let messagesByChat = Dictionary(grouping: allMessages) { $0.chatId }
        
        // 为每个聊天分配消息
        return chats.map { chat in
            var chatWithMessages = chat
            chatWithMessages.messages = messagesByChat[chat.id] ?? []
            return chatWithMessages
        }
    }
    
    private func batchCacheChats(_ chats: [Chat]) async {
        for chat in chats {
            await cacheManager.set(key: chat.id, value: chat, expiration: 300)
        }
    }
    
    // MARK: - ChatRepositoryProtocol 其他必需方法的实现
    
    func fetchChat(id: String) async throws -> Chat? {
        // 先从缓存获取
        if let cachedChat = await cacheManager.get(key: id) {
            return cachedChat
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    let chat: Chat? = try self.database.getObject(
                        fromTable: "chats",
                        where: Chat.Properties.id == id
                    )
                    
                    if let chat = chat {
                        // 加载消息
                        let chatWithMessages = try self.batchLoadMessages(for: [chat]).first
                        
                        // 缓存
                        Task {
                            if let chatWithMessages = chatWithMessages {
                                await self.cacheManager.set(key: chat.id, value: chatWithMessages, expiration: 300)
                            }
                        }
                        
                        continuation.resume(returning: chatWithMessages)
                    } else {
                        continuation.resume(returning: nil)
                    }
                    
                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func saveChat(_ chat: Chat) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.insert(chat, intoTable: "chats")
                    
                    Task {
                        await self.cacheManager.set(key: chat.id, value: chat, expiration: 300)
                    }
                    
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func updateChat(_ chat: Chat) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.update(
                        table: "chats",
                        on: Chat.Properties.all,
                        with: chat,
                        where: Chat.Properties.id == chat.id
                    )
                    
                    Task {
                        await self.cacheManager.set(key: chat.id, value: chat, expiration: 300)
                    }
                    
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func deleteChat(id: String) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.run(transaction: { database in
                        try database.delete(fromTable: "chats", where: Chat.Properties.id == id)
                        try database.delete(fromTable: "chatMessages", where: ChatMessage.Properties.chatId == id)
                    })

                    Task {
                        await self.cacheManager.remove(key: id)
                    }

                    continuation.resume()
                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func archiveChat(id: String, isArchived: Bool) async throws {
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    try self.database.update(
                        table: "chats",
                        on: Chat.Properties.isArchived,
                        with: isArchived,
                        where: Chat.Properties.id == id
                    )
                    
                    Task {
                        await self.cacheManager.remove(key: id)
                    }
                    
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
    
    func cleanupEmptyChats() async throws {
        // 实现清理空聊天的逻辑
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            databaseQueue.async { [weak self] in
                guard let self = self else {
                    continuation.resume(throwing: RepositoryError.databaseNotInitialized)
                    return
                }
                
                do {
                    // 查找没有用户消息的聊天
                    let allChats: [Chat] = try self.database.getObjects(fromTable: "chats")
                    let chatsWithMessages = try self.batchLoadMessages(for: allChats)
                    
                    let emptyChats = chatsWithMessages.filter { chat in
                        !chat.messages.contains { $0.role == .user }
                    }
                    
                    // 删除空聊天
                    for chat in emptyChats {
                        try self.database.delete(fromTable: "chats", where: Chat.Properties.id == chat.id)
                    }
                    
                    continuation.resume()
                    
                } catch {
                    continuation.resume(throwing: RepositoryError.operationFailed(error))
                }
            }
        }
    }
}
