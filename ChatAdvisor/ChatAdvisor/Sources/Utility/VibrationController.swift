//
//  VibrationController.swift
//  JunShi
//
//  Created by md on 2024/5/30.
//

import UIKit

class VibrationController {
    static let shared = VibrationController()

    private var lastFeedbackDate: Date?
    private let baseInterval: TimeInterval
    private let randomRange: TimeInterval

    private init(baseInterval: TimeInterval = 0.25, randomRange: TimeInterval = 0.25) {
        self.baseInterval = baseInterval
        self.randomRange = randomRange
    }

    private func canTriggerFeedback() -> Bool {
        if let lastFeedbackDate {
            let interval = Date().timeIntervalSince(lastFeedbackDate)
            return interval >= getNextInterval()
        }
        return true
    }

    private func updateLastFeedbackDate() {
        lastFeedbackDate = Date()
    }

    private func getNextInterval() -> TimeInterval {
        let randomOffset = Double.random(in: -randomRange ... randomRange)
        return baseInterval + randomOffset
    }

    func triggerImpactFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        guard canTriggerFeedback() else { return }
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.prepare()
        generator.impactOccurred()
        updateLastFeedbackDate()
    }

    func triggerSelectionFeedback() {
        guard canTriggerFeedback() else { return }
        let generator = UISelectionFeedbackGenerator()
        generator.prepare()
        generator.selectionChanged()
        updateLastFeedbackDate()
    }

    func triggerNotificationFeedback(type: UINotificationFeedbackGenerator.FeedbackType) {
        guard canTriggerFeedback() else { return }
        let generator = UINotificationFeedbackGenerator()
        generator.prepare()
        generator.notificationOccurred(type)
        updateLastFeedbackDate()
    }
}
