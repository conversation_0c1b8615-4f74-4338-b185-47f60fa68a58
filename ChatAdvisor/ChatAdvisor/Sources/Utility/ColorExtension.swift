//
//  ColorExtension.swift
//  帮聊
//
//  Created by md on 2024/6/28.
//

import Foundation
import SwiftUI
import UIKit

extension UIColor {
    convenience init(color: Color) {
        let uiColor = UIColor(color)
        self.init(cgColor: uiColor.cgColor)
    }

    var toData: Data? {
        try? NSKeyedArchiver.archivedData(withRootObject: self, requiringSecureCoding: false)
    }

    static func from(data: Data) -> UIColor? {
        try? NSKeyedUnarchiver.unarchivedObject(ofClass: UIColor.self, from: data)
    }
}
