//
//  CryptoFunction.swift
//
//
//  Created by zwt on 2024/4/9.
//

import CommonCrypto
import CryptoKit
import Foundation

class CryptoFunction {
    static var secretKey: String {
        let originalSecretKey = "BIryQ4iRqJo1NqwJzJbMvTShcU6Iz4/bY+YYbcw/+J0="
        let hashed = SHA256.hash(data: Data(originalSecretKey.utf8))
        return hashed.compactMap { String(format: "%02x", $0) }.joined()
    }

    // 函数用于加密数据（aes-256-cbc）
    static func encryptData(data: Data, secretKey: String = CryptoFunction.secretKey, iv: String) -> Data? {
        guard let keyData = Data(hexString: secretKey), let ivData = iv.data(using: .utf8) else {
            print("Error: Secret key or IV format is not correct")
            return nil
        }

        let dataLength = data.count
        let bufferSize = dataLength + kCCBlockSizeAES128
        var buffer = Data(count: bufferSize)
        var numBytesEncrypted = 0

        let cryptStatus = data.withUnsafeBytes { dataBytes in
            buffer.withUnsafeMutableBytes { bufferBytes in
                ivData.withUnsafeBytes { ivBytes in
                    keyData.withUnsafeBytes { keyBytes in
                        CCCrypt(CCOperation(kCCEncrypt), CCAlgorithm(kCCAlgorithmAES), CCOptions(kCCOptionPKCS7Padding),
                                keyBytes.baseAddress, kCCKeySizeAES256,
                                ivBytes.baseAddress,
                                dataBytes.baseAddress, dataLength,
                                bufferBytes.baseAddress, bufferSize,
                                &numBytesEncrypted)
                    }
                }
            }
        }

        if cryptStatus == kCCSuccess {
            return buffer
        } else {
            print("Error: Encryption failed")
            return nil
        }
    }

    // 函数用于创建数据的签名（使用SHA-256）
    static func createSignature(data: Data, secretKey: String = CryptoFunction.secretKey) -> String? {
        guard let secretKeyData = Data(hexString: secretKey) else {
            print("Error: Secret key format is not correct")
            return nil
        }
        let key = SymmetricKey(data: secretKeyData)
        let signature = HMAC<SHA256>.authenticationCode(for: data, using: key)
        return Data(signature).hexString
    }

    static func hashPassword(_ password: String) -> String {
        let inputData = Data(password.utf8)
        let hashed = SHA256.hash(data: inputData)
        return hashed.compactMap { String(format: "%02x", $0) }.joined()
    }
}

extension String {
    init(hexData: Data) {
        self = hexData.map { String(format: "%02x", $0) }.joined()
    }
}

extension Data {
    init?(hexString: String) {
        let len = hexString.count / 2
        var data = Data(capacity: len)
        for i in 0 ..< len {
            let j = hexString.index(hexString.startIndex, offsetBy: i * 2)
            let k = hexString.index(j, offsetBy: 2)
            let bytes = hexString[j ..< k]
            if var num = UInt8(bytes, radix: 16) {
                data.append(&num, count: 1)
            } else {
                return nil
            }
        }
        self = data
    }

    var hexString: String {
        map { String(format: "%02x", $0) }.joined()
    }
}
