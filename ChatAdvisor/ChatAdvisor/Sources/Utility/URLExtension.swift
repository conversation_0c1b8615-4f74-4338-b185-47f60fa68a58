//
//  URLExtension.swift
//  ChatAdvisor
//
//  Created by md on 2024/7/29.
//

import Foundation

extension URL {
    /// 获取参数
    var queryParams: [String: String] {
        guard let host else { return [:] }
        return host.split(separator: "&").reduce(into: [:]) { result, parameter in
            let keyValue = parameter.split(separator: "=")
            result[String(keyValue[0])] = String(keyValue[1])
        }
    }
}
