//
//  ViewExtension.swift
//  JunShi
//
//  Created by md on 2024/4/19.
//

import Foundation
import SwiftUI

extension View {
    func placeholder(
        when shouldShow: Bo<PERSON>,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> some View
    ) -> some View {
        ZStack(alignment: alignment) {
            placeholder().opacity(shouldShow ? 1 : 0)
            self
        }
    }
}

extension View {
    func placeholder(
        _ text: String,
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        color: Color? = nil
    ) -> some View {
        placeholder(when: shouldShow, alignment: alignment) {
            if let color {
                Text(text).foregroundColor(color)
            } else {
                Text(text)
            }
        }
    }
}

enum Device {
    // MARK: 当前设备类型 iphone ipad mac

    enum Devicetype {
        case iphone, ipad, mac
    }

    static var deviceType: Devicetype {
        #if os(macOS)
            return .mac
        #else
            if UIDevice.current.userInterfaceIdiom == .pad {
                return .ipad
            } else {
                return .iphone
            }
        #endif
    }
}

extension View {
    @ViewBuilder func ifIs<T>(_ condition: <PERSON><PERSON>, transform: (Self) -> T) -> some View where T: View {
        if condition {
            transform(self)
        } else {
            self
        }
    }

    @ViewBuilder func ifElse<T: View, V: View>(_ condition: Bool, isTransform: (Self) -> T, elseTransform: (Self) -> V) -> some View {
        if condition {
            isTransform(self)
        } else {
            elseTransform(self)
        }
    }
}
