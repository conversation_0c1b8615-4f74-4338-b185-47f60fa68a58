import Foundation
import SimpleToast
import SwifterSwift
import SwiftUI
import UIKit

// 用于存储不同状态和模式的颜色
enum AppThemes {
    static var primaryColor: UIColor = Preferences.themeColor.value {
        didSet {
            Preferences.themeColor.value = AppThemes.primaryColor
        }
    } // 默认主题颜色
    static var colors: ColorStates {
        ColorStates(baseColor: primaryColor)
    }

    static var padding: CGFloat = 12
    static var cornerRadius: CGFloat = 12
    static var toastOptions = SimpleToastOptions(
        alignment: .top,
        hideAfter: 6
    )

    // 新增按钮样式
    static var buttonCornerRadius: CGFloat = 8
    static var buttonHeight: CGFloat = 44
    static var buttonMaxWidth: CGFloat = 375
    static var fontSize = 16.0
    static var buttonDisabledOpacity: Double = 0.5

    // {{ AURA-X: Add - 微信风格聊天界面设计常量 }}
    // 微信风格聊天设计常量
    enum Chat {
        // {{ AURA-X: Modify - 彻底消除消息间距，实现完全紧密排列 }}
        // 消息间距（完全紧密排列 - 无多余间距）
        static let messageVerticalSpacing: CGFloat = 0       // 同角色连续消息间距（无间距）
        static let messageGroupSpacing: CGFloat = 0          // 不同角色消息组之间的间距（无间距）
        static let messageSectionSpacing: CGFloat = 12       // 消息段落之间的间距（适中）
        static let messageHorizontalPadding: CGFloat = 16    // 消息水平边距
        static let messageBubbleMaxWidth: CGFloat = 0.75     // 消息气泡最大宽度比例

        // 消息气泡样式
        static let bubbleCornerRadius: CGFloat = 18         // 消息气泡圆角
        static let bubbleInnerPadding: CGFloat = 12         // 气泡内边距
        static let bubbleShadowRadius: CGFloat = 2          // 气泡阴影半径
        static let bubbleShadowOpacity: Double = 0.1        // 气泡阴影透明度

        // 用户消息样式（右侧绿色）
        static let userBubbleColor = Color(red: 0.35, green: 0.84, blue: 0.25)  // 微信绿
        static let userTextColor = Color.white

        // AI消息样式（左侧白色）
        static let aiBubbleColor = Color(.systemBackground)
        static let aiTextColor = Color.primary
        static let aiBubbleBorderColor = Color(.systemGray5)

        // 状态指示器
        static let typingIndicatorHeight: CGFloat = 40
        static let statusIndicatorSize: CGFloat = 12

        // 动画参数
        static let messageAppearDuration: Double = 0.3
        static let bubbleScaleEffect: CGFloat = 0.95
        static let contextMenuAnimationDuration: Double = 0.25

        // {{ AURA-X: Add - 滚动性能优化参数 }}
        // 滚动性能参数
        static let scrollAnimationDuration: Double = 0.2        // 统一的滚动动画时长
        static let scrollDebounceDelay: Double = 0.1            // 滚动防抖延迟
        static let autoScrollThreshold: CGFloat = 50            // 自动滚动触发阈值
        static let scrollSpeedThreshold: CGFloat = 100          // 用户滚动速度阈值
    }

    
    static var isDarkMode: Bool {
        UIScreen.main.traitCollection.userInterfaceStyle == .dark
    }

    static var sideMenuWidth: CGFloat {
        if UIDevice.current.isPad {
            UIScreen.main.bounds.width / 2
        } else {
            UIScreen.main.bounds.width - 120
        }
    }

    static var animationFont: Font {
        // 如果是中文,大一些
        if Locale.current.language.languageCode?.identifier == "zh" || UIDevice.current.isPad {
            .system(size: 24, weight: .bold, design: .default)
        } else {
            .system(size: 17, weight: .semibold, design: .default)
        }
    }
}

// 用于存储不同状态和模式的颜色
struct ColorStates {
    let mainBackground: UIColor
    let mainDark: UIColor
    let mainLight: UIColor
    let mainHighlighted: UIColor
    let mainDisabled: UIColor

    let backgroundReverse: UIColor

    init(baseColor: UIColor) {
        // 获取当前用户界面样式
        let userInterfaceStyle = UIScreen.main.traitCollection.userInterfaceStyle
        let isDarkMode = userInterfaceStyle == .dark

        // 根据模式调整基色
        let adjustedBaseColor = baseColor

        // 根据不同模式生成背景颜色
        mainBackground = isDarkMode ? adjustedBaseColor.lighten(by: 0.2) : adjustedBaseColor.darken(by: 0.2)
        mainDark = isDarkMode ? adjustedBaseColor.lighten(by: 0.2) : adjustedBaseColor.darken(by: 0.2)
        mainLight = isDarkMode ? adjustedBaseColor.darken(by: 0.2) : adjustedBaseColor.lighten(by: 0.2)
        mainHighlighted = isDarkMode ? adjustedBaseColor.lighten(by: 0.15) : adjustedBaseColor.darken(by: 0.15)
        mainDisabled = isDarkMode ? adjustedBaseColor.lighten(by: 0.3) : adjustedBaseColor.darken(by: 0.3)

        // 文字颜色是基于亮度决定，以确保足够的对比度
        backgroundReverse = isDarkMode ? .black : .white
    }
}

extension Color {
    static var mainBackground: Color { Color(AppThemes.colors.mainBackground) }
    static var mainDark: Color { Color(AppThemes.colors.mainDark) }
    static var mainLight: Color { Color(AppThemes.colors.mainLight) }
    static var mainHighlighted: Color { Color(AppThemes.colors.mainHighlighted) }
    static var mainDisabled: Color { Color(AppThemes.colors.mainDisabled) }

    static var reverse: Color { Color(AppThemes.colors.backgroundReverse) }

    var uiColor: UIColor {
        UIColor(self)
    }
}
