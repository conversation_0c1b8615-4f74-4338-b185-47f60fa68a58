//
//  DateExtension .swift
//  JunShi
//
//  Created by md on 2024/5/9.
//

import Foundation
import Localize_Swift

extension Date {
    func startOfDay() -> Date {
        Calendar.current.startOfDay(for: self)
    }

    static func == (lhs: Date, rhs: Date) -> Bool {
        Calendar.current.isDate(lhs, inSameDayAs: rhs)
    }

    static func dateFromTimestamp(_ timestamp: Int64) -> Date {
        Date(timeIntervalSince1970: Double(timestamp))
    }

    func chatDateLabel() -> String {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: self)
        
        let today = calendar.startOfDay(for: Date())
        if startOfDay == today {
            return "今天".localized()
        }
        
        let yesterday = calendar.date(byAdding: .day, value: -1, to: today)!
        if startOfDay == yesterday {
            return "昨天".localized()
        }
        
        let formatter = DateFormatter()
        let locale : Locale = NSLocale(localeIdentifier: Localize.currentLanguage()) as Locale
        formatter.locale = locale
        // 如果日期是本周的其他日期，返回“星期几”
        let startOfThisWeek = calendar.date(from: calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: today))!
        if startOfDay >= startOfThisWeek {
            formatter.dateFormat = "EEEE" // 星期几
            return formatter.string(from: self)
        }
        
        // 如果日期是今年的其他日期，返回“月日”
        let startOfThisYear = calendar.date(from: calendar.dateComponents([.year], from: today))!
        if startOfDay >= startOfThisYear {
            formatter.dateFormat = "MMMM, dd" // 月日
            return formatter.string(from: self)
        }
        
        // 如果日期是以前的年份，返回“年-月-日”
        formatter.dateFormat = "yyyy-MMMM-dd" // 年-月-日
        return formatter.string(from: self)
    }

    func chatDateLabelTuple() -> (label: String, date: Date) {
        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: self)

        let today = calendar.startOfDay(for: Date())
        if startOfDay == today {
            return ("今天", startOfDay)
        }

        let yesterday = calendar.date(byAdding: .day, value: -1, to: today)!
        if startOfDay == yesterday {
            return ("昨天", startOfDay)
        }

        if #available(iOS 15.0, *) {
            return (self.formatted(date: .abbreviated, time: .omitted), startOfDay)
        } else {
            return (dateString(), startOfDay)
        }
    }
}
