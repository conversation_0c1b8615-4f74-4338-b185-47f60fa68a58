//
//  UIDeviceExtension.swift
//  帮聊
//
//  Created by z<PERSON><PERSON>g on 2024/6/6.
//

import Foundation
import SwiftUI

extension UIDevice {
    var isPad: Bool {
        userInterfaceIdiom == .pad
    }
}

struct DeviceTypeKey: EnvironmentKey {
    static let defaultValue: Bool = UIDevice.current.isPad
}

extension EnvironmentValues {
    var isPad: Bool {
        get { self[DeviceTypeKey.self] }
        set { self[DeviceTypeKey.self] = newValue }
    }
}

// MARK: - UIApplication Extensions
extension UIApplication {
    /// 检查TikTok是否已安装（已移除TikTok登录功能，始终返回false）
    func isTikTokInstalled() -> Bool {
        return false
    }
}
