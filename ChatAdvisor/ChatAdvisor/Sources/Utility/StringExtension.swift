//
//  StringExtension.swift
//  JunShi
//
//  Created by md on 2024/4/19.
//

import CryptoKit
import Foundation
import Localize_Swift
import SwifterSwift

extension String {
    var chatDate: Date {
        if self == "今天".localized() {
            Date()
        } else if self == "昨天".localized() {
            Date().addingTimeInterval(-86400)
        } else {
            if let date {
                date
            } else {
                Date()
            }
        }
    }

    var md5: String {
        let inputData = Data(utf8)
        let hashed = Insecure.MD5.hash(data: inputData)
        let hashString = hashed.map { String(format: "%02hhx", $0) }.joined()
        return hashString
    }

    var emailPrefix: String {
        components(separatedBy: "@").first ?? ""
    }

    var isContainChinese: Bool {
        for scalar in unicodeScalars {
            if scalar >= "一", scalar <= "\u{9FA5}" {
                return true
            }
        }
        return false
    }
}

extension String {
    func removingMarkdown() -> String {
        // Regular expression patterns to match common markdown syntax
        let patterns = [
            #"!\[.*?\]\(.*?\)"#, // Image ![alt text](url)
            #"\[.*?\]\(.*?\)"#, // Links [text](url)
            #"```[\s\S]*?```"#, // Code blocks ```code``` (multi-line support)
            #"`.*?`"#, // Inline code `code`
            #"\*\*.*?\*\*"#, // Bold **
            #"\*.*?\*"#, // Italic *
            #"\_.*?\_"#, // Italic _
            #"\~\~.*?\~\~"#, // Strikethrough ~~
            #"#+ .*"#, // Headers #
            #"(?m)^- .*?$"#, // Lists -
            #"(?m)^\d+\. .*?$"#, // Ordered lists 1.
            #"(?m)^> .*?$"#, // Blockquotes >
        ]

        var cleanString = self

        for pattern in patterns {
            let regex = try? NSRegularExpression(pattern: pattern, options: [])
            let range = NSRange(location: 0, length: cleanString.utf16.count)
            cleanString = regex?.stringByReplacingMatches(in: cleanString, options: [], range: range, withTemplate: "") ?? cleanString
        }

        // Remove remaining Markdown syntax characters
        let remainingMarkdownChars = ["*", "_", "~", "#", "[", "]", "(", ")", "<", ">", "`", "-", "\n", "\\"]
        for char in remainingMarkdownChars {
            cleanString = cleanString.replacingOccurrences(of: char, with: "")
        }

        // Remove extra spaces and multiple new lines
        cleanString = cleanString.replacingOccurrences(of: "\n", with: " ")
        cleanString = cleanString.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression, range: nil)

        return cleanString.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

extension String {
    func localized(in language: String) -> String {
        // 构建指定语言的 bundle 路径
        guard let path = Bundle.main.path(forResource: language, ofType: "lproj"),
              let bundle = Bundle(path: path)
        else {
            return self
        }

        // 读取 Localizable.strings 文件中的本地化字符串
        let localizedString = NSLocalizedString(self, tableName: nil, bundle: bundle, value: self, comment: "")
        return localizedString
    }
}

extension String {
    func languageName() -> String {
        let locale = Locale(identifier: self)
        return locale.localizedString(forIdentifier: self) ?? self
    }
}



