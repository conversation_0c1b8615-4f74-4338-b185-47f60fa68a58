//
//  UserDefaultsExtension.swift
//  帮聊
//
//  Created by md on 2024/6/28.
//

import Foundation
import UIKit

extension UserDefaults {
    func set(_ color: UIColor?, forKey key: String) {
        guard let color else {
            removeObject(forKey: key)
            return
        }
        set(color.toData, forKey: key)
    }

    func color(forKey key: String) -> UIColor? {
        guard let data = data(forKey: key) else { return nil }
        return UIColor.from(data: data)
    }
}
