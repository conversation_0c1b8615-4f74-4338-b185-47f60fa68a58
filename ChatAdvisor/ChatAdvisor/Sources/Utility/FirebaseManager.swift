import Firebase
import FirebaseAnalytics

class FirebaseManager {
    private let processQueue = DispatchQueue(label: "com.firebaseManager.processQueue")
    static let shared = FirebaseManager()

    func logEvent(_ name: String, parameters: [String: Any]? = nil) {
        processQueue.async {
            Analytics.logEvent(name, parameters: parameters)
        }
    }

    func logUserSignup(userId: String) {
        logEvent("注册", parameters: [
            "用户id": userId,
        ])
    }

    func logUserLogin(userId: String) {
        logEvent("登录", parameters: [
            "用户id": userId,
        ])
    }

    func logPageView(pageName: String) {
        logEvent("浏览_\(pageName)")
    }

    func logPurchase(productId: String, price: String, currency: String) {
        logEvent("购买_\(price)", parameters: [
            "id": productId,
            "价格": price,
            "货币": currency,
        ])
    }

    func logSearch(query: String, isArchived: Bool) {
        logEvent("搜索", parameters: [
            "搜索词": query,
            "归档": isArchived,
        ])
    }

    func logChat(alias: String) {
        logEvent("聊天_\(alias)", parameters: [
            "模型名称": alias,
        ])
    }

    func logInputType(type: String) {
        logEvent("输入_\(type)", parameters: [
            "类型": type,
        ])
    }

    // 删除用户
    func logDeleteUser(userId: String) {
        logEvent("删除用户", parameters: [
            "id": userId,
        ])
    }

    // 用户点击事件
    func logUserClickEvent(event: String) {
        logEvent("点击_\(event)", parameters: [
            "事件": event,
        ])
    }
}
