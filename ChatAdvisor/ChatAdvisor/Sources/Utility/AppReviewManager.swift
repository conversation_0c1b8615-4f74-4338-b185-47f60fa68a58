//
//  AppReviewManager.swift
//  ChatAdvisor
//
//  Created by md on 2024/7/16.
//

import Foundation
import StoreKit

class AppReviewManager {
    static let shared = AppReviewManager()

    private init() {
        Preferences.launchCount.value += 1
    }

    func requestReviewIfAppropriate() {
        let launchCount = Preferences.launchCount.value
        if launchCount % 5 == 0 {
            requestReview()
        }
    }

    private func requestReview() {
        guard let windowScene = UIApplication.shared.windows.first?.windowScene else { return }
        SKStoreReviewController.requestReview(in: windowScene)
        // 重置打开次数
//        Preferences.launchCount.value = 0
    }
}
