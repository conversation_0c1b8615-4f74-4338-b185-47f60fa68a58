# TikTok和Facebook登录移除指南

## 概述

本指南描述了如何完全移除ChatAdvisor应用中的TikTok和Facebook登录功能。

## 已完成的代码修改

### ✅ 已移除的代码组件

1. **LoginView.swift**
   - 移除了TikTok和Facebook登录按钮
   - 移除了相关的import语句

2. **AuthViewModel.swift**
   - 移除了TikTok和Facebook相关的import
   - 移除了`loginManager`、`authRequest`等属性
   - 移除了`handleTikTokSignIn()`和`handleFacebookSignIn()`方法

3. **AuthTarget.swift**
   - 移除了`loginWithTikTok`和`loginWithFacebook`枚举案例
   - 移除了相关的路径和参数处理

4. **AppDelegate.swift**
   - 移除了TikTok和Facebook相关的import
   - 移除了Facebook SDK初始化
   - 移除了TikTok和Facebook的URL处理

5. **ChatAdvisorApp.swift**
   - 移除了TikTok相关的import
   - 移除了TikTok URL处理

6. **Info.plist**
   - 移除了Facebook配置（FacebookAppID、FacebookClientToken等）
   - 移除了TikTok配置（TikTokClientKey）
   - 移除了TikTok URL Scheme
   - 移除了TikTok查询schemes

7. **UIDeviceExtension.swift**
   - 添加了`isTikTokInstalled()`方法的空实现（始终返回false）

## 需要手动完成的步骤

### 🔧 在Xcode中移除SDK依赖

由于Swift Package Manager的依赖需要在Xcode中手动移除，请按照以下步骤操作：

#### 1. 移除TikTok SDK依赖

1. 在Xcode中打开项目
2. 选择项目根目录（ChatAdvisor）
3. 选择ChatAdvisor target
4. 点击"Package Dependencies"标签
5. 找到并选择"tiktok-opensdk-ios"包
6. 点击"-"按钮移除该依赖
7. 确认移除操作

#### 2. 移除Facebook SDK依赖

1. 在同一个"Package Dependencies"界面
2. 找到并选择"facebook-ios-sdk"包
3. 点击"-"按钮移除该依赖
4. 确认移除操作

#### 3. 清理构建设置

1. 选择ChatAdvisor target
2. 点击"Build Phases"标签
3. 展开"Link Binary With Libraries"
4. 确认没有残留的TikTok或Facebook框架引用
5. 如果有，选择并删除它们

#### 4. 验证移除结果

1. 清理项目：Product → Clean Build Folder
2. 重新构建项目：Product → Build
3. 确认没有编译错误
4. 运行应用并验证登录界面只显示Google和Twitter登录选项

## 服务器端清理（可选）

如果您也想清理服务器端的相关代码，可以考虑移除以下文件：

### 后端文件清理

1. **ChatAdvisorServer/src/business/tiktokLogin.ts** - TikTok登录逻辑
2. **ChatAdvisorServer/src/business/facebookLogin.ts** - Facebook登录逻辑
3. **ChatAdvisorServer/src/routers/tiktok.ts** - TikTok路由
4. **ChatAdvisorServer/src/config/appConfig.ts** - 移除TikTok和Facebook配置

### 数据库清理

考虑清理用户表中的TikTok和Facebook相关字段：
- `externalAccounts.tikTokInfo`
- `externalAccounts.facebookInfo`

## 验证清单

完成所有步骤后，请验证以下内容：

### ✅ 前端验证
- [ ] 登录界面不再显示TikTok和Facebook登录按钮
- [ ] 应用可以正常编译和运行
- [ ] Google和Twitter登录功能正常工作
- [ ] 没有与TikTok或Facebook相关的编译错误

### ✅ 配置验证
- [ ] Info.plist中没有TikTok和Facebook相关配置
- [ ] 项目依赖中没有TikTok和Facebook SDK
- [ ] URL Schemes中没有TikTok相关配置

### ✅ 代码验证
- [ ] 搜索代码库，确认没有残留的TikTok或Facebook相关代码
- [ ] 所有import语句都已清理
- [ ] 所有方法调用都已移除

## 注意事项

1. **用户数据**：如果有用户通过TikTok或Facebook登录创建的账户，需要考虑如何处理这些用户的后续登录问题。

2. **数据迁移**：可能需要为现有的TikTok/Facebook用户提供绑定其他登录方式的功能。

3. **测试**：在生产环境部署前，请充分测试所有登录流程。

4. **回滚计划**：建议在移除前创建代码备份，以防需要回滚。

## 故障排除

### 常见问题

**Q: 编译时出现"Module not found"错误**
A: 确保已经在Xcode中完全移除了TikTok和Facebook SDK依赖，并清理了构建文件夹。

**Q: 应用崩溃或出现运行时错误**
A: 检查是否有遗漏的方法调用或import语句，确保所有相关代码都已移除。

**Q: 登录界面布局异常**
A: 检查LoginView.swift中的HStack布局，确保移除按钮后的间距和布局正确。

## 总结

通过以上步骤，您已经成功移除了ChatAdvisor应用中的TikTok和Facebook登录功能。应用现在只支持Apple、Google、Twitter和邮箱登录方式。

如果在移除过程中遇到任何问题，请参考故障排除部分或联系开发团队获取支持。
