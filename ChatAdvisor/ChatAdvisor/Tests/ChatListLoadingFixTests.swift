//
//  ChatListLoadingFixTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025-01-01.
//

import XCTest
import Combine
@testable import ChatAdvisor

class ChatListLoadingFixTests: XCTestCase {
    var chatListViewModel: ChatListViewModel!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        chatListViewModel = ChatListViewModel()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        chatListViewModel = nil
        cancellables = nil
        super.tearDown()
    }
    
    func testDatabaseSetupCompletedNotificationTriggersRefresh() {
        // 测试数据库设置完成通知是否触发聊天列表刷新
        
        let expectation = XCTestExpectation(description: "Chat list should refresh after database setup completed notification")
        
        // 监听聊天列表的变化
        chatListViewModel.$isLoading
            .dropFirst() // 忽略初始值
            .sink { isLoading in
                if isLoading {
                    // 当开始加载时，满足期望
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // 发送数据库设置完成通知
        NotificationCenter.default.post(name: .databaseSetupCompleted, object: nil)
        
        // 等待期望被满足
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testErrorHandlingWhenDatabaseNotInitialized() {
        // 测试数据库未初始化时的错误处理
        
        let expectation = XCTestExpectation(description: "Should handle database not initialized error")
        
        // 监听错误状态
        chatListViewModel.$hasError
            .dropFirst()
            .sink { hasError in
                if hasError {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // 模拟数据库未初始化的情况
        // 注意：这需要在实际测试环境中进行，这里只是示例
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testRetryMechanism() {
        // 测试重试机制
        
        let expectation = XCTestExpectation(description: "Should retry on failure")
        
        // 监听加载状态变化
        var loadingStateChanges = 0
        chatListViewModel.$isLoading
            .sink { isLoading in
                if isLoading {
                    loadingStateChanges += 1
                    if loadingStateChanges >= 2 { // 至少重试一次
                        expectation.fulfill()
                    }
                }
            }
            .store(in: &cancellables)
        
        // 触发重试
        chatListViewModel.retryRefresh()
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testChatListViewModelInitialization() {
        // 测试ChatListViewModel的初始化

        XCTAssertNotNil(chatListViewModel)
        XCTAssertEqual(chatListViewModel.chats.count, 0)
        XCTAssertEqual(chatListViewModel.groupedChats.count, 0)
        XCTAssertFalse(chatListViewModel.isLoading)
        XCTAssertFalse(chatListViewModel.hasError)
        XCTAssertEqual(chatListViewModel.errorMessage, "")
        XCTAssertFalse(chatListViewModel.isInitialLoadCompleted)
    }

    func testPerformInitialLoad() {
        // 测试初始加载功能

        let expectation = XCTestExpectation(description: "Initial load should complete")

        // 监听初始加载完成状态
        chatListViewModel.$isInitialLoadCompleted
            .dropFirst()
            .sink { isCompleted in
                if isCompleted {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)

        // 执行初始加载
        chatListViewModel.performInitialLoad()

        wait(for: [expectation], timeout: 5.0)

        // 验证状态
        XCTAssertTrue(chatListViewModel.isInitialLoadCompleted)
    }

    func testPreventDuplicateInitialLoad() {
        // 测试防止重复初始加载

        var loadCallCount = 0

        // 监听加载状态变化
        chatListViewModel.$isLoading
            .sink { isLoading in
                if isLoading {
                    loadCallCount += 1
                }
            }
            .store(in: &cancellables)

        // 多次调用初始加载
        chatListViewModel.performInitialLoad()
        chatListViewModel.performInitialLoad()
        chatListViewModel.performInitialLoad()

        // 等待一段时间确保所有异步操作完成
        let expectation = XCTestExpectation(description: "Wait for async operations")
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 3.0)

        // 应该只有一次加载
        XCTAssertLessThanOrEqual(loadCallCount, 1, "应该防止重复的初始加载")
    }
    
    func testSearchFunctionality() {
        // 测试搜索功能
        
        let expectation = XCTestExpectation(description: "Search should complete")
        
        chatListViewModel.searchText = "test"
        
        // 监听搜索完成
        chatListViewModel.$chats
            .dropFirst()
            .sink { _ in
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        chatListViewModel.searchChats()
        
        wait(for: [expectation], timeout: 3.0)
    }
}

// MARK: - 集成测试辅助方法

extension ChatListLoadingFixTests {
    
    /// 模拟登录流程并验证会话列表加载
    func testLoginAndChatListLoading() {
        // 这个测试需要在实际环境中运行，因为它依赖于真实的数据库和网络
        
        let expectation = XCTestExpectation(description: "Login and chat list loading should work")
        
        // 监听数据库设置完成通知
        NotificationCenter.default.publisher(for: .databaseSetupCompleted)
            .sink { _ in
                // 数据库设置完成后，验证会话列表是否开始加载
                XCTAssertTrue(self.chatListViewModel.isLoading || !self.chatListViewModel.chats.isEmpty)
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // 模拟登录成功后的数据库设置
        // 注意：这需要有效的用户数据
        // AccountManager.shared.afterLogin()
        
        wait(for: [expectation], timeout: 10.0)
    }
    
    /// 验证Chat对象是否正确包含消息数据
    func testChatObjectContainsMessages() {
        // 这个测试验证修复后的fetchChats方法是否正确加载了消息
        
        let expectation = XCTestExpectation(description: "Chat objects should contain messages")
        
        Task {
            // 假设数据库中有数据
            let chats = await AdvisorDatabaseManager.shared.fetchChats(isArchived: false)
            
            DispatchQueue.main.async {
                // 验证Chat对象包含消息（如果数据库中有数据的话）
                for chat in chats {
                    // 如果Chat有ID，那么它应该有对应的消息数据结构
                    // 即使消息数组为空，也应该是一个有效的数组而不是nil
                    XCTAssertNotNil(chat.messages)
                }
                
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
}
