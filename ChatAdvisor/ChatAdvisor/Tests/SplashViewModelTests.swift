//
//  SplashViewModelTests.swift
//  ChatAdvisorTests
//
//  Created by AI Assistant on 2025/01/29.
//

import XCTest
@testable import ChatAdvisor

@MainActor
final class SplashViewModelTests: XCTestCase {
    
    var splashViewModel: SplashViewModel!
    
    override func setUp() {
        super.setUp()
        splashViewModel = SplashViewModel()
    }
    
    override func tearDown() {
        splashViewModel = nil
        super.tearDown()
    }
    
    // MARK: - 初始化测试
    
    func testInitialState() {
        XCTAssertEqual(splashViewModel.progress, 0.0, "初始进度应该为0")
        XCTAssertEqual(splashViewModel.statusText, "正在启动...", "初始状态文本应该正确")
        XCTAssertEqual(splashViewModel.logoScale, 1.2, "Logo缩放应该已设置")
        XCTAssertEqual(splashViewModel.textOpacity, 1.0, "文本透明度应该已设置")
        XCTAssertFalse(splashViewModel.showForceUpdate, "初始状态不应该显示强制升级")
        XCTAssertNil(splashViewModel.forceUpdateInfo, "初始状态强制升级信息应该为空")
    }
    
    // MARK: - 初始化流程测试
    
    func testInitializationSteps() {
        let expectation = XCTestExpectation(description: "初始化完成")
        
        splashViewModel.startInitialization { success in
            XCTAssertTrue(success, "初始化应该成功")
            XCTAssertEqual(self.splashViewModel.progress, 1.0, "进度应该达到100%")
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 30.0) // 增加超时时间，因为包含网络请求
    }
    
    // MARK: - 强制升级处理测试
    
    func testForceUpdateHandling() {
        // 模拟强制升级情况
        let versionControl = VersionControl(
            needUpdate: true,
            updateType: .force,
            latestVersion: "2.1.0",
            minimumVersion: "2.0.0",
            updateMessage: "强制升级测试",
            downloadUrl: "https://apps.apple.com/us/app/chat-advisor/id6526465428",
            versionCheckEnabled: true
        )
        
        splashViewModel.forceUpdateInfo = versionControl
        splashViewModel.showForceUpdate = true
        
        XCTAssertTrue(splashViewModel.showForceUpdate, "应该显示强制升级弹窗")
        XCTAssertNotNil(splashViewModel.forceUpdateInfo, "强制升级信息应该存在")
        XCTAssertEqual(splashViewModel.forceUpdateInfo?.latestVersion, "2.1.0", "版本信息应该正确")
    }
    
    // MARK: - 性能测试
    
    func testInitializationPerformance() {
        measure {
            let expectation = XCTestExpectation(description: "性能测试")
            
            splashViewModel.startInitialization { _ in
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 30.0)
        }
    }
}
