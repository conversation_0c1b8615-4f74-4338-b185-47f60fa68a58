//
//  RegionDetectorTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/1/29.
//

import XCTest
@testable import ChatAdvisor

class RegionDetectorTests: XCTestCase {

    var regionDetector: RegionDetector!

    override func setUp() {
        super.setUp()
        regionDetector = RegionDetector.shared
        // 清除缓存以确保测试的独立性
        regionDetector.clearCache()
    }

    override func tearDown() {
        regionDetector.clearCache()
        super.tearDown()
    }

    // MARK: - 智能端点选择测试

    func testIntelligentEndpointSelection() async {
        let result = await regionDetector.detectOptimalEndpoint(forceRefresh: true)

        // 验证结果的基本属性
        XCTAssertTrue(result.confidence >= 0.0 && result.confidence <= 1.0, "置信度应该在 0-1 之间")
        XCTAssertFalse(result.sources.isEmpty, "应该有检测来源")
        XCTAssertNotNil(result.timestamp, "应该有时间戳")
        XCTAssertFalse(result.selectedEndpoint.isEmpty, "应该有选择的端点")

        // 验证端点选择的有效性
        XCTAssertTrue(result.selectedEndpoint == "sanva" || result.selectedEndpoint == "sanvaCn", "选择的端点应该是有效的")

        // 验证 isChina 与选择的端点一致
        if result.selectedEndpoint == "sanvaCn" {
            XCTAssertTrue(result.isChina, "选择 sanvaCn 时 isChina 应该为 true")
        } else {
            XCTAssertFalse(result.isChina, "选择 sanva 时 isChina 应该为 false")
        }

        // 验证检测来源包含预期的类型
        let expectedSources = ["地理位置检测", "网络性能测试", "综合决策算法"]
        let hasExpectedSource = expectedSources.contains { sourceType in
            result.sources.contains(sourceType)
        }
        XCTAssertTrue(hasExpectedSource, "应该包含预期的检测来源类型")
    }
    
    // MARK: - 缓存测试

    func testCaching() async {
        // 第一次检测
        let firstResult = await regionDetector.detectOptimalEndpoint(forceRefresh: true)

        // 第二次检测（应该使用缓存）
        let secondResult = await regionDetector.detectOptimalEndpoint(forceRefresh: false)

        // 验证缓存是否生效
        XCTAssertEqual(firstResult.isChina, secondResult.isChina, "缓存的结果应该一致")
        XCTAssertEqual(firstResult.selectedEndpoint, secondResult.selectedEndpoint, "缓存的端点选择应该一致")

        // 验证缓存来源
        if secondResult.sources.contains("缓存结果") {
            XCTAssertTrue(true, "第二次检测应该使用缓存")
        } else {
            // 如果没有使用缓存，可能是因为缓存时间太短或其他原因
            print("警告: 第二次检测没有使用缓存，可能是正常情况")
        }
    }

    // MARK: - 强制刷新测试

    func testForceRefresh() async {
        // 第一次检测
        let firstResult = await regionDetector.detectOptimalEndpoint(forceRefresh: true)

        // 强制刷新检测
        let refreshedResult = await regionDetector.detectOptimalEndpoint(forceRefresh: true)

        // 验证强制刷新不使用缓存
        XCTAssertFalse(refreshedResult.sources.contains("缓存结果"), "强制刷新不应该使用缓存")

        // 结果应该一致（除非网络环境发生变化）
        XCTAssertEqual(firstResult.selectedEndpoint, refreshedResult.selectedEndpoint, "在相同环境下，端点选择应该一致")
    }

    // MARK: - 网络性能测试

    func testNetworkPerformance() async {
        let result = await regionDetector.detectOptimalEndpoint(forceRefresh: true)

        // 验证有测速结果
        XCTAssertFalse(result.speedResults.isEmpty, "应该有网络测速结果")

        // 验证测速结果的有效性
        for speedResult in result.speedResults {
            XCTAssertFalse(speedResult.endpoint.isEmpty, "端点名称不应该为空")
            XCTAssertTrue(speedResult.responseTime >= 0, "响应时间应该大于等于0")
            XCTAssertNotNil(speedResult.timestamp, "应该有时间戳")
        }
    }
    
    // MARK: - 性能测试

    func testDetectionPerformance() {
        measure {
            let expectation = XCTestExpectation(description: "Endpoint selection performance")

            Task {
                _ = await regionDetector.detectOptimalEndpoint()
                expectation.fulfill()
            }

            wait(for: [expectation], timeout: 15.0) // 增加超时时间，因为包含网络测试
        }
    }

    // MARK: - 并发测试

    func testConcurrentDetection() async {
        let expectation = XCTestExpectation(description: "Concurrent detection")
        expectation.expectedFulfillmentCount = 3

        // 同时启动多个检测任务
        Task {
            _ = await regionDetector.detectOptimalEndpoint()
            expectation.fulfill()
        }

        Task {
            _ = await regionDetector.detectOptimalEndpoint()
            expectation.fulfill()
        }

        Task {
            _ = await regionDetector.detectOptimalEndpoint()
            expectation.fulfill()
        }

        await fulfillment(of: [expectation], timeout: 20.0)
    }

    // MARK: - 集成测试

    func testBootManagerIntegration() async {
        // 测试与 BootManager 的集成
        let bootManager = BootManager.shared
        let initialValue = bootManager.isChina

        // 触发 BootManager 的端点重新选择
        bootManager.refreshRegionDetection(forceRefresh: true)

        // 等待异步更新完成
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒，等待网络测试完成

        // 验证 BootManager 有有效的值
        XCTAssertNotNil(bootManager.isChina, "BootManager 应该有有效的 isChina 值")

        // 验证当前结果存在
        XCTAssertNotNil(regionDetector.currentResult, "应该有当前的检测结果")
    }

    // MARK: - 手动刷新测试

    func testManualRefresh() async {
        // 执行手动刷新
        await regionDetector.refreshDetection()

        // 验证有结果
        XCTAssertNotNil(regionDetector.currentResult, "手动刷新后应该有结果")

        let result = regionDetector.currentResult!
        XCTAssertFalse(result.selectedEndpoint.isEmpty, "应该有选择的端点")
        XCTAssertTrue(result.confidence >= 0.0 && result.confidence <= 1.0, "置信度应该在有效范围内")
    }
}
