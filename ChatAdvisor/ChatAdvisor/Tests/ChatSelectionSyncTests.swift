//
//  ChatSelectionSyncTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025-01-01.
//

import XCTest
import Combine
@testable import ChatAdvisor

class ChatSelectionSyncTests: XCTestCase {
    var contentViewModel: ContentViewModel!
    var chatListViewModel: ChatListViewModel!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        contentViewModel = ContentViewModel()
        chatListViewModel = ChatListViewModel()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        contentViewModel = nil
        chatListViewModel = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - 状态同步测试
    
    func testInitialStateIsNil() {
        // 验证初始状态，避免闪烁
        XCTAssertNil(contentViewModel.selectedChatID, "初始selectedChatID应该为nil，避免闪烁")
        XCTAssertTrue(contentViewModel.chatViewModels.isEmpty, "初始chatViewModels应该为空")
    }
    
    func testAtomicChatSelection() async {
        // 测试原子化的会话选择
        
        let testChat = Chat(id: "test-chat-1", messages: [
            ChatMessage(id: "msg1", role: .user, content: "Hello", chatId: "test-chat-1"),
            ChatMessage(id: "msg2", role: .assistant, content: "Hi there!", chatId: "test-chat-1")
        ])
        
        // 执行原子化选择
        await contentViewModel.selectChatAtomically(chat: testChat, chatListViewModel: chatListViewModel)
        
        // 验证状态一致性
        XCTAssertEqual(contentViewModel.selectedChatID, testChat.id, "selectedChatID应该正确设置")
        XCTAssertNotNil(contentViewModel.currentChatViewModel, "currentChatViewModel应该存在")
        XCTAssertEqual(contentViewModel.currentChatViewModel?.currentChat.id, testChat.id, "currentChatViewModel应该对应正确的会话")
    }
    
    func testCurrentChatViewModelConsistency() async {
        // 测试currentChatViewModel的一致性
        
        let testChat1 = Chat(id: "test-chat-1", messages: [])
        let testChat2 = Chat(id: "test-chat-2", messages: [])
        
        // 选择第一个会话
        await contentViewModel.selectChatAtomically(chat: testChat1, chatListViewModel: chatListViewModel)
        
        let firstViewModel = contentViewModel.currentChatViewModel
        XCTAssertNotNil(firstViewModel, "第一个ChatViewModel应该存在")
        XCTAssertEqual(firstViewModel?.currentChat.id, testChat1.id, "第一个ChatViewModel应该对应正确的会话")
        
        // 选择第二个会话
        await contentViewModel.selectChatAtomically(chat: testChat2, chatListViewModel: chatListViewModel)
        
        let secondViewModel = contentViewModel.currentChatViewModel
        XCTAssertNotNil(secondViewModel, "第二个ChatViewModel应该存在")
        XCTAssertEqual(secondViewModel?.currentChat.id, testChat2.id, "第二个ChatViewModel应该对应正确的会话")
        XCTAssertNotEqual(firstViewModel?.currentChat.id, secondViewModel?.currentChat.id, "两个ChatViewModel应该不同")
    }
    
    func testChatViewModelCaching() async {
        // 测试ChatViewModel缓存机制
        
        let testChat = Chat(id: "test-chat-1", messages: [])
        
        // 第一次选择
        await contentViewModel.selectChatAtomically(chat: testChat, chatListViewModel: chatListViewModel)
        let firstViewModel = contentViewModel.currentChatViewModel
        
        // 第二次选择同一个会话
        await contentViewModel.selectChatAtomically(chat: testChat, chatListViewModel: chatListViewModel)
        let secondViewModel = contentViewModel.currentChatViewModel
        
        // 验证是同一个实例（缓存生效）
        XCTAssertTrue(firstViewModel === secondViewModel, "相同会话应该返回缓存的ChatViewModel")
    }
    
    // MARK: - 消息加载测试
    
    func testMessageLoadingAfterSelection() async {
        // 测试选择会话后消息加载
        
        let testChat = Chat(id: "test-chat-1", messages: [])
        
        await contentViewModel.selectChatAtomically(chat: testChat, chatListViewModel: chatListViewModel)
        
        // 验证ChatViewModel存在
        guard let chatViewModel = contentViewModel.currentChatViewModel else {
            XCTFail("ChatViewModel应该存在")
            return
        }
        
        // 验证会话设置正确
        XCTAssertEqual(chatViewModel.currentChat.id, testChat.id, "ChatViewModel应该设置了正确的会话")
    }
    
    func testMessagePreservationOnReselection() async {
        // 测试重新选择时消息保留
        
        let messages = [
            ChatMessage(id: "msg1", role: .user, content: "Hello", chatId: "test-chat-1"),
            ChatMessage(id: "msg2", role: .assistant, content: "Hi!", chatId: "test-chat-1")
        ]
        let testChat = Chat(id: "test-chat-1", messages: messages)
        
        // 第一次选择
        await contentViewModel.selectChatAtomically(chat: testChat, chatListViewModel: chatListViewModel)
        
        // 验证消息存在
        guard let firstViewModel = contentViewModel.currentChatViewModel else {
            XCTFail("第一个ChatViewModel应该存在")
            return
        }
        
        let firstMessages = firstViewModel.currentChat.messages
        XCTAssertEqual(firstMessages.count, messages.count, "消息数量应该正确")
        
        // 选择其他会话再回来
        let otherChat = Chat(id: "other-chat", messages: [])
        await contentViewModel.selectChatAtomically(chat: otherChat, chatListViewModel: chatListViewModel)
        await contentViewModel.selectChatAtomically(chat: testChat, chatListViewModel: chatListViewModel)
        
        // 验证消息仍然存在
        guard let secondViewModel = contentViewModel.currentChatViewModel else {
            XCTFail("第二个ChatViewModel应该存在")
            return
        }
        
        let secondMessages = secondViewModel.currentChat.messages
        XCTAssertEqual(secondMessages.count, firstMessages.count, "重新选择后消息应该保留")
    }
    
    // MARK: - 边界情况测试
    
    func testEmptyChatHandling() async {
        // 测试空会话处理
        
        let emptyChat = Chat(id: "empty-chat", messages: [])
        
        await contentViewModel.selectChatAtomically(chat: emptyChat, chatListViewModel: chatListViewModel)
        
        XCTAssertEqual(contentViewModel.selectedChatID, emptyChat.id, "空会话也应该能正确选择")
        XCTAssertNotNil(contentViewModel.currentChatViewModel, "空会话也应该有对应的ChatViewModel")
    }
    
    func testInitializeIfNeededBehavior() {
        // 测试initializeIfNeeded的行为
        
        // 第一次调用应该初始化
        contentViewModel.initializeIfNeeded(chatListViewModel: chatListViewModel)
        
        // 等待异步操作
        let expectation = XCTestExpectation(description: "初始化完成")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 2.0)
        
        // 记录初始化后的状态
        let initialChatViewModelsCount = contentViewModel.chatViewModels.count
        let initialSelectedChatID = contentViewModel.selectedChatID
        
        // 第二次调用不应该重复初始化
        contentViewModel.initializeIfNeeded(chatListViewModel: chatListViewModel)
        
        XCTAssertEqual(contentViewModel.chatViewModels.count, initialChatViewModelsCount, "不应该重复初始化")
        XCTAssertEqual(contentViewModel.selectedChatID, initialSelectedChatID, "selectedChatID不应该改变")
    }
    
    // MARK: - 性能测试
    
    func testRapidChatSwitching() async {
        // 测试快速切换会话的性能
        
        let chats = (1...5).map { i in
            Chat(id: "chat-\(i)", messages: [
                ChatMessage(id: "msg-\(i)", role: .user, content: "Message \(i)", chatId: "chat-\(i)")
            ])
        }
        
        // 快速切换多个会话
        for chat in chats {
            await contentViewModel.selectChatAtomically(chat: chat, chatListViewModel: chatListViewModel)
            
            // 验证每次切换都正确
            XCTAssertEqual(contentViewModel.selectedChatID, chat.id, "快速切换时selectedChatID应该正确")
            XCTAssertNotNil(contentViewModel.currentChatViewModel, "快速切换时currentChatViewModel应该存在")
        }
        
        // 验证最终状态
        let lastChat = chats.last!
        XCTAssertEqual(contentViewModel.selectedChatID, lastChat.id, "最终应该选中最后一个会话")
    }
    
    // MARK: - 集成测试
    
    func testCompleteUserFlow() async {
        // 测试完整的用户流程
        
        // 1. 应用启动（初始化）
        contentViewModel.initializeIfNeeded(chatListViewModel: chatListViewModel)
        
        // 等待初始化完成
        let initExpectation = XCTestExpectation(description: "初始化完成")
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            initExpectation.fulfill()
        }
        wait(for: [initExpectation], timeout: 2.0)
        
        // 2. 用户选择会话
        let userChat = Chat(id: "user-selected-chat", messages: [
            ChatMessage(id: "msg1", role: .user, content: "User message", chatId: "user-selected-chat")
        ])
        
        await contentViewModel.selectChatAtomically(chat: userChat, chatListViewModel: chatListViewModel)
        
        // 3. 验证最终状态
        XCTAssertEqual(contentViewModel.selectedChatID, userChat.id, "用户选择的会话应该被正确设置")
        XCTAssertNotNil(contentViewModel.currentChatViewModel, "应该有对应的ChatViewModel")
        XCTAssertEqual(contentViewModel.currentChatViewModel?.currentChat.id, userChat.id, "ChatViewModel应该对应正确的会话")
    }
}
