//
//  ChatListInteractionTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025-01-01.
//

import XCTest
import Combine
@testable import ChatAdvisor

class ChatListInteractionTests: XCTestCase {
    var chatListViewModel: ChatListViewModel!
    var contentViewModel: ContentViewModel!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        chatListViewModel = ChatListViewModel()
        contentViewModel = ContentViewModel()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        chatListViewModel = nil
        contentViewModel = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - 分页功能测试
    
    func testPaginationStates() {
        // 测试分页状态管理
        
        XCTAssertTrue(chatListViewModel.hasMoreData, "初始状态应该有更多数据")
        XCTAssertFalse(chatListViewModel.isLoadingMore, "初始状态不应该在加载")
        XCTAssertFalse(chatListViewModel.showNoMoreDataMessage, "初始状态不应该显示无更多数据消息")
    }
    
    func testFetchMoreChatsWithNoData() {
        // 测试没有更多数据时的状态
        
        let expectation = XCTestExpectation(description: "Should handle no more data correctly")
        
        // 监听状态变化
        chatListViewModel.$hasMoreData
            .dropFirst()
            .sink { hasMoreData in
                if !hasMoreData {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // 模拟获取空数据
        Task {
            await chatListViewModel.fetchMoreChatsWithErrorHandling()
        }
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testPaginationLoadingStates() {
        // 测试分页加载状态
        
        let expectation = XCTestExpectation(description: "Should manage loading states correctly")
        
        var stateChanges: [Bool] = []
        chatListViewModel.$isLoadingMore
            .sink { isLoading in
                stateChanges.append(isLoading)
                if stateChanges.count >= 2 {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        chatListViewModel.fetchMoreChats()
        
        wait(for: [expectation], timeout: 3.0)
        
        // 验证状态变化：false -> true -> false
        XCTAssertEqual(stateChanges.first, false, "初始状态应该是false")
    }
    
    // MARK: - 会话选中状态测试
    
    func testChatSelection() {
        // 测试会话选中逻辑
        
        let testChatID = "test-chat-id"
        
        contentViewModel.selectChat(testChatID)
        
        XCTAssertEqual(contentViewModel.selectedChatID, testChatID, "选中的会话ID应该正确设置")
    }
    
    func testChatViewModelCaching() {
        // 测试ChatViewModel缓存机制
        
        let testChat = Chat(id: "test-chat", messages: [])
        
        let viewModel1 = contentViewModel.getOrCreateChatViewModel(for: testChat, chatListViewModel: chatListViewModel)
        let viewModel2 = contentViewModel.getOrCreateChatViewModel(for: testChat, chatListViewModel: chatListViewModel)
        
        XCTAssertTrue(viewModel1 === viewModel2, "相同会话应该返回缓存的ChatViewModel")
    }
    
    func testChatViewModelCacheLimit() {
        // 测试ChatViewModel缓存限制
        
        // 创建超过限制数量的ChatViewModel
        for i in 0..<10 {
            let chat = Chat(id: "chat-\(i)", messages: [])
            _ = contentViewModel.getOrCreateChatViewModel(for: chat, chatListViewModel: chatListViewModel)
        }
        
        XCTAssertLesssThanOrEqual(contentViewModel.chatViewModels.count, 5, "ChatViewModel数量应该不超过缓存限制")
    }
    
    // MARK: - 错误处理测试
    
    func testErrorHandling() {
        // 测试错误处理
        
        let expectation = XCTestExpectation(description: "Should handle errors correctly")
        
        chatListViewModel.$hasError
            .dropFirst()
            .sink { hasError in
                if hasError {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // 模拟错误情况（数据库未初始化）
        // 注意：这需要在实际测试环境中进行
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testRetryMechanism() {
        // 测试重试机制
        
        chatListViewModel.hasError = true
        chatListViewModel.errorMessage = "测试错误"
        
        chatListViewModel.retryRefresh()
        
        // 验证错误状态被重置
        XCTAssertFalse(chatListViewModel.hasError, "重试后错误状态应该被重置")
        XCTAssertEqual(chatListViewModel.errorMessage, "", "重试后错误消息应该被清空")
    }
    
    // MARK: - 性能测试
    
    func testDebounceSelection() {
        // 测试防抖机制
        
        let expectation = XCTestExpectation(description: "Should debounce rapid selections")
        expectation.expectedFulfillmentCount = 1
        
        contentViewModel.$selectedChatID
            .dropFirst()
            .sink { _ in
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // 快速连续选择多个会话
        contentViewModel.selectChat("chat-1")
        contentViewModel.selectChat("chat-2")
        contentViewModel.selectChat("chat-3")
        
        wait(for: [expectation], timeout: 1.0)
        
        // 应该只有最后一次选择生效
        XCTAssertEqual(contentViewModel.selectedChatID, "chat-3")
    }
    
    // MARK: - UI状态测试
    
    func testLoadingStates() {
        // 测试加载状态
        
        XCTAssertFalse(contentViewModel.isLoadingChatDetails, "初始状态不应该在加载")
        
        contentViewModel.isLoadingChatDetails = true
        XCTAssertTrue(contentViewModel.isLoadingChatDetails, "应该能正确设置加载状态")
        
        contentViewModel.isLoadingChatDetails = false
        XCTAssertFalse(contentViewModel.isLoadingChatDetails, "应该能正确重置加载状态")
    }
    
    func testErrorMessageHandling() {
        // 测试错误消息处理
        
        let testErrorMessage = "测试错误消息"
        
        contentViewModel.chatLoadingError = testErrorMessage
        XCTAssertEqual(contentViewModel.chatLoadingError, testErrorMessage, "错误消息应该正确设置")
        
        contentViewModel.chatLoadingError = nil
        XCTAssertNil(contentViewModel.chatLoadingError, "错误消息应该能被清除")
    }
    
    // MARK: - 集成测试
    
    func testCompleteUserFlow() {
        // 测试完整的用户交互流程
        
        let expectation = XCTestExpectation(description: "Complete user flow should work")
        
        // 1. 刷新会话列表
        chatListViewModel.refreshChats()
        
        // 2. 等待加载完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // 3. 选择一个会话
            if let firstChat = self.chatListViewModel.chats.first {
                let chatViewModel = self.contentViewModel.getOrCreateChatViewModel(for: firstChat, chatListViewModel: self.chatListViewModel)
                self.contentViewModel.selectChat(firstChat.id)
                
                // 4. 验证状态
                XCTAssertEqual(self.contentViewModel.selectedChatID, firstChat.id)
                XCTAssertNotNil(self.contentViewModel.currentChatViewModel)
            }
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
}
