//
//  SSEStreamingOptimizationTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025-01-03.
//

import XCTest
import Combine
@testable import ChatAdvisor

class SSEStreamingOptimizationTests: XCTestCase {
    var typewriterManager: TypewriterEffectManager!
    var chatViewModel: ChatViewModel!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        typewriterManager = TypewriterEffectManager()
        chatViewModel = ChatViewModel(chatListViewModel: nil)
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        typewriterManager = nil
        chatViewModel = nil
        cancellables = nil
        super.tearDown()
    }
    
    // MARK: - TypewriterEffectManager Tests
    
    func testTypewriterEffectBasicFunctionality() {
        let expectation = XCTestExpectation(description: "Typewriter effect should display content progressively")
        
        let testContent = "Hello, World!"
        
        // 监听显示内容变化
        typewriterManager.$displayedContent
            .dropFirst() // 忽略初始空值
            .sink { displayedContent in
                if displayedContent == testContent {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // 设置内容
        typewriterManager.setContent(testContent)
        
        wait(for: [expectation], timeout: 5.0)
        
        // 验证最终状态
        XCTAssertEqual(typewriterManager.displayedContent, testContent)
        XCTAssertEqual(typewriterManager.state, .completed)
        XCTAssertEqual(typewriterManager.progress, 1.0)
    }
    
    func testTypewriterEffectAppendContent() {
        let expectation = XCTestExpectation(description: "Typewriter should handle appended content")
        
        let firstContent = "Hello"
        let secondContent = ", World!"
        let fullContent = firstContent + secondContent
        
        // 监听状态变化
        typewriterManager.$state
            .sink { state in
                if state == .completed {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        // 先设置第一部分内容
        typewriterManager.setContent(firstContent)
        
        // 延迟添加第二部分内容
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.typewriterManager.appendContent(secondContent)
        }
        
        wait(for: [expectation], timeout: 10.0)
        
        // 验证最终内容
        XCTAssertEqual(typewriterManager.displayedContent, fullContent)
    }
    
    func testTypewriterEffectPauseAndResume() {
        let testContent = "This is a test for pause and resume functionality."
        
        typewriterManager.setContent(testContent)
        
        // 等待开始打字
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.typewriterManager.pauseTyping()
            XCTAssertEqual(self.typewriterManager.state, .paused)
        }
        
        // 恢复打字
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.typewriterManager.resumeTyping()
            XCTAssertEqual(self.typewriterManager.state, .typing)
        }
        
        let expectation = XCTestExpectation(description: "Typewriter should complete after resume")
        
        typewriterManager.$state
            .sink { state in
                if state == .completed {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 10.0)
    }
    
    func testTypewriterEffectCompleteImmediately() {
        let testContent = "This content should be displayed immediately."
        
        typewriterManager.setContent(testContent)
        
        // 立即完成
        typewriterManager.completeImmediately()
        
        XCTAssertEqual(typewriterManager.displayedContent, testContent)
        XCTAssertEqual(typewriterManager.state, .completed)
        XCTAssertEqual(typewriterManager.progress, 1.0)
    }
    
    func testTypewriterEffectReset() {
        let testContent = "Test content for reset"
        
        typewriterManager.setContent(testContent)
        typewriterManager.reset()
        
        XCTAssertEqual(typewriterManager.displayedContent, "")
        XCTAssertEqual(typewriterManager.state, .idle)
        XCTAssertEqual(typewriterManager.progress, 0.0)
    }
    
    // MARK: - ChatViewModel Integration Tests
    
    func testChatViewModelStreamingMessageDisplayContent() {
        // 创建测试消息
        let testMessage = ChatMessage(
            id: "test-message",
            chatId: "test-chat",
            role: .assistant,
            content: "Test streaming content",
            isComplete: false
        )
        
        // 添加到当前聊天
        chatViewModel.currentChat.messages.append(testMessage)
        
        // 模拟设置当前流式消息ID
        chatViewModel.currentLocalResponseId = "test-message"
        
        // 测试获取显示内容
        let displayContent = chatViewModel.getStreamingMessageDisplayContent(for: "test-message")
        
        // 由于打字机效果刚开始，显示内容应该为空或部分内容
        XCTAssertTrue(displayContent.count <= testMessage.content.count)
    }
    
    // MARK: - Performance Tests
    
    func testTypewriterEffectPerformanceWithLargeContent() {
        let largeContent = String(repeating: "This is a performance test content. ", count: 100)
        
        measure {
            typewriterManager.setContent(largeContent)
            typewriterManager.completeImmediately()
        }
        
        XCTAssertEqual(typewriterManager.displayedContent, largeContent)
    }
    
    func testMultipleContentAppendPerformance() {
        let baseContent = "Base content"
        let appendContent = " appended"
        
        typewriterManager.setContent(baseContent)
        
        measure {
            for _ in 0..<50 {
                typewriterManager.appendContent(appendContent)
            }
        }
        
        // 验证内容正确性
        let expectedContent = baseContent + String(repeating: appendContent, count: 50)
        typewriterManager.completeImmediately()
        XCTAssertEqual(typewriterManager.displayedContent, expectedContent)
    }
    
    // MARK: - Edge Cases Tests
    
    func testTypewriterEffectWithEmptyContent() {
        typewriterManager.setContent("")
        
        XCTAssertEqual(typewriterManager.displayedContent, "")
        XCTAssertEqual(typewriterManager.state, .idle)
    }
    
    func testTypewriterEffectWithSpecialCharacters() {
        let specialContent = "Hello! 你好？ 🌟 \n\t Special chars: @#$%^&*()"
        
        let expectation = XCTestExpectation(description: "Should handle special characters correctly")
        
        typewriterManager.$state
            .sink { state in
                if state == .completed {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)
        
        typewriterManager.setContent(specialContent)
        
        wait(for: [expectation], timeout: 10.0)
        
        XCTAssertEqual(typewriterManager.displayedContent, specialContent)
    }
    
    func testConcurrentContentAppend() {
        let baseContent = "Base"
        typewriterManager.setContent(baseContent)
        
        // 并发添加内容
        let queue = DispatchQueue(label: "test.concurrent", attributes: .concurrent)
        let group = DispatchGroup()
        
        for i in 0..<10 {
            group.enter()
            queue.async {
                self.typewriterManager.appendContent(" \(i)")
                group.leave()
            }
        }
        
        let expectation = XCTestExpectation(description: "Concurrent append should complete")
        group.notify(queue: .main) {
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
        
        // 验证所有内容都被添加（顺序可能不同）
        typewriterManager.completeImmediately()
        let finalContent = typewriterManager.displayedContent
        XCTAssertTrue(finalContent.contains("Base"))
        
        // 验证所有数字都存在
        for i in 0..<10 {
            XCTAssertTrue(finalContent.contains("\(i)"))
        }
    }
    
    // MARK: - Memory Tests
    
    func testMemoryUsageWithLargeContent() {
        // 测试大量内容的内存使用
        let largeContent = String(repeating: "Large content for memory test. ", count: 1000)
        
        autoreleasepool {
            typewriterManager.setContent(largeContent)
            typewriterManager.completeImmediately()
            typewriterManager.reset()
        }
        
        // 验证重置后状态正确
        XCTAssertEqual(typewriterManager.displayedContent, "")
        XCTAssertEqual(typewriterManager.state, .idle)
    }
}
