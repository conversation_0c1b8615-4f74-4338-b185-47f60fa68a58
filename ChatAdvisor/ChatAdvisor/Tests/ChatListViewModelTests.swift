//
//  ChatListViewModelTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/07/01.
//

import XCTest
import Combine
@testable import ChatAdvisor

class ChatListViewModelTests: XCTestCase {
    
    var chatListViewModel: ChatListViewModel!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        chatListViewModel = ChatListViewModel()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        chatListViewModel = nil
        cancellables?.forEach { $0.cancel() }
        cancellables = nil
        super.tearDown()
    }
    
    func testLoginSuccessNotificationTriggersRefresh() {
        // 测试登录成功通知是否触发聊天列表刷新
        
        // 创建期望
        let expectation = XCTestExpectation(description: "Chat list should refresh after login success notification")
        
        // 监听聊天列表的变化
        chatListViewModel.$chats
            .dropFirst() // 忽略初始值
            .sink { chats in
                // 当聊天列表发生变化时，满足期望
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        // 发送登录成功通知
        NotificationCenter.default.post(name: .loginSuccess, object: nil)
        
        // 等待期望被满足
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testLogoutNotificationClearsChats() {
        // 测试登出通知是否清空聊天列表
        
        // 先模拟一些聊天数据
        let mockChat = Chat(id: "test-chat", messages: [
            ChatMessage(id: "msg1", chatId: "test-chat", role: .user, content: "Test", isComplete: true)
        ])
        chatListViewModel.chats = [mockChat]
        chatListViewModel.groupedChats = [Date(): [mockChat]]
        
        // 验证初始状态
        XCTAssertFalse(chatListViewModel.chats.isEmpty, "初始状态应该有聊天数据")
        XCTAssertFalse(chatListViewModel.groupedChats.isEmpty, "初始状态应该有分组聊天数据")
        
        // 发送登出通知
        NotificationCenter.default.post(name: .logout, object: nil)
        
        // 验证聊天列表被清空
        XCTAssertTrue(chatListViewModel.chats.isEmpty, "登出后聊天列表应该被清空")
        XCTAssertTrue(chatListViewModel.groupedChats.isEmpty, "登出后分组聊天列表应该被清空")
    }
    
    func testUpdateMemoryChatWithUserMessages() {
        // 测试更新包含用户消息的聊天会话
        
        let chatWithUserMessage = Chat(id: "test-chat", messages: [
            ChatMessage(id: "msg1", chatId: "test-chat", role: .user, content: "User message", isComplete: true),
            ChatMessage(id: "msg2", chatId: "test-chat", role: .assistant, content: "Assistant response", isComplete: true)
        ])
        
        // 更新聊天会话
        chatListViewModel.updateMemoryChat(newChat: chatWithUserMessage)
        
        // 验证聊天会话被添加到列表中
        XCTAssertEqual(chatListViewModel.chats.count, 1, "应该有一个聊天会话")
        XCTAssertEqual(chatListViewModel.chats.first?.id, chatWithUserMessage.id, "聊天会话ID应该正确")
        XCTAssertFalse(chatListViewModel.groupedChats.isEmpty, "分组聊天列表不应该为空")
    }
    
    func testUpdateMemoryChatWithoutUserMessages() {
        // 测试更新不包含用户消息的聊天会话（应该被过滤掉）
        
        let chatWithoutUserMessage = Chat(id: "test-chat", messages: [
            ChatMessage(id: "msg1", chatId: "test-chat", role: .system, content: "System message", isComplete: true),
            ChatMessage(id: "msg2", chatId: "test-chat", role: .assistant, content: "Assistant response", isComplete: true)
        ])
        
        // 更新聊天会话
        chatListViewModel.updateMemoryChat(newChat: chatWithoutUserMessage)
        
        // 验证聊天会话没有被添加到列表中
        XCTAssertTrue(chatListViewModel.chats.isEmpty, "不包含用户消息的聊天会话不应该被添加")
        XCTAssertTrue(chatListViewModel.groupedChats.isEmpty, "分组聊天列表应该为空")
    }
    
    func testCleanupEmptyChats() {
        // 测试清理空会话功能
        
        let validChat = Chat(id: "valid-chat", messages: [
            ChatMessage(id: "msg1", chatId: "valid-chat", role: .user, content: "User message", isComplete: true)
        ])
        
        let emptyChat = Chat(id: "empty-chat", messages: [
            ChatMessage(id: "msg2", chatId: "empty-chat", role: .system, content: "System message", isComplete: true)
        ])
        
        // 添加聊天会话到列表
        chatListViewModel.chats = [validChat, emptyChat]
        let today = Calendar.current.startOfDay(for: Date())
        chatListViewModel.groupedChats = [today: [validChat, emptyChat]]
        
        // 执行清理
        chatListViewModel.cleanupEmptyChats()
        
        // 验证只有包含用户消息的聊天会话被保留
        XCTAssertEqual(chatListViewModel.chats.count, 1, "应该只有一个有效的聊天会话")
        XCTAssertEqual(chatListViewModel.chats.first?.id, validChat.id, "保留的应该是有效的聊天会话")
        XCTAssertEqual(chatListViewModel.groupedChats[today]?.count, 1, "分组中应该只有一个有效的聊天会话")
    }
    
    func testRemoveMemoryChat() {
        // 测试从内存中移除聊天会话
        
        let chat1 = Chat(id: "chat1", messages: [
            ChatMessage(id: "msg1", chatId: "chat1", role: .user, content: "Message 1", isComplete: true)
        ])
        
        let chat2 = Chat(id: "chat2", messages: [
            ChatMessage(id: "msg2", chatId: "chat2", role: .user, content: "Message 2", isComplete: true)
        ])
        
        // 添加聊天会话
        chatListViewModel.chats = [chat1, chat2]
        let today = Calendar.current.startOfDay(for: Date())
        chatListViewModel.groupedChats = [today: [chat1, chat2]]
        
        // 移除一个聊天会话
        chatListViewModel.removeMemoryChat(id: "chat1")
        
        // 验证聊天会话被正确移除
        XCTAssertEqual(chatListViewModel.chats.count, 1, "应该只剩一个聊天会话")
        XCTAssertEqual(chatListViewModel.chats.first?.id, "chat2", "剩余的应该是chat2")
        XCTAssertEqual(chatListViewModel.groupedChats[today]?.count, 1, "分组中应该只剩一个聊天会话")
    }
}
