//
//  ChatViewModelTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/06/30.
//

import XCTest
@testable import ChatAdvisor

class ChatViewModelTests: XCTestCase {
    
    var chatViewModel: ChatViewModel!
    var mockChatListViewModel: ChatListViewModel!
    
    override func setUp() {
        super.setUp()
        mockChatListViewModel = ChatListViewModel()
        chatViewModel = ChatViewModel(chatListViewModel: mockChatListViewModel)
    }
    
    override func tearDown() {
        chatViewModel = nil
        mockChatListViewModel = nil
        super.tearDown()
    }
    
    func testNewChatInitialState() {
        // 测试新建会话的初始状态
        chatViewModel.startNewChat()
        
        // 验证新建会话时消息数组为空
        XCTAssertTrue(chatViewModel.currentChat.messages.isEmpty, "新建会话时消息数组应该为空")
        
        // 验证isFirstSend标志为true
        XCTAssertTrue(chatViewModel.isFirstSend, "新建会话时isFirstSend应该为true")
        
        // 验证没有系统消息被意外插入
        let systemMessages = chatViewModel.currentChat.messages.filter { $0.role == .system }
        XCTAssertTrue(systemMessages.isEmpty, "新建会话时不应该有系统消息")
    }
    
    func testImportPromptWithEmptyContent() {
        // 测试当提示词内容为空时不插入系统消息
        chatViewModel.startNewChat()
        
        // 确保stepFormViewModel生成空的提示词
        // 这里我们假设默认情况下generatePrompt()返回空字符串
        chatViewModel.importPrompt()
        
        // 验证没有系统消息被插入
        let systemMessages = chatViewModel.currentChat.messages.filter { $0.role == .system }
        XCTAssertTrue(systemMessages.isEmpty, "当提示词为空时不应该插入系统消息")
    }
    
    func testSendFirstMessage() {
        // 测试发送第一条消息时的行为
        chatViewModel.startNewChat()
        
        let testMessage = "Hello, this is a test message"
        chatViewModel.sendMessage(testMessage)
        
        // 验证用户消息被正确添加
        let userMessages = chatViewModel.currentChat.messages.filter { $0.role == .user }
        XCTAssertEqual(userMessages.count, 1, "应该有一条用户消息")
        XCTAssertEqual(userMessages.first?.content, testMessage, "用户消息内容应该正确")
        
        // 验证isFirstSend标志被正确更新
        XCTAssertFalse(chatViewModel.isFirstSend, "发送第一条消息后isFirstSend应该为false")
    }
    
    func testSetCurrentChatWithExistingMessages() {
        // 测试设置已有消息的会话
        let existingChat = Chat(id: "test-chat", messages: [
            ChatMessage(id: "msg1", chatId: "test-chat", role: .user, content: "Test message", isComplete: true),
            ChatMessage(id: "msg2", chatId: "test-chat", role: .assistant, content: "Test response", isComplete: true)
        ])
        
        chatViewModel.setCurrentChat(chat: existingChat)
        
        // 验证会话被正确设置
        XCTAssertEqual(chatViewModel.currentChat.id, existingChat.id, "会话ID应该正确")
        
        // 验证isFirstSend为false（因为已有用户消息）
        XCTAssertFalse(chatViewModel.isFirstSend, "已有用户消息的会话isFirstSend应该为false")
    }
    
    func testMessageFiltering() {
        // 测试消息过滤逻辑
        chatViewModel.startNewChat()
        
        // 手动添加不同类型的消息进行测试
        let systemMessage = ChatMessage(id: "sys1", chatId: chatViewModel.currentChat.id, role: .system, content: "System prompt", isComplete: true)
        let userMessage = ChatMessage(id: "user1", chatId: chatViewModel.currentChat.id, role: .user, content: "User message", isComplete: true)
        let assistantMessage = ChatMessage(id: "asst1", chatId: chatViewModel.currentChat.id, role: .assistant, content: "Assistant response", isComplete: true)
        
        chatViewModel.currentChat.messages = [systemMessage, userMessage, assistantMessage]
        
        // 验证过滤后只有非系统消息
        let nonSystemMessages = chatViewModel.currentChat.messages.filter { $0.role != .system }
        XCTAssertEqual(nonSystemMessages.count, 2, "应该有2条非系统消息")
        XCTAssertTrue(nonSystemMessages.contains { $0.role == .user }, "应该包含用户消息")
        XCTAssertTrue(nonSystemMessages.contains { $0.role == .assistant }, "应该包含助手消息")
        XCTAssertFalse(nonSystemMessages.contains { $0.role == .system }, "不应该包含系统消息")
    }
}
