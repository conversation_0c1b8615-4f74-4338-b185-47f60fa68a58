// Dutch
"empty_session" = "Geen gesprek op dit moment";
"个人" = "Persoonlijk";
"以确认删除" = "Om verwijdering te bevestigen";
"价格" = "Prijs";
"余额" = "Saldo";
"充值" = "Opladen";
"充值结果" = "Oplaadresultaat";
"充值金额" = "Oplaadbedrag";
"删除" = "Verwijderen";
"删除确认" = "Verwijderingsbevestiging";
"删除账号" = "Account verwijderen";
"发送" = "Verzenden";
"发送邮件" = "E-mail verzenden";
"取消归档" = "Archivering annuleren";
"口吻" = "Toon";
"复制" = "Kopiëren";
"好的" = "OK";
"字" = "Teken";
"密码" = "Wachtwoord";
"已复制" = "Gekopieerd";
"已归档" = "Gearchiveerd";
"开启后会话将模仿口吻回复" = "Na inschakeling zal het gesprek de toon van het antwoord nabootsen";
"开始新的对话吧" = "Start een nieuw gesprek";
"归档" = "Archiveren";
"当前余额:" = "Huidige saldo:";
"您确定要删除账号吗？" = "Weet u zeker dat u uw account wilt verwijderen?";
"意见与建议" = "Feedback en suggesties";
"我确定删除账号" = "Ik bevestig de verwijdering van mijn account";
"搜索聊天" = "Zoek chat";
"暂无归档" = "Geen archieven";
"服务条款" = "Algemene voorwaarden";
"朗读" = "Hardop voorlezen";
"条款" = "Voorwaarden";
"每" = "Per";
"没有标题的会话" = "Geen titel";
"注册" = "Registreren";
"版本" = "Versie";
"用户" = "Gebruiker";
"登出" = "Uitloggen";
"登录" = "Inloggen";
"确定" = "Bevestigen";
"确认删除输入错误, 请重新输入" = "Bevestiging van verwijdering is foutief ingevoerd, probeer opnieuw";
"秒后重发" = "seconden om opnieuw te verzenden";
"约" = "Ongeveer";
"设置" = "Instellingen";
"请输入" = "Voer alstublieft in";
"请输入你的邮箱地址和密码" = "Voer uw e-mailadres en wachtwoord in";
"请输入你的邮箱地址，以及大于6位数的密码" = "Voer uw e-mailadres en een wachtwoord van meer dan 6 tekens in";
"请输入新的聊天标题" = "Voer een nieuwe chat-titel in";
"账号删除后无法恢复" = "Account verwijdering wist alle gegevens en kan niet worden hersteld";
"输入:" = "Invoer:";
"输出:" = "Uitvoer:";
"邮箱" = "E-mail";
"邮箱登录" = "E-mail login";
"重命名" = "Naam wijzigen";
"隐私政策" = "Privacybeleid";
"震动反馈" = "Trillingsfeedback";
"验证" = "Verificatie";
"昨天" = "Gisteren";
"今天" = "Vandaag";
"年" = "Jaar";
"月" = "Maand";
"日" = "Dag";
"收费标准" = "Tarieven";
"取消"="Annuleren";
"服务器内部错误"="Interne serverfout";
"当前设备没有开启应用内购买功能"="Het huidige apparaat heeft geen in-app aankopen ingeschakeld";
"获取商品失败"="Kan producten niet ophalen";
"充值说明"="Oplaadinstructies";
"购买失败"="Aankoop mislukt";
"重试"="Opnieuw proberen";
"完整会话"="Volledige sessie";
"开启可以让对话回答更精准,可能会加快余额消耗"="Inschakelen kan ervoor zorgen dat het gesprek nauwkeuriger antwoordt, wat kan leiden tot snellere saldo-verbruik";
"语音"="Stem";
"语音识别"="Selecteer de taal voor spraakherkenning om de nauwkeurigheid te verbeteren";
"选择语言"="Selecteer taal";
"通过Google登录"="Inloggen met Google";
"通过Facebook登录"="Inloggen met Facebook";
"通过Twitter登录"="Inloggen met Twitter";
"主色调"="Hoofdkleur";

"done"="Klaar";
"Previous"="Vorige";
"Next"="Volgende";
"History"="Geschiedenis";
"Context"="Context";
"Become friends"="Word vrienden";
"Choose Please"="Kies alstublieft";
// Relationships
"Friend"="Vriend";
"Family"="Familie";
"Colleague"="Collega";
"Stranger"="Vreemdeling";
"Neighbor"="Buurtgenoot";
"Classmate"="Klasgenoot";
"Teammate"="Teamgenoot";
"Acquaintance"="Kennis";
"Mentor"="Mentor";
"Mentee"="Mentee";
"Business Partner"="Zakelijke partner";
"Romantic Partner"="Romantische partner";
"Spouse"="Echtgenoot/echtgenote";
"Ex-Partner"="Ex-partner";
"Sibling"="Broer/zus";
"Parent"="Ouder";
"Child"="Kind";
"Grandparent"="Grootouder";
"Grandchild"="Kleinkind";
"In-law"="Schoonfamilie";
"Coworker"="Collega";
"Boss"="Baasje";
"Subordinate"="Ondergeschikte";
"Client"="Cliënt";
"Tutor"="Tutor";
"Roommate"="Huisgenoot";
"Lab Partner"="Labpartner";
"Study Buddy"="Studeervriend";
"Project Teammate"="Projectteamgenoot";
"Online Friend"="Online vriend";
"Pen Pal"="Penvriend";
"Travel Companion"="Reisgenoot";
"Sports Teammate"="Sportteamgenoot";
"Doctor"="Dokter";
"Patient"="Patiënt";
"Customer Service"="Klantenservice";
"Supplier"="Leverancier";
"Landlord"="Verhuurder";
"Tenant"="Huurder";
"Business Competitor"="Zakelijke concurrent";
// Degrees
"Close"="Hecht";
"Casual"="Informeel";
"Distant"="Ver";
"Professional"="Professioneel";
"Complicated"="Gecompliceerd";
"Trusted"="Vertrouwd";
"Intimate"="Intiem";
// Styles
"Formal"="Formeel";
"Casual"="Informeel";
"Friendly"="Vriendelijk";
"Professional"="Professioneel";
"Humorous"="Humoristisch";
"Direct"="Direct";
"Empathetic"="Empathisch";
"Supportive"="Ondersteunend";
"Inquisitive"="Nieuwsgierig";
// Response Lengths
"Short"="Kort";
"Medium"="Gemiddeld";
"Long"="Lang";
"Detailed"="Gedetailleerd";
// Forms
"Information"="Informatie";
"Preferences"="Voorkeuren";
"Emotion"="Emotie";
"Custom"="Aangepast";
"Chat_history"="Importeer chatgeschiedenis";
"Your Gender"="Uw geslacht";
"Your Age"="Uw leeftijd";
"Chat Partner's Gender"="Geslacht van chatpartner";
"Chat Partner's Age"="Leeftijd van chatpartner";
"Known Since"="Bekend sinds";
"Topics"="Onderwerpen";
"Goal"="Doel";
"Chat Partner's Emotion"="Emotie van chatpartner";
"Preferred Emotion in Responses"="Gewenste emotie in reacties";
"Preferred_Chat_Style"="Gewenste chatstijl";
"Preferred_Response_Length"="Gewenste lengte van de reactie";
"Relationship_Type"="Relatietype";
"Relationship_Status"="Relatiestatus";
"form_help"="
### Snel stappen wisselen
- Veeg naar links of rechts of klik op de stappen hierboven om snel stappen te wisselen.
### Optionele inhoud
- Alle inhoud is optioneel.
- Hoe gedetailleerder de invoer, hoe nauwkeuriger de AI-antwoorden.
### Chatgeschiedenis importeren
- Klik op de onderstaande knop om te beginnen met het importeren van chatscreenshots.
";
"promot_cloud"="Ik zal een context of doel bieden en hoop dat je een passende reactie kunt genereren op basis van deze informatie en in mijn toon en perspectief. Als er doelen zijn gesteld, help me deze zo snel mogelijk te bereiken. Je hoeft alleen de inhoud van het antwoord te geven.";
"promot_local" = "Je speelt nu de rol van de gebruiker, terwijl de gebruiker de rol speelt van de persoon met wie ze chatten. Zorg ervoor dat je antwoorden in overeenstemming zijn met het door de gebruiker ingestelde scenario en behoud de samenhang en natuurlijkheid. Als doelen zijn gesteld, help de gebruiker dan om ze zo snel mogelijk te bereiken. Houd er rekening mee dat de chatteksten tekstherkenning van afbeeldingen zijn, wat kan leiden tot misalignment.";
"Import_local" = "Lokale herkenning";
"Import_remote" = "Cloud herkenning";
"Relationship Background"="Relatieachtergrond";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Klik op de titel om de contextinformatie te bewerken, klik op Nieuw om nieuwe context te configureren";
"edit" = "Bewerken";
"Me"="Ik";
"对方"="Doel";
"使用"="Gebruik";
"Guest"="Gast";
"收到的的验证码"="Ontvangen verificatiecode";
"允许广告" = "Sta advertenties toe";
"广告提示" = "Gelieve advertenties toe te staan om onze betere ontwikkeling te ondersteunen. Dit wordt opnieuw ingesteld elke keer dat je opnieuw opstart. Je kunt het permanent uitschakelen nadat je hebt opgeladen.";
"Other Apps" = "Andere apps";
"打开" = "Openen";
"下载" = "Downloaden";
"其他应用" = "Andere apps";
"关于" = "Over";
"chat4o" = "Chat4o";
"chat4o_description" = "Een chat voor iedereen, de ultieme AI-assistent! Helpt bij het oplossen van verschillende problemen in het leven, werk, studie en emoties door middel van intelligente gesprekken. Of het nu gaat om dagelijkse taken of complexe uitdagingen, het biedt efficiënte en gepersonaliseerde oplossingen.";
"textGame" = "Story Maker";
"textGame_description" = "Een interactieve tekstgebaseerde verhaalspel waarin je vrij de verhaallijn kiest en je verbeelding de vrije loop laat!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Laat geavanceerde AI chat-antwoorden voor je genereren.";
"通过Tiktok登录" = "Inloggen met Tiktok";
"帮你聊天" = "Laat de meest geavanceerde AI je helpen chatten";
"继续使用" = "Door verder te gaan, ga je akkoord met onze";
"Apple登录" = "Inloggen met Apple";

// Strings gerelateerd aan EmptySessionView
"empty_session_title" = "Begin je eerste gesprek";
"empty_session_subtitle" = "Selecteer een sessie of maak een nieuw gesprek";
"empty_session_tip_click" = "Klik op de";
"empty_session_tip_create" = "knop linksboven om een nieuwe sessie te maken";
"empty_session_tip_menu" = "Vanuit het linkermenu";
"empty_session_tip_select" = "selecteer een bestaande sessie";
