/* 
  Localizable.strings
  JunShi

  Created by zwei<PERSON>g on 2024/6/2.
  
*/

// Turkish (tr)
"empty_session" = "Şu anda bir sohbet yok";
"个人" = "Kişisel";
"以确认删除" = "Silme işlemini onaylamak için";
"价格" = "Fiyat";
"余额" = "Bakiye";
"充值" = "Yükleme";
"充值结果" = "Yükleme sonucu";
"充值金额" = "Yükleme miktarı";
"删除" = "Sil";
"删除确认" = "Silme onayı";
"删除账号" = "Hesabı sil";
"发送" = "Gönder";
"发送邮件" = "E-posta gönder";
"取消归档" = "Arşivlemeyi iptal et";
"口吻" = "Ton";
"复制" = "Kopyala";
"好的" = "Tamam";
"字" = "Harf";
"密码" = "Şifre";
"已复制" = "Kopyalandı";
"已归档" = "Arşivlendi";
"开启后会话将模仿口吻回复" = "Oturum açıldıktan sonra yanıtların tonunu taklit edecek";
"开始新的对话吧" = "Yeni bir sohbet başlatın";
"归档" = "Arşiv";
"当前余额:" = "Mevcut bakiye:";
"您确定要删除账号吗？" = "Hesabı silmek istediğinizden emin misiniz?";
"意见与建议" = "Görüşler ve öneriler";
"我确定删除账号" = "Hesabı silmeyi onaylıyorum";
"搜索聊天" = "Sohbeti ara";
"暂无归档" = "Geçici arşiv yok";
"服务条款" = "Hizmet Şartları";
"朗读" = "Okuma";
"条款" = "Şartlar";
"每" = "Her";
"注册" = "Kayıt ol";
"版本" = "Sürüm";
"用户" = "Kullanıcı";
"登出" = "Çıkış yap";
"登录" = "Giriş yap";
"确定" = "Onayla";
"确认删除输入错误, 请重新输入" = "Silme onayı yanlış, lütfen yeniden girin";
"秒后重发" = "saniye sonra tekrar gönder";
"约" = "Yaklaşık";
"设置" = "Ayarlar";
"请输入" = "Lütfen girin";
"请输入你的邮箱地址和密码" = "Lütfen e-posta adresinizi ve şifrenizi girin";
"请输入你的邮箱地址，以及大于6位数的密码" = "Lütfen e-posta adresinizi ve 6 karakterden uzun bir şifre girin";
"请输入新的聊天标题" = "Lütfen yeni bir sohbet başlığı girin";
"账号删除后无法恢复" = "Hesap silindikten sonra veriler geri yüklenemez";
"输入:" = "Giriş:";
"输出:" = "Çıkış:";
"邮箱" = "E-posta";
"邮箱登录" = "E-posta ile giriş";
"重命名" = "Yeniden adlandır";
"隐私政策" = "Gizlilik Politikası";
"震动反馈" = "Titreşim geri bildirimi";
"验证" = "Doğrulama";
"没有标题的会话" = "Başlık yok";
"昨天" = "Dün";
"今天" = "Bugün";
"年" = "Yıl";
"月" = "Ay";
"日" = "Gün";
"收费标准" = "Ücretl";
"取消"="İptal";
"服务器内部错误"="Sunucu iç hatası";
"当前设备没有开启应用内购买功能"="Geçerli cihazda uygulama içi satın alma işlevi açık değil";
"获取商品失败"="Ürün alınamadı";
"充值说明"="Yükleme açıklaması";
"购买失败"="Satın alma başarısız";
"重试"="Tekrar dene";
"完整会话"="Tam sohbet";
"开启可以让对话回答更精准,可能会加快余额消耗"="Açıldığında, yanıtlar daha doğru olabilir ve bakiye tüketimini hızlandırabilir";
"语音"="Ses";
"语音识别"="Tanıma doğruluğunu artırmak için ses tanıma dilini seçin";
"选择语言"="Dili seç";
"通过Google登录"="Google ile giriş yap";
"通过Facebook登录"="Facebook ile giriş yap";
"通过Twitter登录"="Twitter ile giriş yap";
"主色调"="Ana renk";

"done"="Tamam";
"Previous" = "Önceki";
"Next" = "Sonraki";
"History" = "Geçmiş";
"Context" = "Bağlam";
"Become friends" = "Arkadaş olmak";
"Choose Please" = "Lütfen seçin";
// Relationships
"Friend" = "Arkadaş";
"Family" = "Aile";
"Colleague" = "Meslektaş";
"Stranger" = "Yabancı";
"Neighbor" = "Komşu";
"Classmate" = "Sınıf arkadaşı";
"Teammate" = "Takım arkadaşı";
"Acquaintance" = "Tanıdık";
"Mentor" = "Mentor";
"Mentee" = "Öğrenci";
"Business Partner" = "İş ortağı";
"Romantic Partner" = "Romantik partner";
"Spouse" = "Eş";
"Ex-Partner" = "Eski partner";
"Sibling" = "Kardeş";
"Parent" = "Ebeveyn";
"Child" = "Çocuk";
"Grandparent" = "Büyükbaba/Büyükanne";
"Grandchild" = "Torun";
"In-law" = "Kayın hısımlığı";
"Coworker" = "Çalışma arkadaşı";
"Boss" = "Patron";
"Subordinate" = "Ast";
"Client" = "Müşteri";
"Tutor" = "Özel öğretmen";
"Roommate" = "Oda arkadaşı";
"Lab Partner" = "Laboratuvar arkadaşı";
"Study Buddy" = "Ders çalışma arkadaşı";
"Project Teammate" = "Proje arkadaşı";
"Online Friend" = "Online arkadaş";
"Pen Pal" = "Mektup arkadaşı";
"Travel Companion" = "Seyahat arkadaşı";
"Sports Teammate" = "Spor takım arkadaşı";
"Doctor" = "Doktor";
"Patient" = "Hasta";
"Customer Service" = "Müşteri hizmetleri";
"Supplier" = "Tedarikçi";
"Landlord" = "Ev sahibi";
"Tenant" = "Kiracı";
"Business Competitor" = "İş rakibi";
// Degrees
"Close" = "Yakın";
"Casual" = "Gündelik";
"Distant" = "Uzak";
"Professional" = "Profesyonel";
"Complicated" = "Karmaşık";
"Trusted" = "Güvenilir";
"Intimate" = "Samimi";
// Styles
"Formal" = "Resmi";
"Casual" = "Gündelik";
"Friendly" = "Arkadaşça";
"Professional" = "Profesyonel";
"Humorous" = "Espirili";
"Direct" = "Doğrudan";
"Empathetic" = "Empatik";
"Supportive" = "Destekleyici";
"Inquisitive" = "Meraklı";
// Response Lengths
"Short" = "Kısa";
"Medium" = "Orta";
"Long" = "Uzun";
"Detailed" = "Detaylı";
// Forms
"Information" = "Bilgi";
"Preferences" = "Tercihler";
"Emotion" = "Duygu";
"Custom" = "Özelleştirilmiş";
"Chat_history" = "Sohbet geçmişini içe aktar";
"Your Gender" = "Cinsiyetiniz";
"Your Age" = "Yaşınız";
"Chat Partner's Gender" = "Sohbet arkadaşının cinsiyeti";
"Chat Partner's Age" = "Sohbet arkadaşının yaşı";
"Known Since" = "Tanışma tarihi";
"Topics" = "Konular";
"Goal" = "Hedef";
"Chat Partner's Emotion" = "Sohbet arkadaşının duygusu";
"Preferred Emotion in Responses" = "Cevaplarda tercih edilen duygu";
"Preferred_Chat_Style" = "Tercih edilen sohbet tarzı";
"Preferred_Response_Length" = "Tercih edilen cevap uzunluğu";
"Relationship_Type" = "İlişki türü";
"Relationship_Status" = "İlişki durumu";
"form_help"="
### Adımları hızlıca değiştirme
- Adımları hızlıca değiştirmek için sola veya sağa kaydırın veya yukarıdaki adımlara tıklayın.
### İsteğe bağlı içerik
- Tüm içerik isteğe bağlıdır.
- Ne kadar çok bilgi girilirse, AI'nin cevapları o kadar doğru olur.
### Sohbet geçmişini içe aktarma
- Sohbet ekran görüntülerini içe aktarmaya başlamak için aşağıdaki düğmeye tıklayın.
";
"promot_cloud"="Bir bağlam veya hedef sağlayacağım ve umarım bu bilgiler ve benim tonum ve perspektifim temelinde uygun bir yanıt oluşturabilirsiniz. Hedefler belirlendiyse, bunları olabildiğince çabuk başarmama yardım edin. Sadece yanıtın içeriğini sağlamanız yeterli.";
"promot_local"="Şimdi kullanıcı rolünü oynuyorsunuz, kullanıcı ise sohbet ettiği kişinin rolünü oynayacak. Yanıtlarınızın, kullanıcının belirlediği senaryoya uygun olmasını, tutarlılığı ve doğallığı korumasını sağlayın. Hedefler belirlenmişse, kullanıcının bunları en kısa sürede gerçekleştirmesine yardımcı olun. Sohbet kayıtlarının görüntülerden tanınan metin olduğunu ve bu nedenle hizalanmama olasılığı olduğunu unutmayın.";
"Import_local"="Yerel Tanıma";
"Import_remote"="Bulut Tanıma";

"Relationship Background" = "İlişki geçmişi";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Başlığa tıklayarak bağlam bilgilerini düzenleyin, yeni oluşturmak için tıklayın";
"edit" = "Düzenle";
"Me"="Ben";
"对方"="Hedef";
"使用"="Kullan";
"Guest"="Misafir";
"收到的的验证码"="Alınan doğrulama kodu";
"允许广告" = "Reklamlara izin ver";
"广告提示" = "Daha iyi bir gelişimi desteklemek için lütfen reklamlara izin verin. Her yeniden başlatmada sıfırlanacaktır. Yenileme yaptıktan sonra kalıcı olarak kapatabilirsiniz。";
"Other Apps" = "Diğer Uygulamalar";
"打开" = "Aç";
"下载" = "İndir";
"其他应用" = "Diğer Uygulamalar";
"关于" = "Hakkında";
"chat4o" = "Chat4o";
"chat4o_description" = "Herkes için sohbet, nihai yapay zeka asistanı! Akıllı diyaloglarla hayat, iş, eğitim ve duygusal sorunları çözmeye yardımcı olur. Günlük görevlerden karmaşık zorluklara kadar, verimli ve kişiselleştirilmiş çözümler sunar。";
"textGame" = "Story Maker";
"textGame_description" = "Hikayenin yönünü özgürce seçebileceğiniz interaktif bir metin hikayesi oyunu, hayal gücünüzü sınırsızca genişletin!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Gelişmiş yapay zekanın sizin için sohbet yanıtları oluşturmasına izin verin。";
"通过Tiktok登录" = "Tiktok ile giriş yap";
"帮你聊天" = "En gelişmiş AI'nın sohbet etmenize yardımcı olmasına izin verin";
"继续使用" = "Kullanmaya devam ederek, bizimle";
"Apple登录" = "Apple ile giriş yap";

// EmptySessionView ile ilgili dizeler
"empty_session_title" = "İlk konuşmanızı başlatın";
"empty_session_subtitle" = "Bir oturum seçin veya yeni bir konuşma oluşturun";
"empty_session_tip_click" = "Tıklayın";
"empty_session_tip_create" = "yeni bir oturum oluşturmak için sol üst köşedeki düğmeye";
"empty_session_tip_menu" = "Sol menüden";
"empty_session_tip_select" = "mevcut bir oturum seçin";
