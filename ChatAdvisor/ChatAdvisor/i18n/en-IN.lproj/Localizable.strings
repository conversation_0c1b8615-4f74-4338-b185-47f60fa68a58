"empty_session" = "No conversation at the moment";
"个人" = "Personal";
"以确认删除" = "To confirm deletion";
"价格" = "Price";
"余额" = "Balance";
"充值" = "Top up";
"充值结果" = "Top-up result";
"充值金额" = "Top-up amount";
"删除" = "Delete";
"删除确认" = "Delete confirmation";
"删除账号" = "Delete account";
"发送" = "Send";
"发送邮件" = "Send email";
"取消归档" = "Cancel archive";
"口吻" = "Tone";
"复制" = "Copy";
"好的" = "OK";
"字" = "Character";
"密码" = "Password";
"已复制" = "Copied";
"已归档" = "Archived";
"开启后会话将模仿口吻回复" = "After enabling, the conversation will mimic the tone of the reply";
"开始新的对话吧" = "Start a new conversation";
"归档" = "Archive";
"当前余额:" = "Current balance:";
"您确定要删除账号吗？" = "Are you sure you want to delete your account?";
"意见与建议" = "Feedback and suggestions";
"我确定删除账号" = "I confirm deletion of account";
"搜索聊天" = "Search chat";
"暂无归档" = "No archives";
"服务条款" = "Terms of service";
"朗读" = "Read aloud";
"条款" = "Terms";
"每" = "Per";
"没有标题的会话" = "No title";
"注册" = "Sign up";
"版本" = "Version";
"用户" = "User";
"登出" = "Log out";
"登录" = "Log in";
"确定" = "Confirm";
"确认删除输入错误, 请重新输入" = "Confirmation of deletion input error, please re-enter";
"秒后重发" = "seconds to resend";
"约" = "About";
"设置" = "Settings";
"请输入" = "Please enter";
"请输入你的邮箱地址和密码" = "Please enter your email address and password";
"请输入你的邮箱地址，以及大于6位数的密码" = "Please enter your email address and a password greater than 6 characters";
"请输入新的聊天标题" = "Please enter a new chat title";
"账号删除后无法恢复" = "Account deletion will clear all data and cannot be recovered";
"输入:" = "Input:";
"输出:" = "Output:";
"邮箱" = "Email";
"邮箱登录" = "Email login";
"重命名" = "Rename";
"隐私政策" = "Privacy Policy";
"震动反馈" = "Vibration feedback";
"验证" = "Verification";
"昨天" = "Yesterday";
"今天" = "Today";
"年" = "Year";
"月" = "Month";
"日" = "Day";
"收费标准" = "Charging standard";
"取消"="Cancel";
"服务器内部错误"="Internal server error";
"当前设备没有开启应用内购买功能"="The current device does not have in-app purchase enabled";
"获取商品失败"="Failed to get goods";
"充值说明"="Tips";
"购买失败"="Purchase failed";
"重试"="Retry";
"完整会话"="Full Session";
"开启可以让对话回答更精准,可能会加快余额消耗"="Enabling can make the conversation answer more accurately, which may speed up the balance consumption";
"语音"="Voice";
"语音识别"="Select the language for voice recognition to enhance accuracy";
"选择语言"="Select Language";
"通过Google登录"="Login with Google";
"通过Facebook登录"="Login with Facebook";
"通过Twitter登录"="Login with Twitter";
"主色调"="Main color";
"Apple登录" = "Login with Apple";

// chatadvisor
"done"="Done";
"Previous" = "Previous";
"Next" = "Next";
"History" = "History";
"Context" = "Context";
"Become friends" = "Become friends";
"Choose Please" = "Choose Please";
// Relationships
"Friend" = "Friend";
"Family" = "Family";
"Colleague" = "Colleague";
"Stranger" = "Stranger";
"Neighbor" = "Neighbor";
"Classmate" = "Classmate";
"Teammate" = "Teammate";
"Acquaintance" = "Acquaintance";
"Mentor" = "Mentor";
"Mentee" = "Mentee";
"Business Partner" = "Business Partner";
"Romantic Partner" = "Romantic Partner";
"Spouse" = "Spouse";
"Ex-Partner" = "Ex-Partner";
"Sibling" = "Sibling";
"Parent" = "Parent";
"Child" = "Child";
"Grandparent" = "Grandparent";
"Grandchild" = "Grandchild";
"In-law" = "In-law";
"Coworker" = "Coworker";
"Boss" = "Boss";
"Subordinate" = "Subordinate";
"Client" = "Client";
"Tutor" = "Tutor";
"Roommate" = "Roommate";
"Lab Partner" = "Lab Partner";
"Study Buddy" = "Study Buddy";
"Project Teammate" = "Project Teammate";
"Online Friend" = "Online Friend";
"Pen Pal" = "Pen Pal";
"Travel Companion" = "Travel Companion";
"Sports Teammate" = "Sports Teammate";
"Doctor" = "Doctor";
"Patient" = "Patient";
"Customer Service" = "Customer Service";
"Supplier" = "Supplier";
"Landlord" = "Landlord";
"Tenant" = "Tenant";
"Business Competitor" = "Business Competitor";
// Degrees
"Close" = "Close";
"Casual" = "Casual";
"Distant" = "Distant";
"Professional" = "Professional";
"Complicated" = "Complicated";
"Trusted" = "Trusted";
"Intimate" = "Intimate";
// Styles
"Formal" = "Formal";
"Casual" = "Casual";
"Friendly" = "Friendly";
"Professional" = "Professional";
"Humorous" = "Humorous";
"Direct" = "Direct";
"Empathetic" = "Empathetic";
"Supportive" = "Supportive";
"Inquisitive" = "Inquisitive";
// Response Lengths
"Short" = "Short";
"Medium" = "Medium";
"Long" = "Long";
"Detailed" = "Detailed";
// Forms
"Information" = "Information";
"Preferences" = "Preferences";
"Emotion" = "Emotion";
"Custom" = "Custom";
"Chat_history" = "Import Chat History";
"Your Gender" = "Your Gender";
"Your Age" = "Your Age";
"Chat Partner's Gender" = "Chat Partner's Gender";
"Chat Partner's Age" = "Chat Partner's Age";
"Known Since" = "Known Since";
"Topics" = "Topics";
"Goal" = "Target";
"Chat Partner's Emotion" = "Chat Partner's Emotion";
"Preferred Emotion in Responses" = "Preferred Emotion in Responses";
"Preferred_Chat_Style" = "Preferred Chat Style";
"Preferred_Response_Length" = "Preferred Response Length";
"Relationship_Type" = "Relationship Type";
"Relationship_Status" = "Relationship Status";
"form_help"="
### Quick Step Switching
- Swipe left or right or click on the steps above to quickly switch steps.
### Optional Content
- All content is optional.
- The more detailed the input, the more accurate the AI responses.
### Import Chat History
- Click the button below to start importing chat screenshots.
";
"promot_cloud"="I'll provide a context or goal, and I hope you can generate an appropriate response based on this information and in my tone and perspective. If goals are set, help me achieve them as quickly as possible. You only need to provide the content of the reply.";
"promot_local" = "You are now playing the role of the user, while the user will play the role of the person they are chatting with. Please ensure your responses align with the scenario set by the user, maintaining coherence and naturalness. If goals are set, assist the user in achieving them as quickly as possible. Please note that chat records are text recognized from images, which may result in misalignment.";
"Import_local" = "Local Recognition";
"Import_remote" = "Cloud Recognition";

"Relationship Background"="Relationship Background";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Click the title to edit the context information, click to create a new context configuration";
"edit" = "Edit";
"Me"="Me";
"对方"="Target";
"使用"="Use";
"Guest"="Guest";
"收到的的验证码"="Received verification code";
"允许广告"="Allow ads";
"广告提示"="Please allow ads to support our better development. It will be reset every time you restart. You can permanently turn it off after recharging.";
"Other Apps" = "Other Apps";
"打开" = "Open";
"下载" = "Download";
"其他应用" = "Other Apps";
"关于" = "About";
"chat4o" = "Chat4o";
"chat4o_description" = "A chat for everyone, the ultimate AI assistant! Aims to help solve various life, work, study, and emotional issues through intelligent conversation. Whether it's daily tasks or complex challenges, it provides efficient, personalized solutions.";
"textGame" = "Story Maker";
"textGame_description" = "An interactive text-based story game where you freely choose the storyline and let your imagination run wild!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Let advanced AI generate chat responses for you.";
"通过Tiktok登录" = "Login with Tiktok";
"帮你聊天" = "Let the most advanced AI help you chat";
"继续使用" = "By continuing to use, you agree to our";

// EmptySessionView related strings
"empty_session_title" = "Start your first conversation";
"empty_session_subtitle" = "Select a session or create a new conversation";
"empty_session_tip_click" = "Click the";
"empty_session_tip_create" = "button in the top left to create a new session";
"empty_session_tip_menu" = "From the left menu";
"empty_session_tip_select" = "select an existing session";
