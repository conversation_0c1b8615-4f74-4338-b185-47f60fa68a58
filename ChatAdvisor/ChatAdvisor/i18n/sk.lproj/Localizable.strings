// Slovak
"empty_session" = "Momentálne žiadna konverzácia";
"个人" = "Osobné";
"以确认删除" = "Na potvrdenie odstránenia";
"价格" = "Cena";
"余额" = "Zostatok";
"充值" = "Dobitie";
"充值结果" = "Výsledok dobitia";
"充值金额" = "Suma dobitia";
"删除" = "Odstrániť";
"删除确认" = "Potvrdenie odstránenia";
"删除账号" = "Odstrániť účet";
"发送" = "Odoslať";
"发送邮件" = "Odoslať e-mail";
"取消归档" = "Zrušiť archiváciu";
"口吻" = "Tón";
"复制" = "Kopírovať";
"好的" = "OK";
"字" = "Znak";
"密码" = "Heslo";
"已复制" = "Skopírované";
"已归档" = "Archivované";
"开启后会话将模仿口吻回复" = "Po zapnutí bude konverzácia napodobňovať tón odpovede";
"开始新的对话吧" = "Začať novú konverzáciu";
"归档" = "Archivovať";
"当前余额:" = "Aktuálny zostatok:";
"您确定要删除账号吗？" = "Ste si istý, že chcete odstrániť účet?";
"意见与建议" = "Názory a návrhy";
"我确定删除账号" = "Potvrdzujem odstránenie účtu";
"搜索聊天" = "Hľadať konverzáciu";
"暂无归档" = "Žiadne archívy";
"服务条款" = "Podmienky služby";
"朗读" = "Čítať nahlas";
"条款" = "Podmienky";
"每" = "Na";
"没有标题的会话" = "Konverzácia bez názvu";
"注册" = "Registrácia";
"版本" = "Verzia";
"用户" = "Používateľ";
"登出" = "Odhlásiť sa";
"登录" = "Prihlásiť sa";
"确定" = "Potvrdiť";
"确认删除输入错误, 请重新输入" = "Chyba pri potvrdení odstránenia, prosím zadajte znova";
"秒后重发" = "sekúnd do opätovného odoslania";
"约" = "O";
"设置" = "Nastavenia";
"请输入" = "Prosím zadajte";
"请输入你的邮箱地址和密码" = "Prosím zadajte svoju e-mailovú adresu a heslo";
"请输入你的邮箱地址，以及大于6位数的密码" = "Prosím zadajte svoju e-mailovú adresu a heslo dlhšie ako 6 znakov";
"请输入新的聊天标题" = "Prosím zadajte nový názov konverzácie";
"账号删除后无法恢复" = "Odstránenie účtu vymaže všetky dáta a nemôže byť obnovené";
"输入:" = "Vstup:";
"输出:" = "Výstup:";
"邮箱" = "E-mail";
"邮箱登录" = "Prihlásenie cez e-mail";
"重命名" = "Premenovať";
"隐私政策" = "Zásady ochrany osobných údajov";
"震动反馈" = "Vibračná odozva";
"验证" = "Overenie";
"昨天" = "Včera";
"今天" = "Dnes";
"年" = "Rok";
"月" = "Mesiac";
"日" = "Deň";
"收费标准" = "Cenový štandard";
"取消"="Zrušiť";
"服务器内部错误"="Interná chyba servera";
"当前设备没有开启应用内购买功能"="Aktuálne zariadenie nemá povolené nákupy v aplikácii";
"获取商品失败"="Nepodarilo sa získať produkty";
"充值说明"="Tipy";
"购买失败"="Nákup neúspešný";
"重试"="Skúsiť znova";
"完整会话"="Celá relácia";
"开启可以让对话回答更精准,可能会加快余额消耗"="Zapnutie môže spresniť odpovede v konverzácii, čo môže zrýchliť spotrebu zostatku";
"语音"="Hlas";
"语音识别"="Vyberte jazyk pre rozpoznávanie hlasu na zvýšenie presnosti";
"选择语言"="Vyberte jazyk";
"通过Google登录"="Prihlásiť sa cez Google";
"通过Facebook登录"="Prihlásiť sa cez Facebook";
"通过Twitter登录"="Prihlásiť sa cez Twitter";
"主色调"="Hlavná farba";
"Apple登录" = "Prihlásiť sa cez Apple";

// chatadvisor
"done" = "Hotovo";
"Previous" = "Predchádzajúci";
"Next" = "Ďalší";
"History" = "História";
"Context" = "Kontext";
"Become friends" = "Staňte sa priateľmi";
"Choose Please" = "Prosím, vyberte";
// Relationships
"Friend" = "Priateľ";
"Family" = "Rodina";
"Colleague" = "Kolega";
"Stranger" = "Neznámy";
"Neighbor" = "Sused";
"Classmate" = "Spolužiak";
"Teammate" = "Spoluhráč";
"Acquaintance" = "Známy";
"Mentor" = "Mentor";
"Mentee" = "Mentee";
"Business Partner" = "Obchodný partner";
"Romantic Partner" = "Romantický partner";
"Spouse" = "Manžel/Manželka";
"Ex-Partner" = "Bývalý partner";
"Sibling" = "Súrodenec";
"Parent" = "Rodič";
"Child" = "Dieťa";
"Grandparent" = "Dedko/Babka";
"Grandchild" = "Vnučka/Vnuk";
"In-law" = "Tesný príbuzný";
"Coworker" = "Kolega z práce";
"Boss" = "Šéf";
"Subordinate" = "Podriadený";
"Client" = "Klient";
"Tutor" = "Doučovateľ";
"Roommate" = "Spolubývajúci";
"Lab Partner" = "Partner v laboratóriu";
"Study Buddy" = "Študijný kamarát";
"Project Teammate" = "Spoluhráč v projekte";
"Online Friend" = "Online priateľ";
"Pen Pal" = "Dopisovateľ";
"Travel Companion" = "Spoločník na cestách";
"Sports Teammate" = "Spoluhráč v športe";
"Doctor" = "Doktor";
"Patient" = "Pacient";
"Customer Service" = "Zákaznícka podpora";
"Supplier" = "Dodávateľ";
"Landlord" = "Pán domu";
"Tenant" = "Nájomník";
"Business Competitor" = "Obchodný konkurent";
// Degrees
"Close" = "Blízky";
"Casual" = "Neformálny";
"Distant" = "Vzdialený";
"Professional" = "Profesionálny";
"Complicated" = "Zložitý";
"Trusted" = "Dôveryhodný";
"Intimate" = "Intímny";
// Styles
"Formal" = "Formálny";
"Casual" = "Neformálny";
"Friendly" = "Priateľský";
"Professional" = "Profesionálny";
"Humorous" = "Vtipný";
"Direct" = "Priamy";
"Empathetic" = "Empatický";
"Supportive" = "Podporný";
"Inquisitive" = "Zvedavý";
// Response Lengths
"Short" = "Krátky";
"Medium" = "Stredný";
"Long" = "Dlhý";
"Detailed" = "Podrobný";
// Forms
"Information" = "Informácie";
"Preferences" = "Preferencie";
"Emotion" = "Emócia";
"Custom" = "Vlastný";
"Chat_history" = "Importovať históriu chatu";
"Your Gender" = "Vaše pohlavie";
"Your Age" = "Vaša vek";
"Chat Partner's Gender" = "Pohlavie partnera v chate";
"Chat Partner's Age" = "Vek partnera v chate";
"Known Since" = "Známy od";
"Topics" = "Témy";
"Goal" = "Cieľ";
"Chat Partner's Emotion" = "Emócia partnera v chate";
"Preferred Emotion in Responses" = "Preferovaná emócia v odpovediach";
"Preferred_Chat_Style" = "Preferovaný štýl chatu";
"Preferred_Response_Length" = "Preferovaná dĺžka odpovede";
"Relationship_Type" = "Typ vzťahu";
"Relationship_Status" = "Stav vzťahu";
"form_help" = "
### Rýchla zmena krokov
- Posuňte sa doľava alebo doprava na vyššie uvedené kroky alebo na ne kliknite pre rýchle prepnutie krokov.
### Voliteľný obsah
- Všetok obsah je dobrovoľný.
- Čím podrobnejšie vstupné údaje, tým presnejšie odpovede AI.
### Importovať históriu chatu
- Kliknite na tlačidlo nižšie pre začatie importu snímok obrazovky chatu.";
"promot_cloud"="Poskytnem kontext alebo cieľ a dúfam, že na základe týchto informácií a v mojom tóne a perspektíve vytvoríte vhodnú odpoveď. Ak sú stanovené ciele, pomôžte mi ich dosiahnuť čo najrýchlejšie. Stačí poskytnúť obsah odpovede.";
"promot_local" = "Teraz hráte úlohu používateľa, zatiaľ čo používateľ bude hrať úlohu osoby, s ktorou sa rozpráva. Uistite sa, že vaše odpovede sú v súlade so scenárom, ktorý stanovil používateľ, a zachovajte konzistenciu a prirodzenosť. Ak sú stanovené ciele, pomôžte používateľovi dosiahnuť ich čo najrýchlejšie. Majte na pamäti, že záznamy chatu sú texty rozpoznané z obrázkov, čo môže viesť k nezhodám.";
"Import_local" = "Lokálne rozpoznávanie";
"Import_remote" = "Cloudové rozpoznávanie";
"Relationship Background" = "Pozadie vzťahu";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Kliknutím na názov upravíte informácie o kontexte, kliknutím na Nový vytvoríte nový kontext";
"edit" = "Upraviť";
"Me"="Ja";
"对方"="Cieľ";
"使用"="Použiť";
"Guest"="Host";
"收到的的验证码"="Kód overenia";
"允许广告" = "Povoliť reklamy";
"广告提示" = "Prosím, povoľte reklamy, aby ste podporili náš lepší rozvoj. Toto sa resetuje pri každom reštartovaní. Môžete to trvalo vypnúť po dobíjaní.";
"Other Apps" = "Iné aplikácie";
"打开" = "Otvoriť";
"下载" = "Stiahnuť";
"其他应用" = "Iné aplikácie";
"关于" = "O nás";
"chat4o" = "Chat4o";
"chat4o_description" = "Chat pre každého, ultimátny AI asistent! Pomáha riešiť rôzne problémy v živote, práci, štúdiu a emóciách prostredníctvom inteligentných rozhovorov. Či už ide o každodenné úlohy alebo zložité výzvy, poskytuje efektívne a personalizované riešenia.";
"textGame" = "Story Maker";
"textGame_description" = "Interaktívna textová príbehová hra, kde si slobodne vyberáš smerovanie deja a necháš svoju predstavivosť lietať!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Nechaj pokročilú AI generovať odpovede na chat za teba.";
"通过Tiktok登录" = "Prihlásiť sa cez Tiktok";
"帮你聊天" = "Nechajte najpokročilejšiu AI pomôcť vám chatovať";
"继续使用" = "Pokračovaním v používaní súhlasíte s našimi podmienkami";

// Reťazce súvisiace s EmptySessionView
"empty_session_title" = "Začnite svoju prvú konverzáciu";
"empty_session_subtitle" = "Vyberte reláciu alebo vytvorte novú konverzáciu";
"empty_session_tip_click" = "Kliknite na";
"empty_session_tip_create" = "tlačidlo v ľavom hornom rohu na vytvorenie novej relácie";
"empty_session_tip_menu" = "Z ľavého menu";
"empty_session_tip_select" = "vyberte existujúcu reláciu";
