/*
  Localizable.strings
  JunShi

  Created by z<PERSON><PERSON><PERSON> on 2024/6/2.
  
*/

// malay
"empty_session" = "Tiada perbualan pada masa ini";
"个人" = "Peribadi";
"以确认删除" = "Untuk mengesahkan pemadaman";
"价格" = "Harga";
"余额" = "Baki";
"充值" = "Tambah nilai";
"充值结果" = "Keputusan tambah nilai";
"充值金额" = "Jumlah tambah nilai";
"删除" = "Padam";
"删除确认" = "Pengesahan pemadaman";
"删除账号" = "Padam akaun";
"发送" = "Hantar";
"发送邮件" = "Hantar emel";
"取消归档" = "Batal arkib";
"口吻" = "Nada";
"复制" = "Salin";
"好的" = "Baik";
"字" = "Huruf";
"密码" = "Kata laluan";
"已复制" = "Disalin";
"已归档" = "Diarkibkan";
"开启后会话将模仿口吻回复" = "Selepas diaktifkan, perbualan akan meniru nada balasan";
"开始新的对话吧" = "Mulakan perbualan baru";
"归档" = "Arkib";
"当前余额:" = "Baki semasa:";
"您确定要删除账号吗？" = "Adakah anda pasti mahu memadam akaun anda?";
"意见与建议" = "Maklum balas dan cadangan";
"我确定删除账号" = "Saya mengesahkan pemadaman akaun";
"搜索聊天" = "Cari perbualan";
"暂无归档" = "Tiada arkib";
"服务条款" = "Terma perkhidmatan";
"朗读" = "Baca kuat";
"条款" = "Terma";
"每" = "Setiap";
"没有标题的会话" = "Perbualan tanpa tajuk";
"注册" = "Daftar";
"版本" = "Versi";
"用户" = "Pengguna";
"登出" = "Log keluar";
"登录" = "Log masuk";
"确定" = "Sahkan";
"确认删除输入错误, 请重新输入" = "Kesilapan pengesahan pemadaman, sila masukkan semula";
"秒后重发" = "Saat untuk hantar semula";
"约" = "Lebih kurang";
"设置" = "Tetapan";
"请输入" = "Sila masukkan";
"请输入你的邮箱地址和密码" = "Sila masukkan alamat emel dan kata laluan anda";
"请输入你的邮箱地址，以及大于6位数的密码" = "Sila masukkan alamat emel anda dan kata laluan lebih daripada 6 aksara";
"请输入新的聊天标题" = "Sila masukkan tajuk perbualan baru";
"账号删除后无法恢复" = "Akaun yang dipadam tidak dapat dipulihkan";
"输入:" = "Masukkan:";
"输出:" = "Keluar:";
"邮箱" = "Emel";
"邮箱登录" = "Log masuk emel";
"重命名" = "Namakan semula";
"隐私政策" = "Dasar privasi";
"震动反馈" = "Maklum balas getaran";
"验证" = "Pengesahan";
"昨天" = "Semalam";
"今天" = "Hari ini";
"年" = "Tahun";
"月" = "Bulan";
"日" = "Hari";
"收费标准" = "Standard bayaran";
"取消" = "Batal";
"服务器内部错误" = "Ralat dalaman pelayan";
"当前设备没有开启应用内购买功能" = "Peranti semasa tidak mengaktifkan pembelian dalam aplikasi";
"获取商品失败" = "Gagal mendapatkan barangan";
"充值说明" = "Petua";
"购买失败" = "Pembelian gagal";
"重试" = "Cuba lagi";
"完整会话" = "Sesi penuh";
"开启可以让对话回答更精准,可能会加快余额消耗" = "Mengaktifkan boleh membuat jawapan perbualan lebih tepat, mungkin akan mempercepatkan penggunaan baki";
"语音"="Suara";
"语音识别"="Pilih bahasa untuk pengenalan suara untuk meningkatkan ketepatan";
"选择语言"="Pilih Bahasa";
"通过Google登录"="Log masuk melalui Google";
"通过Facebook登录"="Log masuk melalui Facebook";
"通过Twitter登录"="Log masuk melalui Twitter";
"主色调"="Warna utama";

"done"="Selesai";
"Previous"="Sebelumnya";
"Next"="Seterusnya";
"History"="Sejarah";
"Context"="Konteks";
"Become friends"="Menjadi rakan";
"Choose Please" = "Sila pilih";
// Relationships
"Friend"="Rakan";
"Family"="Keluarga";
"Colleague"="Rakan sekerja";
"Stranger"="Orang asing";
"Neighbor"="Jiran";
"Classmate"="Rakan sekelas";
"Teammate"="Rakan pasukan";
"Acquaintance"="Kenalan";
"Mentor"="Mentor";
"Mentee"="Mentee";
"Business Partner"="Rakan perniagaan";
"Romantic Partner"="Pasangan romantik";
"Spouse"="Pasangan hidup";
"Ex-Partner"="Bekas pasangan";
"Sibling"="Adik-beradik";
"Parent"="Ibu bapa";
"Child"="Anak";
"Grandparent"="Datuk nenek";
"Grandchild"="Cicit";
"In-law"="Saudara ipar";
"Coworker"="Rakan sekerja";
"Boss"="Bos";
"Subordinate"="Bawahan";
"Client"="Pelanggan";
"Tutor"="Tutor";
"Roommate"="Rumate";
"Lab Partner"="Rakan makmal";
"Study Buddy"="Rakan kongsi belajar";
"Project Teammate"="Rakan pasukan projek";
"Online Friend"="Rakan dalam talian";
"Pen Pal"="Pen pal";
"Travel Companion"="Rakan perjalanan";
"Sports Teammate"="Rakan pasukan sukan";
"Doctor"="Doktor";
"Patient"="Pesakit";
"Customer Service"="Perkhidmatan pelanggan";
"Supplier"="Pembekal";
"Landlord"="Tuan rumah";
"Tenant"="Penyewa";
"Business Competitor"="Pesalah perniagaan";
// Degrees
"Close"="Rapat";
"Casual"="Kasual";
"Distant"="Jauh";
"Professional"="Profesional";
"Complicated"="Rumit";
"Trusted"="Dipercayai";
"Intimate"="Intim";
// Styles
"Formal"="Formal";
"Casual"="Kasual";
"Friendly"="Mesra";
"Professional"="Profesional";
"Humorous"="Lawak";
"Direct"="Langsung";
"Empathetic"="Empatik";
"Supportive"="Sokongan";
"Inquisitive"="Penyelidik";
// Response Lengths
"Short"="Pendek";
"Medium"="Sederhana";
"Long"="Panjang";
"Detailed"="Terperinci";
// Forms
"Information"="Maklumat";
"Preferences"="Pilihan";
"Emotion"="Emosi";
"Custom"="Tersuai";
"Chat_history"="Import Sejarah Perbualan";
"Your Gender"="Jantina Anda";
"Your Age"="Umur Anda";
"Chat Partner's Gender"="Jantina Rakan Perbualan";
"Chat Partner's Age"="Umur Rakan Perbualan";
"Known Since"="Dikenali Sejak";
"Topics"="Topik";
"Goal"="Sasaran";
"Chat Partner's Emotion"="Emosi Rakan Perbualan";
"Preferred Emotion in Responses"="Emosi Pilihan dalam Jawapan";
"Preferred_Chat_Style"="Gaya Perbualan Pilihan";
"Preferred_Response_Length"="Panjang Jawapan Pilihan";
"Relationship_Type"="Jenis Hubungan";
"Relationship_Status"="Status Hubungan";
"form_help"="
### Penukaran Langkah Pantas
- Sapu ke kiri atau kanan atau klik pada langkah di atas untuk menukar langkah dengan cepat.
### Kandungan Pilihan
- Semua kandungan adalah pilihan.
- Semakin terperinci input, semakin tepat jawapan AI.
### Import Sejarah Perbualan
- Klik butang di bawah untuk memulakan mengimport screenshot perbualan.
";
"promot_cloud"="Saya akan menyediakan konteks atau tujuan, dan saya berharap anda dapat menghasilkan tindak balas yang sesuai berdasarkan maklumat ini dan dalam nada dan perspektif saya. Sekiranya tujuan ditetapkan, bantu saya mencapainya secepat mungkin. Anda hanya perlu menyediakan kandungan jawapan.";
"promot_local"="Anda kini memainkan peranan pengguna, manakala pengguna akan memainkan peranan orang yang mereka sedang berbual. Pastikan respons anda selaras dengan senario yang ditetapkan oleh pengguna, mengekalkan kesinambungan dan keseragaman. Jika matlamat ditetapkan, bantu pengguna mencapainya secepat mungkin. Sila ambil perhatian bahawa rekod sembang adalah teks yang diiktiraf dari imej, yang mungkin mengakibatkan tidak sepadan.";
"Import_local"="Pengecaman Tempatan";
"Import_remote"="Pengecaman Awan";
"Relationship Background"="Latar Belakang Hubungan";
//"chat_help"="点击标题编辑情境信息,点击新建配置新的情境";
"chat_help"="Klik tajuk untuk mengedit maklumat latar belakang, klik butang baru untuk mengkonfigurasi situasi baru";
"edit" = "Sunting";
"Me"="Saya";
"对方"="Sasaran";
"使用"="Gunakan";
"Guest"="Tetamu";
"收到的的验证码"="Kod pengesahan yang diterima";
"允许广告" = "Benarkan iklan";
"广告提示" = "Sila benarkan iklan untuk menyokong pembangunan kami yang lebih baik. Ia akan ditetapkan semula setiap kali anda memulakan semula. Anda boleh mematikan secara tetap selepas menambah nilai.";
"Other Apps" = "Aplikasi Lain";
"打开" = "Buka";
"下载" = "Muat Turun";
"其他应用" = "Aplikasi Lain";
"关于" = "Tentang";
"chat4o" = "Chat4o";
"chat4o_description" = "Sembang untuk semua, pembantu AI terbaik! Bertujuan untuk membantu menyelesaikan pelbagai masalah kehidupan, kerja, pembelajaran dan emosi melalui perbualan pintar. Sama ada tugasan harian atau cabaran yang rumit, ia memberikan penyelesaian yang berkesan dan diperibadikan.";
"textGame" = "Story Maker";
"textGame_description" = "Permainan cerita berasaskan teks interaktif di mana anda boleh memilih aliran cerita secara bebas dan membiarkan imaginasi anda berkembang!";
"chatAdvisor" = "Chat Advisor";
"chatAdvisor_description" = "Biarkan AI maju menghasilkan balasan perbualan untuk anda.";
"通过Tiktok登录" = "Log masuk dengan Tiktok";
"帮你聊天" = "Biarkan AI yang paling canggih membantu anda berbual";
"继续使用" = "Dengan terus menggunakan, anda bersetuju dengan kami";
"Apple登录" = "Log masuk dengan Apple";

// Rentetan berkaitan EmptySessionView
"empty_session_title" = "Mulakan perbualan pertama anda";
"empty_session_subtitle" = "Pilih sesi atau cipta perbualan baru";
"empty_session_tip_click" = "Klik";
"empty_session_tip_create" = "butang di sudut kiri atas untuk mencipta sesi baru";
"empty_session_tip_menu" = "Dari menu kiri";
"empty_session_tip_select" = "pilih sesi sedia ada";
