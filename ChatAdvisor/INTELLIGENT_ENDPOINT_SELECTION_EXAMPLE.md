# 智能端点选择使用示例

## 概述

本文档展示了新的智能端点选择功能的使用方法和工作原理。

## 基本使用

### 1. 自动初始化

应用启动时，`BootManager` 会自动触发智能端点选择：

```swift
// BootManager.swift
override init() {
    super.init()
    // 快速初始值
    isChina = SKPaymentQueue.default().storefront?.countryCode == "CHN"
    
    // 异步智能选择
    Task {
        let result = await RegionDetector.shared.detectOptimalEndpoint()
        await MainActor.run {
            self.isChina = result.isChina
            logger.info("智能端点选择完成: \(result.selectedEndpoint)")
        }
    }
}
```

### 2. 手动触发重新选择

```swift
// 手动触发重新选择
BootManager.shared.refreshRegionDetection(forceRefresh: true)

// 或者直接使用 RegionDetector
await RegionDetector.shared.refreshDetection()
```

### 3. 获取详细结果

```swift
let result = await RegionDetector.shared.detectOptimalEndpoint()

print("选择的端点: \(result.selectedEndpoint)")
print("是否中国: \(result.isChina)")
print("置信度: \(result.confidence)")
print("检测来源: \(result.sources.joined(separator: ", "))")

// 查看测速结果
for speedResult in result.speedResults {
    print("\(speedResult.endpoint): \(Int(speedResult.responseTime))ms")
}
```

## 工作流程示例

### 场景1：中国用户，两个端点速度相近

```
地理位置检测: 0.8 (中国)
网络测速结果:
- sanva: 150ms
- sanvaCn: 120ms

速度差异: 30ms < 200ms (阈值)
决策: 基于地理位置选择 sanvaCn
结果: isChina = true, selectedEndpoint = "sanvaCn"
```

### 场景2：中国用户，海外端点明显更快

```
地理位置检测: 0.8 (中国)
网络测速结果:
- sanva: 80ms
- sanvaCn: 350ms

速度差异: 270ms > 200ms (阈值)
决策: 选择更快的端点 sanva
结果: isChina = false, selectedEndpoint = "sanva"
```

### 场景3：海外用户，网络测速失败

```
地理位置检测: 0.2 (海外)
网络测速结果: 失败

降级策略: 基于地理位置判断
结果: isChina = false, selectedEndpoint = "sanva"
```

## 缓存机制

### 缓存生效示例

```swift
// 第一次调用 - 执行完整检测
let result1 = await RegionDetector.shared.detectOptimalEndpoint()
// 输出: 执行地理位置检测 + 网络测速

// 第二次调用 - 使用缓存
let result2 = await RegionDetector.shared.detectOptimalEndpoint()
// 输出: 使用缓存结果

// 强制刷新 - 忽略缓存
let result3 = await RegionDetector.shared.detectOptimalEndpoint(forceRefresh: true)
// 输出: 重新执行完整检测
```

### 缓存失效条件

1. **时间过期**: 1小时后自动失效
2. **网络状态变化**: 网络恢复时自动重新检测
3. **手动清除**: 调用 `clearCache()` 方法
4. **强制刷新**: 使用 `forceRefresh: true` 参数

## 自动触发机制

### 网络状态变化

```swift
// NetworkStatusManager.swift
if !wasAvailable && isNetworkAvailable {
    // 网络恢复时自动重新选择端点
    Task {
        await RegionDetector.shared.refreshDetection()
        logger.info("网络恢复，已触发智能端点重新选择")
    }
}
```

### App Store 地区变化

```swift
// BootManager.swift
func paymentQueueDidChangeStorefront(_ queue: SKPaymentQueue) {
    // Storefront 变化时重新选择端点
    Task {
        let result = await RegionDetector.shared.detectOptimalEndpoint(forceRefresh: true)
        await MainActor.run {
            self.isChina = result.isChina
        }
    }
}
```

## 性能特点

### 响应时间

- **缓存命中**: < 1ms
- **首次检测**: 3-5秒（包含网络测速）
- **降级模式**: < 100ms（仅地理位置检测）

### 网络消耗

- **测速请求**: 每个端点 < 1KB（HEAD 请求）
- **缓存有效期**: 1小时，减少重复请求
- **并发测试**: 同时测试多个端点，总耗时更短

## 错误处理

### 网络测速失败

```swift
// 自动降级到地理位置判断
if validResults.isEmpty {
    let isChina = geoScore > 0.5
    let endpoint = isChina ? "sanvaCn" : "sanva"
    logger.info("网络测速失败，回退到地理位置判断: \(endpoint)")
    return (isChina, confidence, endpoint)
}
```

### 超时处理

```swift
// 3秒超时设置
request.timeoutInterval = speedTestTimeout

// 超时后返回失败结果，不影响其他端点测试
```

## 调试信息

### 日志输出示例

```
地理位置检测 - 评分: 0.8, 因子: Storefront: CHN, 语言偏好: 100%, 时区: 100%
网络测速完成 - 结果: sanva: 150ms, sanvaCn: 120ms
速度差异较小(30ms)，基于地理位置选择: sanvaCn
最终决策: 端点=sanvaCn, 地区=中国, 置信度=0.6
智能端点选择完成: sanvaCn, 置信度: 0.6, 来源: 地理位置检测, 网络性能测试, 综合决策算法
```

### 测试验证

```swift
// 运行单元测试
xcodebuild test -scheme ChatAdvisor -only-testing:ChatAdvisorTests/RegionDetectorTests

// 性能测试
func testDetectionPerformance() {
    measure {
        // 测试智能端点选择的性能
    }
}
```

## 最佳实践

1. **应用启动**: 让系统自动执行，无需手动干预
2. **网络变化**: 依赖自动触发机制，无需手动处理
3. **错误处理**: 系统已内置完整的降级策略
4. **性能监控**: 通过日志监控选择结果和响应时间
5. **用户体验**: 整个过程对用户透明，无需额外UI

这个智能端点选择系统能够自动为用户选择最优的网络连接，提供更好的应用体验。
