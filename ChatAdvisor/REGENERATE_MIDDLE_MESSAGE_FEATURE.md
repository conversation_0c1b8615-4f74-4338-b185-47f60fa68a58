# 重新生成中间消息功能实现

## 功能概述

实现了重新生成逻辑的智能调整，区分最后一个消息和中间消息的处理方式：

- **最后一个消息**：保留原有逻辑，直接重新生成
- **中间消息**：弹出确认提示，告知用户后续聊天内容将被清空，用户确认后执行重新生成并清空后续内容

## 实现的功能

### 1. 用户偏好设置
- 添加了 `skipRegenerateMiddleMessageWarning` 偏好设置
- 用户可以选择"下次不再提示"，避免重复确认

### 2. 智能判断逻辑
- 自动判断当前消息是否为最后一个AI消息
- 根据消息位置决定是否显示确认对话框

### 3. 确认对话框
- 美观的自定义确认对话框
- 包含复选框"下次不再提示"功能
- 清晰的警告信息和操作按钮

### 4. 后续消息清空
- 用户确认后，自动删除指定消息之后的所有消息
- 同步更新UI和数据库
- 然后从当前消息位置重新生成

## 修改的文件

### 1. Preferences.swift
```swift
// 添加新的偏好设置
static let skipRegenerateMiddleMessageWarning = Option<Bool>(
    key: "skipRegenerateMiddleMessageWarning", 
    default: false
)
```

### 2. ChatViewModel.swift
- 添加确认对话框状态管理属性
- 重构 `regenerateMessage` 方法，添加智能判断逻辑
- 新增 `isLastAIMessage`、`confirmRegenerate`、`cancelRegenerate` 方法
- 新增 `performRegenerate` 方法，包含清空后续消息的逻辑

### 3. RegenerateConfirmationView.swift (新文件)
- 自定义确认对话框组件
- 支持复选框功能
- 美观的UI设计，符合应用风格

### 4. ChatView.swift
- 添加确认对话框的overlay显示逻辑
- 集成到现有的UI结构中

## 使用流程

1. **用户点击重新生成**：
   - 如果是最后一个消息 → 直接重新生成
   - 如果是中间消息且未设置跳过警告 → 显示确认对话框
   - 如果是中间消息但已设置跳过警告 → 直接重新生成并清空后续消息

2. **确认对话框交互**：
   - 用户可以选择"下次不再提示"
   - 点击"确定" → 执行重新生成并清空后续消息
   - 点击"取消"或点击背景 → 取消操作

3. **重新生成执行**：
   - 清空指定消息之后的所有消息
   - 重置当前消息内容
   - 发送重新生成请求
   - 流式显示新的回复内容

## 技术特点

- **智能判断**：自动识别消息位置，提供不同的处理策略
- **用户友好**：清晰的提示信息和可选的跳过功能
- **数据一致性**：同步更新UI和数据库，确保数据一致性
- **性能优化**：复用现有的重新生成逻辑，最小化代码重复
- **UI集成**：无缝集成到现有的聊天界面中

## 测试建议

1. 测试最后一个消息的重新生成（应该保持原有行为）
2. 测试中间消息的重新生成（应该显示确认对话框）
3. 测试"下次不再提示"功能
4. 测试取消操作
5. 测试后续消息清空功能
6. 测试数据库同步

## 注意事项

- 确保在重新生成前正确保存用户的偏好设置
- 后续消息的删除操作是不可逆的，需要谨慎处理
- 确认对话框的显示不会影响其他UI操作
- 保持与现有重新生成逻辑的兼容性
