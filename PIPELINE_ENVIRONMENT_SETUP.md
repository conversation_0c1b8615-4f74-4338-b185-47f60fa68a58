# 流水线环境配置说明

本文档说明ChatAdvisor项目的GitLab CI/CD流水线如何正确传递环境参数，确保生产环境部署时使用正确的配置。

## 问题背景

当使用 `release:` 触发流水线构建时，需要确保：
1. 构建过程使用生产环境配置
2. 部署后的应用连接到生产数据库
3. 环境变量正确传递到运行时

## 解决方案

### 环境变量传递链路

```
GitLab CI → 构建环境 → PM2配置 → 应用运行时
    ↓           ↓          ↓         ↓
NODE_ENV=production → 生产构建 → 生产配置 → 生产数据库
```

## 配置详情

### 1. GitLab CI配置

#### 前端 (admin-frontend/.gitlab-ci.yml)
```yaml
# 构建阶段设置环境变量
export NODE_ENV=production
yarn build:prod

# 部署阶段传递环境变量
ssh "
  export NODE_ENV=production
  yarn pm-start-only
"
```

#### 后端 (ChatAdvisorServer/.gitlab-ci.yml)
```yaml
# 构建阶段设置环境变量
export NODE_ENV=production
yarn build:prod

# 部署阶段传递环境变量
ssh "
  export NODE_ENV=production
  yarn pm-start-only
"
```

### 2. PM2配置

#### 前端 (admin-frontend/pm2.config.cjs)
```javascript
env: {
  NODE_ENV: 'production',
  PM2_WORKING_PATH: workingPath,
  PM2_LOGS_PATH: logsDirectory,
}
```

#### 后端 (ChatAdvisorServer/pm2.config.cjs)
```javascript
env: {
  NODE_ENV: 'production',
  PORT: 53011,
  ALLOWED_ORIGINS: 'https://advisor.sanva.tk,https://advisor.sanva.top,https://admin.sanva.top',
  PM2_WORKING_PATH: workingPath,
  PM2_LOGS_PATH: logsDirectory,
}
```

### 3. 应用配置

#### 前端环境检测
```typescript
// src/config/api.ts
const isProduction = process.env.NODE_ENV === 'production' || import.meta.env.PROD;

const baseURL = isProduction 
  ? 'https://admin.sanva.top/api'  // 生产环境
  : 'http://localhost:33001';      // 开发环境
```

#### 后端数据库选择
```typescript
// src/config/env.ts
export const ENV_MONGODB_URI = (() => {
    if (process.env.MONGODB_URI) {
        return process.env.MONGODB_URI;
    }
    
    if (env.isProduction) {
        return 'mongodb://localhost:27017/ChatAdvisor?replicaSet=rs0';
    } else {
        return 'mongodb://localhost:27017/ChatAdvisor_test';
    }
})();
```

## 触发流程

### 1. 触发部署
```bash
git commit -m "release: 发布版本 v1.0.4"
git push origin main
```

### 2. 流水线执行
1. **检测触发条件**: 提交消息包含 `release:`
2. **设置环境变量**: `NODE_ENV=production`
3. **构建项目**: 使用生产环境配置构建
4. **部署到服务器**: 传递环境变量到远程服务器
5. **启动服务**: PM2使用生产环境配置启动

### 3. 环境验证
- 前端连接到 `https://admin.sanva.top/api`
- 后端连接到 `ChatAdvisor` 数据库（副本集）
- 界面显示生产环境标识（绿色）

## 验证方法

### 1. 运行检查脚本
```bash
./scripts/check-pipeline-env.sh
```

### 2. 检查部署日志
在GitLab CI/CD流水线中查看：
- 构建阶段是否设置了 `NODE_ENV=production`
- 部署阶段是否传递了环境变量
- PM2启动是否使用了正确配置

### 3. 检查运行时环境
部署完成后，在服务器上检查：
```bash
# 检查PM2进程环境变量
pm2 show admin-frontend
pm2 show chat-advisor-server-v2

# 检查应用日志
pm2 logs admin-frontend
pm2 logs chat-advisor-server-v2
```

## 关键配置文件

### 必需的环境变量设置
1. **GitLab CI**: 在构建和部署阶段设置 `NODE_ENV=production`
2. **PM2配置**: 在 `env` 部分设置 `NODE_ENV: 'production'`
3. **应用代码**: 基于 `NODE_ENV` 自动选择配置

### 构建脚本
- 前端: `yarn build:prod`
- 后端: `yarn build:prod`

### 启动脚本
- 前端: `yarn pm-start-only`
- 后端: `yarn pm-start-only`

## 故障排除

### 问题1: 部署后仍连接开发数据库
**原因**: 环境变量未正确传递
**解决**: 检查GitLab CI和PM2配置中的 `NODE_ENV` 设置

### 问题2: 前端API请求失败
**原因**: 前端未使用生产环境API端点
**解决**: 确保构建时设置了 `NODE_ENV=production`

### 问题3: PM2服务启动失败
**原因**: 环境变量或配置文件问题
**解决**: 检查PM2配置文件和环境变量设置

## 最佳实践

1. **环境隔离**: 开发和生产环境完全分离
2. **自动检测**: 基于 `NODE_ENV` 自动选择配置
3. **一致性**: 所有配置文件使用相同的环境变量
4. **验证**: 部署后验证环境配置是否正确
5. **监控**: 通过日志和界面标识确认环境状态

## 总结

通过在GitLab CI/CD流水线中正确设置和传递 `NODE_ENV=production` 环境变量，确保：

- ✅ 构建时使用生产环境配置
- ✅ 部署时传递环境变量到服务器
- ✅ PM2启动时使用生产环境设置
- ✅ 应用运行时自动选择生产数据库
- ✅ 前端连接到正确的API端点

这样就实现了完整的环境隔离和自动化部署。
